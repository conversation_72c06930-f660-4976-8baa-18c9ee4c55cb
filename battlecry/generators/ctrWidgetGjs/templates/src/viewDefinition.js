/**
 * <AUTHOR> <USER_EMAIL>
 * WIDGET_NAME - view definition
 */

const viewDefinition = (editor, { Builder, appActions: { setNotify } }) => ({
  init({ model }) {
    console.log('[viewDefinition] - init ')
    this.listenTo(model, 'change:yourProps', this.handleChangeYourProps)
  },

  onRender({ model }) {
    // TODO - on render
  },

  removed() {
    // TODO - on remove
  },

  handleChangeYourProps(_, value) {
    // TODO
  }
})

export default viewDefinition
