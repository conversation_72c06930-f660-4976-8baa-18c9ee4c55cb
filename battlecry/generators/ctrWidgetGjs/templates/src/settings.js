/**
 * <AUTHOR> <USER_EMAIL>
 * WIDGET_NAME - settings
 */

export const WIDGET_SETTINGS = {
  // classes: [], // class for main component wrapper
  attributes: {
    yourAtt: 'default-value' // trait without [changeProp]
  },
  yourProp: 'default-value', // trait with [changeProp] = 1
  traits: [
    {
      label: 'Prop label',
      name: 'yourProp',
      type: 'select', // select | checkbox | text ,...
      changeProp: 1
    },
    {
      label: 'Prop label',
      name: 'yourAttr',
      type: 'select' // select | checkbox | text ,...
    }
  ],
  'script-props': []
}
