import { Generator, File, namedCasex, casex, log } from 'battlecry'
const OUTPUT_PATH = 'packages'

export default class WidgetGenerator extends Generator {
  config = {
    generate: {
      args: 'name',
      description: 'Create widget name'
    }
  }

  get widgetName() {
    return this.args.name
  }

  get widgetVersion() {
    return this.args.version || '1.0.0'
  }

  get modulePath() {
    return `${OUTPUT_PATH}/${this.widgetName}`
  }

  init() {
    this.generator('widget-gjs').setArgs({ name: 'todo' }).play('generate')
  }

  generate() {
    const [userName] = this.exec('git config user.name').toString().split('\n')
    const [userEmail] = this.exec('git config user.email').toString().split('\n')

    this.copyFile('package*', null,{userName, userEmail})
    this.copyFile('.eslint*',null, {userName, userEmail})
    this.copyFile('index.js', 'src', {userName, userEmail})
    this.copyFile('index.gjs.js', 'src', {userName, userEmail})
    this.copyFile('_debug*', 'src', {userName, userEmail})
    this.copyFile('controller*', 'src', {userName, userEmail})
    this.copyFile('template.html', 'src', {userName, userEmail})
    this.copyFile('style.scss', 'src', {userName, userEmail})
    this.copyFile('viewDefinition.js', 'src', {userName, userEmail})
    this.copyFile('settings.js', 'src', {userName, userEmail})
    // this.copyFile('viewModel.js', 'src', {userName, userEmail})
  }

  copyFile(fileTemplate, subPath, {userName, userEmail}) {
    const template = this.template(fileTemplate)
    const path = this.modulePath + (subPath ? `/${subPath}` : '')

    let file = new File(`${path}/${template.filename}`, template.filename)

    if (!file.exists) {
      file = template
      file.replaceText('WIDGET_NAME', this.widgetName)
        .replaceText('WIDGET_VERSION', this.widgetVersion)
        .replaceText('WIDGET_VERSION', this.widgetVersion)
        .replaceText('USER_NAME', userName)
        .replaceText('USER_EMAIL', userEmail)

      file.saveAs(`${path}/${template.filename}`, template.filename)
    }
  }

  getInfo() {
    rl.question("What is your name ? ", function(name) {
      rl.question("Where do you live ? ", function(country) {
        console.log(`${name}, is a citizen of ${country}`);
        rl.close();
      });
    });

    rl.on("close", function() {
      console.log("\nBYE BYE !!!");
      process.exit(0);
    });
  }
}
