import { Generator, File, namedCasex, casex, log } from 'battlecry'

const OUTPUT_PATH = 'packages'

export default class WidgetGenerator extends Generator {
  config = {
    generate: {
      args: 'name',
      description: 'Create widget name'
    }
  }

  get widgetName() {
    return this.args.name
  }

  get widgetVersion() {
    return this.args.version || '1.0.0'
  }

  get modulePath() {
    return `${OUTPUT_PATH}/${this.widgetName}`
  }

  init() {
    this.generator('widget').setArgs({ name: 'todo' }).play('generate')
  }

  generate() {
    const [userName] = this.exec('git config user.name').toString().split('\n')
    const [userEmail] = this.exec('git config user.email').toString().split('\n')

    // this.addActionsToDuck()
    this.copyFile('package*', null, {userName, userEmail})
    this.copyFile('.eslint*', null, {userName, userEmail})
    this.copyFile('index*', 'src', {userName, userEmail})
    this.copyFile('_debug*', 'src', {userName, userEmail})
    this.copyFile('controller*', 'src', {userName, userEmail})
    this.copyFile('widget.html', 'src', {userName, userEmail})
    this.copyFile('widget.scss', 'src', {userName, userEmail})
  }

  copyFile(fileTemplate, subPath, {userName, userEmail}) {
    const template = this.template(fileTemplate)
    const path = this.modulePath + (subPath ? `/${subPath}` : '')

    let file = new File(`${path}/${template.filename}`, template.filename)

    if (!file.exists) {
      file = template
      file.replaceText('WIDGET_NAME', this.widgetName)
        .replaceText('WIDGET_VERSION', this.widgetVersion)
        .replaceText('USER_NAME', userName)
        .replaceText('USER_EMAIL', userEmail)

      file.saveAs(`${path}/${template.filename}`, template.filename)
    }
  }
}
