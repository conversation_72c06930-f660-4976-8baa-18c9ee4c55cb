const path = require('path')
const HtmlWebpackPlugin = require('html-webpack-plugin')

module.exports = {
  output: {
    path: path.resolve(__dirname, 'dist')
    // filename: `[name].js`,
    // library: '[name]',
    // libraryTarget: 'umd'
  },
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept'
    },
    contentBase: [path.join(__dirname, './../dist'), path.join(__dirname, './public'), path.join(__dirname, '.')],
    // contentBasePublicPath: '/common',
    // compress: true,
    port: 1234,
    // host: '0.0.0.0',
    sockHost: process.env.WDS_SOCKET_HOST,
    sockPath: process.env.WDS_SOCKET_PATH,
    sockPort: process.env.WDS_SOCKET_POR,
    // host: os.hostname(),
    after: function(app, server, compiler) {
      console.log('started....')
    },
    watchOptions: {
      poll: true
    },
    watchContentBase: true,
    hot: true,
    transportMode: 'ws',
    quiet: true,
    injectClient: false,
    clientLogLevel: 'none'
  },
  // devtool: false,
  plugins: [
    new HtmlWebpackPlugin({
      template: 'index.html'
      // inject: false
    })
  ],
  mode: 'development',
  module: {
    rules: [
      {
        test: /\.html$/,
        use: {
          loader: 'html-loader'
        }
      }, // end of html-loader
      {
        test: /\.m?js$/,
        exclude: /(node_modules)/,
        use: {
          loader: 'babel-loader',
          options: {
            configFile: path.resolve(__dirname, './../babel.config.js')
          }
        }
      }, // end of babel-loader
      // {
      //   test: /\.s[ac]ss$/i,
      //   use: [
      //     'style-loader',
      //     // Translates CSS into CommonJS
      //     'css-loader',
      //     // Compiles Sass to CSS
      //     'sass-loader'
      //   ]
      // }, // end of sass-loader
      {
        test: /\.css$/i,
        use: [
          'style-loader',
          // Translates CSS into CommonJS
          'css-loader'
        ]
      }, // end of scss
      {
        test: /\.s[ac]ss$/i,
        //exclude: /global\.s[ac]ss$/i,
        use: [
          // Translates CSS into CommonJS
          'css-loader',
          // Compiles Sass to CSS
          'sass-loader'
        ]
      }, // end of scss
      {
        test: /\.(eot|svg|otf|ttf|woff|woff2)$/,
        use: 'file-loader'
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: 'svg-url-loader'
          }
        ]
      },
      {
        test: /\.(jpg|png|gif)$/,
        use: [{loader: 'url-loader'},{loader: 'image-webpack-loader'}]
      }
    ]
  },
  resolve: {
    alias: {
      assets: path.resolve(__dirname, './public/assets'),
      gjs: path.resolve(__dirname, './src/gjs/'),
      // assets: path.resolve(__dirname, './public/')
    }
  }
}
