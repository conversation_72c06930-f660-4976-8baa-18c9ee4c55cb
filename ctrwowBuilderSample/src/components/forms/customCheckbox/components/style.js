export default `
.checkboxContainer {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}


.checkboxContainer input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}


.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  border-radius: 4px;
  border: 1px solid #979797;
}


.checkboxContainer:hover input ~ .checkmark {
  background-color: #ccc;
}


.checkmark .icon {
  content: "";
  position: absolute;
  display: none;
}


.checkboxContainer input:checked ~ .checkmark .icon {
  display: block;
  border-color: #2196F3;
}


.checkboxContainer .checkmark .icon {
  left: 8px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 4px 4px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  box-sizing: content-box;
}
`