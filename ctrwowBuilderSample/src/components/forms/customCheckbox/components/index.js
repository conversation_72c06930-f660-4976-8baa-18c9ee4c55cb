
import style from './style'
import {COMPONENT_NAME} from './../constants'
import template from './template'

export default editor => {
  const domComponents = editor.DomComponents
  
  domComponents.addType(COMPONENT_NAME, {
    model: {
      defaults: {
        tagName: 'div',
        name: 'Checkbox',
        droppable: true,
        css: style
      }, // end of [defaults]
    }, //end of model
    
    view: {
      init() {
        const {model} = this
        const components = model.components()
        if (!components.length) {
          components.reset()
          components.add(template)
        }
      }
    }
  })
}

