import imgPath from 'assets/img/builders/icons/check-box.svg'
import getCustomBlockName from 'gjs/utils/getCustomBlockName'
import { COMPONENT_NAME } from "./constants";

export default (editor, config) => {
  const blockManager = editor.BlockManager;
  blockManager.add(COMPONENT_NAME, {
    label: getCustomBlockName('Checkbox', imgPath),
    content: {
      type: COMPONENT_NAME
    },
    ...config
  });
};
