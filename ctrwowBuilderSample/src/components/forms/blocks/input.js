import imgPath from 'assets/img/builders/icons/input.svg'
import getCustomBlockName from 'gjs/utils/getCustomBlockName'


export default function (bm, config = {}) {
  bm.add('input', {
    label: getCustomBlockName(config.labelInputName, imgPath),
    ...config,
    content: {
      type: 'form-group',
      components: [
        {
          type: 'custom-input'
        },
        {
          type: 'control-label',
          content: 'Text'
        }
      ]
    },
  });
}
