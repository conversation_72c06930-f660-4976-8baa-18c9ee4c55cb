import imgPath from 'assets/img/builders/icons/select.svg'
import getCustomBlockName from 'gjs/utils/getCustomBlockName'


export default function (bm, config = {}) {
  bm.add('select', {
    label: getCustomBlockName(config.labelSelectName, imgPath),
    ...config,
    content: {
      type: 'form-group',
      components: [
        {
          type: 'select'
        },
        {
          type: 'control-label',
          content: 'Text'
        }
      ]
    }
  });
}
