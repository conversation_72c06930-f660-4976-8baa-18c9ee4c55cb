import getCustomBlockName from 'gjs/utils/getCustomBlockName'
import imgPath from 'assets/img/builders/icons/check-box.svg'

export default function (bm, config = {}) {
  bm.add('checkbox', {
    label: getCustomBlockName(config.labelCheckboxName, imgPath),
    ...config,
    content: {
      type: 'form-group',
      classes: ['checkbox'],
      components: [
        {
          type: 'checkbox'
        },
        {
          type: 'control-label',
          content: 'Checkbox'
        }
      ]
    },
  });
}

