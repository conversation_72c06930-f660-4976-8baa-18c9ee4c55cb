import button from './button';
// import checkbox from './checkbox';
import form from './form';
import input from './input';
import label from './label';
import radio from './radio';
import select from './select';
import textarea from './textarea';

export default (editor, opts = {}) => {
  const config = opts;
  let bm = editor.BlockManager;

  if (config.blocks.indexOf('form') >= 0) {
    form(bm, config)
  }

  if (config.blocks.indexOf('input') >= 0) {
    input(bm, config)
  }

  if (config.blocks.indexOf('textarea') >= 0) {
    textarea(bm, config)
  }

  if (config.blocks.indexOf('select') >= 0) {
    select(bm, config)
  }

  if (config.blocks.indexOf('button') >= 0) {
    button(bm, config)
  }

  if (config.blocks.indexOf('label') >= 0) {
    label(bm, config)
  }

  // if (config.blocks.indexOf('checkbox') >= 0) {
  //   checkbox(bm, config)
  // }

  if (config.blocks.indexOf('radio') >= 0) {
    radio(bm, config)
  }
}
