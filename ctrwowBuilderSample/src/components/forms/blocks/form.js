import imgPath from 'assets/img/builders/icons/form.svg'
import getCustomBlockName from 'gjs/utils/getCustomBlockName'

export default function (bm, config = {}) {
  bm.add('form', {
    label: getCustomBlockName(config.labelForm, imgPath),
    ...config,
    content: {
      type: 'form',
      components: [
        {
          type: 'form-group',
          components: [
            {
              type: 'custom-input'
            },
            {
              type: 'control-label',
              content: 'Input 1'
            }
          ]
        },
        {
          type: 'form-group',
          components: [
            {
              type: 'custom-input'
            },
            {
              type: 'control-label',
              content: 'Input 2'
            }
          ]
        },
        {
          type: 'form-group',
          components: [{
            type: 'button',
            content: 'Button'
          }]
        }
      ]
    }
  });
}
