import imgPath from 'assets/img/builders/icons/textarea.svg'
import getCustomBlockName from 'gjs/utils/getCustomBlockName'


export default function (bm, config = {}) {
  bm.add('textarea', {
    label: getCustomBlockName(config.labelTextareaName, imgPath),
    ...config,
    content: {
      type: 'form-group',
      components: [
        {
          type: 'textarea'
        },
        {
          type: 'control-label',
          content: 'Textarea'
        }
      ]
    }
  });
}
