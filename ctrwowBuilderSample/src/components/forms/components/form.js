import {traitOptions} from '../traits'
export default function (domc, config = {}) {
  const options = traitOptions(config);
  const css= `
    #id{
      padding: 20px;
    }
  `
  domc.addType('form', {
    isComponent: () => false,
    model: {
      defaults: {
        droppable: ':not([data-gjs-type="form"])',
        draggable: ':not([data-gjs-type="form"])',
        tagName: 'form',
        name: 'Form',
        traits: [
          options.idTrait,
          options.nameTrait,
          options.actionURL,
          options.methodAction,
        ],
        css: css,
        script() {
          var self = this;
          window.ctrwowUtils.getDependencies([
            "https://cdn.jsdelivr.net/npm/jquery-validation@1.19.0/dist/jquery.validate.min.js"
          ]).then(
           () => {
            const handleFocusOut = (element, event) => {
              validator.element(`#${element.id}`);
            }
            const messages = {};
            const inputs = $(`#${self.id}`).find('input, textarea, select');
            if (inputs.length) {
              for (let i = 0; i < inputs.length; i++) {
                messages[inputs[i].name] = {};
                const requiredText = $(inputs[i]).attr('required-text');
                const regexText = $(inputs[i]).attr('regex-text');
                requiredText && (messages[inputs[i].name].required = requiredText);
                regexText && (messages[inputs[i].name].regex = regexText);
              }
            }
            const validator = $(`#${self.id}`).validate({
              onfocusout: handleFocusOut,
              focusCleanup : true,
              onsubmit: false,
              messages
            });
            jQuery.validator.addMethod("regex", function(value, element) {
              const regex = element.getAttribute('regex');
              return new RegExp(regex, "g").test(value);
            }, "Please enter a valid format.");

            window.ctrwowUtils.events.on(self.name, function (data) {
              console.log('form data', data)
              const actionURL = self.getAttribute('action');
              if(actionURL) {
                self.submit();
              }
            });
           })
        }
      },
    },

    view: {
      events: {
        submit(e) {
          e.preventDefault();
        }
      },
      init() {
        const {model} = this;
        const attributes = model.getAttributes();
        if(!attributes.name){
          model.addAttributes({ 'name': model.getId() });
        }
      },
    },
  });
}
