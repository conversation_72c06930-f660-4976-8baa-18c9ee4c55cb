import {traitOptions} from '../traits'
export default function (domc, config = {}) {
  const options = traitOptions(config);
  let css = `
      #id {
        cursor: pointer;
        display: block;
        position: absolute;
        top: 16px;
        left: 0;
        z-index: 1;
        padding: 0 0 0 13px;
        color: #9B9B9B;
        text-align: left;
        transition: all 0.2s ease-in;
        pointer-events: none;
      }
    `;
  // INPUT
  domc.addType('control-label', {
    model: {
      defaults: {
        name: config.labelNameLabel,
        tagName: 'label',
        css: css,
        draggable: '[data-gjs-type="form-group"]',
        droppable: false,
        highlightable: false,
        selectable: false,
        hoverable: false,
        layerable: false,
        traits: [options.forTrait],
        classes: ['control-label']
      },
    }
  });
}
