import {traitOptions} from "../traits";

export default function (domc, config = {}) {
  const options = traitOptions(config);
  const css = `
    #id{
      position: relative;
      width: 100%;
      margin-bottom: 10px;
    }
    #id [type="radio"]:checked,
    #id [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }
    #id [type="radio"]:checked + label,
    #id [type="radio"]:not(:checked) + label
    {
        position: relative;
        padding-left: 28px;
        cursor: pointer;
        line-height: 20px;
        display: inline-block;
        color: #000;
        font-weight: 700;
    }
    #id [type="radio"]:checked + label:before,
    #id [type="radio"]:not(:checked) + label:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 18px;
        height: 18px;
        border: 1px solid #2e98f8;
        border-radius: 100%;
        background: #fff;
    }
    #id [type="radio"]:checked + label:after,
    #id [type="radio"]:not(:checked) + label:after {
        content: '';
        width: 10px;
        height: 10px;
        background: #2e98f8;
        position: absolute;
        top: 5px;
        left: 5px;
        border-radius: 100%;
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;
    }
    #id [type="radio"]:not(:checked) + label:after {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    #id [type="radio"]:checked + label:after {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
    }
  `;
  // RADIO
  domc.addType('radio', {
    extend: 'form-group',
    model: {
      defaults: {
        name: config.labelRadioName,
        attributes: {
          content: 'Radio'
        },
        traits: [
          options.idTrait,
          options.nameTrait,
          {
            name: 'content',
            label: 'Content',
          },
          options.valueTrait,
          options.checkedTrait,
        ],
        css: css,
        script(){
          const self = this;
          const label = self.querySelector('label');
          const isBuilder = document.querySelector('[data-gjs-type="wrapper"]');
          if(!isBuilder){
            label && label.addEventListener("click", () => {
              const input = self.querySelector('input');
              $(input).prop('checked', !input.checked);
              $(input).trigger( "change" );
            });
          }
        }
      },
    },
    view:{
      events: {
        'click': 'handleClick',
      },

      handleClick(e) {
        e.preventDefault();
      },

      init() {
        const {model} = this
        const components = model.components()
        const attributes = model.get('attributes');
        if(!components.length){
          components.add([
            {
              tagName: 'input',
              attributes: {
                type: 'radio',
                name: attributes.name,
                value: attributes.value,
                checked: !!attributes.checked,
              }
            },
            {
              tagName: 'label',
              content: attributes.content,
              highlightable: false,
              selectable: false,
              hoverable: false,
              layerable: false,
            }
          ])
        }
        this.listenTo(model, 'change:attributes', this.handleAttributes);
      },

      handleAttributes() {
        const attributes = this.model.get('attributes');
        const input = this.model.find('input');
        const label = this.model.find('label');
        if(input.length){
          input[0].addAttributes({
            name: attributes.name,
            value: attributes.value,
            checked: !!attributes.checked
          });
        }
        if(label.length){
          label[0].set('content', attributes.content);
        }
      },
    }
 });
}
