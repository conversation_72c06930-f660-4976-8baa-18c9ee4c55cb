import {traitOptions} from '../traits'

export default function (domc, config = {}) {
  const options = traitOptions(config);
  const css = `
    #id {
      width: 100%;
      border: 1px solid #CACACA;
      border-radius: 4px;
      padding: 10px;
      margin: 0;
      line-height: 1.15;
      transition: all 0.2s ease-in;
      font-size: 1.2rem !important;
      overflow: hidden
    }
  `;
  // TEXTAREA
  domc.addType('textarea', {
    extend: 'custom-input',
    model: {
      defaults: {
        name: config.labelTextareaName,
        tagName: 'textarea',
        css: css,
        traits: [
          options.idTrait,
          options.nameTrait,
          options.labelTypeTrait,
          options.labelTrait,
          options.requiredTrait,
          options.requiredTextTrait,
        ]
      },
    }
  });
}
