import {traitOptions} from '../traits'

export default function (domc, config = {}) {
  const options = traitOptions(config);
  const css = `
    #id {
      position: absolute;
      transform: translateY(-50%);
      top: 50%;
      width: 18px;
      height: 18px;
      padding: 15px;
      z-index: 2;
      float: left;
    }
  `;
  // CHECKBOX
  domc.addType('checkbox', {
    model: {
      defaults: {
        tagName: 'input',
        name: config.labelCheckboxName,
        attributes: {
          type: 'checkbox',
          value: ''
        },
        draggable: false,
        droppable: false,
        copyable: false,
        removable: false,
        labelType: 'label',
        traits: [
          options.idTrait,
          options.nameTrait,
          options.labelTrait,
          // options.valueTrait,
          options.requiredTrait,
          options.checkedTrait
        ],
        css: css,
      },

    },
    view:{
      events: {
        'click': 'handleClick',
      },

      handleClick(e) {
        e.preventDefault();
      },

      init() {
        this.listenTo(this.model, 'change:attributes:checked', this.handleChecked);
      },

      handleChecked() {
        this.el.checked = !!this.model.get('attributes').checked;
      },
    }
  });
}
