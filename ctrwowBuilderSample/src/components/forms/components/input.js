import {traitOptions} from '../traits'

export default function (domc, config = {}) {
  const options = traitOptions(config);
  const css = `
    #id {
      width: 100%;
      height: 50px;
      color: inherit;
      border: 1px solid #CACACA;
      border-radius: 4px;
      padding: 10px;
      margin: 0;
      line-height: 1.15;
      transition: all 0.2s ease-in;
      font-size: 1rem;
      outline: none;
    }
  `;
  // INPUT
  domc.addType('custom-input', {
    model: {
      defaults: {
        name: config.labelInputName,
        tagName: 'input',
        draggable: false,
        droppable: false,
        copyable: false,
        removable: false,
        attributes: {
          name: "",
          regex: ""
        },
        traits: [
          options.idTrait,
          options.nameTrait,
          options.valueTrait,
          options.labelTypeTrait,
          options.labelTrait,
          options.selectValidationTrait,
          options.regexTrait,
          options.regexTextTrait,
          options.maskTrait,
          options.requiredTrait,
          options.requiredTextTrait,
          options.minLengthTrait,
          options.maxLengthTrait,
        ],
        css: css,
        script: function() {
          const self = $(this);
          const label = self.parent('.form-group').find('.control-label')
          const setLabelSmall = () => {
            if(label.length){
              label.css({"font-size": "0.8rem", "top": "5px"});
            };
          }
          const setLabelBig = () => {
            if(label.length){
              label.css({"font-size": "1rem", "top": "16px"});
            };
          }
          if(self.val()){
            setLabelSmall();
          } else {
            setLabelBig();
          }
          self.on('focus', function() {
            setLabelSmall();
          });
          self.on('blur change', function() {
            if(self.val()){
              setLabelSmall();
              self.attr('value', self.val());
            } else {
              setLabelBig();
              self.removeAttr('value');
            }
          });
          self.on('keyup', function() {
            const tagName = self.prop("tagName");
            if(tagName === 'TEXTAREA'){
              self.css("height", "1px");
              self.css("height", (5 + self.prop("scrollHeight"))+"px");
            }
          });
          window.ctrwowUtils.getDependencies([
            "https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.15/jquery.mask.min.js",
         ]).then(
           () => {
            var mask = self.attr('mask');
            if (mask) {
              self.mask(mask);
            }
           });
        }
      },
      init(){
        const toolbars = this.get('toolbar');
        toolbars.map((tlb)=>{
          if(tlb.command === "tlb-move"){
            tlb.command = "parent-move";
          }
          return tlb;
        })
      }
    },
    view: {
      init() {
        const {model} = this;
        const attributes = model.getAttributes();
        if(!attributes.name){
          model.addAttributes({ 'name': model.getId() });
        }
        this.listenTo(model, 'change:attributes', ()=>{
          model.trigger('change:script');
        });
        this.listenTo(model, 'change:attributes:required-text change:attributes:regex-text', ()=>{
          model.closest('form').trigger('change:script');
        });
        this.checkHaveLabel();
      },
      checkHaveLabel() {
        const formGroup = this.model.parent();
        const controlList = formGroup.findType('control-label');
        let labelType = 'default';
        let paddingTop = '10px';
        if(controlList && controlList.length){
          const target = controlList[0];
          if(target.get('content')){
            labelType = 'label';
            paddingTop = '1.5rem';
          };
        }
        this.model.set('labelType', labelType);
        const styles = this.model.getStyle();
        this.model.setStyle({...styles, 'padding-top': paddingTop});
      }
    }
  });
}
