export default function (domc, config = {}) {
  const css = `
    #id{
      position: relative;
      width: 100%;
      margin-bottom: 10px;
    }
    #id label.error{
      font-size: 0.8rem;
      word-break: break-word;
      color: #cc4b37;
    }
    #id input.error{
      background-color: #f9ecea;
      border: 1px solid #cc4b37;
    }
  `;
  // INPUT
  domc.addType('form-group', {
    model: {
      defaults: {
        name: config.labelFormGroupName,
        tagName: 'div',
        draggable: true,
        droppable: '[data-gjs-type="control-label"]',
        css: css,
        classes: ['form-group']
      },
    }
  });
}
