import {traitOptions} from '../traits'

export default function (domc, config = {}) {
  const options = traitOptions(config);
  const css = `
    #id {
      width: 100%;
      height: 55px;
      border: 1px solid #CACACA;
      border-radius: 4px;
      padding: 10px 5px;
      margin: 0;
      line-height: 1.15;
      transition: all 0.2s ease-in;
      font-size: 1rem;
      outline: none;
    }
  `;
  // SELECT
  domc.addType('select', {
    extend: 'custom-input',
    model: {
      defaults: {
        name: config.labelSelectName,
        tagName: 'select',
        css: css,
        traits: [
          options.idTrait,
          options.nameTrait,
          options.labelTypeTrait,
          options.labelTrait,
          {
            name: 'value',
            label: 'Default Value'
          },
          {
            label: config.labelTraitOptions,
            type: 'select-options'
          },
          options.requiredTrait,
          options.requiredTextTrait
        ]
      },
    },
    view: {
      events: {
        'mousedown': 'handleClick',
      },
      onRender(){
          const {model} = this;
          const attributes = model.getAttributes();
          if(attributes.value){
            const options = model.components();
            for(let i = 0; i<options.length; i++){
              const model = options.models[i];
              const attOpt = model.getAttributes();
              if(attOpt.value && attOpt.value === attributes.value){
                model.setAttributes({ ...attOpt, selected: true });
              }else{
                model.setAttributes({ ...attOpt, selected: false });
              }
            };
          }
      },

      handleClick(e) {
        e.preventDefault();
      },
    }
  });
}
