import form from './form';
import formGroup from './formGroup';
import input from './input';
import textarea from './textarea';
import select from './select';
import checkbox from './checkbox';
import radio from './radio';
import button from './button';
import label from './label';

export default function(editor, opt = {}) {
  const config = opt;
  const domc = editor.DomComponents;
  form(domc, config);
  formGroup(domc, config);
  input(domc, config);
  textarea(domc, config);
  select(domc, config);
  checkbox(domc, config);
  radio(domc, config);
  button(domc, config);
  label(domc, config);
}
