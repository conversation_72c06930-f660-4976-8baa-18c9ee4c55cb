export default function (domc, config = {}) {

  const css = `
    #id {
      width: 100%;
      height: 50px;
      border: 1px solid #CACACA;
      border-radius: 4px;
      padding: 10px;
      margin: 0;
      line-height: 1.15;
      cursor: pointer;
      transition: all 0.2s ease-in;
      font-size: 1rem;
    }
  `;
  domc.addType('button', {
    model: {
      defaults: {
        name: config.labelButtonName,
        tagName: 'button',
        draggable: false,
        droppable: false,
        copyable: false,
        removable: false,
        attributes: {
          type: 'button'
        },
        css: css,
        script: function () {
          const self = $(this);

          const errorHandler = (form) => {
            const firstError = form.querySelector('.error');
            firstError && firstError.scrollIntoView({block: 'center', behavior: 'smooth'});
          }
          const getFormData = (form) => {
            const formData = {};
            const inputs = $(form).find('input, textarea, select');
            if (inputs.length) {
              for (let i = 0; i < inputs.length; i++) {
                const value = $(inputs[i]).val();
                const type = $(inputs[i]).attr('type');
                if (type === 'radio' && !$(inputs[i]).prop('checked')) {
                  break;
                }
                if (value && inputs[i].name !=='') {
                  formData[inputs[i].name] = value;
                }
              }
            }
            return formData;
          }
          const submitHandler = (forms) => {
            let submitData = {};
            for (let i = 0; i < forms.length; i++) {
              const validator = $(forms[i]).validate();
              if ($(forms[i]).css('display') === 'none') {
                continue;
              }
              const isValid = validator && validator.form();
              if (isValid && submitData) {
                const asyncValidator = window.ctrwowUtils.form.asyncValidateForm();
                if(!asyncValidator.isValid) {
                  submitData = null
                  alert(asyncValidator.message)
                } else {
                  submitData[$(forms[i]).attr('name')] = getFormData(forms[i]);
                }
              } else if(submitData) {
                submitData = null;
                errorHandler(forms[i]);
              }
            }
            return submitData || {};
          }
          const resetHandler = (forms) => {
            for (let i = 0; i < forms.length; i++) {
              const inputs = $(forms[i]).find('input, textarea, select');
              if (inputs.length) {
                for (let i = 0; i < inputs.length; i++) {
                  const type = $(inputs[i]).attr('type');
                  if (type !== 'radio' && type !== 'checkbox') {
                    $(inputs[i]).val('');
                  }
                }
              }
            }
          }

          window.ctrwowUtils.getDependencies([
            "https://cdnjs.cloudflare.com/ajax/libs/pubsub-js/1.7.0/pubsub.min.js",
            "https://cdn.jsdelivr.net/npm/jquery-validation@1.19.0/dist/jquery.validate.min.js",
          ]).then(
            () => {
              self.on('click', (e) => {
                e.preventDefault();
                const type = self.attr('type');
                const parent = self.closest('form');
                const children = $('body').find('form');
                switch (type) {
                  case 'submit': {
                    let submitData = {};
                    if (parent.length) {
                      submitData = submitHandler(parent);
                    }

                    if (children.length && !parent.length) {
                      submitData = submitHandler(children);
                    }
                    console.log('submitData', submitData);
                    Object.keys(submitData).length && window.PubSub.publish(self.attr('name'), submitData);
                    Object.keys(submitData).length && window.ctrwowUtils.events.emit(parent.attr('name'), submitData);
                    Object.keys(submitData).length && window.ctrwowUtils.events.emit('ctr_form_' + self.attr('name'), submitData);
                    break;
                  }
                  case 'reset': {
                    if (parent.length) {
                      resetHandler(parent);
                    }
                    if (children.length && !parent.length) {
                      resetHandler(children);
                    }
                    console.log('resetData');
                    break;
                  }
                  default: {

                  }
                }
              });
            });
        },
        traits: [
          {
            name: 'id',
            label: config.labelTraitId,
          },
          {
            name: 'name',
            label: config.labelTraitName,
          },
          {
            name: 'title',
            label: config.labelTraitTitle,
          },
          {
            type: 'content',
            label: 'Text',
          }, {
            label: config.labelTraitType,
            type: 'select',
            name: 'type',
            default: 'button',
            options: [
              {value: 'submit', name: config.labelTypeSubmit},
              {value: 'reset', name: config.labelTypeReset},
              {value: 'button', name: config.labelTypeButton},
            ]
          }
        ]
      },
    },
    view: {
      init() {
        const {model} = this;
        const attributes = model.getAttributes();
        if (!attributes.name) {
          model.addAttributes({'name': model.getId()});
        }
      },
    },
  });
}
