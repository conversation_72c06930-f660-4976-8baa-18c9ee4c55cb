// import {cmdWidgetSelectOfferSetting} from "widgets-repos/ctr-settings/constants";
// import {store as appStore} from "../../../../app";
// import {closeModal, openModal} from "../../../../containers/BaseModal/actions";
// import {MODEL_SIZE_XS} from "../../../../containers/BaseModal/constants";

export default function (editor, opt = {}) {
  const trm = editor.TraitManager
  const textTrait = trm.getType('text')
  const selectTrait = trm.getType('select')

  trm.addType('content', {
    events: {
      keyup: 'onChange'
    },

    onValueChange: function () {
      var md = this.model
      var target = md.target
      target.set('content', md.get('value'))
    },

    getInputEl: function () {
      if (!this.inputEl) {
        this.inputEl = textTrait.prototype.getInputEl.bind(this)()
        this.inputEl.value = this.target.get('content')
      }
      return this.inputEl
    }
  })

  trm.addType('select-options', {
    events: {
      keyup: 'onChange'
    },

    onValueChange: function () {
      var optionsStr = this.model.get('value').trim()
      var options = optionsStr.split('\n')
      var optComps = []

      for (var i = 0; i < options.length; i++) {
        var optionStr = options[i]
        var option = optionStr.split('::')
        var opt = {
          tagName: 'option',
          attributes: {}
        }
        if (option[1]) {
          opt.content = option[1]
          opt.attributes.value = option[0]
        } else {
          opt.content = option[0]
          opt.attributes.value = option[0]
        }
        optComps.push(opt)
      }

      var comps = this.target.get('components')
      comps.reset(optComps)
      this.target.view.render()
    },

    getInputEl: function () {
      if (!this.$input) {
        var md = this.model
        var trg = this.target
        var name = md.get('name')
        var optionsStr = ''
        var opts = { placeholder: '' }
        var options = trg.get('components')

        for (var i = 0; i < options.length; i++) {
          var option = options.models[i]
          var optAttr = option.get('attributes')
          var optValue = optAttr.value || ''
          optionsStr += `${optValue}::${option.get('content')}\n`
        }

        this.$input = document.createElement('textarea')
        this.$input.value = optionsStr
      }
      return this.$input
    }
  })

  trm.addType('label', {
    events: {
      keyup: 'onChange'
    },

    onValueChange: function () {
      const md = this.model
      const labelType = md.target.get('labelType')
      switch (labelType) {
        case 'label': {
          const formGroup = md.target.parent()
          const controlList = formGroup.find('.control-label')
          if (controlList && controlList.length) {
            const target = controlList[0]
            target.set('content', md.get('value'))
          }
          break
        }
        default: {
          md.target.addAttributes({ placeholder: md.get('value') })
        }
      }
    },

    getInputEl: function () {
      if (!this.inputEl) {
        this.inputEl = textTrait.prototype.getInputEl.bind(this)()
        const labelType = this.target.get('labelType')
        switch (labelType) {
          case 'label': {
            const formGroup = this.target.parent()
            const controlList = formGroup.find('.control-label')
            if (controlList && controlList.length) {
              const target = controlList[0]
              this.inputEl.value = target.get('content')
            }
            break
          }
          default: {
            const { placeholder } = this.target.get('attributes')
            this.inputEl.value = placeholder || ''
          }
        }
      }
      return this.inputEl
    }
  })

  trm.addType('select-label-type', {
    init() {
      this.listenTo(this.model, 'change:options', this.rerender)
    },

    templateInput() {
      const { ppfx, clsField } = this
      return `<div class="${clsField}">
        <div data-input></div>
        <div class="${ppfx}sel-arrow">
          <div class="${ppfx}d-s-arrow"></div>
        </div>
      </div>`
    },
    onValueChange: function () {
      const md = this.model
      const value = md.get('value')
      md.target.set({ labelType: value })
      switch (value) {
        case 'label': {
          const value = md.target.get('attributes').placeholder || ''
          md.target.addAttributes({ placeholder: '' })
          md.target.setStyle({ 'padding-top': '1.5rem' })
          const formGroup = md.target.parent()
          const controlList = formGroup.find('.control-label')
          if (controlList && controlList.length) {
            const target = controlList[0]
            target.set('content', value)
          }
          break
        }
        default: {
          let value = ''
          const formGroup = md.target.parent()
          const controlList = formGroup.find('.control-label')
          if (controlList && controlList.length) {
            const target = controlList[0]
            value = target.get('content')
            target.set('content', '')
          }
          md.target.setStyle({ 'padding-top': '10px' })
          md.target.addAttributes({ placeholder: value })
        }
      }
    },
    getInputEl() {
      return selectTrait.prototype.getInputEl.bind(this)()
    }
  })

  trm.addType('ctr-select-offers', {
    createInput({ trait }) {
      // Here we can decide to use properties from the trait
      const el = document.createElement('div')
      el.style.display = 'flex'
      el.innerHTML = `
      <input name="ctr_builder_trail_select_offers_name" disabled/>
      <button id="ctr_builder_trail_select_offers_button">Select</button>
    `

      // Let's make our content alive
      const $button = el.querySelector('button')
      const $inputName = el.querySelector('input[name="ctr_builder_trail_select_offers_name"]')

      $button.addEventListener('click', () => {
        const fieldName = trait.attributes.name ? trait.attributes.name : 'ctr_settings__offer'
        const component = editor.getSelected()
        const componentSettingData = component.get(fieldName) ? JSON.parse(component.get(fieldName)) : ''
        const callback = (settingsData) => {
          $inputName.value = settingsData.settings.offerName
          if (!trait.attributes.changeProp) {
            component.addAttributes({ [fieldName]: settingsData.settings.offerId })
          }
        }
        // editor.trigger(cmdWidgetSelectOfferSetting, { componentSettingData, component, fieldName, callback });
      })
      return el
    },
    // Update the component based element changes
    onEvent({ elInput, component }) {
      // console.log('on event change', elInput, component);
    },

    onUpdate({ elInput, component, trait }) {
      // console.log('onUpdate', elInput, component);
      const fieldName = trait.attributes.name ? trait.attributes.name : 'ctr_settings__offer'
      const inputType = elInput.querySelector('input[name="ctr_builder_trail_select_offers_name"]')
      const settings = component.get(fieldName) ? JSON.parse(component.get(fieldName)) : ''
      inputType.value = settings ? settings.offerName : ''
      inputType.dispatchEvent(new CustomEvent('change'))
    }
  })

  // editor.on(cmdWidgetSelectOfferSetting, ({ componentSettingData, component, fieldName, callback }) => {
  //   appStore.dispatch(openModal({
  //     type: 'WidgetSelectOfferSettings',
  //     size: MODEL_SIZE_XS,
  //     title: 'Select Offers Settings',
  //     componentSettingData,
  //     className: 'modal-product-setting',
  //     onSubmit: settingsData => {
  //       if(component.__updateCtrSettings) {
  //         component.__updateCtrSettings(settingsData.settings, fieldName);
  //       } else {
  //         component.set(fieldName, JSON.stringify(settingsData.settings));
  //       }
  //       component.set(fieldName, JSON.stringify(settingsData.settings));
  //       callback && callback(settingsData);
  //       appStore.dispatch(closeModal({ type: "WidgetSelectOfferSettings"}));
  //     }
  //   }));
  // });
}

export function traitOptions(opt = {}) {
  const c = opt
  return {
    idTrait: {
      name: 'id',
      label: c.labelTraitId
    },
    forTrait: {
      name: 'for',
      label: c.labelTraitFor
    },
    nameTrait: {
      name: 'name',
      label: c.labelTraitName
    },
    labelTypeTrait: {
      name: 'labelType',
      label: c.labelTraitLabelType,
      type: 'select-label-type',
      options: [
        { value: 'default', name: 'Default' },
        { value: 'label', name: 'Label' }
      ],
      changeProp: 1
    },
    labelTrait: {
      type: 'label',
      label: c.labelTraitLabelValue
    },
    valueTrait: {
      name: 'value',
      label: c.labelTraitValue
    },
    requiredTrait: {
      type: 'checkbox',
      name: 'required',
      label: c.labelTraitRequired
    },
    regexTrait: {
      name: 'regex',
      label: c.labelTraitRegex
    },
    maskTrait: {
      name: 'mask',
      label: c.labelTraitMask
    },
    checkedTrait: {
      label: c.labelTraitChecked,
      type: 'checkbox',
      name: 'checked'
    },
    minLengthTrait: {
      type: 'number',
      name: 'minlength',
      label: 'Min Length'
    },
    maxLengthTrait: {
      type: 'number',
      name: 'maxlength',
      label: 'Max Length'
    },
    selectValidationTrait: {
      label: c.labelTraitType,
      type: 'select',
      name: 'type',
      options: [
        { value: 'text', name: c.labelTypeText },
        { value: 'email', name: c.labelTypeEmail },
        { value: 'password', name: c.labelTypePassword },
        { value: 'number', name: c.labelTypeNumber },
        { value: 'date', name: c.labelTypeDate },
        { value: 'url', name: c.labelTypeUrl }
      ]
    },
    actionURL: {
      name: 'action',
      label: c.actionURL
    },
    methodAction: {
      label: c.labelMethod,
      type: 'select',
      name: 'method',
      options: [
        { value: 'POST', name: c.labelPOST },
        { value: 'GET', name: c.labelGET }
      ]
    },
    requiredTextTrait: {
      name: 'required-text',
      label: 'Required Text'
    },
    regexTextTrait: {
      name: 'regex-text',
      label: 'Regular Expression Text'
    }
  }
}
