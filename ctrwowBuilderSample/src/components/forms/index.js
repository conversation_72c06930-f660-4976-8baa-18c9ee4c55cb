import loadTraits from './traits';
import loadBlocks from './blocks';
import loadComponents from './components';
import customCheckbox from './customCheckbox'
import numberController from './numberController'


export default (editor, opts = {}) => {

  console.log('components-forms-index')
  console.log('components-forms-index')
  console.log('components-forms-index')

  const config = {
    category: "Basic",
    blocks: ['form', 'input', 'textarea', 'select',
      'button','buttonSubmit', 'label', 'checkbox', 'radio'],
    labelTraitAction: 'Action',
    labelTraitState: 'State',
    labelTraitId: 'ID',
    labelTraitFor: 'For',
    labelFormGroupName: 'FormGroup',
    labelInputName: 'Input',
    labelTextareaName: 'Textarea',
    labelSelectName: 'Select',
    labelCheckboxName: 'Checkbox',
    labelRadioName: 'Radio',
    labelButtonName: 'Button',
    labelTraitName: 'Name',
    labelTraitTitle: 'Title',
    labelMethod: "Method",
    labelPOST: "POST",
    labelGET: "GET",
    actionURL: 'Action URL',
    labelTraitLabelType: 'Placeholder Type',
    labelTraitLabelValue: 'Placeholder Value',
    labelTraitValue: 'Default Value',
    labelTraitRequired: 'Required',
    labelTraitRegex: 'Regular Expression',
    labelTraitMask: 'Mask',
    labelTraitType: 'Type',
    labelTraitOptions: 'Options',
    labelTraitChecked: 'Checked',
    labelTypeText: 'Text',
    labelTypeEmail: 'Email',
    labelTypePassword: 'Password',
    labelTypeNumber: 'Number',
    labelTypeDate: 'Date',
    labelTypeUrl: 'Url',
    labelTypeSubmit: 'Submit',
    labelTypeReset: 'Reset',
    labelTypeButton: 'Button',
    labelNameLabel: 'Label',
    labelForm: 'Form',
    labelSelectOption: '- Select option -',
    labelOption: 'Option',
    labelStateNormal: 'Normal',
    labelStateSuccess: 'Success',
    labelStateError: 'Error',
    ...opts
  };

  loadComponents(editor, config);
  loadTraits(editor, config);
  loadBlocks(editor, config);
  customCheckbox(editor, config)
  numberController(editor, config)
};
