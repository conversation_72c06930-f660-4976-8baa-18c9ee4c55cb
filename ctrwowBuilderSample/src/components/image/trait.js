import './style.css'

export default (editor) => {
  const trainManager = editor.TraitManager
  const sourceTypes = { mobile: 0, tablet: 1, desktop: 2 }
  !trainManager.getType('device-src') &&
    trainManager.addType('device-src', {
      createInput({ trait }) {
        const self = this
        const value = trait.get('value')
        const sourceType = trait.get('sourceType')
        const target = trait && trait.target
        const el = document.createElement('div')

        el.classList.add('input-src')
        el.innerHTML = self.renderInput(value, sourceType, { removable: sourceType !== 'desktop' })

        el.addEventListener('click', function (event) {
          if (event.target.matches('.btn-browse')) {
            self.handleChooseImage(target, sourceType, el)
          }
          if (event.target.matches('.btn-remove')) {
            self.handleRemove(event, trait, target, sourceType)
          }
        })

        return el
      },
      renderInput(src, sourceType, opts) {
        return `
        <div class="preview">
          ${src ? `<img src='${src}' class='src-preview'/>` : 'No file'}
          ${src && opts.removable ? `<button type="button" data-src="${sourceType}" class="btn btn-remove">x</button>` : ''}
        </div>
        <div class="actions">
          <button type="button" class="btn btn-browse">${src ? 'Edit' : 'Browse'}</button>
        </div>`
      },
      updateInput(src, sourceType, input, opts = { removable: true }) {
        if (sourceType === 'desktop') {
          opts.removable = false
        }
        input.innerHTML = this.renderInput(src, sourceType, opts)
      },
      resetSrc(sourceType, sourcesets, index) {
        let desktopSrc = ''
        if (sourcesets[sourceTypes.desktop]) {
          desktopSrc = sourcesets[sourceTypes.desktop].split(':')[1]
        }
        if (sourceType === 'mobile') return ''
        if (sourceType === 'tablet' && desktopSrc) return ''
        return sourcesets[index].split(':')[0] + ':' + "''"
      },
      getSourceSet(target) {
        let srcsets = target.getEl().dataset.srcsets
        srcsets = srcsets ? srcsets.split(',') : []
        return srcsets
      },
      handleChooseImage(target, sourceType, input) {
        const self = this
        editor.runCommand('open-assets', {
          target: target,
          sourceType: sourceType,
          onSelectSlide: (asset) => {
            target.setSrc(asset, { sourceType })
            self.updateInput(asset.assetUrl, sourceType, input)
          }
        })
      },
      handleRemove(event, input, selectedModel, sourceType) {
        const removeItem = event.target.dataset.src
        const index = sourceTypes[removeItem]
        const srcset = this.getSourceSet(selectedModel)
        srcset[index] = this.resetSrc(sourceType, srcset, index)
        input.set('value', '')
        event.target.remove()
        selectedModel.addAttributes({ 'data-srcsets': srcset.join(',') })
        if (sourceType === 'desktop') {
          selectedModel.set('src', '')
        }
        if (sourceType === 'tablet') {
          selectedModel.set('src-tablet', '')
        }
        if (sourceType === 'mobile') {
          selectedModel.set('src-mobile', '')
        }

        this.updateInput(null, sourceType, input.el)
        editor.Canvas.getWindow().dispatchEvent(new Event('resize'))
      }
    })
}
