import loadTrait from './trait'
import { MOBILE, TABLET } from './constants'
import { cmdWrapWithLink, cmdUnwrapWithLink, CUSTOM_LINK, DEVICE_MANAGER } from '../../consts'
import { addCommon<PERSON>mageHandler } from '../../utils'

export default (editor, opts = {}) => {
  opts.device = DEVICE_MANAGER
  loadTrait(editor)
  const domComponent = editor.DomComponents
  const mediaMobile = opts.device.MOBILE_MAX_WIDTH ? parseInt(opts.device.MOBILE_MAX_WIDTH, 10) : MOBILE
  const mediaTablet = opts.device.TABLET_MAX_WIDTH ? parseInt(opts.device.TABLET_MAX_WIDTH, 10) : TABLET
  domComponent.addType('image', {
    isComponent: (el) => el.tagName === 'IMG',
    model: {
      defaults: {
        name: 'Image',
        src: '',
        'src-tablet': '',
        'src-mobile': '',
        'data-ctr-islazy': false,
        traits: [
          {
            name: 'src',
            label: 'Desktop',
            type: 'device-src',
            sourceType: 'desktop',
            full: 1
          },
          {
            name: 'src-tablet',
            label: 'Tablet',
            type: 'device-src',
            sourceType: 'tablet',
            full: 1,
            changeProp: 1
          },
          {
            name: 'src-mobile',
            label: 'Mobile',
            type: 'device-src',
            sourceType: 'mobile',
            full: 1,
            changeProp: 1
          },
          {
            type: 'checkbox',
            name: 'data-ctr-islazy',
            label: 'Is Lazy Load',
            changeProp: 1,
            default: false
          },
          {
            type: 'text',
            name: 'alt',
            label: 'Alt'
          },
          {
            type: 'text',
            label: 'Custom Source',
            name: 'src-template'
          }
        ],
        script: addCommonImageHandler
      },
      init() {
        this.initImageToolbar()
        const attrs = this.getAttributes()
        this.getTrait('src').set('value', this.get('src'))
        this.getTrait('src-tablet').set('value', this.get('src-tablet'))
        this.getTrait('src-mobile').set('value', this.get('src-mobile'))
        this.getTrait('data-ctr-islazy').set('value', this.get('data-ctr-islazy'))
        this.on('change:data-ctr-islazy', () => {
          this.addAttributes({ 'data-ctr-islazy': this.changed['data-ctr-islazy'] })
          this.addAttributes({ 'data-lazy-srcsets': attrs['data-srcsets'] })
          editor.getModel().set('changesCount', editor.getModel().get('changesCount') + 1)
        })
      },
      initImageToolbar() {
        const toolbar = this.get('toolbar')
        const parent = this.parent()
        if (!parent) return
        toolbar.some(function (e) {
          return e.command === cmdWrapWithLink
        }) ||
          toolbar.push({
            attributes: {
              class: 'fa fa-link'
            },
            command: cmdWrapWithLink
          })
        parent.is(CUSTOM_LINK) && this.toggleUnLink()
      },
      replaceMedia() {
        // CTR-3290: Update the old images for mobile, tablet
        let dataSrcsets = this.getAttributes()['data-srcsets']
        let dataSrcsetsArray = dataSrcsets ? dataSrcsets.split(',') : []

        if (dataSrcsetsArray.length > 3) {
          dataSrcsetsArray = dataSrcsetsArray.slice(0, 3)
          dataSrcsets = dataSrcsetsArray.join(',')
          this.addAttributes({ 'data-srcsets': dataSrcsets })
          this.addAttributes({ 'data-lazy-srcsets': dataSrcsets })
        }

        if (dataSrcsets && (dataSrcsets.indexOf(`<481:`) !== -1 || dataSrcsets.indexOf(`<768:`) !== -1 || dataSrcsets.indexOf(`<1024:`) !== -1)) {
          dataSrcsets = dataSrcsets
            .replace('<481:', `<${mediaMobile}:`)
            .replace('<768:', `<${mediaMobile}:`)
            .replace('<1024:', `<${mediaTablet}:`)
            .replace('>1024:', `>${mediaTablet}:`)
          this.addAttributes({ 'data-srcsets': dataSrcsets })
          this.addAttributes({ 'data-lazy-srcsets': dataSrcsets })
        }
      },
      handleSourceSet(asset, sourceType) {
        try {
          const device = sourceType !== 'desktop' ? `src-${sourceType}` : 'src'
          const srcTrait = this.getTrait(`${device}`)
          // let srcsets = this.getEl().dataset['tempSrcsets'];
          let srcsets = this.getEl().dataset.srcsets
          srcsets = srcsets ? srcsets.split(',') : []
          switch (sourceType) {
            case 'mobile':
              this.set('src-mobile', asset.assetUrl)
              srcsets[0] = `<${mediaMobile}:${asset.assetUrl}`
              break
            case 'tablet':
              this.set('src-tablet', asset.assetUrl)
              srcsets[1] = `<${mediaTablet}:${asset.assetUrl}`
              break
            default:
              // Desktop
              this.set('src', asset.assetUrl)
              if (!this.get('src-tablet')) {
                srcsets[1] = ''
              }
              srcsets[2] = `>${mediaTablet}:${asset.assetUrl}`
              break
          }

          if (srcTrait && srcTrait.view) {
            srcTrait.set('value', asset.assetUrl)
            srcTrait.el.innerHTML = srcTrait.view.renderInput(asset.assetUrl, sourceType, { removable: sourceType !== 'desktop' })
          }
          if (!this.get('src-tablet') && !this.get('src')) {
            srcsets[1] = `<${mediaTablet}:''`
          }
          if (!this.get('src')) {
            this.set('src', '#')
            srcsets[2] = `>${mediaTablet}:''`
          }
          return srcsets
        } catch (error) {
          console.log('Image Error', error)
        }
      },
      getSrc() {
        const sourceType = editor.getDevice().toLowerCase() || 'desktop'
        let src = ''
        switch (sourceType) {
          case 'mobile':
            src = this.get('src-mobile')
            if (!src) {
              src = this.get('src-tablet')
            }
            if (!src) {
              src = this.get('src')
            }
            break
          case 'tablet':
            src = this.get('src-tablet')
            if (!src) {
              src = this.get('src')
            }
            break
          default:
            // Desktop
            src = this.get('src')
            break
        }
        return src
      },
      setSrc(asset, opts) {
        const currentDevice = editor.getDevice().toLowerCase() || 'desktop'
        const sourceType = opts.sourceType ? opts.sourceType : currentDevice
        const sourceSets = this.handleSourceSet(asset, sourceType)
        this.addAttributes({ 'data-srcsets': sourceSets.join(',') })
        this.addAttributes({ 'data-lazy-srcsets': sourceSets.join(',') })

        // Trigger change device mode
        editor.Canvas.getWindow().dispatchEvent(new Event('resize'))
      },
      removed() {
        const parent = this.parent()
        if (parent && parent.is(CUSTOM_LINK)) {
          // Check exist other children
          if (!parent.components().length) {
            parent.remove()
          }
        }
      },
      toggleUnLink() {
        const toolbar = this.get('toolbar')
        const addedUnLink = toolbar.some(function (e) {
          return e.command === cmdUnwrapWithLink
        })
        !addedUnLink &&
          toolbar.push({
            attributes: {
              class: 'fa fa-unlink'
            },
            command: cmdUnwrapWithLink
          })
      }
    },
    view: {
      onRender({ el, model }) {
        this.model.replaceMedia()
        el.src = model.getSrc()
      }
    }
  })
}
