// import {updateParentLayer} from "../functions/updateComponent";
import _endsWith from 'lodash/fp/endsWith'
import _find from 'lodash/fp/find'
import _isString from 'lodash/fp/isString'

export default (editor) => {
  const cssComposer = editor.CssComposer
  editor.on('component:mount', (model) => {
    // if (model.get("parentLayer")) {
    //   model.addAttributes({ parentLayer: model.cid });
    //   //model.set("resizable", true);
    //   updateParentLayer(model, { isEdit: true });
    // }

    let css = model.get('css')

    const classes = []
    model.get('classes').forEach((cls) => classes.push(_isString(cls) ? cls : cls.get('name')))

    // temporary fix - only add default css if component don't have any global class before
    const hasGlobalClass = _find(_endsWith('_global'), classes)
    const modelClass = `${model.getId()}_global`
    const regexSelector = /([^\r\n,{}]+)(,(?=[^}]*{)|\s*{)/g
    const convertCSS = (selector) => {
      if (selector.includes('#id')) {
        return selector.replace('#id', `.${modelClass}`)
      }
      if (selector.includes('@')) {
        return selector
      }
      return `.${modelClass} ${selector}`
    }
    const isSnatch = false;// editor.Config.data.isSnatch

    if (typeof css === 'string' && css && !hasGlobalClass) {
      model.addClass([modelClass])
      if (!isSnatch) {
        css = css.replace(regexSelector, convertCSS)
        const parser = editor.Parser
        const rules = parser.parseCss(css)
        rules.forEach((rule) => {
          if (rule.atRuleType && rule.atRuleType === 'keyframes') {
            rule.selectorsAdd = rule.selectorsAdd.replace(`.${modelClass}`, `.${modelClass}{}`)
          }
          cssComposer.addCollection(rule)
        })
      }
    }
  })
}
