// import {addMarkIcon} from 'containers/Editor/TooltipWidget'

export default function getCustomBlockName(name, iconUrl, isExternalWidget = false, description) {
  return `<div class="gjs-block-label ctr_custom_widget">
    ${iconUrl ? `<img src="${iconUrl}" style="max-width:45px; max-height:45px; margin-bottom: 10px" />` : ''}
    ${isExternalWidget ? `<div class="ctr_custom_widget_name">${name}</div>` : `<div>${name}</div>`}
  </div>`
}
