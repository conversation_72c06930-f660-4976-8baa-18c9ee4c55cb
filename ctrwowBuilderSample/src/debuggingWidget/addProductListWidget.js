import gjsWidgetModule from '../../../packages/widget-product-list/src/index.gjs'

export default function addListicleWidget(editor) {
  const WIDGET_TYPE_ID = 'product-list-widget'
  const WIDGET_NAME = 'Product List'
  const PACKAGE_NAME = 'widget-product-list'
  const blockManager = editor.BlockManager

  gjsWidgetModule(editor, {
    widgetTypeId: WIDGET_TYPE_ID,
    widgetName: WIDGET_NAME,
    packageName: PACKAGE_NAME,
    Builder: editor,
    appActions: { setNotify: () => console.log('Notify...') }
  })

  blockManager.add('ProductList', {
    label: WIDGET_NAME,
    content: `<div data-gjs-type="${WIDGET_TYPE_ID}">`
  })
}
