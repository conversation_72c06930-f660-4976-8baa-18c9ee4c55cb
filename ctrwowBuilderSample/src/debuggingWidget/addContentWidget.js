import gjsSourceWidgetModule from './../../../packages/widget-cms-content-selector/src/index.gjs'
import gjsListWidgetModule from './../../../packages/widget-cms-content-list-template/src/index.gjs'
import gjsDetailsWidgetModule from './../../../packages/widget-cms-content-details-v2/src/index.gjs'

export default function addListicleWidget(editor) {
  // const WIDGET_TYPE_ID = 'listicle-widget'
  // const WIDGET_NAME = 'Listicle'
  const PACKAGE_NAME = 'my-debugging-widget'
  const blockManager = editor.BlockManager

  gjsSourceWidgetModule(editor, {
    widgetTypeId: 'gjsSourceWidgetModule',
    widgetName: 'Source Selector',
    packageName: PACKAGE_NAME
  })
  blockManager.add('Source Selector', {
    label: 'Source Selector',
    content: `<div data-gjs-type="gjsSourceWidgetModule">`
  })

  gjsListWidgetModule(editor, {
    widgetTypeId: 'gjsListWidgetModule',
    widgetName: 'Content List',
    packageName: PACKAGE_NAME
  })
  blockManager.add('Source Selector', {
    label: 'Content List',
    content: `<div data-gjs-type="gjsListWidgetModule">`
  })

  gjsListWidgetModule(editor, {
    widgetTypeId: 'gjsListWidgetModule',
    widgetName: 'Content List',
    packageName: PACKAGE_NAME
  })
  blockManager.add('Content List', {
    label: 'Content List',
    content: `<div data-gjs-type="gjsListWidgetModule">`
  })

  gjsDetailsWidgetModule(editor, {
    widgetTypeId: 'gjsDetailsWidgetModule',
    widgetName: 'Content Details',
    packageName: PACKAGE_NAME
  })
  blockManager.add('Content Details', {
    label: 'Content Details',
    content: `<div data-gjs-type="gjsDetailsWidgetModule">`
  })
}
