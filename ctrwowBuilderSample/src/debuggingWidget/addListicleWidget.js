import gjsWidgetModule from './../../../packages/widget-listicle-list/src/index.gjs'

export default function addListicleWidget(editor) {
  const WIDGET_TYPE_ID = 'listicle-widget'
  const WIDGET_NAME = 'Listicle'
  const PACKAGE_NAME = 'my-debugging-widget'
  const blockManager = editor.BlockManager

  gjsWidgetModule(editor, {
    widgetTypeId: WIDGET_TYPE_ID,
    widgetName: WIDGET_NAME,
    packageName: PACKAGE_NAME
  })

  blockManager.add('Listicle', {
    label: WIDGET_NAME,
    content: `<div data-gjs-type="${WIDGET_TYPE_ID}">`
  })
}
