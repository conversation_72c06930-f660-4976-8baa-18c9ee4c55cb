import grapesjs from 'grapesjs'
import basicBlocks from 'grapesjs-blocks-basic'
import 'grapesjs/dist/css/grapes.min.css'

import debuggWidgetPlugin from './debuggingWidget'
import componentMount from './componentMount'
import loadComponents from './components'
import { addCommonImageHandler } from './utils'

var editor = grapesjs.init({
  container: '#app',
  fromElement: true,
  // dragMode: 'translate',
  // dragMode: 'translate',
  // dragMode: 'absolute',
  height: '100vh',
  width: 'auto',
  styleManager: {
    clearProperties: 1
  },
  canvas: {
    // styles: EXTERNAL_STYLES,
    scripts: [
      'https://cdn.wowsuite.ai/ctrwow/common/js/jquery-3.4.1.custom.min.js',
      'https://cdn.wowsuite.ai/ctrwow/common/packages/ctrwowUtils-v2.3.0.min.js'
      // 'https://code.jquery.com/jquery-3.5.1.min.js'
    ]
  },
  plugins: [basicBlocks, debuggWidgetPlugin]
})

editor.on('load', () => {
  loadComponents(editor)
  const canvas = editor.Canvas
  const doc = canvas.getDocument()
  // editor.$(doc).ready(() => {
  //   addCommonImageHandler()
  // })
  addCommonImageHandler(doc)
})

componentMount(editor)
