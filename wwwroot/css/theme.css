
:root {
    /* === PRIMARY COLORS - Bootstrap Default === */
    --primary-color: #007bff;
    --primary-hover: #0056b3;
    --primary-active: #004085;
    --primary-light: #e3f2fd;
    --primary-dark: #003d82;

    --secondary-color: #6c757d;
    --secondary-hover: #545b62;
    --secondary-active: #3d4142;
    --secondary-light: #f8f9fa;
    --secondary-dark: #343a40;

    /* === ACCENT COLORS - Bootstrap Info === */
    --accent-color: #17a2b8;
    --accent-hover: #138496;
    --accent-active: #0c5460;

    /* === STATUS COLORS - Bootstrap Default === */
    --success-color: #28a745;
    --success-hover: #218838;
    --success-light: #d4edda;
    --success-dark: #155724;

    --warning-color: #ffc107;
    --warning-hover: #e0a800;
    --warning-light: #fff3cd;
    --warning-dark: #856404;

    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --danger-light: #f8d7da;
    --danger-dark: #721c24;

    --info-color: #17a2b8;
    --info-hover: #138496;
    --info-light: #d1ecf1;
    --info-dark: #0c5460;

    /* === NEUTRAL COLORS - Bootstrap Default === */
    --white: #ffffff;
    --light: #f8f9fa;
    --light-gray: #e9ecef;
    --gray: #6c757d;
    --dark-gray: #495057;
    --dark: #343a40;
    --black: #000000;

    /* === BACKGROUND COLORS - Bootstrap Default === */
    --bg-primary: var(--white);
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-dark: var(--dark);

    /* === TEXT COLORS - Bootstrap Default === */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #6c757d;
    --text-light: var(--white);
    --text-link: #007bff;
    --text-link-hover: #0056b3;

    /* === BORDER COLORS - Bootstrap Default === */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #adb5bd;

    /* === SHADOW COLORS - Bootstrap Default === */
    --shadow-light: rgba(0, 0, 0, 0.075);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --shadow-dark: rgba(0, 0, 0, 0.175);
    --shadow-colored: rgba(0, 123, 255, 0.25);

    /* === OPACITY COLORS - Bootstrap Default === */
    --white-10: rgba(255, 255, 255, 0.1);
    --white-20: rgba(255, 255, 255, 0.2);
    --white-60: rgba(255, 255, 255, 0.6);
    --white-80: rgba(255, 255, 255, 0.8);
    --black-10: rgba(0, 0, 0, 0.1);
    --black-20: rgba(0, 0, 0, 0.2);
    --black-60: rgba(0, 0, 0, 0.6);
    --black-80: rgba(0, 0, 0, 0.8);

    /* === TYPOGRAPHY - Font hiện đại === */
    --font-family-primary: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-secondary: 'Poppins', 'Arial', sans-serif;
    --font-family-monospace: 'JetBrains Mono', 'Fira Code', monospace;

    /* === FONT SIZES - Kích thước linh hoạt === */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* === FONT WEIGHTS - Trọng lượng font === */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* === LINE HEIGHTS - Khoảng cách dòng === */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* === SPACING - Khoảng cách === */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* === BORDER RADIUS - Bo góc mềm mại === */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 9999px;

    /* === LAYOUT - Bố cục === */
    --sidebar-width: 280px;
    --header-height: 70px;
    --footer-height: 60px;

    /* === TRANSITIONS - Hiệu ứng chuyển động === */
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* === Z-INDEX - Lớp hiển thị === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* === DARK THEME VARIABLES === */
@media (prefers-color-scheme: dark) {
    :root {
        /* === BACKGROUND COLORS - DARK === */
        --bg-primary: #121212;
        --bg-secondary: #1e1e1e;
        --bg-tertiary: #2d2d2d;
        --bg-dark: #0a0a0a;

        /* === TEXT COLORS - DARK === */
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --text-muted: #8a8a8a;
        --text-light: #ffffff;
        --text-link: #4fc3f7;
        --text-link-hover: #29b6f6;

        /* === BORDER COLORS - DARK === */
        --border-color: #404040;
        --border-light: #333333;
        --border-dark: #555555;

        /* === NEUTRAL COLORS - DARK === */
        --light: #2d2d2d;
        --light-gray: #404040;
        --gray: #8a8a8a;
        --dark-gray: #b3b3b3;
        --dark: #ffffff;

        /* === SHADOW COLORS - DARK === */
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.5);
        --shadow-dark: rgba(0, 0, 0, 0.7);

        /* === COMPONENT SPECIFIC - DARK === */
        --card-bg: #1e1e1e;
        --card-border: #404040;
        --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

        --table-stripe-bg: rgba(255, 255, 255, 0.02);
        --table-hover-bg: rgba(255, 255, 255, 0.05);
        --table-border: #404040;

        --input-border: #404040;
        --input-focus-border: var(--primary-color);
        --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

        /* === STATUS COLORS - DARK VARIANTS === */
        --success-light: rgba(40, 167, 69, 0.2);
        --success-dark: #4caf50;

        --warning-light: rgba(255, 193, 7, 0.2);
        --warning-dark: #ffeb3b;

        --danger-light: rgba(220, 53, 69, 0.2);
        --danger-dark: #f44336;

        --info-light: rgba(23, 162, 184, 0.2);
        --info-dark: #2196f3;

        /* === SIDEBAR - DARK === */
        --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
        --sidebar-text: #ffffff;
        --sidebar-hover: rgba(255, 255, 255, 0.15);
    }
}

/* === DARK THEME TOGGLE CLASS === */
[data-theme="dark"] {
    /* === BACKGROUND COLORS - DARK === */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --bg-dark: #0a0a0a;

    /* === TEXT COLORS - DARK === */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #8a8a8a;
    --text-light: #ffffff;
    --text-link: #4fc3f7;
    --text-link-hover: #29b6f6;

    /* === BORDER COLORS - DARK === */
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;

    /* === NEUTRAL COLORS - DARK === */
    --light: #2d2d2d;
    --light-gray: #404040;
    --gray: #8a8a8a;
    --dark-gray: #b3b3b3;
    --dark: #ffffff;

    /* === SHADOW COLORS - DARK === */
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
    --shadow-dark: rgba(0, 0, 0, 0.7);

    /* === COMPONENT SPECIFIC - DARK === */
    --card-bg: #1e1e1e;
    --card-border: #404040;
    --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

    --table-stripe-bg: rgba(255, 255, 255, 0.02);
    --table-hover-bg: rgba(255, 255, 255, 0.05);
    --table-border: #404040;

    --input-border: #404040;
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

    /* === STATUS COLORS - DARK VARIANTS === */
    --success-light: rgba(40, 167, 69, 0.2);
    --success-dark: #4caf50;

    --warning-light: rgba(255, 193, 7, 0.2);
    --warning-dark: #ffeb3b;

    --danger-light: rgba(220, 53, 69, 0.2);
    --danger-dark: #f44336;

    --info-light: rgba(23, 162, 184, 0.2);
    --info-dark: #2196f3;

    /* === SIDEBAR - DARK === */
    --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
    --sidebar-text: #ffffff;
    --sidebar-hover: rgba(255, 255, 255, 0.15);
}

/* === CUSTOM PROPERTIES FOR SPECIFIC COMPONENTS === */
:root {
    /* Sidebar - Bootstrap Default */
    --sidebar-bg: var(--dark);
    --sidebar-text: var(--white);
    --sidebar-hover: var(--white-20);

    /* Cards - Thiết kế hiện đại */
    --card-bg: var(--white);
    --card-border: var(--border-color);
    --card-shadow: 0 4px 20px var(--shadow-light);
    --card-hover-shadow: 0 8px 30px var(--shadow-medium);

    /* Buttons - Bootstrap Default */
    --btn-shadow: 0 2px 4px var(--shadow-light);
    --btn-shadow-hover: 0 4px 8px var(--shadow-medium);
    --btn-gradient-primary: var(--primary-color);
    --btn-gradient-secondary: var(--secondary-color);

    /* Tables - Bootstrap Default */
    --table-stripe-bg: rgba(0, 0, 0, 0.05);
    --table-hover-bg: rgba(0, 0, 0, 0.075);
    --table-border: var(--border-light);

    /* Forms - Bootstrap Default */
    --input-border: var(--border-color);
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    --input-bg: var(--white);

    /* Dark Mode Toggle */
    --toggle-bg: var(--light-gray);
    --toggle-thumb: var(--white);
    --toggle-active-bg: var(--primary-color);

    /* Scrollbar - Bootstrap Default */
    --scrollbar-track: var(--light);
    --scrollbar-thumb: var(--gray);
    --scrollbar-thumb-hover: var(--primary-color);

    /* Checkout specific - Bootstrap Default */
    --checkout-bg: var(--bg-secondary);
    --checkout-card-bg: var(--white);
    --checkout-accent: var(--accent-color);
    --checkout-success: var(--success-color);
}

/* === MODERN UI COMPONENTS - Giao diện hiện đại === */

/* Buttons với gradient và hiệu ứng */
.btn-modern {
    background: var(--btn-gradient-primary);
    border: none;
    border-radius: var(--border-radius-lg);
    color: var(--white);
    font-weight: var(--font-weight-medium);
    padding: 0.75rem 1.5rem;
    transition: var(--transition-normal);
    box-shadow: var(--btn-shadow);
    position: relative;
    overflow: hidden;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--btn-shadow-hover);
    color: var(--white);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-normal);
}

.btn-modern:hover::before {
    left: 100%;
}

/* Cards với hiệu ứng hover */
.card-modern {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--card-shadow);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
}

/* Form controls hiện đại */
.form-control-modern {
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius-lg);
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    background: var(--input-bg);
}

.form-control-modern:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    outline: none;
}

/* Checkout specific styles */
.checkout-container {
    background: var(--checkout-bg);
    min-height: 100vh;
    padding: 2rem 0;
}

.checkout-card {
    background: var(--checkout-card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-2xl);
    box-shadow: 0 8px 32px rgba(108, 92, 231, 0.1);
}

.checkout-header {
    background: var(--btn-gradient-primary);
    color: var(--white);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    padding: 1.5rem;
}

.checkout-step {
    background: var(--success-light);
    color: var(--success-dark);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--success-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse-on-hover:hover {
    animation: pulse 0.6s ease-in-out;
}

/* Responsive design */
@media (max-width: 768px) {
    .checkout-container {
        padding: 1rem;
    }

    .checkout-card {
        margin: 0.5rem;
    }

    .btn-modern {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* === BOOTSTRAP DEFAULT BUTTON COLORS === */
.btn-primary, .btn-modern {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: #ffffff !important;
    font-weight: 400 !important;
    box-shadow: none !important;
    transition: all 0.15s ease-in-out !important;
}

.btn-primary:hover, .btn-modern:hover {
    background-color: #0056b3 !important;
    border-color: #004085 !important;
    color: #ffffff !important;
    transform: none !important;
    box-shadow: none !important;
}

.card-header, .checkout-header {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    font-weight: 500 !important;
    border-bottom: 1px solid #dee2e6 !important;
}

.text-primary {
    color: #007bff !important;
}

.border-primary {
    border-color: #007bff !important;
}

.bg-primary {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* Sidebar với Bootstrap default */
.sidebar {
    background-color: var(--dark) !important;
}

/* Form focus states */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color) !important;
    box-shadow: var(--input-focus-shadow) !important;
}

/* Additional soft blue theme overrides */
.checkout-step {
    background: linear-gradient(135deg, #f0fdff, #f0f8ff) !important;
    border-left: 4px solid #a8dadc !important;
}

.alert-info {
    background-color: #f0fdff !important;
    border-color: #e0f2fe !important;
    color: #0369a1 !important;
}

.card-modern {
    border: 1px solid #e0f2fe !important;
}

.checkout-container {
    background: linear-gradient(135deg, #f0fdff 0%, #f0f8ff 100%) !important;
}

/* Icons with soft blue color */
.text-primary i, .text-primary .fas {
    color: #2b6cb0 !important;
}

/* Success elements */
.text-success {
    color: #38a169 !important;
}

/* Navbar and navigation - Bootstrap Default */
.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700 !important;
}

.nav-link.active {
    color: var(--white) !important;
    background-color: var(--primary-color) !important;
    border-radius: var(--border-radius-md) !important;
    font-weight: 600 !important;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--shadow-colored) !important;
    border-radius: var(--border-radius-md) !important;
}

/* Sidebar navigation improvements */
.sidebar .nav-link {
    color: var(--sidebar-text) !important;
    transition: all var(--transition-normal) !important;
    margin: 2px 8px !important;
    border-radius: var(--border-radius-md) !important;
}

.sidebar .nav-link:hover {
    color: var(--white) !important;
    background-color: var(--sidebar-hover) !important;
    transform: translateX(4px) !important;
}

.sidebar .nav-link.active {
    color: var(--white) !important;
    background-color: var(--primary-color) !important;
    border-left: 4px solid var(--white) !important;
    font-weight: 600 !important;
}

/* Additional text improvements */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
}

.form-label {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
}

/* Table header improvements - Bootstrap Default */
.table thead th {
    background-color: var(--dark) !important;
    color: var(--white) !important;
    font-weight: 600 !important;
    border: none !important;
    border-bottom: 2px solid var(--dark-gray) !important;
    padding: 1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 0.875rem !important;
    text-align: center !important;
    vertical-align: middle !important;
    display: table-cell !important;
    align-items: center !important;
    justify-content: center !important;
}

.table thead th:first-child {
    border-top-left-radius: var(--border-radius-lg) !important;
}

.table thead th:last-child {
    border-top-right-radius: var(--border-radius-lg) !important;
}

/* Table body improvements */
.table tbody tr {
    transition: all 0.2s ease !important;
}

.table tbody tr:hover {
    background-color: var(--table-hover-bg) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px var(--shadow-light) !important;
}

.table tbody td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* === BOOTSTRAP DEFAULT ACTION BUTTONS === */
.btn-success {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
    color: var(--white) !important;
    font-weight: 400 !important;
    box-shadow: none !important;
}

.btn-success:hover {
    background-color: var(--success-hover) !important;
    border-color: var(--success-hover) !important;
    color: var(--white) !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-warning {
    background-color: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
    color: var(--dark) !important;
    font-weight: 400 !important;
    box-shadow: none !important;
}

.btn-warning:hover {
    background-color: var(--warning-hover) !important;
    border-color: var(--warning-hover) !important;
    color: var(--dark) !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-danger {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
    color: var(--white) !important;
    font-weight: 400 !important;
    box-shadow: none !important;
}

.btn-danger:hover {
    background-color: var(--danger-hover) !important;
    border-color: var(--danger-hover) !important;
    color: var(--white) !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-info {
    background-color: var(--info-color) !important;
    border-color: var(--info-color) !important;
    color: var(--white) !important;
    font-weight: 400 !important;
    box-shadow: none !important;
}

.btn-info:hover {
    background-color: var(--info-hover) !important;
    border-color: var(--info-hover) !important;
    color: var(--white) !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
    color: var(--white) !important;
    font-weight: 400 !important;
    box-shadow: none !important;
}

.btn-secondary:hover {
    background-color: var(--secondary-hover) !important;
    border-color: var(--secondary-hover) !important;
    color: var(--white) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* === BUTTON TẠO MỚI - BOOTSTRAP SUCCESS === */
.btn-create, .btn-add-new {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #ffffff !important;
    font-weight: 400 !important;
    font-size: 1rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.25rem !important;
    box-shadow: none !important;
    transition: all 0.15s ease-in-out !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-create::before, .btn-add-new::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    transition: left 0.5s !important;
}

.btn-create:hover, .btn-add-new:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    color: #ffffff !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-create:hover::before, .btn-add-new:hover::before {
    left: 100% !important;
}

.btn-create:active, .btn-add-new:active {
    transform: translateY(-1px) scale(0.98) !important;
}

/* === SEARCH COMPONENT === */
.search-container {
    position: relative;
    max-width: 400px;
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 12px 20px 12px 50px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    font-size: 16px;
    background-color: var(--white);
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-light);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--input-focus-shadow), 0 4px 12px var(--shadow-medium);
    background-color: var(--white);
}

.search-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 18px;
    pointer-events: none;
    transition: color 0.3s ease;
}

.search-input:focus + .search-icon {
    color: var(--primary-color);
}

.search-clear {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
}

.search-clear.show {
    opacity: 1;
    visibility: visible;
}

.search-clear:hover {
    color: var(--danger-color);
    background-color: var(--danger-light);
}

/* Search results highlight */
.search-highlight {
    background-color: var(--warning-light);
    color: var(--warning-dark);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* No results message */
.no-results {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
    font-style: italic;
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}
