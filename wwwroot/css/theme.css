
:root {
    /* === PRIMARY COLORS - Soft Blue nhẹ nhàng === */
    --primary-color: #a8dadc;
    --primary-hover: #8ecae6;
    --primary-active: #7bb3f0;
    --primary-light: #f0f8ff;
    --primary-dark: #6b9bd1;

    --secondary-color: #b8f2e6;
    --secondary-hover: #a3e4d7;
    --secondary-active: #8ed1c6;
    --secondary-light: #f0fffe;
    --secondary-dark: #7fb3d3;

    /* === ACCENT COLORS - Cam pastel nhẹ === */
    --accent-color: #ffd6a5;
    --accent-hover: #ffcc8f;
    --accent-active: #ffb366;

    /* === STATUS COLORS - Màu pastel dịu nhẹ === */
    --success-color: #90e0ef;
    --success-hover: #7dd3fc;
    --success-light: #f0fdff;
    --success-dark: #0891b2;

    --warning-color: #ffd6a5;
    --warning-hover: #ffcc8f;
    --warning-light: #fffbf0;
    --warning-dark: #f59e0b;

    --danger-color: #ffb3ba;
    --danger-hover: #ff9aa2;
    --danger-light: #fff5f5;
    --danger-dark: #dc2626;

    --info-color: #a8dadc;
    --info-hover: #8ecae6;
    --info-light: #f0f8ff;
    --info-dark: #0369a1;

    /* === NEUTRAL COLORS - Màu trung tính mềm mại === */
    --white: #ffffff;
    --light: #fafbfc;
    --light-gray: #f1f3f4;
    --gray: #8e9aaf;
    --dark-gray: #636e72;
    --dark: #2d3436;
    --black: #000000;

    /* === BACKGROUND COLORS - Nền tươi mát === */
    --bg-primary: var(--white);
    --bg-secondary: #f0fdff;
    --bg-tertiary: #f0f8ff;
    --bg-dark: var(--dark);

    /* === TEXT COLORS - Màu chữ dễ đọc === */
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-light: var(--white);
    --text-link: #2b6cb0;
    --text-link-hover: #2c5282;

    /* === BORDER COLORS - Viền mềm mại === */
    --border-color: #e0f2fe;
    --border-light: #f0f8ff;
    --border-dark: #7dd3fc;

    /* === SHADOW COLORS - Bóng đổ nhẹ nhàng === */
    --shadow-light: rgba(168, 218, 220, 0.08);
    --shadow-medium: rgba(168, 218, 220, 0.12);
    --shadow-dark: rgba(168, 218, 220, 0.16);
    --shadow-colored: rgba(184, 242, 230, 0.15);

    /* === TYPOGRAPHY - Font hiện đại === */
    --font-family-primary: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-secondary: 'Poppins', 'Arial', sans-serif;
    --font-family-monospace: 'JetBrains Mono', 'Fira Code', monospace;

    /* === FONT SIZES - Kích thước linh hoạt === */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* === FONT WEIGHTS - Trọng lượng font === */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* === LINE HEIGHTS - Khoảng cách dòng === */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* === SPACING - Khoảng cách === */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* === BORDER RADIUS - Bo góc mềm mại === */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 9999px;

    /* === LAYOUT - Bố cục === */
    --sidebar-width: 280px;
    --header-height: 70px;
    --footer-height: 60px;

    /* === TRANSITIONS - Hiệu ứng chuyển động === */
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* === Z-INDEX - Lớp hiển thị === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* === DARK THEME VARIABLES === */
@media (prefers-color-scheme: dark) {
    :root {
        /* === BACKGROUND COLORS - DARK === */
        --bg-primary: #121212;
        --bg-secondary: #1e1e1e;
        --bg-tertiary: #2d2d2d;
        --bg-dark: #0a0a0a;

        /* === TEXT COLORS - DARK === */
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --text-muted: #8a8a8a;
        --text-light: #ffffff;
        --text-link: #4fc3f7;
        --text-link-hover: #29b6f6;

        /* === BORDER COLORS - DARK === */
        --border-color: #404040;
        --border-light: #333333;
        --border-dark: #555555;

        /* === NEUTRAL COLORS - DARK === */
        --light: #2d2d2d;
        --light-gray: #404040;
        --gray: #8a8a8a;
        --dark-gray: #b3b3b3;
        --dark: #ffffff;

        /* === SHADOW COLORS - DARK === */
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.5);
        --shadow-dark: rgba(0, 0, 0, 0.7);

        /* === COMPONENT SPECIFIC - DARK === */
        --card-bg: #1e1e1e;
        --card-border: #404040;
        --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

        --table-stripe-bg: rgba(255, 255, 255, 0.02);
        --table-hover-bg: rgba(255, 255, 255, 0.05);
        --table-border: #404040;

        --input-border: #404040;
        --input-focus-border: var(--primary-color);
        --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

        /* === STATUS COLORS - DARK VARIANTS === */
        --success-light: rgba(40, 167, 69, 0.2);
        --success-dark: #4caf50;

        --warning-light: rgba(255, 193, 7, 0.2);
        --warning-dark: #ffeb3b;

        --danger-light: rgba(220, 53, 69, 0.2);
        --danger-dark: #f44336;

        --info-light: rgba(23, 162, 184, 0.2);
        --info-dark: #2196f3;

        /* === SIDEBAR - DARK === */
        --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
        --sidebar-text: #ffffff;
        --sidebar-hover: rgba(255, 255, 255, 0.15);
    }
}

/* === DARK THEME TOGGLE CLASS === */
[data-theme="dark"] {
    /* === BACKGROUND COLORS - DARK === */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --bg-dark: #0a0a0a;

    /* === TEXT COLORS - DARK === */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #8a8a8a;
    --text-light: #ffffff;
    --text-link: #4fc3f7;
    --text-link-hover: #29b6f6;

    /* === BORDER COLORS - DARK === */
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;

    /* === NEUTRAL COLORS - DARK === */
    --light: #2d2d2d;
    --light-gray: #404040;
    --gray: #8a8a8a;
    --dark-gray: #b3b3b3;
    --dark: #ffffff;

    /* === SHADOW COLORS - DARK === */
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
    --shadow-dark: rgba(0, 0, 0, 0.7);

    /* === COMPONENT SPECIFIC - DARK === */
    --card-bg: #1e1e1e;
    --card-border: #404040;
    --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

    --table-stripe-bg: rgba(255, 255, 255, 0.02);
    --table-hover-bg: rgba(255, 255, 255, 0.05);
    --table-border: #404040;

    --input-border: #404040;
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

    /* === STATUS COLORS - DARK VARIANTS === */
    --success-light: rgba(40, 167, 69, 0.2);
    --success-dark: #4caf50;

    --warning-light: rgba(255, 193, 7, 0.2);
    --warning-dark: #ffeb3b;

    --danger-light: rgba(220, 53, 69, 0.2);
    --danger-dark: #f44336;

    --info-light: rgba(23, 162, 184, 0.2);
    --info-dark: #2196f3;

    /* === SIDEBAR - DARK === */
    --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
    --sidebar-text: #ffffff;
    --sidebar-hover: rgba(255, 255, 255, 0.15);
}

/* === CUSTOM PROPERTIES FOR SPECIFIC COMPONENTS === */
:root {
    /* Sidebar - Gradient trẻ trung */
    --sidebar-bg: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --sidebar-text: var(--white);
    --sidebar-hover: rgba(255, 255, 255, 0.15);

    /* Cards - Thiết kế hiện đại */
    --card-bg: var(--white);
    --card-border: var(--border-color);
    --card-shadow: 0 4px 20px var(--shadow-light);
    --card-hover-shadow: 0 8px 30px var(--shadow-medium);

    /* Buttons - Hiệu ứng nổi bật */
    --btn-shadow: 0 2px 8px var(--shadow-light);
    --btn-shadow-hover: 0 4px 15px var(--shadow-medium);
    --btn-gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --btn-gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--accent-color));

    /* Tables - Giao diện sạch sẽ */
    --table-stripe-bg: rgba(168, 218, 220, 0.03);
    --table-hover-bg: rgba(184, 242, 230, 0.08);
    --table-border: var(--border-light);

    /* Forms - Input hiện đại */
    --input-border: var(--border-color);
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 3px rgba(168, 218, 220, 0.1);
    --input-bg: var(--white);

    /* Dark Mode Toggle */
    --toggle-bg: var(--light-gray);
    --toggle-thumb: var(--white);
    --toggle-active-bg: var(--primary-color);

    /* Scrollbar - Thiết kế mịn màng */
    --scrollbar-track: var(--light);
    --scrollbar-thumb: var(--gray);
    --scrollbar-thumb-hover: var(--primary-color);

    /* Checkout specific - Màu sắc đặc biệt cho checkout */
    --checkout-bg: linear-gradient(135deg, #f0fdff 0%, #f0f8ff 100%);
    --checkout-card-bg: rgba(255, 255, 255, 0.95);
    --checkout-accent: var(--accent-color);
    --checkout-success: var(--success-color);
}

/* === MODERN UI COMPONENTS - Giao diện hiện đại === */

/* Buttons với gradient và hiệu ứng */
.btn-modern {
    background: var(--btn-gradient-primary);
    border: none;
    border-radius: var(--border-radius-lg);
    color: var(--white);
    font-weight: var(--font-weight-medium);
    padding: 0.75rem 1.5rem;
    transition: var(--transition-normal);
    box-shadow: var(--btn-shadow);
    position: relative;
    overflow: hidden;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--btn-shadow-hover);
    color: var(--white);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-normal);
}

.btn-modern:hover::before {
    left: 100%;
}

/* Cards với hiệu ứng hover */
.card-modern {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--card-shadow);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
}

/* Form controls hiện đại */
.form-control-modern {
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius-lg);
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    background: var(--input-bg);
}

.form-control-modern:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    outline: none;
}

/* Checkout specific styles */
.checkout-container {
    background: var(--checkout-bg);
    min-height: 100vh;
    padding: 2rem 0;
}

.checkout-card {
    background: var(--checkout-card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-2xl);
    box-shadow: 0 8px 32px rgba(108, 92, 231, 0.1);
}

.checkout-header {
    background: var(--btn-gradient-primary);
    color: var(--white);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    padding: 1.5rem;
}

.checkout-step {
    background: var(--success-light);
    color: var(--success-dark);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--success-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse-on-hover:hover {
    animation: pulse 0.6s ease-in-out;
}

/* Responsive design */
@media (max-width: 768px) {
    .checkout-container {
        padding: 1rem;
    }

    .checkout-card {
        margin: 0.5rem;
    }

    .btn-modern {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* === IMPROVED BUTTON COLORS === */
.btn-primary, .btn-modern {
    background: linear-gradient(135deg, #2b6cb0, #3182ce) !important;
    border-color: #2b6cb0 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(43, 108, 176, 0.3) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover, .btn-modern:hover {
    background: linear-gradient(135deg, #2c5282, #2a69ac) !important;
    border-color: #2c5282 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(43, 108, 176, 0.4) !important;
}

.card-header, .checkout-header {
    background: linear-gradient(135deg, #a8dadc, #b8f2e6) !important;
    color: #1a202c !important;
}

.text-primary {
    color: #2b6cb0 !important;
}

.border-primary {
    border-color: #a8dadc !important;
}

.bg-primary {
    background: linear-gradient(135deg, #a8dadc, #b8f2e6) !important;
    color: #1a202c !important;
}

/* Sidebar với theme soft blue */
.sidebar {
    background: linear-gradient(135deg, #a8dadc, #b8f2e6) !important;
}

/* Form focus states */
.form-control:focus, .form-select:focus {
    border-color: #a8dadc !important;
    box-shadow: 0 0 0 0.2rem rgba(168, 218, 220, 0.25) !important;
}

/* Additional soft blue theme overrides */
.checkout-step {
    background: linear-gradient(135deg, #f0fdff, #f0f8ff) !important;
    border-left: 4px solid #a8dadc !important;
}

.alert-info {
    background-color: #f0fdff !important;
    border-color: #e0f2fe !important;
    color: #0369a1 !important;
}

.card-modern {
    border: 1px solid #e0f2fe !important;
}

.checkout-container {
    background: linear-gradient(135deg, #f0fdff 0%, #f0f8ff 100%) !important;
}

/* Icons with soft blue color */
.text-primary i, .text-primary .fas {
    color: #2b6cb0 !important;
}

/* Success elements */
.text-success {
    color: #38a169 !important;
}

/* Navbar and navigation - Cải thiện highlight */
.navbar-brand {
    color: #1a202c !important;
    font-weight: 700 !important;
}

.nav-link.active {
    color: #ffffff !important;
    background-color: #2b6cb0 !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
}

.nav-link:hover {
    color: #2b6cb0 !important;
    background-color: rgba(43, 108, 176, 0.1) !important;
    border-radius: 8px !important;
}

/* Sidebar navigation improvements */
.sidebar .nav-link {
    color: #1a202c !important;
    transition: all 0.3s ease !important;
    margin: 2px 8px !important;
    border-radius: 8px !important;
}

.sidebar .nav-link:hover {
    color: #ffffff !important;
    background-color: rgba(43, 108, 176, 0.8) !important;
    transform: translateX(4px) !important;
}

.sidebar .nav-link.active {
    color: #ffffff !important;
    background-color: #2b6cb0 !important;
    border-left: 4px solid #ffffff !important;
    font-weight: 600 !important;
}

/* Additional text improvements */
h1, h2, h3, h4, h5, h6 {
    color: #1a202c !important;
}

.form-label {
    color: #1a202c !important;
    font-weight: 600 !important;
}

/* Table header improvements */
.table thead th {
    background: linear-gradient(135deg, #2b6cb0 0%, #3182ce 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 0.875rem !important;
}

.table thead th:first-child {
    border-top-left-radius: 12px !important;
}

.table thead th:last-child {
    border-top-right-radius: 12px !important;
}

/* Table body improvements */
.table tbody tr {
    transition: all 0.2s ease !important;
}

.table tbody tr:hover {
    background-color: rgba(43, 108, 176, 0.05) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(43, 108, 176, 0.1) !important;
}

/* === IMPROVED ACTION BUTTONS === */
.btn-success {
    background: linear-gradient(135deg, #38a169, #48bb78) !important;
    border-color: #38a169 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3) !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, #2f855a, #38a169) !important;
    border-color: #2f855a !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(56, 161, 105, 0.4) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ed8936, #f6ad55) !important;
    border-color: #ed8936 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3) !important;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #dd6b20, #ed8936) !important;
    border-color: #dd6b20 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(237, 137, 54, 0.4) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #e53e3e, #fc8181) !important;
    border-color: #e53e3e !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3) !important;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c53030, #e53e3e) !important;
    border-color: #c53030 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4) !important;
}

.btn-info {
    background: linear-gradient(135deg, #3182ce, #63b3ed) !important;
    border-color: #3182ce !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3) !important;
}

.btn-info:hover {
    background: linear-gradient(135deg, #2c5282, #3182ce) !important;
    border-color: #2c5282 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(49, 130, 206, 0.4) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #718096, #a0aec0) !important;
    border-color: #718096 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3) !important;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4a5568, #718096) !important;
    border-color: #4a5568 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4) !important;
}
