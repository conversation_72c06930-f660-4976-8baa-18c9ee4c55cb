
:root {
    /* === PRIMARY COLORS - Xanh lá tươi mới === */
    --primary-color: #2ecc71;
    --primary-hover: #27ae60;
    --primary-active: #229954;
    --primary-light: #e8f8f5;
    --primary-dark: #1e8449;

    --secondary-color: #1abc9c;
    --secondary-hover: #16a085;
    --secondary-active: #138d75;
    --secondary-light: #e8f8f5;
    --secondary-dark: #117a65;

    /* === ACCENT COLORS - <PERSON><PERSON><PERSON> sắc tư<PERSON>i sáng === */
    --accent-color: #58d68d;
    --accent-hover: #52c41a;
    --accent-active: #389e0d;

    /* === STATUS COLORS - Màu pastel dịu nhẹ === */
    --success-color: #52c41a;
    --success-hover: #389e0d;
    --success-light: #f6ffed;
    --success-dark: #237804;

    --warning-color: #fadb14;
    --warning-hover: #d4b106;
    --warning-light: #feffe6;
    --warning-dark: #ad8b00;

    --danger-color: #ff4d4f;
    --danger-hover: #ff7875;
    --danger-light: #fff2f0;
    --danger-dark: #cf1322;

    --info-color: #40a9ff;
    --info-hover: #1890ff;
    --info-light: #e6f7ff;
    --info-dark: #0050b3;

    /* === NEUTRAL COLORS - Màu trung tính mềm mại === */
    --white: #ffffff;
    --light: #fafbfc;
    --light-gray: #f1f3f4;
    --gray: #8e9aaf;
    --dark-gray: #636e72;
    --dark: #2d3436;
    --black: #000000;

    /* === BACKGROUND COLORS - Nền tươi sáng === */
    --bg-primary: var(--white);
    --bg-secondary: #f6ffed;
    --bg-tertiary: #f0f9f0;
    --bg-dark: var(--dark);

    /* === TEXT COLORS - Màu chữ dễ đọc === */
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-muted: #8e9aaf;
    --text-light: var(--white);
    --text-link: #2ecc71;
    --text-link-hover: #27ae60;

    /* === BORDER COLORS - Viền mềm mại === */
    --border-color: #d9f7be;
    --border-light: #f0f9f0;
    --border-dark: #95de64;

    /* === SHADOW COLORS - Bóng đổ nhẹ nhàng === */
    --shadow-light: rgba(46, 204, 113, 0.08);
    --shadow-medium: rgba(46, 204, 113, 0.12);
    --shadow-dark: rgba(46, 204, 113, 0.16);
    --shadow-colored: rgba(88, 214, 141, 0.15);

    /* === TYPOGRAPHY - Font hiện đại === */
    --font-family-primary: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-secondary: 'Poppins', 'Arial', sans-serif;
    --font-family-monospace: 'JetBrains Mono', 'Fira Code', monospace;

    /* === FONT SIZES - Kích thước linh hoạt === */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* === FONT WEIGHTS - Trọng lượng font === */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* === LINE HEIGHTS - Khoảng cách dòng === */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* === SPACING - Khoảng cách === */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* === BORDER RADIUS - Bo góc mềm mại === */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 9999px;

    /* === LAYOUT - Bố cục === */
    --sidebar-width: 280px;
    --header-height: 70px;
    --footer-height: 60px;

    /* === TRANSITIONS - Hiệu ứng chuyển động === */
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* === Z-INDEX - Lớp hiển thị === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* === DARK THEME VARIABLES === */
@media (prefers-color-scheme: dark) {
    :root {
        /* === BACKGROUND COLORS - DARK === */
        --bg-primary: #121212;
        --bg-secondary: #1e1e1e;
        --bg-tertiary: #2d2d2d;
        --bg-dark: #0a0a0a;

        /* === TEXT COLORS - DARK === */
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --text-muted: #8a8a8a;
        --text-light: #ffffff;
        --text-link: #4fc3f7;
        --text-link-hover: #29b6f6;

        /* === BORDER COLORS - DARK === */
        --border-color: #404040;
        --border-light: #333333;
        --border-dark: #555555;

        /* === NEUTRAL COLORS - DARK === */
        --light: #2d2d2d;
        --light-gray: #404040;
        --gray: #8a8a8a;
        --dark-gray: #b3b3b3;
        --dark: #ffffff;

        /* === SHADOW COLORS - DARK === */
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.5);
        --shadow-dark: rgba(0, 0, 0, 0.7);

        /* === COMPONENT SPECIFIC - DARK === */
        --card-bg: #1e1e1e;
        --card-border: #404040;
        --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

        --table-stripe-bg: rgba(255, 255, 255, 0.02);
        --table-hover-bg: rgba(255, 255, 255, 0.05);
        --table-border: #404040;

        --input-border: #404040;
        --input-focus-border: var(--primary-color);
        --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

        /* === STATUS COLORS - DARK VARIANTS === */
        --success-light: rgba(40, 167, 69, 0.2);
        --success-dark: #4caf50;

        --warning-light: rgba(255, 193, 7, 0.2);
        --warning-dark: #ffeb3b;

        --danger-light: rgba(220, 53, 69, 0.2);
        --danger-dark: #f44336;

        --info-light: rgba(23, 162, 184, 0.2);
        --info-dark: #2196f3;

        /* === SIDEBAR - DARK === */
        --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
        --sidebar-text: #ffffff;
        --sidebar-hover: rgba(255, 255, 255, 0.15);
    }
}

/* === DARK THEME TOGGLE CLASS === */
[data-theme="dark"] {
    /* === BACKGROUND COLORS - DARK === */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --bg-dark: #0a0a0a;

    /* === TEXT COLORS - DARK === */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #8a8a8a;
    --text-light: #ffffff;
    --text-link: #4fc3f7;
    --text-link-hover: #29b6f6;

    /* === BORDER COLORS - DARK === */
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;

    /* === NEUTRAL COLORS - DARK === */
    --light: #2d2d2d;
    --light-gray: #404040;
    --gray: #8a8a8a;
    --dark-gray: #b3b3b3;
    --dark: #ffffff;

    /* === SHADOW COLORS - DARK === */
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
    --shadow-dark: rgba(0, 0, 0, 0.7);

    /* === COMPONENT SPECIFIC - DARK === */
    --card-bg: #1e1e1e;
    --card-border: #404040;
    --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);

    --table-stripe-bg: rgba(255, 255, 255, 0.02);
    --table-hover-bg: rgba(255, 255, 255, 0.05);
    --table-border: #404040;

    --input-border: #404040;
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.4);

    /* === STATUS COLORS - DARK VARIANTS === */
    --success-light: rgba(40, 167, 69, 0.2);
    --success-dark: #4caf50;

    --warning-light: rgba(255, 193, 7, 0.2);
    --warning-dark: #ffeb3b;

    --danger-light: rgba(220, 53, 69, 0.2);
    --danger-dark: #f44336;

    --info-light: rgba(23, 162, 184, 0.2);
    --info-dark: #2196f3;

    /* === SIDEBAR - DARK === */
    --sidebar-bg: linear-gradient(135deg, #1a1a2e, #16213e);
    --sidebar-text: #ffffff;
    --sidebar-hover: rgba(255, 255, 255, 0.15);
}

/* === CUSTOM PROPERTIES FOR SPECIFIC COMPONENTS === */
:root {
    /* Sidebar - Gradient trẻ trung */
    --sidebar-bg: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --sidebar-text: var(--white);
    --sidebar-hover: rgba(255, 255, 255, 0.15);

    /* Cards - Thiết kế hiện đại */
    --card-bg: var(--white);
    --card-border: var(--border-color);
    --card-shadow: 0 4px 20px var(--shadow-light);
    --card-hover-shadow: 0 8px 30px var(--shadow-medium);

    /* Buttons - Hiệu ứng nổi bật */
    --btn-shadow: 0 2px 8px var(--shadow-light);
    --btn-shadow-hover: 0 4px 15px var(--shadow-medium);
    --btn-gradient-primary: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --btn-gradient-secondary: linear-gradient(135deg, var(--secondary-color), #52c41a);

    /* Tables - Giao diện sạch sẽ */
    --table-stripe-bg: rgba(46, 204, 113, 0.03);
    --table-hover-bg: rgba(88, 214, 141, 0.08);
    --table-border: var(--border-light);

    /* Forms - Input hiện đại */
    --input-border: var(--border-color);
    --input-focus-border: var(--primary-color);
    --input-focus-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
    --input-bg: var(--white);

    /* Dark Mode Toggle */
    --toggle-bg: var(--light-gray);
    --toggle-thumb: var(--white);
    --toggle-active-bg: var(--primary-color);

    /* Scrollbar - Thiết kế mịn màng */
    --scrollbar-track: var(--light);
    --scrollbar-thumb: var(--gray);
    --scrollbar-thumb-hover: var(--primary-color);

    /* Checkout specific - Màu sắc đặc biệt cho checkout */
    --checkout-bg: linear-gradient(135deg, #f6ffed 0%, #f0f9f0 100%);
    --checkout-card-bg: rgba(255, 255, 255, 0.95);
    --checkout-accent: var(--accent-color);
    --checkout-success: var(--success-color);
}

/* === MODERN UI COMPONENTS - Giao diện hiện đại === */

/* Buttons với gradient và hiệu ứng */
.btn-modern {
    background: var(--btn-gradient-primary);
    border: none;
    border-radius: var(--border-radius-lg);
    color: var(--white);
    font-weight: var(--font-weight-medium);
    padding: 0.75rem 1.5rem;
    transition: var(--transition-normal);
    box-shadow: var(--btn-shadow);
    position: relative;
    overflow: hidden;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--btn-shadow-hover);
    color: var(--white);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-normal);
}

.btn-modern:hover::before {
    left: 100%;
}

/* Cards với hiệu ứng hover */
.card-modern {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--card-shadow);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
}

/* Form controls hiện đại */
.form-control-modern {
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius-lg);
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    background: var(--input-bg);
}

.form-control-modern:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    outline: none;
}

/* Checkout specific styles */
.checkout-container {
    background: var(--checkout-bg);
    min-height: 100vh;
    padding: 2rem 0;
}

.checkout-card {
    background: var(--checkout-card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-2xl);
    box-shadow: 0 8px 32px rgba(108, 92, 231, 0.1);
}

.checkout-header {
    background: var(--btn-gradient-primary);
    color: var(--white);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    padding: 1.5rem;
}

.checkout-step {
    background: var(--success-light);
    color: var(--success-dark);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--success-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse-on-hover:hover {
    animation: pulse 0.6s ease-in-out;
}

/* Responsive design */
@media (max-width: 768px) {
    .checkout-container {
        padding: 1rem;
    }

    .checkout-card {
        margin: 0.5rem;
    }

    .btn-modern {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* === FORCE GREEN THEME OVERRIDE === */
.btn-primary, .btn-modern {
    background: linear-gradient(135deg, #2ecc71, #58d68d) !important;
    border-color: #2ecc71 !important;
    color: white !important;
}

.btn-primary:hover, .btn-modern:hover {
    background: linear-gradient(135deg, #27ae60, #52c41a) !important;
    border-color: #27ae60 !important;
    color: white !important;
}

.card-header, .checkout-header {
    background: linear-gradient(135deg, #2ecc71, #58d68d) !important;
    color: white !important;
}

.text-primary {
    color: #2ecc71 !important;
}

.border-primary {
    border-color: #2ecc71 !important;
}

.bg-primary {
    background-color: #2ecc71 !important;
}

/* Sidebar với theme xanh lá */
.sidebar {
    background: linear-gradient(135deg, #2ecc71, #1abc9c) !important;
}

/* Form focus states */
.form-control:focus, .form-select:focus {
    border-color: #2ecc71 !important;
    box-shadow: 0 0 0 0.2rem rgba(46, 204, 113, 0.25) !important;
}
