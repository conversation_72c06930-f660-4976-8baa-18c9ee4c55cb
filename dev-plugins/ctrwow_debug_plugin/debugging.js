// eslint-disable-next-line no-undef
chrome.storage.local.get(['key'], function (result) {
  try {
    const debugModule = document.getElementById("debugModule")
    let parts = result.key || ""
    parts = parts.split(",")
    debugModule.innerHTML = parts.join("<br/>") || "[None]"
  } catch (e) {
    // do nothing
  }
})

// eslint-disable-next-line no-undef
chrome.storage.local.get(['__ctrDebuggingModule__keys'], function (result) {
  try {
    const debugModule = document.getElementById("debugModule__gjs")
    let parts = result.__ctrDebuggingModule__keys || ""
    parts = parts.split(",")

    debugModule.innerHTML = parts.join("<br/>") || "[None]"
  } catch (e) {
    // do nothing
  }
})
