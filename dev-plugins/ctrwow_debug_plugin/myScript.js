/* eslint-disable */

const adDebugScript = (__ctrDebuggingModule = '') => {
  if (__ctrDebuggingModule.length > 0) {
    console.log('%c[CTRwowDebugPlugin] debugging widgets: ' + __ctrDebuggingModule, 'color:#669900;font-size:18px;font-weight:bold;')
  }

  const isRun = (el, packageName, props) => {

    const checkExitPackage = (configPackage, current) => configPackage.split(',').includes(current)

    const isDebugging__simple = checkExitPackage(window.__ctrDebuggingModule || '', packageName)
    const isDebugging__gjs = checkExitPackage(window.__ctrDebuggingModule__gjs || '', packageName)
    const isDebugging = isDebugging__simple || isDebugging__gjs
    const getDebugHost = () => `http://localhost:${isDebugging__gjs ? '9900' : '9000'}`
    const getModuleRoot = () => isDebugging__gjs ? window.ctrExternalWidgetModule__loaders || {} : window
    let retry = null

    if (!packageName || !isDebugging) {
      return false
    }

    runComponent(el, props)

    if (packageName) {
      window.ctrwowUtils.getDependencies([`${getDebugHost()}/${packageName}.js`]).catch((e) => {
        console.log(e)
        console.log('[ctrwowUtils][debugMode] load js code fail')
      })
      return true
    }

    function runComponent(...args) {
      if (getModuleRoot()[packageName]) {
        try {
          if (typeof getModuleRoot()[packageName].default === 'function') {
            getModuleRoot()[packageName].default(...args)
          }
          if (typeof getModuleRoot()[packageName] === 'function') {
            getModuleRoot()[packageName](...args)
          }
        } catch (e) {
          console.log(e)
        }
        retry && clearInterval(retry)
        return
      }

      if (!retry) {
        retry = setInterval(() => runComponent(...args), 20)
      }
    }
  }

  console.log('APPEND SCRIPT AFTER')
  // 1. append main script
  const script = document.createElement('script')
  script.type = 'text/javascript'
  script.textContent = `window.__ctrDebuggingModule='${__ctrDebuggingModule}';
  window.ctrDebug={run: ${isRun.toString()}}`
  ;(document.head || document.documentElement).appendChild(script)
}

const getDebugHost = () => 'http://localhost:9000'
// 1. append main script
// eslint-disable-next-line no-undef
chrome.storage.local.get(['key'], function(result) {
  adDebugScript(result.key || '')
})

// 2. get debugging module
fetch([`${getDebugHost()}/debuggingPackages.json`])
  .then((res) => res.json())
  .then((res) => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({key: res.toString()}, function() {
      console.log('Value is set to ' + res.toString())
    })
  })
  .catch(() => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({key: null}, function() {
      console.log('Value is set to None')
    })
  })

// TODO - for css injection
// window.addEventListener('DOMContentLoaded', (event) => {
//   console.log('DOM fully loaded and parsed')
//   console.log('DOM fully loaded and parsed')
//   console.log('DOM fully loaded and parsed')
//   console.log(document.querySelectorAll('link[rel="stylesheet"]'))
//   console.log(window.__ctrDebuggingModule)
//   chrome.storage.local.get(['key'], function (result) {
//     console.log('==============Value currently is ' + result.key)
//     const __ctrDebuggingModule = result.key.split(',')
//
//     __ctrDebuggingModule.forEach((widgetName) => {
//       const link = document.createElement('link')
//       link.rel = 'stylesheet'
//       link.href = `${getDebugHost()}/${widgetName}.css}`
//       ;(document.body || document.documentElement).appendChild(link)
//     })
//   })
// })

const adDebugScript__gjs = (__ctrDebuggingModule__gjs = '') => {
  if (__ctrDebuggingModule__gjs.length > 0) {
    console.log('%c[CTRwowDebugPlugin] debugging widgets gjs: ' + __ctrDebuggingModule__gjs, 'color:blue;font-size:18px;font-weight:bold;')
  }

  const isRun = (packageName) => window.__ctrDebuggingModule__gjs && window.__ctrDebuggingModule__gjs.indexOf(packageName) > -1
  const getPackagePath = (packageName) => `http://localhost:9900/${packageName}__gjs.js`
  const getPackageName = (packageName) => `${packageName}__gjs`

  const exportedFuncs = [
    `isRun: ${isRun.toString()}`,
    `getPackagePath: ${getPackagePath.toString()}`,
    `getPackageName: ${getPackageName.toString()}`
  ].join(',')

  console.log('APPEND SCRIPT AFTER')
  // 1. append main script
  const script = document.createElement('script')
  script.type = 'text/javascript'
  script.textContent = `window.__ctrDebuggingModule__gjs='${__ctrDebuggingModule__gjs}';
  window.ctrDebug__gjs={${exportedFuncs}}`
  ;(document.head || document.documentElement).appendChild(script)
}

const getDebugHost__gjs = () => 'http://localhost:9900'
// 1. append main script
// eslint-disable-next-line no-undef
chrome.storage.local.get(['__ctrDebuggingModule__keys'], function(result) {
  adDebugScript__gjs(result.__ctrDebuggingModule__keys || '')
})

// 2. get debugging module
fetch([`${getDebugHost__gjs()}/debuggingPackages__gjs.json`])
  .then((res) => res.json())
  .then((res) => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({__ctrDebuggingModule__keys: res.toString()}, function() {
      console.log('Value is set to ' + res.toString())
    })
  })
  .catch(() => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({__ctrDebuggingModule__keys: null}, function() {
      console.log('Value is set to None')
    })
  })
