/* eslint-disable */


//1 - ADD DEBUGGING SCRIP TO ACTIVE PAGE
var s = document.createElement('script');
s.src = chrome.runtime.getURL('ctrDebug.js');
//-start
// s.type = 'text/javascript'
// s.textContent = chrome.runtime.getURL('ctrDebug.js');
//-end
(document.head || document.documentElement).appendChild(s);




//teest
window.__bl_123aaa= 'dfakdjadkjf'
window.__bl_123=  {
  f1: () => {console.log('abc1')}
}




// 2. READ DEBUGGING-PACKAGE FROM [CHROME STORAGE] AND SET TO [ACTIVE-WEBPAGE]
const getDebugHost__gjs = () => 'http://localhost:9900'
chrome.storage.local.get(['__ctrDebuggingModule__keys'], function(result) {
  console.log('[myScript] result', result)
  var s = document.createElement('template');
  s.id = '__ctrDebuggingModule__gjs';
  s.textContent = JSON.stringify(result.__ctrDebuggingModule__keys);
  (document.head || document.documentElement).appendChild(s);
})


chrome.storage.local.get(['__ctrDebuggingModuleSimple__keys'], function(result) {
  console.log('[myScript] __ctrDebuggingModuleSimple__keys', result)
  var s = document.createElement('template');
  s.id = '__ctrDebuggingModule';
  s.textContent = JSON.stringify(result.__ctrDebuggingModuleSimple__keys);
  (document.head || document.documentElement).appendChild(s);
})


// 3. ASYNC ACTION - READ DEBUGGING-PACKAGE FROM DEBUGGING-PORT AND SET TO [CHROME STORAGE]
fetch([`${getDebugHost__gjs()}/debuggingPackages__gjs.json`])
  .then((res) => res.json())
  .then((res) => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({__ctrDebuggingModule__keys: res.toString()}, function() {
      console.log('%c[ctrDebug:mainScript]Value is set to ' + res.toString(), 'background-color: #bbef9a;font-size:11px')
    })
  })
  .catch((e) => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({__ctrDebuggingModule__keys: null}, function() {
      console.log('%c[ctrDebug:mainScript]Value is set to [NONE]', 'background-color: #bbef9a;font-size:11px')
      console.log('Error', e)
    })
  })

fetch([`http://localhost:9000/debuggingPackages.json`])
  .then((res) => res.json())
  .then((res) => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({__ctrDebuggingModuleSimple__keys: res.toString()}, function() {
      console.log('%c[ctrDebug:mainScript][__ctrDebuggingModuleSimple__keys] Value is set to ' + res.toString(), 'background-color: #bbef9a;font-size:11px')
    })
  })
  .catch((e) => {
    // eslint-disable-next-line no-undef
    chrome.storage.local.set({__ctrDebuggingModuleSimple__keys: null}, function() {
      console.log('%c[ctrDebug:mainScript][__ctrDebuggingModuleSimple__keys] Value is set to [NONE]', 'background-color: #bbef9a;font-size:11px')
      console.log('Error', e)
    })
  })

