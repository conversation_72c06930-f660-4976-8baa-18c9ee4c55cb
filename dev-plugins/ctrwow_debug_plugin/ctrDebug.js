/*eslint-disable*/
const isPublishedDomain = ["https://publish.ctrwow.com", "https://ctrwowdevweb.z5.web.core.windows.net"].indexOf(location.origin) >= 0
if (isPublishedDomain) {
  console.log('%c[ctrDebug] - INIT SCRIPT', 'background-color: #bbef9a;font-size:11px');
} else {
  console.log('%c[CTRwow Widget Plugin] - please DISABLE CACHE on [NETWORK] tab to debug widgets on published-page', 'background-color: #bbef9a;font-size:22px;color:red');
}

const re = document.querySelector('template')
const isDebuggingWidget = (widgetName, isGjs) => {
  try {
    const debuggingWidget = getDebuggingWidgets(isGjs) || ""
    const re = debuggingWidget.split(",").indexOf(widgetName) >= 0
    return re
  } catch (e) {
    return false
  }

  function getDebuggingWidgets(isGjs) {
    try {
      const re = document.querySelector(`template#${isGjs ? "__ctrDebuggingModule__gjs" : "__ctrDebuggingModule"}`)
      return JSON.parse(re.textContent)
    } catch(e) {
      return []
    }
  }
}

/************************ FOR SIMPLE WIDGET ************************/
const isRun = (el, packageName, props) => {
  const isDebugging__simple = isDebuggingWidget(packageName, false)
  const isDebugging__gjs = isDebuggingWidget(packageName, true)
  const isDebugging = isDebugging__simple || isDebugging__gjs

  const getDebugHost = () => `http://localhost:${isDebugging__gjs ? '9900' : '9000'}`
  const getModuleRoot = () => isDebugging__gjs ? window.ctrExternalWidgetModule__loaders || {} : window
  let retry = null

  if (!packageName || !isDebugging) {
    return false
  }

  runComponent(el, props)

  if (packageName) {
    window.ctrwowUtils.getDependencies([`${getDebugHost()}/${packageName}.js`]).catch((e) => {
      console.log('%c[ctrDebug] load js code fail:', 'background-color: #bbef9a;font-size:11px', packageName)
      console.log(e)
    })
    return true
  }

  function runComponent(...args) {
    if (getModuleRoot()[packageName]) {
      try {
        if (typeof getModuleRoot()[packageName].default === 'function') {
          console.log('%c[ctrDebug][run controller]: ', 'background-color: #bbef9a;font-size:11px', packageName)
          getModuleRoot()[packageName].default(...args)
        }
        if (typeof getModuleRoot()[packageName] === 'function') {
          console.log('%c[ctrDebug][run controller]: ', 'background-color: #bbef9a;font-size:11px', packageName)
          getModuleRoot()[packageName](...args)
        }
      } catch (e) {
        console.log(e)
      }
      retry && clearInterval(retry)
      return
    }

    if (!retry) {
      retry = setInterval(() => runComponent(...args), 20)
    }
  }
}
window.ctrDebug = {
  run: isRun
}


/************************ FOR GJS WIDGET ************************/
const isRun_gjs = (packageName) => isDebuggingWidget(packageName, true)
const getPackagePath = (packageName) => `http://localhost:9900/${packageName}__gjs.js`
const getPackageName = (packageName) => `${packageName}__gjs`
window.ctrDebug__gjs = {
  isRun: isRun_gjs,
  getPackagePath,
  getPackageName
}
