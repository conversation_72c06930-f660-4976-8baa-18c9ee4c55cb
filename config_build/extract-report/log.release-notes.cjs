const utils = require("./utils.confluence.cjs")
const fs = require("fs")


const TICKET_CONFIG = {
  patterns_find: [/ctr-\d{3,5}/gi, /csb-\d{3,5}/gi],
  // patterns_replace: [/cxw-/gi, /cx-/gi],
  // ticketPrefix: 'CXWOW-'
}


// const CONTENT_ID = 3077767177//release note

const extractTicket = async () => {
  const fileName = "cxwfeteam_dev_change_log.txt"

  const CONTENT_ID = process.argv[process.argv.length - 1]
  const buildId = process.argv[process.argv.length - 2] || ""
  const repoName = process.argv[process.argv.length - 3] || ""
  const branchName = process.argv[process.argv.length - 4] || ""


  console.log('[log.release-notes::extractTicket] CONTENT_ID', CONTENT_ID, buildId)

  let gitLog = fs.readFileSync(fileName, "utf16le").toString("utf-8") || ""
  // let gitLog = fs.readFileSync(fileName).toString("utf-8") || ""

  let ticketKeys = utils.extractAllTicketKeys(TICKET_CONFIG, gitLog)
    .map(utils.getJiraLink)
    .map(str => `<li>${str}</li>`)


  console.log('[log.release-notes::extractTicket] ticketKeys', ticketKeys)
  // if (!ticketKeys || !ticketKeys.length) {
  //   return
  // }
// return

  const extractedDate = (new Date()).toString()

  const projectInfo = [
    `<h1>Branch name: <strong><span style="color: rgb(191,38,0);">${branchName}</span></strong> - BuildID: <a href="https://dfoglobal.visualstudio.com/DFO/_build/results?buildId=${buildId}" target="_blank">${buildId}</a></h1>`,
    `<p>Extracted Time: ${extractedDate}</p>`,
      ...(ticketKeys.length ? [`<h4>JIRA TICKETS</h4><ol>${ticketKeys.join("")}</ol>`] : []),
    `<h4>COMMITS</h4>`,
    utils.getBlockCode(gitLog)
    // "<ol>",
    // ...(gitLog.split("*1*").map(str => `<li>${str}</li>`)),
    // "</ol>"
  ].join("")

  // await utils.addContent(CONTENT_ID, `${projectInfo} <hr/>`)
  await utils.addContent(CONTENT_ID, `${projectInfo}<hr/>`, undefined )
}


extractTicket()
