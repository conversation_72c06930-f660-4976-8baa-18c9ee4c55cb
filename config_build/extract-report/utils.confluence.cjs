// const fetch = require("node-fetch")

const COMMON_HEADERS = {
  "Content-Type": "application/json",
  "Authorization": "Basic ********************************************************************************************************************************************************************************************************************************************************************************************",
}


const getLatestDetails = async (contentId) => {
  try {
    const {title, type, version: {number}, body: {storage: {value: current_content}}} = await (await fetch(
      `https://dfoglobal.atlassian.net/wiki/rest/api/content/${contentId}?expand=body.storage,version`, {
        method: "GET",
        headers: COMMON_HEADERS
      })).json()

    console.log("[log.release-notes::getLatestDetails] re", type, number, current_content)
    return {title, type, current_version: number, current_content}

  } catch (e) {
    return ""
  }
}

const addContent = async (contentId, newContent, newTitle, {isReplace}={}) => {
  try {
    console.log('[utils.confluence::addContent] newContent', newContent)
    const {title, type, current_version, current_content: prev_content} = await getLatestDetails(contentId)


    // return

    const re = await fetch(`https://dfoglobal.atlassian.net/wiki/rest/api/content/${contentId}`, {
      method: "PUT",
      headers: COMMON_HEADERS,
      body: JSON.stringify({
        title: newTitle || title,
        "version": {
          "number": current_version + 1,
          "message": "FE-TEAM - script to extract git commits"
        },
        type,
        "body": {
          "storage": {
            // "value": "<p1>test</p1>",
            "value": isReplace ?  newContent : `${newContent || ""}${prev_content}`,
            "representation": "storage"
          }
        }
      })
    })
    console.log("[log.release-notes::sendMessage] re", re)
  } catch (e) {
    console.log("[log.release-notes::sendMessage] e", e)
  }

}

const getJiraLink = ticketKey => `<ac:structured-macro ac:name="jira" ac:schema-version="1" ac:macro-id="3cb8856c-e8a6-43c9-8317-7d60b589f90b"><ac:parameter ac:name="key">${ticketKey}</ac:parameter><ac:parameter ac:name="serverId">b5f78b4b-8603-3879-a97e-a16b5dacbf76</ac:parameter><ac:parameter ac:name="server">System JIRA</ac:parameter></ac:structured-macro>`
const getBlockCode = content => `<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="7ae61461-b2ca-4f1e-90b9-47e81fb177d4"><ac:plain-text-body><![CDATA[ ${content}]]></ac:plain-text-body></ac:structured-macro>`

const extractAllTicketKeys = (ticketConfig, content) => {
  // let tickets = [
  //   ...(content.match() || []),
  //   ...(content.match() || []),
  //   ...(content.match(/cxwow-\d{3,5}/gi) || [])
  // ].map(str => (str || "").toUpperCase()
  //     .replace(/cxw-/gi, "CXWOW-")
  //     .replace(/cx-/gi, "CXWOW-")
  //   )
  //   .filter(str => str && str.length > 4)
  //   .filter(str => !str.startsWith("CXWOW-0"))
  //
  // return [...new Set(tickets)]

  let tickets = []

  ticketConfig.patterns_find.forEach(pattern => {
    tickets = tickets.concat(content.match(pattern) || [])
  })

  tickets = tickets.map(ticket => (ticketConfig.patterns_replace || [])
    .reduce((re, pattern) => {
      return re.replace(pattern, ticketConfig.ticketPrefix || "")
    }, ticket || ""))

  console.log('[utils.confluence::extractAllTicketKeys] tickets', tickets)


  tickets = [...new Set(tickets)]

  console.log('[utils.confluence::extractAllTicketKeys] tickets', tickets)

  return tickets
}



module.exports ={
  addContent,
  getJiraLink,
  getBlockCode,
  extractAllTicketKeys
}
