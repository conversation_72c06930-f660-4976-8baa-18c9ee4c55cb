<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Debug page</title>
  </head>
  <body>
    <h1>Widgets</h1>

    <ul id="item-list"></ul>
    <div id='packageList' style="display:none">${require('./debuggingPackages.json')}</div>
    <script>
        document.querySelector('#packageList').innerHTML.split(',')
        .forEach(name => {
            const a = document.createElement("a")
            a.innerHTML = name
            a.href = name

            const li = document.createElement('li')
            li.appendChild(a)
            document.querySelector('#item-list').appendChild(li)
        })
    </script>
  </body>

</html>
