import { detectToggleCreditCardForm } from 'shared-checkout-flow/src/upsell/detectToggleCreditCardForm'
import { populateDataToNodeContent } from 'shared-checkout-flow/src/canReplaceContentWithPlaceholder'
import createCrmInstance from 'shared-checkout-flow/src/createCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'

export function replaceBracketsStrings() {
  const imgLoading = `<span class="js-img-loading">
                <img src="//d16hdrba6dusey.cloudfront.net/sitecommon/images/loading-price-v1.gif" width="20" height="10" class="no-lazy"  style="width: 20px; display: inline;">
            </span>`
  populateDataToNodeContent((textContent) =>
    textContent
      .replace(/{unit}/g, `<span class="unit">${imgLoading}</span>`)
      .replace(/{upsellQty}/g, `<span class="upsell-qty">${imgLoading}</span>`)
      .replace(/{price}/g, `<span class="spanUpsellPrice">${imgLoading}</span>`)
      .replace(/{depositPrice}/g, `<span class="spanUpsellDepositPrice">${imgLoading}</span>`)
      .replace(/{fullprice}/g, `<span class="spanFullPrice">${imgLoading}</span>`)
      .replace(/{taxamount}/g, `<span class="tax-amount">${imgLoading}</span>`)
      .replace(/{unitprice}/g, `<span class="spanUnitPrice">${imgLoading}</span>`)
      .replace(/{saveprice}/g, `<span class="spanSavePrice">${imgLoading}</span>`)
      .replace(/{unitfullprice}/g, `<span class="spanUnitFullPrice">${imgLoading}</span>`)
      .replace(/{nameProduct}/g, `<span class="spanProductName">${imgLoading}</span>`)
      .replace(/{unitx\d+}/g, (unitx) => `<span class="${unitx}">${imgLoading}</span>`)
      .replace(/{unitpriceWithQty}/g, `<span class="spanUnitPriceWithQty">...</span>`)
      .replace(/{shippingPrice}/g, `<span class="spanShippingPrice">${imgLoading}</span>`)
      .replace(/{regularprice}/g, `<span class="spanRegularPrice">${imgLoading}</span>`)
  )
}

export function populateProductInfo(product, quantity = product.quantity) {
  window.sessionStorage.removeItem('shippingInfo')

  // const hadTax = window.localStorage.getItem('bindTax')
  const hadTax = false
  const { _qAll, _q, ctrwowUtils: utils } = window
  const orderInfo = JSON.parse(window.localStorage.getItem('orderInfo'))
  const isDoubleQuantity = utils.localStorage().get('realDoubleQuantity')
  if (isDoubleQuantity === 'true') {
    quantity /= 2
  }

  const unitElms = _qAll('.unit')
  for (const unitElm of unitElms) {
    unitElm.innerText = quantity
  }

  Array.prototype.slice.call(_qAll('[class^="unitx"]')).forEach((cls) => {
    const num = Number(cls.className.replace(/[^0-9]/g, ''))
    cls.textContent = quantity * num
  })

  Array.prototype.slice.call(_qAll('.upsell-qty')).forEach((cls) => {
    const numberInput = _q('[name="numberInput"]')
    let upsellQty = 1
    if (numberInput) {
      upsellQty = numberInput.value
    }
    cls.textContent = upsellQty
  })

  const spanUpsellPriceElems = _qAll('.spanUpsellPrice')
  for (const spanUpsellPrice of spanUpsellPriceElems) {
    if (!hadTax) {
      spanUpsellPrice.innerHTML = product.productPrices.DiscountedPrice.FormattedValue
    }

    // ? Get upgrade price: CSB-1857
    if (window.upsell__IsUpgrade !== false && window.objpid && !hadTax) {
      let discountPrice = product.productPrices.DiscountedPrice.Value - orderInfo.orderTotal
      if (window.upgrade_upsell_product) {
        discountPrice = product.productPrices.DiscountedPrice.Value - window.upsellPrice
      }
      const discountPriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(
        discountPrice,
        product.productPrices.DiscountedPrice.FormattedValue
      )
      spanUpsellPrice.innerHTML = discountPriceFormat
    }
  }

  // Binding Deposit Price for upsell product
  const spanUpsellDepositPriceElems = _qAll('.spanUpsellDepositPrice')
  const isDepositCheckout = JSON.parse(window.localStorage.getItem('isDepositCheckout')) // Switch to display Deposit Price.
  for (const spanUpsellPrice of spanUpsellDepositPriceElems) {
    if (!hadTax & isDepositCheckout) {
      spanUpsellPrice.innerHTML = product.productPrices.PreSaleAmount1.FormattedValue
    }

    // ? Get upgrade price: CSB-1857
    if (window.upsell__IsUpgrade !== false && window.objpid && !hadTax & isDepositCheckout) {
      let discountPrice = product.productPrices.PreSaleAmount1.Value - orderInfo.orderTotal
      if (window.upgrade_upsell_product) {
        discountPrice = product.productPrices.PreSaleAmount1.Value - window.upsellPrice
      }
      const discountPriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(
        discountPrice,
        product.productPrices.DiscountedPrice.FormattedValue
      )
      spanUpsellPrice.innerHTML = discountPriceFormat
    }
  }

  const spanSavePriceElems = _qAll('.spanSavePrice')
  for (const spanSavePrice of spanSavePriceElems) {
    const savePrice = product.productPrices.FullRetailPrice.Value - product.productPrices.DiscountedPrice.Value
    spanSavePrice.innerHTML = window.ctrwowUtils.number.formaterNumberByFormattedValue(
      savePrice,
      product.productPrices.DiscountedPrice.FormattedValue
    )
  }

  const spanFullPriceElems = _qAll('.spanFullPrice')
  for (const spanFullPrice of spanFullPriceElems) {
    spanFullPrice.innerHTML = product.productPrices.FullRetailPrice.FormattedValue
  }

  const spanUnitPriceElems = _qAll('.spanUnitPrice')
  for (const spanUnitPrice of spanUnitPriceElems) {
    if (!hadTax) {
      let uPrice = product.productPrices.UnitDiscountRate
      if (product.productPrices.UnitDiscountRate) {
        uPrice = product.productPrices.UnitDiscountRate.FormattedValue
      } else {
        uPrice = product.productPrices.DiscountedPrice.Value / quantity
        uPrice = window.ctrwowUtils.number.formaterNumberByFormattedValue(uPrice, product.productPrices.DiscountedPrice.FormattedValue)
      }
      spanUnitPrice.innerHTML = uPrice
    }

    // ? Get upgrade price: CSB-1141
    if (window.upsell__IsUpgrade !== false && window.objpid && !hadTax) {
      let unitPrice = (product.productPrices.DiscountedPrice.Value - orderInfo.orderTotal) / quantity
      if (window.upgrade_upsell_product) {
        unitPrice = (product.productPrices.DiscountedPrice.Value - window.upsellPrice) / quantity
      }
      const unitPriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(
        unitPrice,
        product.productPrices.DiscountedPrice.FormattedValue
      )
      spanUnitPrice.innerHTML = unitPriceFormat
    }
  }

  const spanShippingPrice = _qAll('.spanShippingPrice')
  for (const spanShipping of spanShippingPrice) {
    if (product.shippings && product.shippings.length > 0) {
      spanShipping.innerHTML = product.shippings[0].formattedPrice
    }
  }

  const array__SelectNumberInput = _qAll('.productQuantityControlWrapper select[name=numberInput]')
  if (array__SelectNumberInput.length > 0) {
    const arrProductList = window.ctrwowUpsell.productListData.getProductList().prices
    Array.prototype.slice.call(array__SelectNumberInput).forEach((item) => {
      const spanUnitPriceWithQtyElems = item.querySelectorAll('.spanUnitPriceWithQty')
      if (arrProductList.length > 0) {
        for (var i = 0; i < spanUnitPriceWithQtyElems.length; i++) {
          if (!hadTax) {
            spanUnitPriceWithQtyElems[i].innerHTML = arrProductList[i].productPrices.UnitDiscountRate.FormattedValue
          }

          // ? Get upgrade price: CSB-1141
          if (window.upsell__IsUpgrade !== false && window.objpid && !hadTax) {
            let unitPrice = (product.productPrices.DiscountedPrice.Value - orderInfo.orderTotal) / quantity
            if (window.upgrade_upsell_product) {
              unitPrice = (product.productPrices.DiscountedPrice.Value - window.upsellPrice) / quantity
            }
            const unitPriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(
              unitPrice,
              product.productPrices.UnitDiscountRate.FormattedValue
            )
            spanUnitPriceWithQtyElems[i].innerHTML = unitPriceFormat
          }
        }
      } else {
        console.log('cannot get product list')
      }
    })
  }

  const spanUnitFullPriceElems = _qAll('.spanUnitFullPrice')
  for (const spanUnitFullPriceElem of spanUnitFullPriceElems) {
    spanUnitFullPriceElem.innerHTML =
      typeof product.productPrices.UnitFullRetailPrice !== 'undefined'
        ? product.productPrices.UnitFullRetailPrice.FormattedValue
        : product.productPrices.FullRetailPrice.FormattedValue
  }

  if (window.upsell__IsUpgrade) {
    let regularprice = orderInfo.orderTotal
    if (window.upgrade_upsell_product) {
      regularprice = window.upsellPrice
    }
    const spanRegularPriceElms = _qAll('.spanRegularPrice')
    for (const spanRegularPrice of spanRegularPriceElms) {
      spanRegularPrice.innerHTML = window.ctrwowUtils.number.formaterNumberByFormattedValue(
        regularprice,
        product.productPrices.UnitDiscountRate.FormattedValue
      )
    }
  }
  window.ctrwowUtils.events.emit('upsellPopuplateDataFinish')
}

export function impletementNumberXOrderQuantity() {
  const { ctrwowUtils: utils } = window

  const getQuantity = (xorderString) => {
    const num = Number(xorderString.replace(/[^0-9]/g, ''))
    let qty = utils.localStorage().get('orderInfo') ? JSON.parse(utils.localStorage().get('orderInfo')).quantity : 1

    if (utils.localStorage().get('realDoubleQuantity')) {
      qty /= 2
    }
    return qty * num
  }

  populateDataToNodeContent((textContent) => textContent.replace(/{orderunitx\d+}/g, getQuantity))
  // if (canReplaceContentWithPlaceholder(elem)) {
  //   elem.innerHTML = elem.innerHTML.replace(/{orderunitx\d+}/g, getQuantity)
  // }
}

// function appendLoadingImage() {
//   const imgLoading = `<span class="js-img-loading">
//                 <img src="//d16hdrba6dusey.cloudfront.net/sitecommon/images/loading-price-v1.gif" width="20" height="10" class="no-lazy"  style="width: 20px;">
//             </span>`

//   Array.prototype.slice
//     .call(window._qAll('.discountedPrice, .fullPrice, .spanUnitDiscountRate, .unit-price, .spanUnitUpsellPrice, .spanUpsellPrice'))
//     .forEach((taxElem) => {
//       taxElem.innerHTML = imgLoading
//     })
// }

function implementTax(selectedProdduct) {
  const shippingFeeFormatted = selectedProdduct.shippings[0].formattedPrice
  const totalPrice = selectedProdduct.productPrices.DiscountedPrice.Value
  const taxAmount = totalPrice * window.taxPercent

  Array.prototype.slice.call(window._qAll('.tax-amount')).forEach((elm) => {
    elm.textContent = window.ctrwowUtils.number.formaterNumberByFormattedValue(taxAmount, shippingFeeFormatted)
  })

  detectToggleCreditCardForm(window.__productListData.data.productList)

  // Array.prototype.slice.call(window._qAll('.spanUpsellPrice')).forEach((grandTotalElem) => {
  //   grandTotalElem.textContent = window.ctrwowUtils.number.formaterNumberByFormattedValue(totalPrice, shippingFeeFormatted)
  // })

  // Array.prototype.slice.call(window._qAll('.unit-price, .spanUnitUpsellPrice, .spanUnitPrice')).forEach((elm) => {
  //   const qty = selectedProdduct.quantity
  //   const unitPrice = totalPrice / qty
  //   elm.textContent = window.ctrwowUtils.number.formaterNumberByFormattedValue(unitPrice, shippingFeeFormatted)
  // })

  // const array__SelectNumberInput = _qAll('.productQuantityControlWrapper select[name=numberInput]')
  // const orderInfo = JSON.parse(window.localStorage.getItem('orderInfo'))
  // if (array__SelectNumberInput.length > 0) {
  //   const arrProductList = window.ctrwowUpsell.productListData.getProductList().prices

  //   Array.prototype.slice.call(array__SelectNumberInput).forEach((item) => {
  //     const spanUnitPriceWithQtyElems = item.querySelectorAll('.spanUnitPriceWithQty')

  //     if (arrProductList.length > 0) {
  //       for (var i = 0; i < spanUnitPriceWithQtyElems.length; i++) {
  //         try {
  //           const unitPrice = arrProductList[i].productPrices.UnitDiscountRate.Value
  //           spanUnitPriceWithQtyElems[i].innerHTML = window.ctrwowUtils.number.formaterNumberByFormattedValue(unitPrice, shippingFeeFormatted)

  //           // ? Get upgrade price: CSB-1141
  //           if (window.objpid) {
  //             const unitPriceUp = (arrProductList[i].productPrices.DiscountedPrice.Value - orderInfo.orderTotal) / arrProductList[i].quantity

  //             spanUnitPriceWithQtyElems[i].innerHTML = window.ctrwowUtils.number.formaterNumberByFormattedValue(unitPriceUp, shippingFeeFormatted)
  //           }
  //         } catch (e) {
  //           console.log(e)
  //         }
  //       }
  //     } else {
  //       console.log('cannot get product list')
  //     }
  //   })
  // }

  // Render price for Product list
  // ! Chua co trang nao de ap dung thoi !
  /*
  const productItems = Array.prototype.slice.call(window._qAll('.js-list [data-id]'))
  productItems.forEach((item) => {
    const productId = Number(item.getAttribute('data-id'))
    const data = window.__productListData.data.productList.prices.find((item) => item.productId === productId)
    const taxData = window.taxArray.find((tax) => tax.productId === productId)
    const shippingFee = data.shippings[0].price

    const totalDiscountPrice = data.productPrices.DiscountedPrice.Value + shippingFee
    const totalDiscountFormatPrice = window.ctrwowUtils.number.formaterNumberByFormattedValue(totalDiscountPrice, shippingFeeFormatted)

    const totalFullPrice = data.productPrices.FullRetailPrice.Value
    const totalFullFormatPrice = window.ctrwowUtils.number.formaterNumberByFormattedValue(totalFullPrice, shippingFeeFormatted)

    const qty = data.quantity
    const unitPrice = totalDiscountPrice / qty
    const unitFormatPrice = window.ctrwowUtils.number.formaterNumberByFormattedValue(unitPrice, shippingFeeFormatted)

    Array.prototype.slice.call(item.querySelectorAll('.js-discount-price')).forEach((item) => {
      item.textContent = totalDiscountFormatPrice
    })
    Array.prototype.slice.call(item.querySelectorAll('.js-retail-price')).forEach((item) => {
      item.textContent = totalFullFormatPrice
    })
    Array.prototype.slice.call(item.querySelectorAll('.js-unit-price')).forEach((item) => {
      item.textContent = unitFormatPrice
    })
  })
  */
}
function callTaxAjax(postData, selectedProduct, isExistingTax = false) {
  // const emanage = createCrmInstance({
  //   webKey: window.__CTRWOW_CONFIG.webKey,
  //   cid: window.__CTRWOW_CONFIG.cid,
  //   lang: '',
  //   isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
  // })
  console.log('add pageSetting to instance 1')
  const pageSettings = getPageSettings() || {}
  const emanage = createCrmInstance({
    ...pageSettings,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
  })
  // const url = `${emanage.Order.baseAPIEndpoint}/orders/CreateEstimate/${window.__CTRWOW_CONFIG.webKey}`
  // const url = `${emanage.Order.baseAPIEndpoint}/orders/CreateEstimate/${emanage.Order.webkey}`

  emanage.Order.baseAPIEndpoint = window.ctrwowUtils.getSalesSupportCRMBaseUrl(emanage.Order)
  const url = `${emanage.Order.baseAPIEndpoint}/taxes/estimate/${emanage.Order.webkey}`
  const options = {
    method: 'POST',
    headers: {
      // X_CID: window.__CTRWOW_CONFIG.cid,
      X_CID: emanage.Order.cid,
      'content-type': 'application/json'
    },
    body: JSON.stringify(postData)
  }

  // appendLoadingImage()
  if (isExistingTax && window.taxArray.length > 0) {
    implementTax(selectedProduct)
    return
  }

  window.ctrwowUtils
    .callAjax(url, options)
    .then((result) => {
      let items = []
      if (result && result.items && result.items.length > 0) {
        items = result.items
      } else {
        items = postData.items.map((item) => {
          item.taxAmount = 0
          item.taxRate = 0
          return item
        })
      }
      window.ctrwowUtils.events.emit('bindTax')
      window.taxArray = items
      window.taxPercent = window.taxArray[0].taxRate / 100
      implementTax(selectedProduct)
    })
    .catch(() => {
      const items = postData.items.map((item) => {
        item.taxAmount = 0
        item.taxRate = 0
        return item
      })
      window.ctrwowUtils.events.emit('bindTax')
      window.taxArray = items
      window.taxPercent = window.taxArray[0].taxRate / 100
      implementTax(selectedProduct)
    })
}

export function implementAvalaraTax(products, isExistingTax = true) {
  console.log(products)
  const hadTax = window.localStorage.getItem('bindTax')
  if (!hadTax) {
    detectToggleCreditCardForm(window.__productListData.data.productList)
    return
  }

  // ! Call API to get Tax
  const selectedProduct = products[window.upsell_productindex]
  const customerAddress = JSON.parse(window.localStorage.getItem('customerAddress'))
  const postData = {
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest'),
    items: [],
    customerAddress: customerAddress
  }
  postData.items = window.__productListData.data.productList.prices.map((item) => {
    const discountedPrice = item.productPrices.DiscountedPrice.Value

    const quantity = window.isDoubleQuantity ? item.quantity / 2 : item.quantity
    return {
      productId: item.productId,
      sku: item.sku,
      quantity: quantity,
      unitPrice: item.productPrices.UnitDiscountRate ? item.productPrices.UnitDiscountRate.Value : discountedPrice / quantity,
      totalPrice: discountedPrice,
      description: item.productName
    }
  })

  callTaxAjax(postData, selectedProduct, isExistingTax)
}
