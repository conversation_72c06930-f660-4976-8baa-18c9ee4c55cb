export default function addQuantityController(quantityControlType, productListLength, onChange) {
  if (quantityControlType === 'incrementer') {
    addQuantityControllerToIncrementerControl(productListLength, onChange)
  }
  if (quantityControlType === 'dropdown') {
    addQuantityControllerToDropdownControl(onChange)
  }
}

function addQuantityControllerToIncrementerControl(productListLength, onChange) {
  const { _qAll, _q } = window
  const selectorPrefix = '.productQuantityControlWrapper .quantityControl.incrementer'

  const maxQuantityElm = document.querySelector(`${selectorPrefix} [max-value]`)
  let maxQuantity = maxQuantityElm ? parseInt(maxQuantityElm.getAttribute('max-value')) : null
  maxQuantity = maxQuantity && productListLength > maxQuantity ? maxQuantity : productListLength

  if (window._q('.js-upsell-packages .active')) {
    const qty = window._q('.js-upsell-packages .active').getAttribute('upsell-package').split(',').length
    maxQuantity = qty
  }

  const isValidQuantity = (number) => number > 0 && number <= maxQuantity
  const updateQuantityToElm = (value) => {
    Array.prototype.slice.call(_qAll(`${selectorPrefix} [name='numberInput']`)).forEach((input) => (input.value = value))
    Array.prototype.slice.call(_qAll('.upsell-qty')).forEach((upsellQtyElm) => (upsellQtyElm.textContent = value))
  }

  Array.prototype.slice.call(_qAll(`${selectorPrefix} .minus`)).forEach((minus) => {
    minus.addEventListener(
      'click',
      () => {
        const numberVal = (Number(_q(`${selectorPrefix} [name='numberInput']`).value) || 1) - 1

        if (isValidQuantity(numberVal)) {
          updateQuantityToElm(numberVal)
          onChange(numberVal)
        }
      },
      false
    )
  })

  Array.prototype.slice.call(_qAll(`${selectorPrefix} .plus`)).forEach((plus) => {
    plus.addEventListener(
      'click',
      () => {
        const numberVal = (Number(_q(`${selectorPrefix} [name='numberInput']`).value) || 1) + 1

        if (isValidQuantity(numberVal)) {
          updateQuantityToElm(numberVal)
          onChange(numberVal)
        }
      },
      false
    )
  })

  Array.prototype.slice.call(_qAll('.js-upsell-packages .package')).forEach((pack) => {
    pack.addEventListener(
      'click',
      (e) => {
        window._q('.js-upsell-packages .active') && window._q('.js-upsell-packages .active').classList.remove('active')
        e.currentTarget.classList.add('active')
        maxQuantity = e.currentTarget.getAttribute('upsell-package').split(',').length

        let numberVal = Number(_q(`${selectorPrefix} [name='numberInput']`).value) || 1
        if (numberVal > maxQuantity) {
          numberVal = maxQuantity
          _q(`${selectorPrefix} [name='numberInput']`).value = maxQuantity
        }

        if (isValidQuantity(numberVal)) {
          updateQuantityToElm(numberVal)
          onChange(numberVal)
        }
      },
      false
    )
  })
}

function addQuantityControllerToDropdownControl(onChange) {
  const selectorPrefix = '.productQuantityControlWrapper .quantityControl.dropdown'
  const selectors = Array.prototype.slice.call(_qAll(`${selectorPrefix} select`))
  selectors.forEach((selector) => {
    selector.addEventListener(
      'change',
      () => {
        const numberVal = parseInt(selector.value)

        selectors.forEach((item) => {
          // eslint-disable-next-line eqeqeq
          if (item != selector) {
            item.value = numberVal
          }
        })

        onChange(numberVal)
      },
      false
    )
  })
}

export function getDefaultQuantity(quantityControlType) {
  const input = document.querySelector(`.productQuantityControlWrapper .quantityControl.${quantityControlType} [name='numberInput']`)

  if (input) {
    return Number(input.value)
  }
}
