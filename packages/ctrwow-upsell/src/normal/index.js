import getEmanageCRMJS from 'shared-checkout-flow/src/getEmanageCRMJS'
import { getPageSettingsAsync } from 'shared-checkout-flow/src/getPageSettings'
import addUpsellWarrantyHandler from './upsell-page-handler'

import * as mainProductList from 'shared-checkout-flow/src/orderInfo/mainProductList'
export { mainProductList as productListData }

if (!window.__ctrUpsell) {
  window.__ctrUpsell = {
    isReady: false,
    isInit: false
  }

  window.__ctrUpsell.processing = new Promise((resolve) => {
    // load dependencies
    getEmanageCRMJS()
      .then(() => {
        console.log('loaded [EmanageCRMJS] code')
        window.__ctrUpsell.isReady = true
        resolve(true)
      })
      .catch((e) => {
        console.error('cannot create ctrwow instance')
      })
  })
} // end of check instance

async function intUpsellPage(config) {
  if (window.ctrwowUtils.isBuilderMode() || window.__ctrUpsell.isInit) {
    // do not call api in builder
    return
  }
  window.__ctrUpsell.isInit = true
  console.log('ctrwowUpsell - star to init')
  console.log(config)

  const checkoutConfig = await getPageSettingsAsync()

  const siteSetting = {
    webKey: checkoutConfig.webKey,
    campaignName: checkoutConfig.offerName,
    CID: checkoutConfig.cid,
    declineUrl: checkoutConfig.declineUrl,
    successUrl: checkoutConfig.confirmUrl
    // languageFolder: "en"
    // buttonReferrer: "Go Back"
    // redirectURL: ""
  }

  window.upsellWebKey = siteSetting.webKey

  addUpsellWarrantyHandler(siteSetting, config)
}

export const ready = (elm, quantityControlType) => {
  if (elm) {
    window.ratio = elm.getAttribute('ratio') !== null && parseInt(elm.getAttribute('ratio')) > 0 ? parseInt(elm.getAttribute('ratio')) : 1
  }
  const config = { quantityControlType }
  if (!window.__ctrUpsell.config) {
    window.__ctrUpsell.config = { quantityControlType }
  } else if (JSON.stringify(config) !== JSON.stringify(window.window.__ctrUpsell.config)) {
    console.warn('[ctrwowUpsell] component - quantity config of upsell-button component are different, config for first-component is in-used')
    console.warn(JSON.stringify(window.window.__ctrUpsell.config))
  }

  if (!window.__ctrUpsell.isReady) {
    window.__ctrUpsell.processing.then(() => {
      intUpsellPage(window.__ctrUpsell.config)
    })

    return window.__ctrUpsell.processing
  } else {
    intUpsellPage(window.__ctrUpsell.config)
    return Promise.resolve(true)
  }
}
