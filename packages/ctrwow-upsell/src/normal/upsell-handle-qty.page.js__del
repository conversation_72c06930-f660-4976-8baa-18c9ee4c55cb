import canReplaceContentWithPlaceholder from './../canReplaceContentWithPlaceholder'

const { _qAll, _q, ctrwowUtils: utils } = window

function triggerProductItem(index) {
  const unitElms = _qAll('.unit')
  for (const unitElm of unitElms) {
    unitElm.innerText = index
  }
  Array.prototype.slice.call(_qAll('[class^="unitx"]')).forEach((cls) => {
    const num = Number(cls.className.replace(/[^0-9]/g, ''))
    cls.textContent = index * num
  })
  --index

  if (_q('.js-list-group')) {
    const activePackage = _q('.js-list-group .active').dataset.package.split(',')
    const pid = activePackage[index]

    _q('.productRadioListItem input[id="product_' + pid + '"]').click()
  } else {
    _qAll('.productRadioListItem input')[index].click()
  }
}

function impletementNumberXOrderQuantity() {
  const getQuantity = (xorderString) => {
    const num = Number(xorderString.replace(/[^0-9]/g, ''))
    let qty = utils.localStorage().get('orderInfo') ? JSON.parse(utils.localStorage().get('orderInfo')).quantity : 1

    if (utils.localStorage().get('doubleQuantity')) {
      qty /= 2
    }
    return qty * num
  }

  const allElements = _qAll('body *')
  for (const elem of allElements) {
    if (canReplaceContentWithPlaceholder(elem)) {
      elem.innerHTML = elem.innerHTML.replace(/{orderunitx\d.}/g, getQuantity)
    }
  }

  Array.prototype.slice.call(_qAll('[class^="orderunitx"]')).forEach((cls) => {
    //
    // const num = Number(cls.className.replace(/[^0-9]/g, ''))
    // let qty = utils.localStorage().get('orderInfo') ? JSON.parse(utils.localStorage().get('orderInfo')).quantity : 1
    //
    // if (utils.localStorage().get('doubleQuantity')) {
    //   qty /= 2
    // }
    cls.textContent = getQuantity(cls.className)
  })
}

function q(selector) {
  var qSelector = document.querySelectorAll(selector)

  return {
    setValue: function (value) {
      for (const elm of qSelector) {
        elm.value = value
      }
    }
  }
}

function listener() {
  Array.prototype.slice.call(_qAll('.upsell_quantityControl .minus')).forEach((minus) => {
    minus.addEventListener(
      'click',
      () => {
        let numberVal = Number(_q('.numberTxt').value)

        if (numberVal <= 1) {
        } else {
          --numberVal
          q('.numberTxt').setValue(numberVal)
          triggerProductItem(numberVal)
        }
      },
      false
    )
  })

  Array.prototype.slice.call(_qAll('.upsell_quantityControl .plus')).forEach((plus) => {
    plus.addEventListener(
      'click',
      () => {
        let numberVal = Number(_q('.numberTxt').value)
        let max = _qAll('.productRadioListItem').length || 4

        if (_q('.js-list-group')) {
          max = _q('.js-list-group ul').dataset.packagedisplay.split(',').length
        }
        if (numberVal >= max) {
        } else {
          ++numberVal
          q('.numberTxt').setValue(numberVal)
          triggerProductItem(numberVal)
        }
      },
      false
    )
  })
}

function initial() {
  impletementNumberXOrderQuantity()
}

window.addEventListener('DOMContentLoaded', () => {
  initial()
  listener()
})
