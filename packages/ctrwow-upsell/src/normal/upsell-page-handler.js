import CommonUpsell from './../CommonUpsell'
import { replaceBracketsStrings, populateProductInfo, impletementNumberXOrderQuantity } from './populateProductInfo'
import getProduct from './../getProduct'
import updateSelectedProduct from './updateSelectedProduct'
// import convertCurrency from './convertCurrency'
import convertCurrency from 'shared-formatter/src/convertCurrency'
import handleBasicUpsellCTAButton from './handleBasicUpsellButton'
import populateUserInfo from './../populateUserInfo'

export default function (siteSetting, config) {
  if (window.stopCRMAPI) return

  const utils = window.ctrwowUtils
  if (!utils) {
    console.log('modules is not found')
    return
  }

  const upsell = {
    orderInfo: JSON.parse(utils.localStorage().get('orderInfo')),
    products: [],
    upsellCampaignName: '',
    mainWebKey: siteSetting.webKey,
    upsellWebKey: window.upsellWebKey,
    CID: siteSetting.CID
  }

  populateUserInfo()
  replaceBracketsStrings()
  impletementNumberXOrderQuantity()

  getProduct(upsell).then((products) => {
    updateSelectedProduct(products, config)
    populateProductInfo(products.prices[window.upsell_productindex])
    convertCurrency(products.prices[0].productPrices.DiscountedPrice.FormattedValue)
    window.ctrwowUtils.events.emit('afterPopulateProductInfo', products)
  })

  // convertCurrency()

  // try {
  //   utils.checkAffAndFireEvents()
  // } catch (e) {
  //   console.log(e)
  // }

  const insUpsell = new CommonUpsell()
  insUpsell.init()

  // return handleBasicUpsellCTAButton
  if (/complete|interactive|loaded/.test(document.readyState)) {
    // In case the document has finished parsing, document's readyState will
    // be one of "complete", "interactive" or (non-standard) "loaded".
    handleBasicUpsellCTAButton(siteSetting, upsell)
  } else {
    // The document is not ready yet, so wait for the DOMContentLoaded event
    document.addEventListener('DOMContentLoaded', () => handleBasicUpsellCTAButton(siteSetting, upsell), false)
  }
}
