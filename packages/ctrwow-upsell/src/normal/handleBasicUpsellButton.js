import cancelUpsellOrder from './../cancelUpsellOrder'
import placeUpsellOrder from './../placeUpsellOrder'
import getUpsellNameFromUrl from './../getUpsellNameFromUrl'

import { __showAmazonButton, __initAmazonPay, __getUpsellPayLoad } from 'shared-checkout-flow/src/payment/amazonPay.helpers'
import { isVisibleElm } from 'shared-common/src/isVisibleElm'
import { upsellBluesnapApple } from './placeUpsellOrder_AllPaymentMethods'
import { ctrwow_FastLane, loadPayPalSdk } from 'shared-checkout-flow/src/payment/paypalFastlane.helpers'
import { ctrPayPal } from 'shared-checkout-flow/src/payment/paypal.helper'

const __ctrPageConfiguration = window.__ctrPageConfiguration

export default async function handleBasicUpsellCTAButton(siteSetting, upsell) {
  const { _qAll } = window
  const ctaButtons = _qAll('.js-btn-place-upsell-order')
  let paymentType = ''
  if (window.ctrwowUtils.localStorage().get('userPaymentType')) {
    paymentType = window.ctrwowUtils.localStorage().get('userPaymentType').toLocaleLowerCase()
  } else {
    const orderInfo = window.ctrwowUtils.localStorage().get('orderInfo') ? JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo')) : null
    if (orderInfo && orderInfo.useCreditCard) {
      paymentType = 'creditcard'
    } else {
      paymentType = 'paypal'
    }
  }

  const eCRM_PAYMENTS = new window.EmanageCRMJS({
    webkey: siteSetting.webKey,
    cid: siteSetting.CID,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })
  eCRM_PAYMENTS.Order.baseAPIEndpoint = window.ctrwowUtils.getSalesSupportCRMBaseUrl()

  const eCRM = new window.EmanageCRMJS({
    webkey: siteSetting.webKey,
    cid: siteSetting.CID,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })
  const _getUpParam = () => {
    let upParam = ''
    const upsellName = getUpsellNameFromUrl()
    if (upsellName) {
      upParam = 'up_' + upsellName
    }
    return upParam
  }

  function placeUpsellOrderByGoogleOrApple(ctaButtons) {
    window.ctrwowUtils.getDependencies(['https://js.stripe.com/v3/']).then(() => {
      initStripe(ctaButtons)
    })
  }
  function initStripe(ctaButtons) {
    eCRM_PAYMENTS.Order.getMidAndPrn((data) => {
      if (!window.Stripe) {
        console.log('no stripe loaded')
      }

      try {
        // data.midId = eCRM.isTest ? parseInt(data.midId.toString().substring(0, 3)) : data.midId

        data.midId = eCRM.isTest ? 2 : data.midId
        window.midId = data.midId
        const stripe = window.Stripe(data.prnCode.split(';')[0], { stripeAccount: data.prnCode.split(';')[1] })

        // init Stripe instance with default value
        let countryCode = window.ctrwowUtils.localStorage().get('countryCode')
        let currencyCode = window.ctrwowUtils.localStorage().get('currencyCode')

        // override for testing at the unsupported countries
        const isTestGAP = window.ctrwowUtils.link.getQueryParameter('isTestGAP')
        if (isTestGAP && isTestGAP === 'true') {
          countryCode = 'US'
          currencyCode = 'usd'
        }

        // init instance with default value
        const paymentRequest = stripe.paymentRequest({
          country: countryCode,
          currency: currencyCode.toLowerCase(),
          total: {
            label: 'Sample Product',
            amount: 0
          },
          requestPayerName: true,
          requestPayerEmail: true,
          requestPayerPhone: true
        })

        // Check the availability of the Payment Request API first.
        paymentRequest.canMakePayment().then(function (result) {
          if (result) {
            console.log(result)
            Array.prototype.slice.call(ctaButtons).forEach((ele) => {
              ele.addEventListener('click', function (e) {
                e.preventDefault()
                const spanUpsellPriceElm = document.querySelector('.spanUpsellPrice')
                let amount = 0
                if (spanUpsellPriceElm) {
                  amount = Number(spanUpsellPriceElm.innerText.replace(/[^0-9]/g, ''))
                }
                window.taxPercent = window.taxPercent || 0
                amount = Number((amount * (1 + window.taxPercent)).toFixed(0))

                // get product name from className js-name or crm
                const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
                const elm__MainProductName = document.querySelector('.js-name')
                const productName = elm__MainProductName ? elm__MainProductName.innerText : product.productName

                paymentRequest.update({
                  currency: window.ctrwowUtils.localStorage().get('currencyCode').toLowerCase(),
                  total: {
                    label: productName,
                    amount
                  }
                })
                paymentRequest.show()
              })
            })
          } else {
            console.log('not support')
          }
        })
        paymentRequest.on('source', function (event) {
          console.log(event)
          event.complete('success')
          window.ctrwowUtils.localStorage().set('google_apple_upsell_email', event.payerEmail)
          placeUpsellOrder(event, siteSetting, upsell, window.upsell_productindex)
        })
        paymentRequest.on('cancel', function (event) {
          console.log(event)
          document.querySelector('.loading-wrapper').style.display = 'block'
          window.ctrwowUtils.link.redirectPage(__ctrPageConfiguration.confirmUrl + '?' + _getUpParam() + '=0')
        })
      } catch (err) {
        console.log(err)
      }
    }, 54)
  }

  function placeUpsellOrderByBraintree(ctaButtons) {
    window.ctrwowUtils.getDependencies(['https://js.braintreegateway.com/js/braintree-2.32.1.min.js']).then(() => {
      eCRM_PAYMENTS.Order.getMidAndPrn((data) => {
        try {
          data.midId = eCRM.isTest ? parseInt(data.midId.toString().substring(0, 3)) : data.midId
          window.objMidBraintreePaypal = data

          window.token = data.token
          Array.prototype.slice.call(ctaButtons).forEach((ele) => {
            ele.addEventListener('click', function () {
              initBraintree()
              const checkoutUpsell = setInterval(() => {
                if (window.checkout) {
                  clearInterval(checkoutUpsell)
                  window.checkout.paypal.initAuthFlow()
                }
              }, 200)
            })
          })
        } catch (err) {
          console.log('Braintree get token error: ', err)
        }
      }, 52)
    })
  }
  function initBraintree() {
    // const spanUpsellPriceElm = document.querySelector('.spanUpsellPrice')
    // let amount = 0
    // if (spanUpsellPriceElm) {
    //   amount = Number(spanUpsellPriceElm.innerText.replace(/[^0-9]/g, ''))
    // }
    const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
    const amount = product.productPrices.DiscountedPrice.Value

    window.braintree.setup(window.token, 'custom', {
      onReady: function (integration) {
        window.checkout = integration
      },
      onPaymentMethodReceived: function (payload) {
        // receive payload with nonce
        console.log(payload)
        if (payload && payload.nonce) {
          placeUpsellOrder(payload, siteSetting, upsell, window.upsell_productindex)
        } else {
          console.log('Cannot find nonce!')
        }
      },
      paypal: {
        singleUse: true,
        amount,
        currency: window.ctrwowUtils.localStorage().get('currencyCode'),
        enableShippingAddress: 'true',
        headless: true,
        onAuthorizationDismissed: function (err) {
          console.log('Cancelled Braintree Paypal. Msg: ', err)
          window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl + '?' + _getUpParam() + '=0')
        }
      }
    })
  }

  function placeUpsellOrderByBlueSnapGoogle(ctaButtons) {
    window.ctrwowUtils.getDependencies(['https://pay.google.com/gp/p/js/pay.js']).then(() => {
      eCRM_PAYMENTS.Order.getMidAndPrn((data) => {
        try {
          data.midId = eCRM.isTest ? parseInt(data.midId.toString().substring(0, 3)) : data.midId
          window.objMidBlueSnapGoogle = data

          initBlueSnapGoogle(data)
          Array.prototype.slice.call(ctaButtons).forEach((ele) => {
            ele.addEventListener('click', function () {
              window.__paymentWithBlueSnapGoogle()
            })
          })
        } catch (err) {
          console.log('BlueSnap Google Error: ', err)
        }
      }, 58)
    })
  }
  function initBlueSnapGoogle(data) {
    const objMid = data
    // set const value
    const baseRequest = {
      apiVersion: 2,
      apiVersionMinor: 0
    }
    const allowedCardNetworks = ['AMEX', 'MASTERCARD', 'VISA']
    const allowedCardAuthMethods = ['PAN_ONLY', 'CRYPTOGRAM_3DS']
    const tokenizationSpecification = {
      type: 'PAYMENT_GATEWAY',
      parameters: {
        gateway: 'bluesnap',
        gatewayMerchantId: objMid.token.toString()
      }
    }
    const baseCardPaymentMethod = {
      type: 'CARD',
      parameters: {
        allowedAuthMethods: allowedCardAuthMethods,
        allowedCardNetworks: allowedCardNetworks
      }
    }
    const cardPaymentMethod = Object.assign({}, baseCardPaymentMethod, {
      tokenizationSpecification: tokenizationSpecification
    })
    let paymentsClient = null
    const getGoogleIsReadyToPayRequest = () => {
      return Object.assign({}, baseRequest, {
        allowedPaymentMethods: [baseCardPaymentMethod]
      })
    }

    const getGooglePaymentDataRequest = () => {
      const paymentDataRequest = Object.assign({}, baseRequest)
      paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod]
      paymentDataRequest.transactionInfo = getGoogleTransactionInfo()
      paymentDataRequest.merchantInfo = {
        // @todo a merchant ID is available for a production environment after approval by Google
        // See {@link https://developers.google.com/pay/api/web/guides/test-and-deploy/integration-checklist|Integration checklist}
        merchantId: objMid.token.toString(), // '12345678901234567890',
        merchantName: objMid.entity
      }
      paymentDataRequest.shippingAddressParameters = getGoogleShippingAddressParameters()
      paymentDataRequest.emailRequired = true
      return paymentDataRequest
    }

    function getGoogleShippingAddressParameters() {
      return {
        phoneNumberRequired: true
      }
    }

    const getGooglePaymentsClient = () => {
      let environment = 'TEST'
      if (!eCRM.isTest) {
        environment = 'PRODUCTION'
      }
      if (paymentsClient === null) {
        paymentsClient = new window.google.payments.api.PaymentsClient({ environment })
      }
      return paymentsClient
    }

    const onGooglePayLoaded = () => {
      const paymentsClient = getGooglePaymentsClient()
      paymentsClient
        .isReadyToPay(getGoogleIsReadyToPayRequest())
        .then(function (response) {
          if (response.result) {
            // addGooglePayButton()
            // @todo prefetch payment data to improve performance after confirming site functionality
            // prefetchGooglePaymentData();
          }
        })
        .catch(function (err) {
          // show error in developer console for debugging
          console.error(err)
        })
    }
    onGooglePayLoaded()

    const getGoogleTransactionInfo = () => {
      // dynamic data
      const tax = parseFloat(window.taxPercent) || 0
      const arrUpsellProducts = window.ctrwowUpsell && window.ctrwowUpsell.productListData.getProductList().prices
      const upsellProduct = arrUpsellProducts && arrUpsellProducts.length > 0 ? arrUpsellProducts[window.upsell_productindex] : null
      const upsellProductPrice = upsellProduct && upsellProduct.productPrices.DiscountedPrice.Value
      const upsellShippingPrice = upsellProduct && upsellProduct.shippings.length > 0 && upsellProduct.shippings[0].price
      const totalPrice = (upsellProductPrice + upsellShippingPrice) * (1 + tax)
      return {
        countryCode: window.ctrwowUtils.localStorage().get('ctr__countryCode').toUpperCase(),
        currencyCode: window.ctrwowUtils.localStorage().get('currencyCode').toUpperCase(),
        totalPriceStatus: 'FINAL',
        totalPriceLabel: 'Total',
        totalPrice: totalPrice.toString()
      }
    }

    const onGooglePaymentButtonClicked = () => {
      const paymentDataRequest = getGooglePaymentDataRequest()
      paymentDataRequest.transactionInfo = getGoogleTransactionInfo()
      paymentDataRequest.shippingAddressRequired = true // fill shipping address on the popup

      const paymentsClient = getGooglePaymentsClient()
      paymentsClient
        .loadPaymentData(paymentDataRequest)
        .then(function (paymentData) {
          // handle the response
          processPayment(paymentData)
        })
        .catch(function () {
          // console.error(err)
          window.ctrwowUtils.link.redirectPage(__ctrPageConfiguration.confirmUrl + '?' + _getUpParam() + '=0')
        })
    }
    window.__paymentWithBlueSnapGoogle = onGooglePaymentButtonClicked

    const processPayment = (paymentData) => {
      // show returned data in developer console for debugging
      console.log(paymentData)
      const paymentToken = b642utf8(JSON.stringify(paymentData))
      // @todo pass payment token to your gateway to process payment
      console.log(paymentToken)
      placeUpsellOrder(paymentToken, siteSetting, upsell, window.upsell_productindex)
    }

    const b642utf8 = (str) => {
      return btoa(
        str.replace(/[\u00A0-\u2666]/g, function (c) {
          return '&#' + c.charCodeAt(0) + ';'
        })
      )
    }

    // function placeOrder(token) {
    //   const postData = window.orderInfo
    //   postData.payment.walletPaymentToken = token
    //   postData.payment.name = ''
    //   console.log(postData)
    // }
  }

  function placeUpsellOrderByAmazon(ctaButtons) {
    eCRM_PAYMENTS.Order.getMidAndPrn((data) => {
      try {
        data.midId = eCRM.isTest ? parseInt(data.midId.toString().substring(0, 3)) : data.midId
        window.objMidAmazonPay = data
        window.sellerId = window.objMidAmazonPay.merchantId
        window.clientId = window.objMidAmazonPay.clientId

        // append HTML Amazon into upsell page
        var appendAmazonHTML = function appendAmazonHTML() {
          var str__AmazonHTML = `<div class="amazon-wrapper" style="max-width: 300px; margin: 0 auto;">
            <div id="amazonButton"></div>
            <div id="amazonShippingAddress" style="display: none; height: 200px; margin-bottom: 15px;"></div>
            <div id="amazonWallet" style="display: none; height: 200px;"></div>
          </div>`
          var elm__AmazonHTML = document.createElement('div')
          elm__AmazonHTML.innerHTML = str__AmazonHTML
          var elm__AmazonSubmitButton = document.querySelectorAll(`.${elm__AmazonPay.classAmazonSubmitButton}`)

          if (elm__AmazonSubmitButton) {
            var elm__Parent = elm__AmazonSubmitButton[0].parentNode
            elm__Parent && elm__Parent.insertBefore(elm__AmazonHTML, elm__AmazonSubmitButton[0])
          }

          elm__AmazonSubmitButton.length > 0 &&
            Array.prototype.slice.call(elm__AmazonSubmitButton).forEach((item) => {
              item.style.opacity = '0.7'
              item.style.pointerEvents = 'none'
            })
        }

        var elm__AmazonPay = {
          idAmazonButton: 'amazonButton',
          idAmazonShipping: 'amazonShippingAddress',
          idAmazonWallet: 'amazonWallet',
          classAmazonSubmitButton: 'js-btn-place-upsell-order'
        }
        appendAmazonHTML()

        __showAmazonButton({ id__AmazonButton: elm__AmazonPay.idAmazonButton })
        __initAmazonPay({
          elm: document,
          id__AmazonButton: elm__AmazonPay.idAmazonButton,
          id__AmazonShipping: elm__AmazonPay.idAmazonShipping,
          id__AmazonWallet: elm__AmazonPay.idAmazonWallet,
          class__AmazonSubmitButton: elm__AmazonPay.classAmazonSubmitButton
        })

        var addEventListenerToAmazonButton = function addEventListenerToAmazonButton() {
          Array.prototype.slice.call(ctaButtons).forEach(function (ele) {
            ele.addEventListener('click', function (e) {
              e.preventDefault()
              var postData = __getUpsellPayLoad()
              placeUpsellOrder(postData, siteSetting, upsell, window.upsell_productindex)
            })
          })
        }

        window.ctrwowUtils.events.on('emit__AmazonSaveClientBehavior', function () {
          addEventListenerToAmazonButton()
        })
      } catch (err) {
        console.log('Amazon Pay Error: ', err)
      }
    }, 61)
  }

  switch (paymentType) {
    case 'google_apple_pay':
      if (ctaButtons) {
        placeUpsellOrderByGoogleOrApple(ctaButtons)
      }
      break
    case 'braintree':
      if (ctaButtons) {
        placeUpsellOrderByBraintree(ctaButtons)
      }
      break
    case 'bluesnap_google':
      if (ctaButtons) {
        placeUpsellOrderByBlueSnapGoogle(ctaButtons)
      }
      break
    case 'amazon_pay':
      if (ctaButtons) {
        placeUpsellOrderByAmazon(ctaButtons)
      }
      break
    case 'bluesnap_apple':
      if (ctaButtons) {
        upsellBluesnapApple.init({
          ctaButtons,
          siteSetting,
          upsell
        })
      }
      break
    case 'stripe_elements':
      if (ctaButtons) {
        Array.prototype.slice.call(ctaButtons).forEach((ele) => {
          ele.addEventListener('click', function (e) {
            e.preventDefault()
            if (isVisibleElm(window._q('[name="payment"]')) && !$('[name="payment"]').valid()) {
              return
            }
            placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)
          })
        })
      }
      break
    case 'klarna':
      if (ctaButtons) {
        window.ctrwowUtils.getDependencies(['https://js.stripe.com/v3/']).then(() => {
          Array.prototype.slice.call(ctaButtons).forEach((ele) => {
            ele.addEventListener('click', function (e) {
              e.preventDefault()
              if (isVisibleElm(window._q('[name="payment"]')) && !$('[name="payment"]').valid()) {
                return
              }
              placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)
            })
          })
        })
      }
      break
    case 'fastlane':
      if (ctaButtons) {
        const midObj = window.ctrwowUtils.localStorage().get('ctrwow_fastlane')
          ? JSON.parse(window.ctrwowUtils.localStorage().get('ctrwow_fastlane'))
          : null
        if (!midObj) throw new Error('Cannot get mid')
        const { clientId, token: clientToken } = midObj

        // load paypal sdk
        const sdkLoaded = await loadPayPalSdk(clientId, clientToken)
        if (!sdkLoaded) {
          throw new Error('Failed to load Paypal Sdk')
        }

        window.fastlane = await ctrwow_FastLane()
        if (!window.fastlane) {
          throw new Error('Failed to load Fastlane')
        }
        window.fastlane.init()

        Array.prototype.slice.call(ctaButtons).forEach((ele) => {
          ele.addEventListener('click', function (e) {
            e.preventDefault()
            placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)
          })
        })
        // eCRM_PAYMENTS.Order.getMidAndPrn(async (midObj) => {
        //   if (!midObj) throw new Error('Cannot get mid')
        //   const { clientId, token: clientToken } = midObj

        //   // load paypal sdk
        //   const sdkLoaded = await loadPayPalSdk(clientId, clientToken)
        //   if (!sdkLoaded) {
        //     throw new Error('Failed to load Paypal Sdk')
        //   }

        //   window.fastlane = await ctrwow_FastLane()
        //   if (!window.fastlane) {
        //     throw new Error('Failed to load Fastlane')
        //   }
        //   window.fastlane.init()

        //   Array.prototype.slice.call(ctaButtons).forEach((ele) => {
        //     ele.addEventListener('click', function (e) {
        //       e.preventDefault()
        //       placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)
        //     })
        //   })
        // }, 89)
      }
      break
    case 'paypalgooglepay':
      if (ctaButtons) {
        const __placeUpsellOrder = (paymentData) => {
          placeUpsellOrder(paymentData, siteSetting, upsell, window.upsell_productindex)
        }
        eCRM_PAYMENTS.Order.getMidAndPrn(async (midObj) => {
          if (!midObj) throw new Error('Cannot get mid')
          const __paypal = ctrPayPal()
          await __paypal.loadSDK(['GOOGLE'], midObj)
          const { allowedPaymentMethods, merchantInfo } = await window.paypal.Googlepay().config()
          __paypal
            .Google(
              {
                objMid: midObj,
                usePurchasePopup: false,
                allowedPaymentMethods,
                merchantInfo
              },
              __placeUpsellOrder
            )
            .init()

          Array.prototype.slice.call(ctaButtons).forEach((ele) => {
            ele.addEventListener('click', function (e) {
              e.preventDefault()
              window.__onProcessPayPalGooglePay()
            })
          })
        }, 91)
      }
      break
    default:
      if (ctaButtons) {
        Array.prototype.slice.call(ctaButtons).forEach((ele) => {
          ele.addEventListener('click', function (e) {
            e.preventDefault()
            if (isVisibleElm(window._q('[name="payment"]')) && !$('[name="payment"]').valid()) {
              return
            }
            placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)
          })
        })
      }
      break
  }
  // if (paymentType && paymentType === 'google_apple_pay') {
  //   if (ctaButtons) {
  //     placeUpsellOrderByGoogleOrApple(ctaButtons)
  //   }
  // } else {
  //   if (ctaButtons) {
  //     Array.prototype.slice.call(ctaButtons).forEach((ele) => {
  //       ele.addEventListener('click', function (e) {
  //         e.preventDefault()
  //         placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)
  //       })
  //     })
  //   }
  // }

  const notThanks = _qAll('.js-btn-no-thanks')
  notThanks &&
    Array.prototype.slice.call(notThanks).forEach((elm) =>
      elm.addEventListener('click', function (e) {
        e.preventDefault()
        cancelUpsellOrder(siteSetting, upsell)
      })
    )
}
