import createCrmInstance from 'shared-checkout-flow/src/createCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
const PAYMENT_METHOD = {
  PAYPAL: 'paypal',
  CREDIT_CARD: 'creditcard'
}

function getOrderInfo() {
  try {
    return JSON.parse(localStorage.getItem('orderInfo'))
  } catch (e) {
    return {}
  }
}

function getCrmInstance() {
  const pageSettings = getPageSettings() || {}
  console.log('add pageSetting to instance 1')
  const crmInstance = createCrmInstance({
    ...pageSettings,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
  })
  return crmInstance
}

const isPaidByCreditCard = () => window.localStorage.getItem('userPaymentType') === PAYMENT_METHOD.CREDIT_CARD

export function updateUpsellsStatus() {
  if (!isPaidByCreditCard()) return
  const checkoutData = getOrderInfo() || {}
  const eCRM = getCrmInstance()
  const { orderNumber } = checkoutData

  if (checkoutData.upsellUrls && checkoutData.upsellUrls.length > 0) {
    eCRM.Order.baseAPIEndpoint = window.ctrwowUtils.getSalesPciCRMBaseUrl(eCRM.Order)
    eCRM.Order.updateUpsellsStatus(orderNumber, function (result) {
      if (result) {
        console.log('upsells status is updated')
        console.log(JSON.stringify(result || {}))
      }
    })
  }
}
