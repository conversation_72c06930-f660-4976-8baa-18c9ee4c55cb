import { populateProductInfo, implementAvalaraTax } from './populateProductInfo'
import addQuantityController, { getDefaultQuantity } from './addQuantityController'
import { updateUpsellsStatus } from './updateUpsellsStatus'

export function findProductIndexByQuantity(productList = [], selectedQuantity) {
  const ratio = window.ratio !== undefined ? parseInt(window.ratio) : 1
  const index = productList.findIndex(({ quantity }) => quantity === selectedQuantity * ratio)
  return index >= 0 ? index : 0
}

export function findProductIndexByPid(productList = [], pid) {
  const index = productList.findIndex(({ productId }) => productId === pid)
  return index >= 0 ? index : 0
}

export default function updateSelectedProduct({ prices: productList }, { quantityControlType }) {
  if (window.objpid) {
    // ? Get PID upgrade
    const orderInfo = JSON.parse(window.localStorage.getItem('orderInfo'))
    let pid = orderInfo.orderedProducts[0].pid
    const swapProductInfo = localStorage.getItem('swapProductInfo') ? JSON.parse(localStorage.getItem('swapProductInfo')) : null

    // map with case swap product to main in checkout page (creditcard submit button widget, paypal widget and addon product widget)
    if (swapProductInfo && swapProductInfo.mainProCurrent) {
      pid = swapProductInfo.mainProCurrent.productId
    }

    if (orderInfo.upgradePid) {
      pid = orderInfo.upgradePid
    }

    // upgrade product upsell
    let upsellInfoToUpgrade = null
    if (window.upgrade_upsell_product) {
      Object.keys(window.objpid).forEach((key) => {
        if (orderInfo.upsellUrls) {
          if (!upsellInfoToUpgrade) {
            upsellInfoToUpgrade = orderInfo.upsellUrls.find((info) => {
              return info.orderedProducts[0].pid === parseInt(key)
            })
          }
        }
      })
    }

    if (upsellInfoToUpgrade) {
      pid = upsellInfoToUpgrade.orderedProducts[0].pid
      window.orderNumberUpgrade = upsellInfoToUpgrade.orderNumber
      window.upsellPrice = upsellInfoToUpgrade.price
      window.pidRegularUpgrade = pid

      // update to change order status when upgrade
      updateUpsellsStatus()
    }
    // end upgrade product upsell

    const nextUpgradePid = window.objpid[pid]
    // ? Get upsell_productindexby
    window.upsell_productindex = findProductIndexByPid(productList, nextUpgradePid)
    window.upsell_productindex = window.upsell_productindex < 0 ? 0 : window.upsell_productindex
    implementAvalaraTax(productList, false)
    return
  }

  if (quantityControlType === 'none') {
    window.upsell_productindex = 0
    implementAvalaraTax(productList, false)
    return
  }

  const defaultQuantity = getDefaultQuantity(quantityControlType)

  if (!defaultQuantity || defaultQuantity < 1) {
    window.upsell_productindex = 0
    implementAvalaraTax(productList, false)
    return
  }

  // by default - select product which has quantity = 1
  window.upsell_productindex = findProductIndexByQuantity(productList, defaultQuantity)
  window.upsell_productindex = window.upsell_productindex < 0 ? 0 : window.upsell_productindex
  implementAvalaraTax(productList, false)

  const productListLength = productList.length

  addQuantityController(quantityControlType, productListLength, (selectedQuantity) => {
    console.log('upsell element: ')
    window.upsell_productindex = findProductIndexByQuantity(productList, selectedQuantity)

    const activePackageElm = window._q('.js-upsell-packages .active')
    if (activePackageElm) {
      let activePackage = activePackageElm
        .getAttribute('upsell-package')
        .split(',')
        .map((num) => Number(num))
      if (
        window.__ctrPageConfiguration &&
        window.__ctrPageConfiguration.sourceConfig &&
        window.__ctrPageConfiguration.sourceConfig.source === 'SHOPIFY'
      ) {
        activePackage = activePackageElm.getAttribute('upsell-package').split(',')
      }
      const pid = activePackage[selectedQuantity - 1]
      window.upsell_productindex = findProductIndexByPid(productList, pid)
    }

    populateProductInfo(productList[window.upsell_productindex])
    implementAvalaraTax(productList, true)
  })
}
