import { populateDataToNodeContent } from 'shared-checkout-flow/src/canReplaceContentWithPlaceholder'

export function replaceBracketsStrings() {
  populateDataToNodeContent((textContent) =>
    textContent
      .replace(/{unit}/g, '<span class="unit"></span>')
      .replace(/{price}/g, '<span class="spanUpsellPrice"></span>')
      .replace(/{fullprice}/g, '<span class="spanFullPrice"></span>')
      .replace(/{unitprice}/g, '<span class="spanUnitPrice"></span>')
      .replace(/{nameProduct}/g, '<span class="spanProductName"></span>')
  )
  // if (canReplaceContentWithPlaceholder(elem)) {
  //   elem.innerHTML = elem.innerHTML.replace(/{unit}/g, '<span class="unit"></span>')
  //   elem.innerHTML = elem.innerHTML.replace(/{price}/g, '<span class="spanUpsellPrice"></span>')
  //   elem.innerHTML = elem.innerHTML.replace(/{fullprice}/g, '<span class="spanFullPrice"></span>')
  //   elem.innerHTML = elem.innerHTML.replace(/{unitprice}/g, '<span class="spanUnitPrice"></span>')
  // }
}

export function populateProductInfo(product) {
  const { _qAll } = window

  const unitElms = _qAll('.unit')
  for (const unitElm of unitElms) {
    unitElm.innerText = product.quantity
  }

  const spanUpsellPriceElems = _qAll('.spanUpsellPrice')
  for (const spanUpsellPrice of spanUpsellPriceElems) {
    spanUpsellPrice.innerHTML = product.productPrices.DiscountedPrice.FormattedValue
  }

  const spanFullPriceElems = _qAll('.spanFullPrice')
  for (const spanFullPrice of spanFullPriceElems) {
    spanFullPrice.innerHTML = product.productPrices.FullRetailPrice.FormattedValue
  }

  const spanUnitPriceElems = _qAll('.spanUnitPrice')
  for (const spanUnitPrice of spanUnitPriceElems) {
    spanUnitPrice.innerHTML = product.productPrices.UnitDiscountRate.FormattedValue
  }
}
