import cancelUpsellOrder from './../cancelUpsellOrder'
import placeUpsellOrder from './../placeUpsellOrder'

const getCheckboxElm = (elm) => elm.querySelector('.consentMessageSection input[type="checkbox"]')

const canPlaceOrder = (elm) => {
  try {
    return getCheckboxElm(elm).checked
  } catch (e) {
    return false
  }
}

function askToCheckConsentCheckbox(elm) {
  const showModelBtn = elm.querySelector('.consentMessageSection .js-trigger-modal-button')
  showModelBtn && showModelBtn.click()
}

const addCloseModelHandler = (wrapperElm) => {
  const closeModalBtns = wrapperElm.querySelectorAll('.consentMessageSection .ctrwow-modal .js-btn-close')

  closeModalBtns &&
    closeModalBtns.forEach((elm) => elm.addEventListener('click', () => getCheckboxElm(wrapperElm).scrollIntoView({ behavior: 'smooth' })))
}

export default function handleBasicUpsellCTAButton(siteSetting, upsell, { elm, isEnableConsent }) {
  isEnableConsent && addCloseModelHandler(elm)
  const { _qAll } = window

  const placeUpsellOrderCb = () => placeUpsellOrder('', siteSetting, upsell, window.upsell_productindex)

  const ctaButtons = _qAll('.js-btn-place-upsell-order')
  ctaButtons &&
    Array.prototype.slice.call(ctaButtons).forEach((ele) => {
      ele.addEventListener('click', function (e) {
        e.preventDefault()

        if (!isEnableConsent) {
          return placeUpsellOrderCb(e)
        }

        if (canPlaceOrder(elm)) {
          return placeUpsellOrderCb(e)
        }

        askToCheckConsentCheckbox(elm)
      })
    })

  const notThanks = _qAll('.js-btn-no-thanks')

  notThanks &&
    Array.prototype.slice.call(notThanks).forEach((elm) =>
      elm.addEventListener('click', function (e) {
        e.preventDefault()
        cancelUpsellOrder(siteSetting, upsell)
      })
    )
}
