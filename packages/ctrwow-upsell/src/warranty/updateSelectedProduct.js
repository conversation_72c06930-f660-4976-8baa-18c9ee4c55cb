const utils = window.ctrwowUtils

export default function updateSelectedProduct(products) {
  if (utils.localStorage().get('orderInfo')) {
    const orderedData = JSON.parse(utils.localStorage().get('orderInfo'))
    const sku = orderedData.orderedProducts[0].sku

    if (
      !!utils.localStorage().get('quantityOnUI') &&
      utils.localStorage().get('quantityOnUI') !== '' &&
      utils.localStorage().get('quantityOnUI') !== '0'
    ) {
      window.upsell_productindex = parseInt(utils.localStorage().get('quantityOnUI')) - 1
    } else if (utils.localStorage().get('doubleQuantity') === 'true') {
      window.upsell_productindex = Math.round(JSON.parse(utils.localStorage().get('orderInfo')).quantity / 2) - 1
    } else {
      window.upsell_productindex = orderedData.quantity - 1
      if (!!utils.localStorage().get('isActiveFreeGift') && utils.localStorage().get('isActiveFreeGift') === 'true') {
        window.upsell_productindex = window.upsell_productindex - 1
      }
    }

    if (sku.indexOf('_3_1') > -1) {
      for (let i = 0; i < products.prices.length; i++) {
        const item = products.prices[i]
        if (item.sku.indexOf('_3_1') > -1) {
          window.upsell_productindex = i
          break
        }
      }
    }
  }
}
