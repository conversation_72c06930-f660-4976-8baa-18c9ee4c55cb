const utils = window.ctrwowUtils
const { _q, _qAll } = window

const upsellPage = (() => {
  const getQuantity = () => {
    if (utils.localStorage().get('orderInfo')) {
      let quantityOrder = JSON.parse(utils.localStorage().get('orderInfo')).quantity

      if (
        !!utils.localStorage().get('quantityOnUI') &&
        utils.localStorage().get('quantityOnUI') !== '' &&
        utils.localStorage().get('quantityOnUI') !== '0'
      ) {
        quantityOrder = parseInt(utils.localStorage().get('quantityOnUI'))
      } else if (!!utils.localStorage().get('doubleQuantity') && utils.localStorage().get('doubleQuantity') === 'true') {
        quantityOrder = quantityOrder / 2
      }

      if (!!utils.localStorage().get('isActiveFreeGift') && utils.localStorage().get('isActiveFreeGift') === 'true') {
        quantityOrder = quantityOrder - 1
      }

      const allElements = _qAll('body *')
      console.log('run me')
      for (const elem of allElements) {
        elem.innerHTML = elem.innerHTML.replace(/{quantity}/g, quantityOrder)
      }
      if (_q('.main-img-boost')) {
        _q('.main-img-quantity').classList.add('hidden')
      }
    }
  }

  const getSignature = () => {
    try {
      var firstName = localStorage.getItem('user_firstname') == null ? '' : localStorage.getItem('user_firstname')
      var lastName = localStorage.getItem('user_lastname') == null ? '' : localStorage.getItem('user_lastname')
      var htmlInfo = document.querySelector('.upsellWarranty__protection__signbox .signature')
      htmlInfo.innerHTML = htmlInfo.innerHTML.replace('{firstname}', firstName).replace('{lastname}', lastName)
    } catch (e) {}
  }

  const initFloatingBar = () => {
    try {
      if (utils.isDevice()) {
        const btnYes = _q('.btn-upsell-order')
        const offsetTopOfButton = btnYes.offsetTop + 100
        window.addEventListener('scroll', function (e) {
          if (window.pageYOffset > offsetTopOfButton) {
            btnYes.classList.add('fix_bottom')
          } else {
            btnYes.classList.remove('fix_bottom')
          }
        })
      }
    } catch (e) {}
  }

  const initial = () => {
    getQuantity()
    getSignature()
    initFloatingBar()
  }

  return {
    initial: initial
  }
})()

window.addEventListener('load', () => {
  upsellPage.initial()
})
