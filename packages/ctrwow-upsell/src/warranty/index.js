import getEmanageCRMJS from 'shared-checkout-flow/src/getEmanageCRMJS'
import { getPageSettingsAsync } from 'shared-checkout-flow/src/getPageSettings'
import addUpsellWarrantyHandler from './upsell-warranty.page'

if (!window.__ctrUpsellWarranty) {
  window.__ctrUpsellWarranty = {
    isReady: false
  }
  window.__ctrUpsellWarranty.processing = new Promise((resolve) => (window.__ctrUpsellWarranty.resolve = resolve))

  getEmanageCRMJS()
    .then(() => {
      console.log('start to int [EmanageCRMJS] instance')
      window.__ctrUpsellWarranty.resolve(true)
    })
    .catch((e) => {
      console.error('cannot create ctrwow instance')
    })
}

async function intUpsellPage(config) {
  if (window.ctrwowUtils.isBuilderMode()) {
    // do not call api in builder
    return
  }
  const checkoutConfig = await getPageSettingsAsync()

  const siteSetting = {
    webKey: checkoutConfig.webKey,
    campaignName: checkoutConfig.offerName,
    CID: checkoutConfig.cid,
    declineUrl: checkoutConfig.declineUrl,
    successUrl: checkoutConfig.confirmUrl
  }

  window.upsellWebKey = siteSetting.webKey

  addUpsellWarrantyHandler(siteSetting, config)
}

export const ready = (elm, isEnableConsent) => {
  console.log('ctrwowUpsellWarranty - is loaded')
  if (!window.__ctrUpsellWarranty.isReady) {
    window.__ctrUpsellWarranty.processing.then(() => {
      window.__ctrUpsellWarranty.isReady = true
      intUpsellPage({ elm, isEnableConsent })
    })
  }
}
