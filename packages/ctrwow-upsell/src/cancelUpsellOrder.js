import getUpsellNameFromUrl from './getUpsellNameFromUrl'

function handleLastUpsellOrError(siteSetting, upsell) {
  const utils = window.ctrwowUtils
  let upParam = ''
  const upsellName = getUpsellNameFromUrl()
  if (upsellName) {
    upParam = '?up_' + upsellName

    if (upsell.orderInfo.isUpsellOrdered === 1) {
      upParam += '=1'
    } else {
      upParam += '=0'
    }
  }

  const redirectUrl = siteSetting.successUrl
  utils.link.redirectPage(redirectUrl + upParam)
}

export default function cancelUpsellOrder(siteSetting, upsell) {
  const utils = window.ctrwowUtils
  // update localStorage
  upsell.orderInfo.isUpsellOrdered = 0

  const upsellName = getUpsellNameFromUrl()
  let upParam = ''
  if (upsellName) {
    upParam = 'up_' + upsellName + '=0'
  }

  upsell.orderInfo.upsellIndex += window.upsellStepNo || 1
  utils.localStorage().set('orderInfo', JSON.stringify(upsell.orderInfo))

  if (upsell.orderInfo.upsellIndex < upsell.orderInfo.upsells.length) {
    const upsellUrl = upsell.orderInfo.upsells[upsell.orderInfo.upsellIndex].upsellUrl
    // const redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
    let redirectUrl
    const splitLink = upsellUrl.split('?')
    if (splitLink.length <= 1) {
      redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
    } else {
      redirectUrl = splitLink[0].substr(splitLink[0].lastIndexOf('/') + 1)
    }
    utils.link.redirectPage(redirectUrl + '?' + upParam)
  } else {
    handleLastUpsellOrError(siteSetting, upsell)
  }
}
