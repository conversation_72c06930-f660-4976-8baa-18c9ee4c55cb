import trackingPlaceOrder from 'shared-checkout-flow/src/tracking/trackingPlaceOrder'

// Common Upsell classs is used in all sites
export default class CommonUpsell {
  init() {
    try {
      // checkAffAndFireEvents()
      const delayedCheckoutShopify = window.localStorage.getItem('delayedCheckoutShopify')
      if (delayedCheckoutShopify !== 'true') {
        trackingPlaceOrder()
      }
      // this.fireMainOrderToGTMConversion()
      // this.fireGtmPurchaseEvent()
    } catch (e) {
      console.log('[CommonUpsell::init]')
      console.log(e)
    }
  }
}
