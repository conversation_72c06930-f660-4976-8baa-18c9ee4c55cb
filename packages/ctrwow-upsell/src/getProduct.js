import createCrmInstance from 'shared-checkout-flow/src/createCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'

export default function getProduct(upsell) {
  window.ctrwowUtils.showGlobalLoading()
  try {
    window.localStorage.setItem('ctr___debugging__pageConfig', JSON.stringify(window.__ctrPageConfiguration))
  } catch (e) {
    console.log(e)
  }

  // reset currency from setting
  function changeCurrentSymbol(products) {
    const currencyDefind = localStorage.getItem('currencyDefind') ? JSON.parse(localStorage.getItem('currencyDefind')) : null
    if (!currencyDefind) return

    const currency = currencyDefind[products.location.countryCode]
    if (!currency) return
    products.prices.forEach((pro) => {
      pro.productPrices.DiscountedPrice.FormattedValue = pro.productPrices.DiscountedPrice.FormattedValue.replace(
        currency.current,
        `${currency.replace} `
      )
      pro.productPrices.FullRetailPrice.FormattedValue = pro.productPrices.FullRetailPrice.FormattedValue.replace(
        currency.current,
        `${currency.replace} `
      )
      pro.productPrices.UnitDiscountRate.FormattedValue = pro.productPrices.UnitDiscountRate.FormattedValue.replace(
        currency.current,
        `${currency.replace} `
      )
      if (pro.productPrices.UnitFullRetailPrice) {
        pro.productPrices.UnitFullRetailPrice.FormattedValue = pro.productPrices.UnitFullRetailPrice.FormattedValue.replace(
          currency.current,
          `${currency.replace} `
        )
      }

      pro.shippings.forEach((ship) => {
        ship.formattedPrice = ship.formattedPrice.replace(currency.current, `${currency.replace} `)
      })
    })
  }

  return new Promise((resolve) => {
    const pageSettings = getPageSettings() || {}
    // const crmInstance = createCrmInstance({
    //   webKey: upsell.upsellWebKey,
    //   cid: upsell.CID,
    //   lang: '',
    //   isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
    // })
    console.log('add pageSetting to instance 1')
    const crmInstance = createCrmInstance({
      ...pageSettings,
      lang: '',
      isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
    })

    crmInstance.Campaign.getProducts(function (products) {
      changeCurrentSymbol(products)
      // sort shipping data
      products.prices = products.prices.map((product) => {
        product.shippings.sort((a, b) => a.price - b.price)
        return product
      })

      // [ctr-Conversion] tracking - collect info
      window.localStorage.setItem('upsell_currencyCode', products.location.currencyCode)
      window.localStorage.setItem('upsell_ip', products.location.ip)

      upsell.products = products.prices
      upsell.upsellCampaignName = typeof products.campaignName !== 'undefined' ? products.campaignName : ''

      window.ctrwowUtils.hideGlobalLoading()

      resolve(products)
    })
  })
}
