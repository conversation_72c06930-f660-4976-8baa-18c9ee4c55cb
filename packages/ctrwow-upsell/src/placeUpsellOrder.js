// import getUpsellData from 'packages/ctrwow-checkout-flow/src/upsell/getUpsellData'
// import saveInfoToLocalForUpsells from 'packages/ctrwow-checkout-flow/src/upsell/saveInfoToLocalForUpsells'
import getUpsellNameFromUrl from './getUpsellNameFromUrl'
import addAddedInfoToOrderPayload from 'shared-checkout-flow/src/orderInfo/addAddedInfoToOrderPayload'
import createCrmInstance from 'shared-checkout-flow/src/createCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
// import { addGiftCardIntoUpsellData } from 'shared-checkout-flow/src/upsell/addGiftCardIntoUpsellData'
import { detectAddCreditCardIntoUpsellData } from 'shared-checkout-flow/src/upsell/addGiftCardIntoUpsellData'
import { appendCtaClickIdTrackingParam, updateCurrentBLTrackingParam } from 'shared-trackings'
import { confirmCoinOrder, handlePopupWithUrl } from 'shared-checkout-flow/src/payment/coin.helpers'
import { getPaymentName } from 'shared-checkout-flow/src/payment/paymentType.constants'
import { afterCheckoutWithStripe } from 'shared-checkout-flow/src/payment/klarna.helper'
import { ctrwow__Stripe3Ds, loading3Ds } from 'shared-checkout-flow/src/payment/stripe.helpers'
const utils = window.ctrwowUtils

const orderInfo = JSON.parse(localStorage.getItem('orderInfo'))
if (orderInfo) {
  console.log(`used field useCreditCard ${orderInfo.useCreditCard}`)
}

const checkPaymentProcess = (paymentProcessorId) => {
  if (paymentProcessorId === 5 || paymentProcessorId === 31) {
    return true
  }
  return false
}

export function getUpsellData(upsell, upsell_productindex) {
  let pay = {
    cardId: upsell.orderInfo.cardId
  }

  var isPaymentPaypal = (upsell.orderInfo.paymentProcessorId && checkPaymentProcess(parseInt(upsell.orderInfo.paymentProcessorId))) || false
  window.localStorage.getItem('userPaymentProcessorId') &&
    (upsell.orderInfo.paymentProcessorId = Number(window.localStorage.getItem('userPaymentProcessorId')))

  // if (window.localStorage.getItem('userPaymentType') === 'google_apple_pay') {
  //   upsell.orderInfo.paymentProcessorId = 54
  // }
  if (!upsell.orderInfo.useCreditCard && upsell.orderInfo.paymentProcessorId) {
    pay = {
      paymentProcessorId: Number(upsell.orderInfo.paymentProcessorId)
    }
  } else {
    // add installment
    if (!!upsell.orderInfo.installmentValue && upsell.orderInfo.installmentValue !== '') {
      pay.Instalments = upsell.orderInfo.installmentValue
    }
  }

  // Ver: 1.2.0
  // let pay = {
  //   cardId: upsell.orderInfo.cardId
  // }

  // var isPaymentPaypal = (upsell.orderInfo.paymentProcessorId && checkPaymentProcess(parseInt(upsell.orderInfo.paymentProcessorId))) || false
  // if (isPaymentPaypal) {
  //   pay = {
  //     paymentProcessorId: Number(upsell.orderInfo.paymentProcessorId)
  //   }
  // } else {
  //   // add installment
  //   if (!!upsell.orderInfo.installmentValue && upsell.orderInfo.installmentValue !== '') {
  //     pay.Instalments = upsell.orderInfo.installmentValue
  //   }
  // }

  // add callback param to server to keep track
  let replacedParam = window.ctrwowUtils.link
    .getCustomPathName()
    .replace(/\?|\&*paymentId=[^&]*/g, '')
    .replace(/\?|\&*token=[^&]*/g, '')
    .replace(/\?|\&*(transactionCode|errorCode|cancel)=[^&]*/g, '') // remove param of ale pay
    .replace(
      // eslint-disable-next-line no-useless-escape
      /\?|\&*PayerID=[^&]*/g,
      ''
    )
  replacedParam = replacedParam !== '' ? '?' + replacedParam + '&' + getUpParam() : '?' + getUpParam()
  pay.callBackParam = updateCurrentBLTrackingParam(replacedParam)

  // Use for shopify
  if (sessionStorage.getItem('cvvdata')) {
    pay.cvv = sessionStorage.getItem('cvvdata')
  }

  let antiFraud
  try {
    antiFraud = JSON.parse(utils.localStorage().get('antiFraud'))
  } catch (ex) {
    console.log(ex)
    antiFraud = null
  }

  let radarSession
  try {
    radarSession = JSON.parse(utils.localStorage().get('stripeRadar')).radarSession
  } catch (ex) {
    console.log(ex)
    radarSession = null
  }

  // case using multi shipping for upsell
  const chargeShippingFeeOnUpsell =
    window.ctrwowUtils.localStorage().get('chargeShippingFeeOnUpsell') &&
    JSON.parse(window.ctrwowUtils.localStorage().get('chargeShippingFeeOnUpsell'))

  let shippingMethodId =
    upsell.products[upsell_productindex].shippings.length > 0 ? upsell.products[upsell_productindex].shippings[0].shippingMethodId : null

  if (chargeShippingFeeOnUpsell !== null) {
    // sort shipping price
    upsell.products[upsell_productindex].shippings.sort((a, b) => a.price - b.price)
    // get shippingMethodId with shippingIndex
    if (
      chargeShippingFeeOnUpsell.shippingIndex !== undefined &&
      upsell.products[upsell_productindex].shippings.length > Number(chargeShippingFeeOnUpsell.shippingIndex)
    ) {
      // case chargeShippingFeeOnUpsell: true in checkout page
      shippingMethodId = upsell.products[upsell_productindex].shippings[chargeShippingFeeOnUpsell.shippingIndex].shippingMethodId
    } else if (chargeShippingFeeOnUpsell.shippingName !== undefined) {
      // case chargeShippingFeeOnUpsell: false in checkout page
      const shippingName = chargeShippingFeeOnUpsell.shippingName + ' Up'
      const shippingWithName = upsell.products[upsell_productindex].shippings.filter(
        (shipping) => shipping.shippingName.toLowerCase() === shippingName.toLowerCase()
      )
      if (shippingWithName.length > 0) {
        shippingMethodId = shippingWithName[0].shippingMethodId
      }
    }
  }

  const shopifySourceConfig = window.__ctrPageConfiguration.sourceConfig ? window.__ctrPageConfiguration.sourceConfig.source : null
  let successUrl
  let declineUrl
  let customerInfo = { email: !isPaymentPaypal ? upsell.orderInfo.cusEmail : null }
  if (shopifySourceConfig === 'SHOPIFY') {
    successUrl = window.__ctrPageConfiguration.successUrl
    declineUrl = window.__ctrPageConfiguration.declineUrl

    if (upsell.orderInfo.cusEmail === null || upsell.orderInfo.cusEmail === '') {
      customerInfo = null
    }
  }

  const upsellData = {
    campaignUpsell: {
      webKey: upsell.mainWebKey,
      relatedOrderNumber: upsell.orderInfo.orderNumber
    },
    shippingMethodId,
    comment: '',
    useShippingAddressForBilling: true,
    productId: window.upsellProductList ? window.upsellProductList[upsell_productindex].productId : upsell.products[upsell_productindex].productId,
    // customer: { email: !isPaymentPaypal ? upsell.orderInfo.cusEmail : null },
    customer: customerInfo,
    payment: pay,
    // shippingAddress: upsell.orderInfo.addressId != null ? { id: upsell.orderInfo.addressId } : null,
    shippingAddress: upsell.orderInfo.addressId !== null && upsell.orderInfo.addressId !== '' ? { id: upsell.orderInfo.addressId } : null,
    funnelBoxId: 0,
    antiFraud: {
      sessionId: antiFraud ? antiFraud.sessionId : '',
      StripeRadarSessionId: radarSession ? radarSession.id : null
    },
    successUrl,
    declineUrl
  }

  // midId: this field using for Google Apple Pay => widget-google-apple-checkout
  if (pay.paymentProcessorId === 54) {
    upsellData.mid = {
      midId: window.midId.toString()
    }
  }

  if (window.maroPostId) {
    upsellData.additionalInfo = [
      {
        key: 'MaropostSettingsId',
        value: window.maroPostId
      }
    ]
  }

  // update shipping info for case delayshopify = FALSE
  const delayedCheckoutShopify = localStorage.getItem('delayedCheckoutShopify')
  const userPaymentType = localStorage.getItem('userPaymentType')
  if (shopifySourceConfig === 'SHOPIFY' && !upsellData.shippingAddress && delayedCheckoutShopify !== 'true' && userPaymentType === 'creditcard') {
    upsellData.shippingAddress = {
      address1: upsell.orderInfo.address1 || '',
      address2: upsell.orderInfo.address2 || '',
      city: upsell.orderInfo.cusCity,
      countryCode: upsell.orderInfo.cusCountry,
      firstName: upsell.orderInfo.cusFirstName,
      lastName: upsell.orderInfo.cusLastName,
      phoneNumber: upsell.orderInfo.cusPhone,
      state: upsell.orderInfo.cusState,
      zipCode: upsell.orderInfo.cusZip
    }
  }

  // submit AdditionalInfo for upsell
  const formAdditionalInfo = document.querySelector('form[name="AdditionalInfo"]')
  if (formAdditionalInfo) {
    upsellData.AdditionalInfo = []
    const inputs = formAdditionalInfo.querySelectorAll('input, select')

    for (const item of inputs) {
      upsellData.AdditionalInfo.push({
        Key: item.getAttribute('name'),
        Value: item.value
      })
    }
  }

  return upsellData

  function getUpParam() {
    const upsellName = getUpsellNameFromUrl()
    let upParam = ''

    if (upsellName) {
      upParam = 'up_' + upsellName + '=1'
    }
    return upParam
  }
}

// save informations to local storage for upsells
export function saveInfoToLocalForUpsells(siteSetting, responseData, upsell, upsell_productindex) {
  const { ctrwowUtils: utils } = window

  const _getUpParam = () => {
    let upParam = ''
    const upsellName = getUpsellNameFromUrl()
    if (upsellName) {
      upParam = 'up_' + upsellName
    }
    return upParam
  }

  const _handleLastUpsellOrError = () => {
    let upParam = ''
    const upsellName = getUpsellNameFromUrl()
    if (upsellName) {
      upParam = '?up_' + upsellName

      if (upsell.orderInfo.isUpsellOrdered === 1) {
        upParam += '=1'
      } else {
        upParam += '=0'
      }
    }

    let redirectUrl = siteSetting.successUrl + upParam
    redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
    utils.link.redirectPage(redirectUrl)
  }

  if (responseData != null && responseData.success) {
    // store param in localStorage to fire gtm event of purchase
    utils.localStorage().set('fireUpsellForGTMPurchase', _getUpParam() || upsell.upsellCampaignName)
    utils.localStorage().set('paypal_isMainOrder', 'upsell')
    utils.localStorage().set('isMainOrder', 'upsell')
    utils.localStorage().set('upsellOrderNumber', responseData.orderNumber)

    // success page will use this trackingNumber to call comfirm payment api
    if (responseData.trackingNumber) {
      utils.localStorage().set('trackingNumber', responseData.trackingNumber)
    }
    // upsell.orderInfo.upsellCustomerId = responseData.customerResult.customerId
    upsell.orderInfo.upsellIndex += window.upsellStep || 1
    const selectedProduct = upsell.products[upsell_productindex]
    const savedOfUpsell = selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value

    upsell.orderInfo.upsellPriceToUpgrade = selectedProduct.productPrices.DiscountedPrice.Value

    upsell.orderInfo.savedTotal += savedOfUpsell
    upsell.orderInfo.isUpsellOrdered = 1
    const { upsellUrls = [] } = upsell.orderInfo
    upsellUrls.push({
      index: upsell.orderInfo.upsellIndex,
      price: selectedProduct.productPrices.DiscountedPrice.Value,
      campaignWebKey: upsell.upsellWebKey,
      campaignName: upsell.upsellCampaignName,
      orderNumber: responseData.orderNumber,
      customerId: responseData.customerResult ? responseData.customerResult.customerId : '', // Format customer data for Shopify
      url: location.href,
      orderedProducts: [
        {
          sku: selectedProduct.sku,
          pid: selectedProduct.productId,
          name: selectedProduct.productName,
          quantity: selectedProduct.quantity || ''
        }
      ]
    })

    // support activetion code to submit register app
    if (window.isActivationCode) {
      const { activationCode = [] } = upsell.orderInfo
      activationCode.push({
        type: 'upsell',
        orderNumber: responseData.orderNumber
      })

      upsell.orderInfo.activationCode = activationCode
    }

    upsell.orderInfo.upsellUrls = upsellUrls
    upsell.orderInfo.cardId = responseData.cardId
    upsell.orderInfo.useCreditCard = responseData.useCreditCard || false
    utils.localStorage().set('orderInfo', JSON.stringify(upsell.orderInfo))
    utils.localStorage().set('webkey_to_check_paypal', upsell.upsellWebKey)

    // if (responseData.callBackUrl) {
    //   document.location = responseData.callBackUrl
    // } else if (
    //   responseData.paymentContinueResult &&
    //   responseData.paymentContinueResult.actionUrl !== null &&
    //   responseData.paymentContinueResult.actionUrl !== ''
    // ) {
    //   document.location = responseData.paymentContinueResult.actionUrl
    // } else if (upsell.orderInfo.upsellIndex < upsell.orderInfo.upsells.length) {
    //   const upsellUrl = upsell.orderInfo.upsells[upsell.orderInfo.upsellIndex].upsellUrl
    //   let redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
    //   redirectUrl = redirectUrl + '?' + _getUpParam() + '=1'
    //   redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
    //   utils.link.redirectPage(redirectUrl)
    // } else {
    //   _handleLastUpsellOrError()
    // }

    // const paymentKlarnaMid = window.ctrwowUtils.localStorage().get('klarnaMid')
    const userPaymentProcessorId = window.ctrwowUtils.localStorage().get('userPaymentProcessorId')

    if (userPaymentProcessorId === '71' && responseData.paymentContinueResult !== null && responseData.paymentContinueResult.items !== null) {
      /**
       * redirect to klarna type on checkout page
       */

      // const paymentKlarnaType = responseData.paymentContinueResult.items.filter((item) => item.key === paymentKlarnaTypeName)
      // paymentKlarnaType.length > 0 && (document.location = paymentKlarnaType[0].value)

      afterCheckoutWithStripe(responseData, null)
    } else if (responseData.callBackUrl) {
      document.location = responseData.callBackUrl
    } else if (
      responseData.paymentContinueResult &&
      responseData.paymentContinueResult.actionUrl !== null &&
      responseData.paymentContinueResult.actionUrl !== ''
    ) {
      document.location = responseData.paymentContinueResult.actionUrl
    } else if (upsell.orderInfo.upsellIndex < upsell.orderInfo.upsells.length) {
      const upsellUrl = upsell.orderInfo.upsells[upsell.orderInfo.upsellIndex].upsellUrl
      // let redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
      let redirectUrl
      const splitLink = upsellUrl.split('?')
      if (splitLink.length <= 1) {
        redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
      } else {
        redirectUrl = splitLink[0].substr(splitLink[0].lastIndexOf('/') + 1)
      }

      redirectUrl = redirectUrl + '?' + _getUpParam() + '=1'
      redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
      utils.link.redirectPage(redirectUrl)
    } else {
      _handleLastUpsellOrError()
    }
  } else {
    _handleLastUpsellOrError()
  }
}

export default async function placeUpsellOrder(source, siteSetting, upsell, upsell_productindex) {
  // Hoa Comment
  // const eCRM = new window.EmanageCRMJS({
  //   webkey: upsell.mainWebKey,
  //   cid: upsell.CID,
  //   lang: '',
  //   isTest: !!utils.link.getQueryParameter('isCardTest')
  // })
  // const eCRM = createCrmInstance({
  //   webKey: upsell.mainWebKey,
  //   cid: upsell.CID,
  //   lang: '',
  //   isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
  // })
  const pageSettings = getPageSettings() || {}
  console.log('add pageSetting to instance 1')
  const eCRM = createCrmInstance({
    ...pageSettings,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getQueryParameter('isCardTest')
  })

  // set data payload for each payment type
  const paymentType =
    utils.localStorage().get('userPaymentType').toLocaleLowerCase() || getPaymentName(upsell.orderInfo.paymentProcessorId).toLocaleLowerCase()
  const upsellData = getUpsellData(upsell, upsell_productindex)
  // addGiftCardIntoUpsellData(upsellData)
  detectAddCreditCardIntoUpsellData(upsellData)

  // This case is used for product with multi variants in linebundle page
  if (window.Variants !== undefined) {
    upsellData.Variants = window.Variants
  }

  // check and switch product when case more products
  if (window.multiMainProducts && window.multiMainProducts.length > 0) {
    upsellData.productId = '0'
    upsellData.products = window.multiMainProducts
  }

  if (paymentType) {
    switch (paymentType) {
      case 'braintree': {
        upsellData.payment.nonce = source && source.nonce ? source.nonce : null
        upsellData.payment.deviceData = source && source.deviceData ? source.deviceData : null
        upsellData.mid = {
          midId: window.objMidBraintreePaypal.midId
        }
        break
      }
      case 'bluesnap_google': {
        upsellData.payment.walletPaymentToken = source
        upsellData.payment.name = ''
        upsellData.mid = {
          midId: window.objMidBlueSnapGoogle.midId
        }
        break
      }
      case 'sepa': {
        upsellData.payment.iban = window.ctrwowUtils.localStorage().get('ctr__iBanNumber')
        upsellData.payment.name =
          window.ctrwowUtils.localStorage().get('user_firstname') + ' ' + window.ctrwowUtils.localStorage().get('user_lastname')
        break
      }
      case 'amazon_pay': {
        upsellData.payment.region = source.region
        upsellData.payment.merchantId = source.merchantId
        upsellData.payment.providerOrderReferenceId = source.providerOrderReferenceId
        upsellData.mid = {
          midId: window.objMidAmazonPay.midId
        }
        break
      }
      case 'coin': {
        const obj__CryptoInfo = window.ctrwowUtils.localStorage().get('obj__CryptoInfo')
          ? JSON.parse(window.ctrwowUtils.localStorage().get('obj__CryptoInfo'))
          : null
        // upsellData.payment.CryptoWalletAddress = obj__CryptoInfo ? obj__CryptoInfo.CryptoWalletAddress : ''
        upsellData.payment.CryptoCurrency = obj__CryptoInfo ? obj__CryptoInfo.CryptoCurrency : 'BTC'
        break
      }
      case 'paypal': {
        window.ctrwowUtils.events.emit('onBeforePlaceUpsellOrderPaypal')
        const elmLoading = document.querySelector('.paymentProccessing.paymentProccessing_paypal')
        if (elmLoading) {
          elmLoading && elmLoading.classList.remove('js-hidden')
          window.isNotShowLoading = true
        }
        break
      }
      case 'bluesnap_apple': {
        upsellData.payment.paymentFieldToken = source
        break
      }
      case 'klarna': {
        const __shippingAddress = window.ctrwowUtils.localStorage().get('ctr-shippingAddress')
          ? JSON.parse(window.ctrwowUtils.localStorage().get('ctr-shippingAddress'))
          : {}
        upsellData.shippingAddress = {
          ...upsellData.shippingAddress,
          ...__shippingAddress
        }
        break
      }
      case 'fastlane': {
        // const paymentToken = window.ctrwowUtils.localStorage().get('paymentToken')
        const paymentToken = await window.fastlane.getPaymentToken()
        upsellData.payment = {
          ...upsellData.payment,
          ...(paymentToken && { PaymentFieldToken: paymentToken })
        }

        const elmLoading = document.querySelector('.paymentProccessing.paymentProccessing_paypal')
        if (elmLoading) {
          elmLoading && elmLoading.classList.remove('js-hidden')
          window.isNotShowLoading = true
        }
        break
      }
      case 'creditcard': {
        const elmLoading = document.querySelector('.paymentProccessing.paymentProccessing_creditcard ')
        if (elmLoading) {
          elmLoading && elmLoading.classList.remove('js-hidden')
          window.isNotShowLoading = true
        }
        break
      }
      default: {
        const elmLoading = document.querySelector('.paymentProccessing.paymentProccessing_creditcard ')
        if (elmLoading) {
          elmLoading && elmLoading.classList.remove('js-hidden')
          window.isNotShowLoading = true
        }
        break
      }
    }
  }

  // Update webkey for test API
  upsellData.campaignUpsell.webKey = eCRM.Order.webkey

  // Assigne multipleMiniUpsells data if exist - Tu Nguyen
  if (!!window.multipleMiniUpsells && window.multipleMiniUpsells.length > 0) {
    upsellData.multipleMiniUpsells = window.multipleMiniUpsells
  }
  if (!window.isNotShowLoading) {
    utils.showGlobalLoading()
  }
  addAddedInfoToOrderPayload(upsellData)
  window.ctrwowUtils.events.emit('onBeforePlaceUpsellOrder')

  // update new Api for upsell
  eCRM.Order.baseAPIEndpoint = window.ctrwowUtils.getSalesPciCRMBaseUrl(eCRM.Order)
  // eCRM.Order.placeUpsellOrder(upsellData, upsell.upsellWebKey, function (result) {

  window.__loading3Ds = null
  loading3Ds && (window.__loading3Ds = loading3Ds(false))
  eCRM.Order.placeUpsellOrder(upsellData, window.otherCampaign || eCRM.Order.webkey, async function (result) {
    if (paymentType) {
      switch (paymentType) {
        case 'google_apple_pay': {
          let midId
          if (eCRM.isTest) {
            midId = window.midId.toString().substr(0, 3)
          } else {
            midId = window.midId
          }
          // don't need to wait response of confirmation payment to redirect to upsell
          eCRM.Order.confirmGoogleApplePay(result.trackingNumber, source.source.id, midId, (dataResponse) => {
            console.log(dataResponse)
            if (dataResponse && dataResponse.success === true) {
              if (!window.isPauseProcess) {
                saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
              } else {
                window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
                window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                  saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                })
              }
            } else {
              // confirmGoogleApplePay false => redirect to confirm page
              let upParam = ''
              const upsellName = getUpsellNameFromUrl()
              if (upsellName) {
                upParam = '?up_' + upsellName

                if (upsell.orderInfo.isUpsellOrdered === 1) {
                  upParam += '=1'
                } else {
                  upParam += '=0'
                }
              }

              let redirectUrl = siteSetting.successUrl + upParam
              redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
              utils.link.redirectPage(redirectUrl)
            }
          })
          break
        }
        case 'sepa': {
          result.callBackUrl = null
          if (!window.isPauseProcess) {
            saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
          } else {
            window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
            window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
              saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
            })
          }
          break
        }
        case 'coin': {
          // confirm coin order
          confirmCoinOrder(result.trackingNumber).then((dataResponse) => {
            if (dataResponse && dataResponse.success) {
              handlePopupWithUrl(result, () => {
                result.callBackUrl = null
                if (!window.isPauseProcess) {
                  saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                } else {
                  window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
                  window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                    saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                  })
                }
              })
            } else {
              console.log('Confirm Coin Error')
            }
          })
          break
        }
        case 'paypal': {
          if (!window.isPauseProcess__Paypal) {
            saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
          } else {
            window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrderPaypal')
            window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcessPaypal', () => {
              saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
            })
          }
          break
        }
        case 'paypalgooglepay': {
          if (!result?.trackingNumber) {
            window.ctrwowUtils.link.redirectPage(getPageSettings().confirmUrl)
          } else {
            const { status } = await window.paypal.Googlepay().confirmOrder({
              orderId: result.trackingNumber,
              paymentMethodData: source.paymentMethodData
            })

            if (status === 'APPROVED' && result?.trackingNumber) {
              const __url = new window.URL(window.location.href.toLocaleLowerCase())
              const environment = __url.searchParams.get('iscardtest') === '1' ? 'TEST' : 'PRODUCTION'

              const URL = `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/orders/${window.__ctrPageConfiguration.webKey}?trackingNumber=${
                result.trackingNumber
              }${environment === 'TEST' ? '&isTest=true' : ''}`
              window.ctrwowUtils
                .callAjax(URL, {
                  method: 'PUT',
                  headers: {
                    X_CID: window.__ctrPageConfiguration.cid,
                    'Content-Type': 'application/json'
                  }
                })
                .then((rs) => {
                  if (!window.isPauseProcess) {
                    saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                  } else {
                    window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
                    window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                      saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                    })
                  }
                })
                .catch((error) => {
                  console.error('Error updating order status:', error)
                  window.ctrwowUtils.link.redirectPage(getPageSettings().confirmUrl)
                })
            } else {
              window.ctrwowUtils.link.redirectPage(getPageSettings().confirmUrl)
            }
          }
          break
        }
        default: {
          // === start check 3Ds
          let __TIMEOUT = 0
          if (window.ctrwowUtils.localStorage().get('stripe_3Ds') === 'true') {
            window.__loading3Ds.waiting()
            const __orderInfo = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
            // window.timeout4SuccessPopup defined in creditcardSB widget
            __TIMEOUT = window.timeout4SuccessPopup
            const __ctrwow__Stripe3Ds = await ctrwow__Stripe3Ds()
            __ctrwow__Stripe3Ds
              .confirmUpsell({ orderNumber: __orderInfo.orderNumber })
              .then(async (__confirmUpsell) => {
                const __continueResult = __confirmUpsell[0].items.reduce((objResult, item) => {
                  objResult[item.key] = item.value
                  return objResult
                }, {})

                if (__continueResult && __continueResult.requires_action === 'true') {
                  window.__loading3Ds.waiting()
                  // window.timeout4SuccessPopup defined in creditcardSB widget
                  __TIMEOUT = window.timeout4SuccessPopup
                  const { requires_action_publishkey, requires_action_client_secret } = __continueResult
                  __ctrwow__Stripe3Ds
                    .init({
                      publicKey: requires_action_publishkey,
                      clientSecrect: requires_action_client_secret
                    })
                    .then((rsVerify3Ds) => {
                      if (rsVerify3Ds && rsVerify3Ds.success) {
                        __ctrwow__Stripe3Ds
                          .confirmOrder(result)
                          .then((__confirm3Ds) => {
                            if (__confirm3Ds.success) {
                              // window.isUseSuccessAndFailPopup defined in creditcardSB widget
                              window.isUseSuccessAndFailPopup && window.__loading3Ds.success()

                              setTimeout(() => {
                                if (!window.isPauseProcess) {
                                  saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                                } else {
                                  window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
                                  window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                                    saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                                  })
                                }
                              }, __TIMEOUT)
                            } else {
                              // window.isUseSuccessAndFailPopup defined in creditcardSB widget
                              window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
                              // saveInfoToLocalForUpsells => if (result.success ==== false) => error => else => success
                              result.success = false
                              saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                            }
                          })
                          .catch((error) => {
                            // saveInfoToLocalForUpsells => if (result.success ==== false) => error => else => success
                            result.success = error.success
                            saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                          })
                      } else {
                        // window.isUseSuccessAndFailPopup defined in creditcardSB widget
                        window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
                        // saveInfoToLocalForUpsells => if (result.success ==== false) => error => else => success
                        result.success = false
                        saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                      }
                    })
                    .catch((error) => {
                      result.success = error.success || false
                      saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                    })
                } else {
                  console.log('upsell not require 3DS')
                  if (!window.isPauseProcess) {
                    saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                  } else {
                    window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
                    window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                      saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
                    })
                  }
                }
              })
              .catch((error) => {
                // skip flow
                // saveInfoToLocalForUpsells => if (result.success ==== false) => error => else => success
                console.log(error.message)
                // result.success = error.success
                saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
              })
          } else {
            if (!window.isPauseProcess) {
              saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
            } else {
              window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
              window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
              })
            }
          }
          break
        }
      }
    }

    // if (paymentType && paymentType === 'google_apple_pay') {
    //   // confirm payment for only google apple pay
    //   let midId
    //   if (eCRM.isTest) {
    //     midId = window.midId.toString().substr(0, 3)
    //   } else {
    //     midId = window.midId
    //   }
    //   // don't need to wait response of confirmation payment to redirect to upsell
    //   eCRM.Order.confirmGoogleApplePay(result.trackingNumber, source.source.id, midId, (dataResponse) => {
    //     console.log(dataResponse)
    //     if (dataResponse && dataResponse.success === true) {
    //       if (!window.isPauseProcess) {
    //         saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
    //       } else {
    //         window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
    //         window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
    //           saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
    //         })
    //       }
    //     } else {
    //       // confirmGoogleApplePay false => redirect to confirm page
    //       let upParam = ''
    //       const upsellName = getUpsellNameFromUrl()
    //       if (upsellName) {
    //         upParam = '?up_' + upsellName

    //         if (upsell.orderInfo.isUpsellOrdered === 1) {
    //           upParam += '=1'
    //         } else {
    //           upParam += '=0'
    //         }
    //       }

    //       let redirectUrl = siteSetting.successUrl + upParam
    //       redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
    //       utils.link.redirectPage(redirectUrl)
    //     }
    //   })
    //   // setTimeout(() => {
    //   //   if (!window.isPauseProcess) {
    //   //     saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
    //   //   } else {
    //   //     window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
    //   //     window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
    //   //       saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
    //   //     })
    //   //   }
    //   // }, 3000)
    // } else {
    //   if (paymentType && paymentType === 'sepa') {
    //     result.callBackUrl = null
    //   }
    //   if (!window.isPauseProcess) {
    //     saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
    //   } else {
    //     window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
    //     window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
    //       saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
    //     })
    //   }
    // }
  })
}

// export default function placeUpsellOrder(siteSetting, upsell, upsell_productindex) {
//   const upsellData = getUpsellData(upsell, upsell_productindex)
//   const eCRM = new window.EmanageCRMJS({
//     webkey: upsell.mainWebKey,
//     cid: upsell.CID,
//     lang: '',
//     isTest: !!utils.link.getQueryParameter('isCardTest')
//   })

//   utils.showGlobalLoading()

//   addAddedInfoToOrderPayload(upsellData)

//   eCRM.Order.placeUpsellOrder(upsellData, upsell.upsellWebKey, function (result) {
//     saveInfoToLocalForUpsells(siteSetting, result, upsell, upsell_productindex)
//   })
// }
