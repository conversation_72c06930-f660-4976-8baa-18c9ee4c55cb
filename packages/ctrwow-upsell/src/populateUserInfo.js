import { populateDataToNodeContent } from 'shared-checkout-flow/src/canReplaceContentWithPlaceholder'

function populateData() {
  console.log('==================================================runme')
  const firstName = localStorage.getItem('user_firstname') == null ? '' : localStorage.getItem('user_firstname')
  const lastName = localStorage.getItem('user_lastname') == null ? '' : localStorage.getItem('user_lastname')
  // const headingElms = document.querySelectorAll('.headingContent')
  if (!firstName && !lastName) {
    return
  }

  populateDataToNodeContent((textContent) => textContent.replace('{firstname}', firstName).replace('{lastname}', lastName))
  // if (canReplaceContentWithPlaceholder(elem)) {
  //   elem.innerHTML = elem.innerHTML.replace('{firstname}', firstName)
  //   elem.innerHTML = elem.innerHTML.replace('{lastname}', lastName)
  // }
}

export default function populateUserInfo() {
  // return handleBasicUpsellCTAButton
  if (/complete|interactive|loaded/.test(document.readyState)) {
    // In case the document has finished parsing, document's readyState will
    // be one of "complete", "interactive" or (non-standard) "loaded".
    populateData()
  } else {
    // The document is not ready yet, so wait for the DOMContentLoaded event
    document.addEventListener('DOMContentLoaded', populateData, false)
  }
}
