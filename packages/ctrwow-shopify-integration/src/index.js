/* eslint-disable prefer-const */
import { fromEvent, Subject } from 'rxjs'
import * as ALL_ACTIONS from './actionTypes.constants'
import { filter, map, takeUntil, takeWhile } from 'rxjs/operators'
import { getArrayBufferFromObj, getObjDataFromArrayBuffer } from './decodeTransferData'

/** ************ INTERNAL - STATE **************/
let internalConfig = {
  common_targetOrigin: undefined,
  notiChannel: undefined
}

export const setCommonTargetOrigin = (val) => {
  internalConfig.common_targetOrigin = val
}

/** ************ GENERAL HELPERS **************/
const isValidType = (type) => type && Object.values(ALL_ACTIONS).indexOf(type) > -1

const postMessage = (triggerSourceWindow, action, origin) => {
  if (!action || !action.type || !isValidType(action.type) || !triggerSourceWindow || !triggerSourceWindow.postMessage) {
    console.log('[dispatchAction] - invalid type', action)
    return
  }
  if (action && !action.__actionId) {
    action.__actionId = `${action.type}__${Date.now()}`
  }
  triggerSourceWindow.postMessage(getArrayBufferFromObj(action), origin || internalConfig.common_targetOrigin)
  return action
}

const relyAction = (__sourceActionId, event) => (action) => {
  event && postMessage(event && event.source, { ...action, __sourceActionId }, event.origin)
}

/** ************ SET UP - COMMUNICATION CHANNEL **************/
const end$ = new Subject()
const messages$ = fromEvent(window, 'message').pipe(
  takeUntil(end$),
  filter(({ origin, data }) => {
    // TODO - bich - need to filter by origin
    // console.log(origin, data)
    return true
  }),
  map((event) => ({ action: getObjDataFromArrayBuffer(event.data), event })),
  filter(({ action }) => action && action.type && isValidType(action.type)),
  map(({ action, event }) => ({
    action,
    event,
    ...(action && action.__actionId && { relyAction: relyAction(action.__actionId, event) })
  }))
)

window.addEventListener('beforeunload', function () {
  try {
    console.log('%c [window beforeunload]start to unsubscribe! - location: ' + (location && location.origin), 'background: #222; color: #bada55; font-size:20px')
    end$.next('CancelMe')
  } catch (e) {
    console.log(e)
  }
})

/** ************ HELPERS ON STREAM **************/
export const subscribeAll = () => messages$

export const subscribeAction = (actionType, takeNumber) => subscribeAll().pipe(
  filter(({ action: { type } }) => type === actionType),
  takeWhile((action, index) =>  takeNumber ? index < takeNumber : true)
)

export const dispatchAction = (action, iframeTarget, origin) => {
  const { __actionId: actionId, type: actionType } = postMessage(iframeTarget && iframeTarget.contentWindow, action, origin) || {}
  if (!actionId) {
    return
  }

  return subscribeAction(actionType).pipe(filter(({ action: { __sourceActionId } }) => __sourceActionId === actionId))
}

/** ************ NOTI CHANNEL **************/
export const setToastMessageChannel = ({ dispatchReply, ...toastConfig }) => {
  if (typeof dispatchReply !== 'function') {
    return
  }

  internalConfig.notiChannel = {
    ...toastConfig,
    sendMessage: (toastMessage) =>
      dispatchReply({
        type: ACTIONS.TOAST_MESSAGE,
        payload: { toastMessage }
      })
  }
}
export const getToastMessageChannel = () => {
  return internalConfig.notiChannel
}

export const ACTIONS = ALL_ACTIONS
