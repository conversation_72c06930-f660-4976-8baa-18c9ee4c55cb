import LZString from 'lz-string'
import {convertFromObjToString, convertFromStringToObj} from 'shared-gjs-utils/src/parseJson'


export const getArrayBufferFromObj = (objData) => {
  // console.log('[decodeTransferData] objData', objData)
  return LZString.compressToEncodedURIComponent(convertFromObjToString(objData))
}

export const getObjDataFromArrayBuffer = (arrBuf) => {
  try {
    // console.log('[decodeTransferData] arrBuf', arrBuf)
    return convertFromStringToObj(LZString.decompressFromEncodedURIComponent(arrBuf))
  } catch (e) {
    return arrBuf
  }
}
