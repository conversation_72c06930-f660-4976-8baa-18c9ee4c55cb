import device from 'current-device'
import { getParameterByName, apiClient } from './utils'
import {
  SPLIT_TEST_PARAMS,
  HEATMAP_DEVICE_TYPE,
  SPLIT_TEST_INFO,
  PREVIEW_PARAMS_SL,
  PREVIEW_PARAMS_HM,
  API_END_POINT_URL
} from './constants'

export function getSplitTestId() {
  return window.__CTR_SPL_TRACKING_SETTINGS ? window.__CTR_SPL_TRACKING_SETTINGS.SPL_ID : getParameterByName(SPLIT_TEST_PARAMS.ID)
}

export function getSplitTestSessionIdParam() {
  return getParameterByName(SPLIT_TEST_PARAMS.S_ID)
}

export function getDeviceType() {
  if (device.desktop()) {
    return HEATMAP_DEVICE_TYPE.desktop
  }
  if (device.mobile()) {
    return HEATMAP_DEVICE_TYPE.mobile
  }
  if (device.tablet()) {
    return HEATMAP_DEVICE_TYPE.tablet
  }
}

function setCookie(name, value, daysToExpire) {
  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + daysToExpire);

  const cookieValue = encodeURIComponent(value) + "; expires=" + expirationDate.toUTCString() + "; path=/";
  document.cookie = name + "=" + cookieValue;
}

function getCookie(name) {
  const cookieName = name + "=";
  const cookieArray = document.cookie.split(';');

  for (let i = 0; i < cookieArray.length; i++) {
    let cookie = cookieArray[i];

    while (cookie.charAt(0) === ' ') {
      cookie = cookie.substring(1);
    }

    if (cookie.indexOf(cookieName) === 0) {
      return decodeURIComponent(cookie.substring(cookieName.length, cookie.length));
    }
  }

  return null;
}


export function getPreviousExecutionData(splitTestId) {
  const splitTestInfo = JSON.parse(getCookie(SPLIT_TEST_INFO));
  return splitTestInfo?.[splitTestId] || null;
}

export function saveExecutionData(splitTestId, data) {
  const splitTestInfo = JSON.parse(getCookie(SPLIT_TEST_INFO)) || {};
  splitTestInfo[splitTestId] = data;
  setCookie(SPLIT_TEST_INFO, JSON.stringify(splitTestInfo), 3);
}

export function isPreviewMode() {
  return getParameterByName(PREVIEW_PARAMS_SL) === '1' || getParameterByName(PREVIEW_PARAMS_HM) === '1'
}

export function updateURLParameter(url, param, paramVal) {
  let newAdditionalURL = ''
  let tempArray = url.split('?')
  const baseURL = tempArray[0]
  const additionalURL = tempArray[1]
  let temp = ''
  if (additionalURL) {
    tempArray = additionalURL.split('&')
    for (let i = 0; i < tempArray.length; i++) {
      // eslint-disable-next-line eqeqeq
      if (tempArray[i].split('=')[0] != param) {
        newAdditionalURL += temp + tempArray[i]
        temp = '&'
      }
    }
  }

  const rows_txt = temp + '' + param + '=' + paramVal
  return baseURL + '?' + newAdditionalURL + rows_txt
}

export function pushErrorLogExecute(errors) {
  if(errors instanceof TypeError) {
    errors = errors.message
  }
  try {
    const apiEndPointUrl = API_END_POINT_URL + '/CreateFunctionHttpTrigger?code=UaJqPA9RGvefKokk5Jt6CXObgyELuRwSLvpiGFZi6FtD//RXEBzcIg==&trackingId=ctrwow_log_split_test_execute'
    const bodyLog = {
      fingerprint: "ctrwow_log_split_test_execute",
      variables: window._EA_VARS || [],
      location: window.location,
      sessionId: "ctrwow_log_split_test_execute",
      isNewSession: false,
      fnName: "SPLIT_EXECUTE_ERROR",
      customData: {
        splitId: getSplitTestId(),
        location: window.location,
        error: errors
      }
    }
    const options = {
      method: 'POST',
      body: JSON.stringify(bodyLog)
    }
    return apiClient(apiEndPointUrl, options)
      .then(function(rp) {
        console.log("send log success.", rp)
      })
      .catch(function(err) {
        console.log("send log error.", err)
      })
  } catch (err) {
    console.warn('CTR_SPLIT_TEST_EXECUTE error: ' + err)
    return false
  }
}