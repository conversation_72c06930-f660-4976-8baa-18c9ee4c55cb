export const SPLIT_TEST_PARAMS = {
  S_ID: 'ctr_spl_sid',
  ID: 'ctr_spl_id',
  TARGET_PAGE_TYPE: 'ctr_spl_tpt',
}
export const HEATMAP_DEVICE_TYPE = Object.freeze({
  mobile: 1,
  tablet: 2,
  desktop: 3
})
export const SPLIT_TEST_INFO = "splitTestInfo"
export const SPLIT_TEST_EXECUTE_FAIL = "splitTestExecuteFail"
export const PREVIEW_PARAMS_SL = "ctr_sl_heatmap_preview"
export const PREVIEW_PARAMS_HM = "ctr_heatmap_preview"
export const API_END_POINT_URL = 'https://ctrwow-prod-fingerprint-microservice.azurewebsites.net/api'