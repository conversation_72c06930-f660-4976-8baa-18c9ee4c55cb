import {
  getSplitTestSessionIdParam,
  getSplitTestId,
  getDeviceType,
  getPreviousExecutionData,
  saveExecutionData,
  isPreviewMode,
  updateURLParameter,
  pushErrorLogExecute
} from './helpers'
import { apiClient, hideBody, existingExecuteFailInList, setArrayCookieSplitTestFail } from './utils'
import { SPLIT_TEST_PARAMS } from './constants'

function buildInitSplitTestResp(data = {}) {
  const { targetUrl, splitTestSessionId, splitTestId, splitTestTargetPageType } = data;
  return {
    redirectSplitTestUrl: targetUrl,
    customData: {
      splitTestSessionId,
      splitTestId,
      splitTestTargetPageType
    }
  }
}

function getSplitTestInfo(splitTestId) {
  const previousExecutionData = getPreviousExecutionData(splitTestId);
  const url = `${process.env.API_ENDPOINT_URL}/splittests/${splitTestId}/execute?ctr_device_type=${getDeviceType()}`
  const body = JSON.stringify({
    controlUrl: `${location.href}`,
    previousExecutionData
  })
  return apiClient(url, {
    method: 'POST',
    body: body
  }).then(rs => rs)
    .catch(errors => {
      throw {
        errorMessage: errors,
        executeUrl: url,
        bodyExecute: body
      }
    });
}


function initSplitTestTrack() {
  const splitTestId = getSplitTestId();
  return getSplitTestInfo(splitTestId)
    .then((splitTestRs = {}) => {
      const { splitTestApplied, data } = splitTestRs
      if (splitTestApplied && data.targetUrl) {
        const { sessionId, splitTestTargetPageType } = data
        let targetUrl = updateURLParameter(data.targetUrl, SPLIT_TEST_PARAMS.S_ID, data.sessionId)
        targetUrl = updateURLParameter(targetUrl, SPLIT_TEST_PARAMS.ID, splitTestId)
        targetUrl = updateURLParameter(targetUrl, SPLIT_TEST_PARAMS.TARGET_PAGE_TYPE, splitTestTargetPageType)

        saveExecutionData(splitTestId, data);
        return buildInitSplitTestResp({
          targetUrl,
          splitTestSessionId: sessionId,
          splitTestId,
          splitTestTargetPageType
        })
      }
      return buildInitSplitTestResp()
    })
}
function handleExecuteSplitTest() {
  try {
    const splitTestId = getSplitTestId();
    const splitTestSessionIdParam = getSplitTestSessionIdParam();
    const previewMode = isPreviewMode();

    if (splitTestId && !splitTestSessionIdParam && !previewMode) {
      if(existingExecuteFailInList(splitTestId)) return;

      hideBody();
      return initSplitTestTrack(splitTestId).then((splitTestDataRp = {}) => {
        if (!window._CTR_CUSTOM_DATA) {
          window._CTR_CUSTOM_DATA = {};
        }
        const { customData, redirectSplitTestUrl } = splitTestDataRp || {}
        if (customData) {
          window._CTR_CUSTOM_DATA = window._CTR_CUSTOM_DATA
            ? {
              ...window._CTR_CUSTOM_DATA,
              ...customData
            }
            : window._CTR_CUSTOM_DATA
        }
        if (redirectSplitTestUrl) {
          window.location.replace(redirectSplitTestUrl);
        }
      }).catch((errors) => {
        pushErrorLogExecute(errors)
        setArrayCookieSplitTestFail(splitTestId)
        location.reload()
      });
    }
  } catch (errors) {
    pushErrorLogExecute(errors);
  }
}

handleExecuteSplitTest();


