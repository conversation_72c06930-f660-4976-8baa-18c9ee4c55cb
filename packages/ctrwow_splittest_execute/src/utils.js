import {SPLIT_TEST_EXECUTE_FAIL} from "./constants"

export function getParameterByName(name, url) {
  if (!url) url = window.location.href
  // eslint-disable-next-line no-useless-escape
  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

export async function apiClient(url, settings) {
  try {
    if (!url) {
      throw new Error('API URL is missing!');
    }

    const headersOptions = {
      'content-type': 'application/json'
    };

    const mergedSettings = {
      method: 'GET',
      headers: { ...headersOptions, ...settings?.headers },
      ...settings
    };

    const response = await window.fetch(url, mergedSettings);

    if (response.status === 204 || response.status === 205) {
      return null;
    }

    if (response.ok && response.status >= 200 && response.status < 300) {
      const responseJson = await response.json();
      return responseJson || {};
    }

    const errorResponse = {
      status: response.status,
      statusText: `BE - ${response.statusText}`
    };

    try {
      const contentType = response.headers.get('content-type');
      const responseContent = contentType && contentType.includes('application/json')
        ? await response.json()
        : await response.text();

      errorResponse.response = responseContent;
    } catch (error) {
      // Failed to parse response as JSON
      errorResponse.response = "FE - Failed to parse response.";
    }

    throw JSON.stringify(errorResponse);
  } catch (errors) {
    let errorsMgs = errors
    if(errors instanceof TypeError) {
      errorsMgs = errors.message
    }
    throw errorsMgs;
  }

}

export const hideBody = () => setInterval(() => {
  try {
    const html = document.getElementsByTagName('html')[0]
    if(html && document.head && document.body) {
      html.removeChild(document.head)
      html.removeChild(document.body)
    }
  } catch (e) {
    console.log(e)
  }
}, 10);


function getArrayCookieSplitTestFail(name) {
  const cookieValue = document.cookie
    .split('; ')
    .find(row => row.startsWith(name + '='));

  if (cookieValue) {
    // Get the serialized array from the cookie and decode it
    const decodedValue = decodeURIComponent(cookieValue.split('=')[1]);

    try {
      // Parse the serialized array and return the JavaScript array
      return JSON.parse(decodedValue) || [];
    } catch (error) {
      // If there's an error parsing the JSON, return an empty array
      console.error('Error parsing array from cookie:', error);
      return [];
    }
  } else {
    // If the cookie doesn't exist, return an empty array
    return [];
  }
}

export function setArrayCookieSplitTestFail(splitTestId) {
  const array = getArrayCookieSplitTestFail(SPLIT_TEST_EXECUTE_FAIL);

  // Convert the array to a string using JSON.stringify()
  const serializedArray = JSON.stringify([...array, splitTestId]);

  // Create the cookie string with the serialized array
  document.cookie = `${SPLIT_TEST_EXECUTE_FAIL}=${encodeURIComponent(serializedArray)}`;
}


export const existingExecuteFailInList = (splitTestId) => {
  const array = getArrayCookieSplitTestFail(SPLIT_TEST_EXECUTE_FAIL);
  return array.indexOf(splitTestId) !== -1
}

