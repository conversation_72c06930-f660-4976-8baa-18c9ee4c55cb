export function loadScript() {
  return $.getScript(process.env.CTR_BLUESHIFT_SCRIPT_SRC)
}

export function trackPageLoad() {
  loadScript().done(() => window._ctrBlueshiftIntegration.trackPageLoad())
}

export function track(eventName, data) {
  loadScript().done(() => window._ctrBlueshiftIntegration.track(eventName, data))
}

export function identify(data) {
  loadScript().done(() => window._ctrBlueshiftIntegration.identify(data))
}
