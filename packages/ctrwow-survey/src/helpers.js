/* eslint-disable */
import CryptoJS from 'crypto-js'
import * as blueshiftTrackingHelpers from './blueshiftTrackingHelpers'

const COMPLETED_CONVERSION_ACT_TYPE = 1

const NETWORK_KAINERO_ID = '245'
const NETWORK_VERVE_ID = '69'
const NETWORK_DFO_BLUE_ID = '952'

const EVENTS_ID = {
  SubmitEmail: 731,
  SubmitPersonalInfo: 732,
  TakeSurvey: 733,
  Lead: 734
}

const NETWORK_EVENT_ID = {
  [NETWORK_KAINERO_ID]: {
    [EVENTS_ID.SubmitEmail]: 777,
    [EVENTS_ID.SubmitPersonalInfo]: 778,
    [EVENTS_ID.TakeSurvey]: 779
  },
  [NETWORK_VERVE_ID]: {
    [EVENTS_ID.SubmitEmail]: 220,
    [EVENTS_ID.SubmitPersonalInfo]: 221,
    [EVENTS_ID.TakeSurvey]: 222
  },
  [NETWORK_DFO_BLUE_ID]: {
    [EVENTS_ID.SubmitEmail]: 132,
    [EVENTS_ID.SubmitPersonalInfo]: 133,
    [EVENTS_ID.TakeSurvey]: 134
  }
}

const NETWORK_REQURIE_IDS = Object.keys(NETWORK_EVENT_ID)


function getAdvId(networkId, eventId) {
  return NETWORK_EVENT_ID[networkId.toString()][eventId]
}

function getSurveyId() {
  return window.localStorage.getItem('ctrWowSurvey__id') || ''
}

function setClientId(clientId) {
  window.localStorage.setItem('dfo_leadgen__clientId', clientId)
}

function getClientId() {
  return window.localStorage.getItem('dfo_leadgen__clientId') || ''
}

function toJson(rp) {
  return rp.json()
}

export const getSurveyUrl = function(fingerprintId) {
  const ctrwowApiUrl = process.env.SURVEY_API_ENDPOINT
  const surveyUrl = ctrwowApiUrl + '/surveys'
  if (fingerprintId) {
    return surveyUrl + '/' + fingerprintId
  }
  return surveyUrl
}

export const initSurvey = function(surveyId, isInit) {
  (isInit === 'true' || isInit === true) && window.localStorage.setItem('ctrWowSurvey__id', surveyId)
}

function handleSetClientId(rp) {
  if (rp && rp.clientId) {
    setClientId(rp.clientId)
  }
  return rp
}

export const getSurveys = function(surveyId) {
  surveyId = surveyId || getSurveyId()
  try {
    // window.ctrwowUtils.showGlobalLoading()
    return window.__CTR_FP_TRACKING
      .getFingerPrintId()
      .then(function(fingerprintId) {
        return window.ctrwowUtils
          .callAjax(getSurveyUrl(fingerprintId) + '/' + surveyId + '/' + getClientId(), {
            method: 'GET'
          })
          .then(handleSetClientId)
      })
      .catch((e) => {
        console.log(e)
        throw e
      })
      .finally(() => window.ctrwowUtils.hideGlobalLoading())
  } catch (e) {
    console.log(e)
  }
}

export const submitSurvey = function(surveyId, data) {
  surveyId = surveyId || getSurveyId()
  let affId = window.ctrwowUtils.link.getParameterByName('Affid')
  const customFields = {}
  if (affId) {
    customFields['affid'] = affId
  }
  if (data.user) {
    data.user = {
      ...data.user,
      customFields
    }
  }
  data.surveyName = window.__CTRWOW_CONFIG ? window.__CTRWOW_CONFIG.SITE_NAME : ''
  return window.__CTR_FP_TRACKING.getFingerPrintId().then(function(fingerprintId) {
    return window.ctrwowUtils
      .callAjax(getSurveyUrl(fingerprintId) + '/' + surveyId + '/' + getClientId(), {
        method: 'PUT',
        body: JSON.stringify(data)
      })
      .then(handleSetClientId)
  })
}

function getRequestHeader() {
  const headers = {
    'content-type': 'application/json'
  }
  if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.X_CID) {
    headers.X_CID = window.__CTRWOW_CONFIG.X_CID
  }
  return headers
}

function webSaleApiRequest(path, { method = 'GET', body = {} }) {
  let apiEndpint = ''
  const options = {
    method: method,
    headers: {
      ...getRequestHeader()
    }
  }
  if (method.toLowerCase() == 'get') {
    apiEndpint = process.env.CRM_WEBSALE_GET_API_ENDPOINT_URL
  } else {
    apiEndpint = process.env.CRM_WEBSALE_POST_API_ENDPOINT_URL
    options.body = JSON.stringify(body)
  }
  return window.fetch(`${apiEndpint}/${path}`, options)
}

function leadgenApiRequest(path, { method = 'GET', body = {} }) {
  let apiEndpint = process.env.CRM_WEBSALE_POST_API_ENDPOINT_URL
  const options = {
    method: method,
    headers: {
      ...getRequestHeader()
    }
  }
  if (method.toLowerCase() == 'post') {
    options.body = JSON.stringify(body)
  }
  return window.fetch(`${apiEndpint}/${path}`, options)
}

function leadgenApiRequestWithToJson(path, options) {
  return leadgenApiRequest(path, options).then(toJson)
}

function webSaleApiRequestWithToJson(path, options) {
  return webSaleApiRequest(path, options).then(toJson)
}

export function getOfferPrices(webkey) {
  return webSaleApiRequestWithToJson(`api/campaigns/${webkey}/products/prices`, { method: 'GET' })
}

export function filterOffers(email) {
  try {
    if (!email) {
      throw new Error('Missing email param')
    }
    window.ctrwowUtils.showGlobalLoading()
    return window.ctrwowUtils
      .callAjax(getSurveyUrl() + '/customers?email=' + encodeURIComponent(email), {
        method: 'GET',
        headers: getRequestHeader()
      })
      .catch((e) => {
        console.warn(e)
      })
      .finally(window.ctrwowUtils.hideGlobalLoading)
  } catch (e) {
    console.log(e)
    window.ctrwowUtils.hideGlobalLoading()
  }
}

export function trackPixel(eventId = '') {
  return trackPixel2(eventId)
}

function getEvScriptUrl() {
  const domain1 = window.ctrwowUtils.link.getParameterByName('domain1') || ''
  if (!domain1) {
    return
  }
  return `https://${domain1}/scripts/sdk/everflow.js`
}

export function fireEvClickEvent() {
  try {
    if (window.localStorage.getItem('ctr_survey_tracking_click_fired')) {
      return
    }
    const network_id = window.ctrwowUtils.link.getParameterByName('network_id') || ''
    let affId = window.ctrwowUtils.link.getParameterByName('Affid')

    if (!affId || !network_id || !NETWORK_REQURIE_IDS.includes(network_id.toString())) {
      return
    }
    if (network_id.toString() === NETWORK_KAINERO_ID.toString()) {
      affId = affId.toLowerCase().split('k').join('')
    }
    $.getScript(getEvScriptUrl())
      .done(function(script, textStatus) {
        console.log(textStatus)
        try {
          window.EF.click({
            offer_id: EF.urlParameter('s4'),
            affiliate_id: affId,
            sub1: EF.urlParameter('s1'),
            sub2: EF.urlParameter('s2'),
            sub3: EF.urlParameter('s3'),
            sub4: EF.urlParameter('s4'),
            sub5: EF.urlParameter('gclid'),
            uid: EF.urlParameter('uid'),
            source_id: EF.urlParameter('source_id'),
            transaction_id: EF.urlParameter('s5')
          }).then(() => {
            console.log('EV Click Fired')
            window.localStorage.setItem('ctr_survey_tracking_click_fired', 'true')
          })
        } catch (e) {
          resolve(false)
          console.warn('Cannot trackEveflow' + e)
        }
      })
      .fail(function(jqxhr, settings, exception) {
        resolve(false)
        console.warn('Cannot load script ' + domain1)
      })

  } catch (e) {
    console.log(e)
  }
}

export function trackPixel2(eventId = '', eventData = {}) {
  const network_id = window.ctrwowUtils.link.getParameterByName('network_id') || ''
  const evScriptUrl = getEvScriptUrl()
  if (!evScriptUrl || !network_id || !NETWORK_REQURIE_IDS.includes(network_id.toString())) {
    return Promise.resolve(false)
  }
  const source_id = window.ctrwowUtils.link.getParameterByName('source_id') || ''
  const coupon_code = window.ctrwowUtils.link.getParameterByName('cc') || ''
  const S4 = window.ctrwowUtils.link.getParameterByName('s4') || ''
  const s5 = window.ctrwowUtils.link.getParameterByName('s5') || ''
  const advEventId = eventId ? getAdvId(network_id, eventId) : ''
  const eventPayload = {
    offer_id: S4,
    adv_event_id: advEventId,
    transaction_id: s5,
    coupon_code: coupon_code,
    source_id: source_id,
    ...eventData
  }

  return new Promise(function(resolve, reject) {
    $.getScript(getEvScriptUrl())
      .done(function(script, textStatus) {
        console.log(textStatus)
        try {
          console.log('window.EF.conversion')
          window.EF.conversion(eventPayload)
            .then((rs) => {
              resolve(rs)
            })
            .catch(function() {
              resolve(false)
            })
        } catch (e) {
          resolve(false)
          console.warn('Cannot trackEveflow' + e)
        }
      })
      .fail(function(jqxhr, settings, exception) {
        resolve(false)
        console.warn('Cannot load script ' + domain1)
      })
  })
}

/**
 * https://dfoglobal.atlassian.net/browse/CTR-4020
 * @Deprecated
 * 4/3/2021
 */
export function trackGtm(eventData = {}) {
  window.dataLayer = window.dataLayer || []
  window.dataLayer.push(eventData)
}

export function trackGtmFpEvent() {
  window.__CTR_FP_TRACKING.getFingerPrintId().then((fpId) => {
    window.ctrwowSurvey.trackGtm({
      event: 'FingerprintID',
      fpid: fpId
    })
  })
}

export const claimOffer = function(email) {
  return window.ctrwowUtils.callAjax(`${getSurveyUrl()}/claimoffer?email=${encodeURIComponent(email)}`, {
    method: 'POST',
    headers: {
      ...getRequestHeader()
    }
  })
}

export function getCRMCustomerInfoPayload(customerSurveyInfo) {
  function getGenderIdByName(name) {
    if (name === 'male') {
      return 2
    }
    if (name === 'female') {
      return 1
    }
    return 3
  }

  return {
    firstName: customerSurveyInfo.firstName,
    lastName: customerSurveyInfo.lastName,
    address1: customerSurveyInfo.address,
    city: customerSurveyInfo.city,
    zipCode: customerSurveyInfo.zip,
    state: customerSurveyInfo.state,
    countryCode: customerSurveyInfo.country,
    gender: getGenderIdByName(customerSurveyInfo.gender),
    dob: customerSurveyInfo.dob,
    email: customerSurveyInfo.email,
    phoneNumber: customerSurveyInfo.phoneNumber,
    isFromEmailWidget: false
  }
}

export function createCRMCustomerInfo(webkey, payload) {
  return window.ctrwowUtils.getUserAnalyticsInfo().then(function(analyticsV2) {
    const apiRequest = leadgenApiRequest(`api/leadgens/${webkey}`, {
      method: 'POST',
      body: { ...payload, analyticsV2 }
    })
    return apiRequest.then((r) => r.json().then((data) => ({ status: r.status, body: data })))
  })
}

export function createActivity(actPayload) {
  const webkey = window.localStorage.getItem('dfo_leadgen__webKey')
  const apiRequest = leadgenApiRequestWithToJson(`api/leadgens/${webkey}/activities`, {
    method: 'POST',
    body: actPayload
  })
  return apiRequest
}

export function checkEmailVerified(payload) {
  const webkey = window.localStorage.getItem('dfo_leadgen__webKey')
  return createCRMCustomerInfo(webkey, payload).then(function(rp) {
    if (rp.status >= 200 && rp.status < 300) {
      window.localStorage.setItem('ctr_survey_leadgen_id', rp.body.id)
      const isVerified = rp && rp.body && rp.body.emailVerified != false
      return isVerified
    }
    return rp.text().then((text) => {
      const defaultErrMessage = 'Something went wrong, please try again or contact ctrwow <NAME_EMAIL>'
      const errorMessage = get(JSON.parse(text), 'message', null)
      throw { message: errorMessage || defaultErrMessage }
    })
  })
}

export function getActivities(email, type) {
  const webkey = window.localStorage.getItem('dfo_leadgen__webKey')
  const apiRequest = leadgenApiRequestWithToJson(
    `api/leadgens/${webkey}/activities?email=${encodeURIComponent(email)}&activityType=${type}&page=1&pageSize=1`,
    { method: 'GET' }
  )
  return apiRequest
}

export function checkTrackingConversion(email) {
  if (!email) {
    throw new Error('Missing email')
  }
  return getActivities(email, COMPLETED_CONVERSION_ACT_TYPE)
    .then((rs) => rs && rs.totalQuantity === 0)
    .catch(() => true)
}

function hashData(text = '') {
  return CryptoJS.SHA256(text).toString(CryptoJS.enc.Hex)
}

export function fireEverFlowLeadEvent(userInfo) {
  const order_id = window.localStorage.getItem('ctr_survey_leadgen_id') || ''
  const { email, firstName, lastName, phoneNumber } = userInfo
  return trackPixel2('', {
    order_id,
    adv1: order_id,
    adv2: hashData(firstName),
    adv3: hashData(lastName),
    adv4: hashData(email),
    adv5: hashData(phoneNumber)
  })
}

export function handleTrackConversion(userInfo) {
  const { email, firstName, lastName, phoneNumber } = userInfo
  trackGtm({
    event: 'Lead'
  })
  const order_id = window.localStorage.getItem('ctr_survey_leadgen_id') || ''
  const promise2 = trackPixel2('', {
    order_id,
    adv1: order_id,
    adv2: hashData(firstName),
    adv3: hashData(lastName),
    adv4: hashData(email),
    adv5: hashData(phoneNumber)
  })
  window.__CTR_FP_TRACKING && window.__CTR_FP_TRACKING.trackConversion()
  const promise3 = createActivity({
    email,
    activityType: COMPLETED_CONVERSION_ACT_TYPE,
    description: `Leadgen, Survey, Track Conversion, ${new Date()}`
  })
  window.ctrwowUtils.showGlobalLoading()
  return Promise.all([promise2, promise3])
    .then((values) => {
      window.localStorage.removeItem('dfo__leadgen_checkedAff')
      window.ctrwowUtils.hideGlobalLoading()
    })
    .catch(function() {
      window.ctrwowUtils.hideGlobalLoading()
      window.localStorage.removeItem('dfo__leadgen_checkedAff')
    })
}

export function getTopPerfOffers(campaignIds = []) {
  const campaignIdsStr = campaignIds.join()
  const toDate = new Date()
  let endDate = new Date()
  endDate.setDate(endDate.getDate() + 2)
  endDate.setHours(0)
  endDate.setMinutes(0)
  endDate.setSeconds(0)
  endDate.setMilliseconds(0)
  endDate = endDate.toISOString()

  toDate.setDate(toDate.getDate() - 14)
  toDate.setHours(0)
  toDate.setMinutes(0)
  toDate.setSeconds(0)
  toDate.setMilliseconds(0)
  const startDate = toDate.toISOString()
  // console.log('endDate', endDate);
  // console.log('startDate', startDate);

  const url = `api/campaigns/top-performance?campaignIds=${campaignIdsStr}&startDate=${startDate}&endDate=${endDate}`
  return leadgenApiRequestWithToJson(url, { method: 'GET' })
}

function isOfferExpired(offerDate) {
  if (offerDate && offerDate.split('/').length === 3) {
    const dateArr = offerDate.split('/')
    // MM/DD/YYYY
    const day = dateArr[1]
    const month = dateArr[0]
    const year = dateArr[2]
    const offerDateObj = new Date(year, month - 1, day, 23, 59, 59)
    const toDay = new Date()
    return offerDateObj.getTime() < toDay.getTime()
  } else if (offerDate && offerDate.split('-').length === 3) {
    const offerDateObj = new Date(offerDate)
    const toDay = new Date()
    return offerDateObj.getTime() < toDay.getTime()
  }
}

const DEFAULT_MAP = {
  // acttpId: globalId
  //Blaux Wearable AC EN VSL (ACPTA)
  ['5745']: 5688,
  // PlayBeatz
  ['5735']: 5911,
  //OshenWatch
  ['5761']: 5790,
  //QBKraftz
  ['5736']: 5912,
  //TVBuddy
  ['5489']: 4483
}

export function getSortedOfferItems(offerItems = [], globalOfferIdsMapper = DEFAULT_MAP) {
  const cloneOfferItems = offerItems.map((item) => {
    return {
      ...item,
      globalId: globalOfferIdsMapper[`${item.offerId}`]
    }
  })
  const expiredOffers = cloneOfferItems.filter((item) => isOfferExpired(item.expiredDate))
  const nonExpiredOffers = cloneOfferItems.filter((item) => !isOfferExpired(item.expiredDate))

  const campaignIds = nonExpiredOffers.filter((oi) => oi && oi.globalId).map((oi) => oi.globalId)
  return getTopPerfOffers(campaignIds)
    .then(function(sortedOfferItems) {
      const rs = []
      sortedOfferItems.forEach((campaignItem) => {
        const newOfferItemIndex = nonExpiredOffers.findIndex((oi) => oi.globalId === campaignItem.campaignId)
        if (newOfferItemIndex > -1) {
          const newOfferItem = { ...nonExpiredOffers[newOfferItemIndex] }
          rs.push(newOfferItem)
          nonExpiredOffers.splice(newOfferItemIndex, 1)
        }
      })
      return [...rs, ...nonExpiredOffers, ...expiredOffers]
    })
    .catch(() => {
      return offerItems
    })
}


/////

export function handleRegister(userData) {
  console.log({
    registerUser: userData
  })

  if (!userData) {
    return Promise.reject('Missing userData')
  }

  const data = {
    email: userData.email,
    firstName: userData.firstName,
    lastName: userData.lastName,
    phoneNumber: userData.phoneNumber,
    contacts: []
  }
  const settings = {
    url: 'https://kainero-prod-api.azurewebsites.net/api/auth/quick-register/v1',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: JSON.stringify(data)
  }
  return new Promise(function(resolve, reject) {
    $.ajax(settings)
      .done(function(response) {
        if (response) {
          window.localStorage.setItem('dfo_survey_register_app_data', JSON.stringify(response))
          response.token && window.localStorage.setItem('dfo_survey_register_app_token', response.token)
        }
        resolve(response)
      })
      .fail(function(error) {
        reject(error)
      })
  })
}

export function validatePhoneNumber(data) {
  const { phoneNumber, countryCode } = data || {}
  if (!phoneNumber || !countryCode) {
    return Promise.reject(false)
  }
  return window.ctrwowUtils
    .callAjax(`https://apilayer.net/api/validate?access_key=********************************&number=${phoneNumber}&country_code=${countryCode}`)
    .then((rs) => {
      return rs && rs
    }).catch(() => false)
}

export function trackBlueShiftConfirm(userInfo) {
  const {
    id: customerId,
    address,
    city,
    country,
    dob,
    email,
    firstName,
    lastName,
    phoneNumber,
    state,
    zip
  } = userInfo

  window.__CTR_FP_TRACKING
    .getFingerPrintId()
    .then(function(fingerprintId) {
      blueshiftTrackingHelpers.track('survey_confirm', {
        customer_id: customerId,
        email_verified: true,
        email,
        firstname: firstName,
        lastname: lastName,
        phone_number: phoneNumber,
        fingerprint_id: fingerprintId
      })
    })
}

export function trackBlueShiftIdentify(userInfo = {}) {
  const {
    id: customerId,
    address,
    city,
    country,
    dob,
    email,
    firstName,
    lastName,
    phoneNumber,
    state,
    zip,
    emailVerified,
    phone_valid
  } = userInfo

  window.__CTR_FP_TRACKING
    .getFingerPrintId()
    .then(function(fingerprintId) {
      blueshiftTrackingHelpers.identify({
        customer_id: customerId,
        email,
        email_verified: emailVerified,
        firstname: firstName,
        lastname: lastName,
        phone_number: phoneNumber,
        fingerprint_id: fingerprintId,
        bs_tag: window.__CTR_BS_TAG || 'kainero',
        phone_valid
      })
    })
}

window.addEventListener('load', function() {
  try {
    fireEvClickEvent()
  } catch (e) {
    console.log(e)
  }
})

window.addEventListener('unload', function() {
  try {
    window.localStorage.removeItem('ctr_survey_tracking_click_fired')
  } catch (e) {
    console.log(e)
  }
})

