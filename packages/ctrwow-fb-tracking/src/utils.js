function getCustomPathName() {
  const regex = new RegExp('[?&]')
  const results = regex.exec(window.location.href)
  const newlocationSearch = results ? window.location.href.substring(results.index) : ''

  return newlocationSearch
}

function convertHrefToUrlObject(href) {
  let url = ''

  try {
    url = new URL(href)
    // Fix for IE 11
    if (url && url.origin === '//') {
      url = ''
    }
  } catch (error) {
    url = ''
    // console.log('the href is invalid', href)
  }
  return url
}

export function extractQueryString(url) {
  const trackingParam = ''
  if (!url) {
    url = location
  }
  if (typeof url === 'string') {
    url = convertHrefToUrlObject(url)
  }

  if (!url.search) return trackingParam

  const params = new URLSearchParams(getCustomPathName())
  var isSpecialCharacter = getCustomPathName().indexOf('#') > -1

  if (!params.toString()) return trackingParam

  const queryString = []

  for (const param of params) {
    const paramName = param[0]
    const paramValue = isSpecialCharacter ? decodeURIComponent(param[1]) : encodeURIComponent(param[1])
    queryString.push([paramName, paramValue].join('='))
  }

  if (!queryString.length) return trackingParam
  if (!trackingParam) return queryString.join('&')
  return queryString.join('&') + '&' + trackingParam
}

function removeDuplicateParams(href) {
  const url = convertHrefToUrlObject(href)
  if (!url) return href
  const params = url.search
  const queryString = params.substr(1)
  const arrayParams = queryString.split('&')
  if (arrayParams.length === 0) return url
  const keys = []
  const newParams = []
  // override the previous params if they existen
  arrayParams.reverse().forEach(function (param) {
    const key = param.split('=')[0]
    if (keys.includes(key)) return
    keys.push(key)
    newParams.push(param)
  })
  return url.origin + url.pathname + '?' + newParams.reverse().join('&')
}

function getJoinString(href) {
  const url = convertHrefToUrlObject(href)
  if (!url) return '?'
  // Check exist query string from url
  const joinString = url.search === '' ? '?' : '&'
  return joinString
}

export function getFinalLink(href, queryString) {
  if (!queryString) return href
  // const joinString = getJoinString(href)

  let joinString = getJoinString(href)

  if (href.indexOf('?') > -1) {
    joinString = '&'
  }

  const result = removeDuplicateParams(href + joinString + queryString)
  return result
}


export function updateURLParameter(url, param, paramVal) {
  let newAdditionalURL = ''
  let tempArray = url.split('?')
  const baseURL = tempArray[0]
  const additionalURL = tempArray[1]
  let temp = ''
  if (additionalURL) {
    tempArray = additionalURL.split('&')
    for (let i = 0; i < tempArray.length; i++) {
      // eslint-disable-next-line eqeqeq
      if (tempArray[i].split('=')[0] != param) {
        newAdditionalURL += temp + tempArray[i]
        temp = '&'
      }
    }
  }

  const rows_txt = temp + '' + param + '=' + paramVal
  return baseURL + '?' + newAdditionalURL + rows_txt
}
export function getParameterByName(name, url) {
  if (!url) url = window.location.href
  // eslint-disable-next-line no-useless-escape
  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

export function apiClient(url, settings) {
  let settingsClone = {}
  if (!url) {
    throw new Error('API URL is missing!')
  }
  const headersOptions = {
    'content-type': 'application/json'
  }
  if (settings && typeof settings === 'object') {
    settingsClone = Object.assign(
      {
        method: 'GET',
        headers: settings.headers ? Object.assign(headersOptions, settings.headers) : headersOptions
      },
      settings
    )
  }
  return window.fetch(url, settingsClone).then(function (response) {
    if (response.status === 204 || response.status === 205) {
      return null
    }

    if (response.status >= 200 && response.status < 300) {
      // return response.json();
      return response.text().then(function (text) {
        try {
          return text ? JSON.parse(text) : {}
        } catch (e) {
          return {}
        }
      })
    }
    throw JSON.stringify({
      status: response.status,
      statusText: response.statusText,
      response: response
    })
  })
}

// eslint-disable-next-line no-unused-vars
export function create_UUID() {
  var dt = new Date().getTime()
  var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (dt + Math.random() * 16) % 16 | 0
    dt = Math.floor(dt / 16)
    // eslint-disable-next-line eqeqeq
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
  return uuid
}

export function getUrlParams() {
  try {
    const urlParams = new URLSearchParams(location.search)
    const params = {}
    for (const [key, value] of urlParams) {
      params[key] = value
    }
    return params
  } catch (error) {
    console.log('error', error);
    return {}
  }
}

export function checkHasScriptLink(scriptUrl) {
  const scripts = document.getElementsByTagName('script');

  for (let i = 0; i < scripts.length; i++) {
    if (scripts[i].src === scriptUrl) {
      return true;
    }
  }

  return false;
}

export function appendScriptViewHeatmap(scriptUrl) {
  if (checkHasScriptLink(scriptUrl)) return;

  const head = document.head || document.getElementsByTagName('head')[0];
  const script = document.createElement('script');

  script.src = scriptUrl;
  script.defer = true;

  head.appendChild(script);
}

