import { create_UUID, extractQ<PERSON>yString, getFinalLink, getParameterByName, updateURLParameter } from './utils'

const BL_PARAMS = {
  CrossSiteSessionId: 'ctr_cssid',
  InteractionOrder: 'ctr_io',
  PreSiteId: 'ctr_psid',
  PrePageId: 'ctr_ppid',
  PrePageUrl: 'ctr_ppu'
}

const sessionStorgeKey = 'ctr_bl_CrossSiteSessionId'

function setCrossSiteSessionIdStored(sId) {
  window.sessionStorage.setItem(sessionStorgeKey, sId)
}

function getCrossSiteSessionIdStored() {
  return window.sessionStorage.getItem(sessionStorgeKey)
}

function getPageUrl() {
  return `${window.location.origin}${window.location.pathname}`
}

function getSiteId() {
  return window.__CTRWOW_CONFIG ? window.__CTRWOW_CONFIG.siteId : ''
}

function getPageId() {
  return window.__CTRWOW_CONFIG ? window.__CTRWOW_CONFIG.pageId : ''
}

function isLandingPage() {
  return !getParameterByName(BL_PARAMS.CrossSiteSessionId)
}

function getCrossSiteSessionId() {
  if (isLandingPage() && !getCrossSiteSessionIdStored()) {
    const ssId = create_UUID()
    setCrossSiteSessionIdStored(ssId)
    return getCrossSiteSessionIdStored()
  } else if (isLandingPage() && getCrossSiteSessionIdStored()) {
    return getCrossSiteSessionIdStored()
  }

  return getParameterByName(BL_PARAMS.CrossSiteSessionId)
}

function getPreviousData() {
  if (isLandingPage()) {
    return {
      pageId: null,
      siteId: null,
      pageUrl: null
    }
  }
  return {
    pageId: getParameterByName(BL_PARAMS.PrePageId),
    siteId: getParameterByName(BL_PARAMS.PreSiteId),
    pageUrl: decodeURIComponent(getParameterByName(BL_PARAMS.PrePageUrl))
  }
}

function getInteractionOrder() {
  if (isLandingPage()) {
    return 1
  }
  return parseInt(getParameterByName('ctr_io'))
}

export function getBehaviorFlowCustomData() {
  return {
    previousPage: getPreviousData(),
    crossSiteSessionId: getCrossSiteSessionId(),
    interactionOrder: getInteractionOrder()
  }
}

function poppulateBLParam(href, crossSiteSessionId) {
  let updateHref = href
  updateHref = updateURLParameter(updateHref, BL_PARAMS.CrossSiteSessionId, crossSiteSessionId)
  updateHref = updateURLParameter(updateHref, BL_PARAMS.InteractionOrder, getInteractionOrder() + 1)
  updateHref = updateURLParameter(updateHref, BL_PARAMS.PrePageId, getPageId())
  updateHref = updateURLParameter(updateHref, BL_PARAMS.PreSiteId, getSiteId())
  updateHref = updateURLParameter(updateHref, BL_PARAMS.PrePageUrl, encodeURIComponent(getPageUrl()))
  return updateHref
}

export function populateTrackingParamAndBLToLinks(crossSiteSessionId) {
  const currentQueryString = extractQueryString()
  try {
    document.querySelectorAll('a').forEach(function ($link) {
      let currentHref = $link.href
      if (
        !currentHref ||
        currentHref.startsWith('#') ||
        currentHref.startsWith('mailto') ||
        currentHref.startsWith('tel') ||
        currentHref.includes('javascript')
      ) {
        return
      }
      currentHref = getFinalLink(currentHref, currentQueryString)
      const clickId = create_UUID()
      const originalClickId = getParameterByName('ctr_tracking__original_click_id') || clickId
      currentHref = updateURLParameter(currentHref, 'ctr_tracking__click_id', clickId)
      currentHref = updateURLParameter(currentHref, 'ctr_tracking__original_click_id', originalClickId)
      $link.setAttribute('click-id', clickId)
      $link.setAttribute('call-to-action', 'true')
      currentHref = poppulateBLParam(currentHref, crossSiteSessionId)
      $link.href = currentHref
    })
  } catch (err) {
    console.log('populateParamToLinks link error: ', err)
  }
}

export function populateBehaviorFlowParamsToLinks(crossSiteSessionId) {
  try {
    const $links = document.querySelectorAll('a')
    $links.forEach(function ($link) {
      let currentHref = $link.href
      if (
        !currentHref ||
        currentHref.startsWith('#') ||
        currentHref.startsWith('mailto') ||
        currentHref.startsWith('tel') ||
        currentHref.includes('javascript')
      ) {
        return
      }

      currentHref = poppulateBLParam(currentHref, crossSiteSessionId)
      $link.href = currentHref
    })
  } catch (err) {
    console.error('populateBehaviorFlowParamsToLinks error: ', err)
  }
}

export function populateBehaviorFlowParamsToCtaBtns(crossSiteSessionId) {
  try {
    const $ctaButtons = document.querySelectorAll('button.ctr_cta_button')
    $ctaButtons.forEach(function ($ctaButton) {
      $ctaButton.setAttribute(BL_PARAMS.CrossSiteSessionId, crossSiteSessionId)
      $ctaButton.setAttribute(BL_PARAMS.PrePageId, getPageId())
      $ctaButton.setAttribute(BL_PARAMS.PreSiteId, getSiteId())
      $ctaButton.setAttribute(BL_PARAMS.PrePageUrl, encodeURIComponent(getPageUrl()))
      $ctaButton.setAttribute(BL_PARAMS.InteractionOrder, getInteractionOrder() + 1)
    })
  } catch (err) {
    console.error('populateBehaviorFlowParamsToCtaBtns error: ', err)
  }
}

export function initBehaviorFlowTracking(isInternal) {
  const flowCustomData = getBehaviorFlowCustomData()
  console.log({
    flowCustomData
  })
  if (!isInternal) {
    populateTrackingParamAndBLToLinks(flowCustomData.crossSiteSessionId)
  } else {
    populateBehaviorFlowParamsToLinks(flowCustomData.crossSiteSessionId)
    populateBehaviorFlowParamsToCtaBtns(flowCustomData.crossSiteSessionId)
  }
  return Promise.resolve(flowCustomData)
}
