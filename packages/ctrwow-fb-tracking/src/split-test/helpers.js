import device from 'current-device'
import { getParameterByName } from '../utils'
import { SPLIT_TEST_PARAMS, HEATMAP_DEVICE_TYPE, SPLIT_TEST_INFO } from '../constants'

export function getSplitTestSessionIdParam() {
  return getParameterByName(SPLIT_TEST_PARAMS.S_ID)
}

export function getSplitTestTargetTypeParam() {
  return getParameterByName(SPLIT_TEST_PARAMS.TARGET_PAGE_TYPE)
}

export function getSplitTestId() {
  return window.__CTR_SPL_TRACKING_SETTINGS ? window.__CTR_SPL_TRACKING_SETTINGS.SPL_ID : getParameterByName(SPLIT_TEST_PARAMS.ID)
}

export function isApplySplitTest() {
  return !!getSplitTestId()
}

export function isPreviewMode() {
  return getParameterByName('ctr_sl_heatmap_preview') === '1' || getParameterByName('ctr_heatmap_preview') === '1'
}

export function getDeviceType() {
  if (device.desktop()) {
    return HEATMAP_DEVICE_TYPE.desktop
  }
  if (device.mobile()) {
    return HEATMAP_DEVICE_TYPE.mobile
  }
  if (device.tablet()) {
    return HEATMAP_DEVICE_TYPE.tablet
  }
}

function setCookie(name, value, daysToExpire) {
  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + daysToExpire);

  const cookieValue = encodeURIComponent(value) + "; expires=" + expirationDate.toUTCString() + "; path=/";
  document.cookie = name + "=" + cookieValue;
}

function getCookie(name) {
  const cookieName = name + "=";
  const cookieArray = document.cookie.split(';');

  for (let i = 0; i < cookieArray.length; i++) {
    let cookie = cookieArray[i];

    while (cookie.charAt(0) === ' ') {
      cookie = cookie.substring(1);
    }

    if (cookie.indexOf(cookieName) === 0) {
      return decodeURIComponent(cookie.substring(cookieName.length, cookie.length));
    }
  }

  return null;
}


export function getPreviousExecutionData(splitTestId) {
  const splitTestInfo = JSON.parse(getCookie(SPLIT_TEST_INFO));
  return splitTestInfo?.[splitTestId] || null;
}

export function saveExecutionData(splitTestId, data) {
  const splitTestInfo = JSON.parse(getCookie(SPLIT_TEST_INFO)) || {};
  splitTestInfo[splitTestId] = data;
  setCookie(SPLIT_TEST_INFO, JSON.stringify(splitTestInfo), 3);
}
