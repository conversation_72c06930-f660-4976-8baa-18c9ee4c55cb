import {
  getSplitTestId,
  getSplitTestSessionIdParam,
  getSplitTestTargetTypeParam,
  isApplySplitTest,
  isPreviewMode,
} from './helpers'


function buildInitSplitTestResp(data = {}) {
  const { splitTestSessionId, splitTestId, splitTestTargetPageType } = data;
  return {
    isApplySplitTest: isApplySplitTest(),
    customData: {
      splitTestSessionId,
      splitTestId,
      splitTestTargetPageType
    }
  }
}

function initSplitTestTrack() {
  const splitTestId = getSplitTestId()
  return Promise.resolve(
    buildInitSplitTestResp({
      splitTestSessionId: getSplitTestSessionIdParam(),
      splitTestId,
      splitTestTargetPageType: getSplitTestTargetTypeParam()
    })
  )
}

export function initSplitTest() {
  return new Promise((resolve) => {
    if (isPreviewMode()) {
      resolve(buildInitSplitTestResp())
    } else if (getSplitTestId()) {
      initSplitTestTrack().then(resolve)
    } else {
      resolve(buildInitSplitTestResp())
    }
  })
}
