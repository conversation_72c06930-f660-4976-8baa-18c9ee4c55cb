/* eslint-disable */
import { apiClient, getParameterByName, getUrlParams, appendScriptViewHeatmap } from './utils'
import { initSplitTest } from './split-test'
import { getBehaviorFlowCustomData, initBehaviorFlowTracking } from './behavior-flow.helpers'
import { isPreviewMode } from "./split-test/helpers"

const isInternal =
  window.__CTR_FP_TRACKING_SETTINGS && (window.__CTR_FP_TRACKING_SETTINGS.FP_TRACKING_ID || window.__CTR_FP_TRACKING_SETTINGS.IS_INTERNAL)
const isFree = isInternal && window.__CTR_FP_TRACKING_SETTINGS && !window.__CTR_FP_TRACKING_SETTINGS.isChargeVersion

const FREE_LIB_SRC = 'https://d16hdrba6dusey.cloudfront.net/ctrwow_fp_analytics.min.js'
const CHARGE_LIB_SRC = 'https://d16hdrba6dusey.cloudfront.net/sitecommon/js/commons/ctrwow_analytics.v3.pro.min.js'

const API_END_POINT_URL =
  window.__CTR_FP_TRACKING_SETTINGS && window.__CTR_FP_TRACKING_SETTINGS.MODE === 'dev'
    ? 'https://ctrwow-dev-fingerprint-microservice.azurewebsites.net/api'
    : 'https://ctrwow-prod-fingerprint-microservice.azurewebsites.net/api'

const FP_SCRIPT_SRC =
  window.__CTR_FP_TRACKING_SETTINGS && window.__CTR_FP_TRACKING_SETTINGS.MODE === 'dev'
    ? 'https://ctrwowdevuser.blob.core.windows.net/assets/ctrwow_analytics.min.js'
    : isFree
    ? FREE_LIB_SRC
    : CHARGE_LIB_SRC

const FP_USERINFO_API_GET_CODE =
  window.__CTR_FP_TRACKING_SETTINGS && window.__CTR_FP_TRACKING_SETTINGS.FP_USERINFO_API_GET_CODE
    ? window.__CTR_FP_TRACKING_SETTINGS.FP_USERINFO_API_GET_CODE
    : ''

const FP_USERINFO_API_POST_CODE =
  window.__CTR_FP_TRACKING_SETTINGS && window.__CTR_FP_TRACKING_SETTINGS.FP_USERINFO_API_POST_CODE
    ? window.__CTR_FP_TRACKING_SETTINGS.FP_USERINFO_API_POST_CODE
    : ''

const PAGE_LOADED_EVENT_NAME = 'PAGE_LOADED'

if (isInternal) {
  window._CTR_TRACKING_ID = window.__CTR_FP_TRACKING_SETTINGS.FP_TRACKING_ID
  window._CTR_CUSTOM_DATA = JSON.parse(window.__CTR_FP_TRACKING_SETTINGS.FP_TRACKING_CUSTOM_DATA || '{}')
}

function isBuilder() {
  return window.ctrwowUtils && window.ctrwowUtils.isBuilderMode()
}

function validateTracking() {
  if ((window.__CTR_ENV && window.__CTR_ENV === 'CTR_APP') || isBuilder()) {
    throw new Error('Cannot track in ctr_wow app')
  }
  return true
}

function isFunction(functionToCheck) {
  return functionToCheck && {}.toString.call(functionToCheck) === '[object Function]'
}

function injectCtrFpTrackingScript() {
  try {
    // scriptElement.parentNode.removeChild(scriptElement);
    window._CTR_FINGERPRINTJS_TOKEN = '1xDWTKZNrjpwZjuWUjub'
    var s = document.createElement('script')
    s.setAttribute('id', '_ctr__fp_tracking_script')
    s.setAttribute('src', FP_SCRIPT_SRC)
    s.setAttribute('async', '')
    document.head.appendChild(s)
  } catch (e) {
    console.warn('injectCtrFpTrackingScript error: ' + e)
  }
}

let global_loadScriptingProcessing = undefined
const loadAnalyticScript = () => {
  global_loadScriptingProcessing = new Promise((resolve) => {
    window._CTR_FingerprintGeneratedCallback = function(fbId) {
      window.__CTRWOW_FINGER_PRINT_ID = fbId
      resolve(fbId)
      initAffId()
      initClickId()
      console.log('[loadAnalyticScript] global_loadScriptingProcessing - resolve')
      global_loadScriptingProcessing = null
    }
    injectCtrFpTrackingScript()
  })
  return global_loadScriptingProcessing
}

function loadFingerPrintId() {
  try {
    // window._CTR_IS_SPA = !isEnableTrackView;
    if (!window._CTR_TRACKING_ID) {
      throw new Error('Missing _CTR_TRACKING_ID')
    }
    if (!FP_SCRIPT_SRC) {
      throw new Error('Missing FP_SCRIPT_SRC')
    }
    validateTracking()
    return new Promise(function(resolve) {
      if (window.__CTRWOW_FINGER_PRINT_ID) {
        return resolve(window.__CTRWOW_FINGER_PRINT_ID)
      }
      if (global_loadScriptingProcessing && global_loadScriptingProcessing.then) {
        return global_loadScriptingProcessing.then((...args) => resolve(...args))
      }
      if (!document.getElementById('_ctr__fp_tracking_script')) {
        return loadAnalyticScript().then((...args) => resolve(...args))
      }
      if (!window.__CTRWOW_FINGER_PRINT_ID) {
        const resolveFpId = setInterval(function() {
          console.log('setInterval - resolveFpId')
          if (window.__CTRWOW_FINGER_PRINT_ID) {
            resolve(window.__CTRWOW_FINGER_PRINT_ID)
            clearInterval(resolveFpId)
          }
        }, 200)
      }
    })
    // return new Promise(function(resolve) {
    //   if (window.__CTRWOW_FINGER_PRINT_ID) {
    //     resolve(window.__CTRWOW_FINGER_PRINT_ID)
    //   } else {
    //     const scriptElement = document.getElementById('_ctr__fp_tracking_script')
    //     if (!scriptElement) {
    //       window._CTR_FingerprintGeneratedCallback = function(fbId) {
    //         window.__CTRWOW_FINGER_PRINT_ID = fbId
    //         resolve(fbId)
    //       }
    //       injectCtrFpTrackingScript()
    //     } else if (!window.__CTRWOW_FINGER_PRINT_ID) {
    //       const resolveFpId = setInterval(function() {
    //         if (window.__CTRWOW_FINGER_PRINT_ID) {
    //           resolve(window.__CTRWOW_FINGER_PRINT_ID)
    //           clearInterval(resolveFpId)
    //         }
    //       }, 0)
    //     }
    //   }
    // })
  } catch (e) {
    console.warn('getFingerPrintId error' + e)
    return Promise.reject(false)
  }
}

function apiClientWrapper(apiResource, options, onSuccess, onError) {
  apiClient(API_END_POINT_URL + '/' + apiResource, options)
    .then(function(rp) {
      isFunction(onSuccess) && onSuccess(rp)
    })
    .catch(function(err) {
      isFunction(onError) && onError(err)
    })
}

function validateParams(params) {
  if (!window._CTR_TRACKING_ID) {
    throw new Error('Missing TRACKING_ID')
  }
  if (!params) {
    throw new Error('Tracking Data invalid')
  }
}

function initAffId() {
  if (!window._CTR_CUSTOM_DATA) {
    window._CTR_CUSTOM_DATA = {}
  }
  if (getParameterByName('ctr_tracking__refid')) {
    window._CTR_CUSTOM_DATA.affiliateId = getParameterByName('ctr_tracking__refid')
  } else if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.AFFILIATE_ID) {
    window._CTR_CUSTOM_DATA.affiliateId = window.__CTRWOW_CONFIG.AFFILIATE_ID
  }
}

function initClickId() {
  const clickId = getParameterByName('ctr_tracking__click_id')
  const originalClickId = getParameterByName('ctr_tracking__original_click_id')
  const adsClickId = getParameterByName('ads-click-id')
  const adsNetwork = getParameterByName('ads-network')
  const adsEventName = getParameterByName('ads-event-name')
  const urlParams = getUrlParams();

  if (!window._CTR_CUSTOM_DATA) {
    window._CTR_CUSTOM_DATA = {}
  }

  if (clickId && originalClickId) {
    window._CTR_CUSTOM_DATA.clickId = clickId
    window._CTR_CUSTOM_DATA.originalClickId = originalClickId
  }

  if (adsClickId) {
    window._CTR_CUSTOM_DATA.adsClickId = adsClickId
  }

  if (adsNetwork) {
    window._CTR_CUSTOM_DATA.adsNetwork = adsNetwork
  }

  if (adsEventName) {
    window._CTR_CUSTOM_DATA.adsEventName = adsEventName
  }

  window._CTR_CUSTOM_DATA.urlParams = urlParams;

}

function inIframe() {
  try {
    return window.self !== window.top
  } catch (e) {
    return true
  }
}

function trackView() {
  return loadFingerPrintId()
}

function initInternal() {
  // initAffId()
  // initClickId()

  return new Promise((resolve, reject) => {
    if (!window._CTR_TRACKING_ID) {
      return resolve(false)
    }
    trackView()
      .then(function() {
        console.log('init ctr fp tracking')
        if (!inIframe()) {
          push(PAGE_LOADED_EVENT_NAME)
          initCtaLinkEvent()
          initCtaButtonsEvent()
        }
        resolve(true)
      })
      .catch((e) => {
        console.warn(e)
        return resolve(false)
      })
  })
}

function initExternal() {
  // initAffId()
  initClickId()
  initCtaExLinkEvent()
  return new Promise((resolve, reject) => {
    if (window._CTR_TRACKING_ID) {
      setInterval(function() {
        if (window._EA_PUSH && window._EA_ID) {
          window._EA_PUSH(PAGE_LOADED_EVENT_NAME)
          resolve(true)
        }
      }, 5000)
    } else {
      window._CTR_TRACKING_ID = getParameterByName('ctr_tracking__site_id')
      push(PAGE_LOADED_EVENT_NAME, () => {
        resolve(true)
      })
    }
  })
}

function validateTrackUserInfoParams(params) {
  validateParams(params)
  if (!FP_USERINFO_API_POST_CODE) {
    throw new Error('Missing FP_USERINFO_API_POST_CODE')
  }
  if (!FP_USERINFO_API_GET_CODE) {
    throw new Error('Missing FP_USERINFO_API_GET_CODE')
  }
}

/**
 *
 * @param userInfo
 * @param onSuccess
 * @param onError
 * @returns {boolean}
 */
export function pushUserInfo(userInfo, onSuccess, onError) {
  try {
    validateTrackUserInfoParams(userInfo)
    loadFingerPrintId().then(function(fingerprintId) {
      const apiEndPoint = 'userinfo/' + fingerprintId + '?code=' + FP_USERINFO_API_POST_CODE
      const options = {
        method: 'POST',
        body: JSON.stringify({
          userInfo: userInfo
        })
      }
      apiClientWrapper(apiEndPoint, options, onSuccess, onError)
    })
  } catch (err) {
    console.warn('CTR_FP_TRACKING error: ' + err)
    return false
  }
  return true
}

/**
 *
 * @param event: String
 * @param values: Array
 */
function push(event, values, callback, isUseSocket) {
  try {
    loadFingerPrintId().then(function() {
      window._EA_PUSH(event, values, !!isUseSocket)
      callback && callback()
    })
  } catch (e) {
    console.warn('CTR_FP_TRACKING push event error: ' + e)
  }
}

function pushProductPrice(oneUnitProductPrice) {
  try {
    if (Object.keys(oneUnitProductPrice)) {
      window._CTR_CUSTOM_DATA.oneUnitProductPrice = oneUnitProductPrice
      push('PRODUCT_PRICE_LOADED', [])
    }
  } catch (e) {
    console.warn('CTR_FP_TRACKING loadProductPrice error: ' + e)
  }
}

// eslint-disable-next-line no-unused-vars
function pushConversion(value, conversionUrl, onSucess) {
  try {
    const behaviorFlowCustomData = getBehaviorFlowCustomData();
    const { trackingPageId } = value;
    window._CTR_CUSTOM_DATA.conversionUrl = conversionUrl
    window._CTR_CUSTOM_DATA.conversionPageId = trackingPageId
    window._CTR_CUSTOM_DATA = {
      ...window._CTR_CUSTOM_DATA,
      ...(behaviorFlowCustomData || {})
    }
    push('CONVERSION', [value || 0], onSucess)
  } catch (e) {
    console.warn('CTR_FP_TRACKING pushConversion error: ' + e)
  }
}

function pushExConversion(value) {
  try {
    const ctr_tracking_site_id = getParameterByName('ctr_tracking__site_id')
    const ctr_tracking_page_id = getParameterByName('ctr_tracking__page_id')
    const ctr_tracking_conversion_url = getParameterByName('ctr_tracking__conversion_url')
    const clickId = getParameterByName('ctr_tracking__click_id')
    const originalClickId = getParameterByName('ctr_tracking__original_click_id')
    const behaviorFlowCustomData = getBehaviorFlowCustomData();

    if (ctr_tracking_site_id && ctr_tracking_page_id && clickId && originalClickId) {
      const backupSettingTrackingId = window._CTR_TRACKING_ID
      const backupIsSpa = window._CTR_IS_SPA
      const backupCustomData = window._CTR_CUSTOM_DATA ? { ...window._CTR_CUSTOM_DATA } : window._CTR_CUSTOM_DATA
      if (!window._CTR_CUSTOM_DATA) {
        window._CTR_CUSTOM_DATA = {}
      }
      window._CTR_IS_SPA = true
      window._CTR_TRACKING_ID = ctr_tracking_site_id
      window._CTR_CUSTOM_DATA.conversionUrl = decodeURIComponent(ctr_tracking_conversion_url || '')
      window._CTR_CUSTOM_DATA.conversionPageId = ctr_tracking_page_id
      window._CTR_CUSTOM_DATA.pageId = ctr_tracking_page_id
      window._CTR_CUSTOM_DATA.siteId = ctr_tracking_site_id
      window._CTR_CUSTOM_DATA.clickId = clickId
      window._CTR_CUSTOM_DATA.originalClickId = originalClickId
      window._CTR_CUSTOM_DATA = {
        ...window._CTR_CUSTOM_DATA,
        ...(behaviorFlowCustomData || {})
      }
      push('CONVERSION', [value || 0], () => {
        window._CTR_IS_SPA = backupIsSpa
        window._CTR_TRACKING_ID = backupSettingTrackingId
        window._CTR_CUSTOM_DATA = backupCustomData
      })
    }
  } catch (e) {
    console.warn('CTR_FP_TRACKING pushConversion error: ' + e)
  }
}

function initCtaLinkEvent() {
  const $a = $('a[call-to-action]')
  window.__ctr_clicked_ids = []

  function ctaClickHandler() {
    window._CTR_CUSTOM_DATA.clickId = $(this).attr('click-id')
    if (window.__ctr_clicked_ids.includes(window._CTR_CUSTOM_DATA.clickId)) {
      return
    }
    const payout = $(this).attr('payout')
    window._CTR_CUSTOM_DATA.ctaId = $(this).attr('cta-id')
    window._CTR_CUSTOM_DATA.ctaName = $(this).attr('title') || $(this).text() || $(this).attr('id')
    if (!window._CTR_CUSTOM_DATA.originalClickId) {
      window._CTR_CUSTOM_DATA.originalClickId = window._CTR_CUSTOM_DATA.clickId
    }
    if (payout) {
      window._CTR_CUSTOM_DATA.payout = payout
    }
    //Send Click event with ctaId, ctaName, clickId, originalClickId, payout
    console.log({
      PUSH_CLICK: window._CTR_CUSTOM_DATA
    })
    push(
      'CLICK',
      [],
      () => {
        window.__ctr_clicked_ids.push(window._CTR_CUSTOM_DATA.clickId)
        console.log('push CLICK event success')
      },
      false
    )
  }
  $a.on('click', ctaClickHandler)
  $a.on('contextmenu', ctaClickHandler)
}

function initCtaExLinkEvent() {
  window.__ctr_clicked_ids = []

  function ctaClickHandler($a) {
    window._CTR_CUSTOM_DATA.clickId = $a.getAttribute('click-id')
    if (window.__ctr_clicked_ids.includes(window._CTR_CUSTOM_DATA.clickId)) {
      return
    }
    if (!window._CTR_CUSTOM_DATA.originalClickId) {
      window._CTR_CUSTOM_DATA.originalClickId = window._CTR_CUSTOM_DATA.clickId
    }
    console.log({
      PUSH_CLICK: window._CTR_CUSTOM_DATA
    })
    push(
      'CLICK',
      [],
      () => {
        window.__ctr_clicked_ids.push(window._CTR_CUSTOM_DATA.clickId)
        console.log('push CLICK event success')
      },
      false
    )
  }

  document.addEventListener('click', (event) => {
    const isLink = event.target.nodeName === 'A';
    if (isLink) {
      ctaClickHandler(event.target)
      console.log(event.target)
    }
  })
}

function initCtaButtonsEvent() {
  const $ctaButtons = $('button.ctr_cta_button')
  $ctaButtons.on('click', function(event) {
    const payout = $(this).attr('payout')
    window._CTR_CUSTOM_DATA.ctaId = $(this).attr('ctr-cta-id')
    window._CTR_CUSTOM_DATA.ctaName = $(this).attr('title') || $(this).attr('name') || $(this).text() || $(this).attr('id')
    window._CTR_CUSTOM_DATA.clickId = $(this).attr('ctr-tracking-click-id')
    if (!window._CTR_CUSTOM_DATA.originalClickId) {
      window._CTR_CUSTOM_DATA.originalClickId = window._CTR_CUSTOM_DATA.clickId
    }
    if (payout) {
      window._CTR_CUSTOM_DATA.payout = payout
    }
    //Send Click event with ctaId, ctaName, clickId, originalClickId, payout

    push(
      'CLICK',
      [],
      () => {
        console.log('push CLICK event success')
      },
      false
    )
  })
}

function initTracking() {
  try {
    if (isInternal) {
      return initInternal()
    } else {
      return initExternal()
    }
  } catch (e) {
    console.warn('ctr_fp_tracking innit error: ', { e })
    return Promise.resolve(false)
  }
}

window.addEventListener('load', function() {
  initSplitTest()
    .then(({customData = {}}) => {
      if(!window._CTR_CUSTOM_DATA) {
        window.__CTR_CUSTOM_DATA = {};
      }
      if (customData) {
        window._CTR_CUSTOM_DATA = window._CTR_CUSTOM_DATA
          ? {
            ...window._CTR_CUSTOM_DATA,
            ...customData
          }
          : window._CTR_CUSTOM_DATA
      }
      initBehaviorFlowTracking(isInternal).then(behaviorFlowCustomData => {
        window._CTR_CUSTOM_DATA = {
          ...window._CTR_CUSTOM_DATA,
          ...(behaviorFlowCustomData || {})
        }
        setTimeout(() => {
          window._CTR_IS_SPA = false
          initTracking().then(() => {
            console.log('Init tracking done')
          })
        }, window.ctrDevDebugger__UtilsTesting__delayTime || 0)
      })
    })
})

function listenEventViewHm() {
  if(!isPreviewMode()) return;
  window.addEventListener(
    'message',
    (event) => {
      const key = event.message ? 'message' : 'data'
      const dataEvent = event[key]

      if (dataEvent.eventName && dataEvent.eventName === 'ctr_init_heatmap_view') {
        appendScriptViewHeatmap(dataEvent.data)
      }
    },
    false
  )
}

listenEventViewHm();
export const track = push
export const trackConversion = pushConversion
export const trackExConversion = pushExConversion
export const trackProductPrice = pushProductPrice
export const getFingerPrintId = loadFingerPrintId
