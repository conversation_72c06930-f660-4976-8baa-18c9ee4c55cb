import { appendCtaClickIdTrackingParam } from '../'

export function appendCtaClickIdTrackingParamTest() {
  const clickId = 'testclickid'
  const oClickId = 'testorclickid'
  const url = 'http://test.com/abc.html'
  const expectationResult = `${url}?ctr_tracking__click_id=${clickId}&ctr_tracking__original_click_id=${oClickId}`
  // $('.checkoutButton')[0]
  const $currentTarget =
    $('.checkoutWithCreditCard')[0] ||
    $(`<button ctr-tracking-click-id="${clickId}" ctr-tracking-original-click-id="${oClickId}"> Button CTA Tracking </button>`)
  let rs = appendCtaClickIdTrackingParam(url, $currentTarget)
  console.log('Should run appendCtaClickIdTrackingParam correctly ', rs === expectationResult)

  rs = appendCtaClickIdTrackingParam(url)
  console.log('Should run appendCtaClickIdTrackingParam with missing $currentTarget correctly ', rs === url)
}
