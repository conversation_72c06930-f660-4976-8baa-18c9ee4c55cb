export default function updateCurrentBLTrackingParam(href, crossSiteSessionId, updateParams = []) {
  const { getParameterByName, updateURLParameter } = window.ctrwowUtils.link
  const BL_PARAMS = {
    CrossSiteSessionId: 'ctr_cssid',
    InteractionOrder: 'ctr_io',
    PreSiteId: 'ctr_psid',
    PrePageId: 'ctr_ppid',
    PrePageUrl: 'ctr_ppu'
  }
  const ioValue = parseInt(getParameterByName('ctr_io')) + 1
  let updateHref = href
  if (crossSiteSessionId) {
    updateHref = updateURLParameter(updateHref, BL_PARAMS.CrossSiteSessionId, crossSiteSessionId)
  }
  if(updateParams && updateParams.length) {
    if(updateParams.includes(BL_PARAMS.PrePageUrl)) {
      updateHref = updateURLParameter(updateHref, BL_PARAMS.PrePageUrl, encodeURIComponent(`${window.location.origin}${window.location.pathname}`))
    }
    if(updateParams.includes(BL_PARAMS.InteractionOrder)) {
      updateHref = updateURLParameter(updateHref, BL_PARAMS.InteractionOrder, ioValue)
    }
    if(updateParams.includes(BL_PARAMS.PrePageId)) {
      updateHref = updateURLParameter(updateHref, BL_PARAMS.PrePageId, window.__CTRWOW_CONFIG.pageId)
    }
    if(updateParams.includes(BL_PARAMS.PreSiteId)) {
      updateHref = updateURLParameter(updateHref, BL_PARAMS.PreSiteId, window.__CTRWOW_CONFIG.siteId)
    }
  } else {
    updateHref = updateURLParameter(updateHref, BL_PARAMS.InteractionOrder, ioValue)
    updateHref = updateURLParameter(updateHref, BL_PARAMS.PrePageId, window.__CTRWOW_CONFIG.pageId)
    updateHref = updateURLParameter(updateHref, BL_PARAMS.PreSiteId, window.__CTRWOW_CONFIG.siteId)
    updateHref = updateURLParameter(updateHref, BL_PARAMS.PrePageUrl, encodeURIComponent(`${window.location.origin}${window.location.pathname}`))
  }
  return updateHref
}
