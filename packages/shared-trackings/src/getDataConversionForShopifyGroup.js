import { TRACKING_EVENTS } from './constants'
function getEFDomain(zdn1) {
  let domain = ''
  const objDomain = {
    1: 'www.dapidata.com',
    2: 'vervedirect.servecvr.com',
    3: 'www.sdwetrk.com',
    4: 'www.esplma.com',
    5: 'www.eldrflwr.com',
    6: 'www.frnchprl.com',
    7: 'www.frstbte.com',
    8: 'www.flrtni.com',
    9: 'www.flrdra.com',
    10: 'www.emrldisle.com',
    11: 'www.edlwss.com',
    12: 'www.whtrsn.com',
    13: 'www.esprssmrtn.com',
    14: 'www.whskysr.com',
    15: 'www.finegizmos.com',
    16: 'www.selectgizmos.com',
    17: 'www.techselectgadgets.com',
    18: 'www.newtechgizmo.com',
    19: 'www.englshrs.com',
    20: 'www.elprsdnt.com',
    21: 'www.frscosr.com',
    22: 'www.frnchsprkl.com',
    23: 'www.bldycsr.com',
    24: 'www.smsmrtni.com',
    25: 'www.allclear-skin.com',
    26: 'www.allcleartools.com',
    27: 'www.oobots.com',
    28: 'www.djpcraze.com',
    29: 'www.freedomcharming.com',
    30: 'www.dealthem.com',
    31: 'www.imyellowpages.com',
    32: 'www.altoacre.com',
    33: 'www.veldbrand.com',
    34: 'www.tmtsub.com',
    35: 'track.shopnovawave.com',
    36: 'track.mynovawave.com',
    37: 'www.phlifeplus.com',
    38: 'www.peoplegrow.com',
    39: 'www.amasasky.com',
    40: 'www.beautifultruly.com',
    41: 'www.biggestdealsonline.com',
    42: 'www.earlytechadopter.com',
    43: 'www.producttrustedreviews.com',
    44: 'www.oggadget.com',
    45: 'www.onlythebiggestdeals.com',
    46: 'www.nanotechloop.com',
    47: 'www.smartermoneysaver.com',
    48: 'www.healthyglowmag.com',
    49: 'www.specialdreamdeals.com',
    50: 'www.dealsforyoudaily.com',
    51: 'www.supersavingsbuddy.com',
    52: 'www.techmonkeypost.com',
    53: 'www.thetechadopter.com',
    54: 'www.theweeklyloop.com',
    55: 'www.truesurvivorsonly.com',
    56: 'www.picbalancer.com',
    57: 'www.firescoops.com',
    58: 'www.honestitemsreviews.com',
    59: 'www.hypebuzzed.com',
    60: 'www.earn1m.com',
    61: 'www.shredzcrush.com',
    62: 'www.02good4u.com'
  }
  if (objDomain[zdn1]) domain = objDomain[zdn1]
  return domain
}
export default function getDataConversionForShopifyGroup(orderInfo) {
  const conversionTrackingData = []
  const {
    ctrwowUtils: { link }
  } = window

  try {
    const domain2 = link.getParameterByName(TRACKING_EVENTS.everflowDomainParams2)
    const domainKa = link.getParameterByName(TRACKING_EVENTS.everflowDomainParamsKa)
    let domain = link.getParameterByName(TRACKING_EVENTS.everflowDomainParams) || domain2 || domainKa || ''
    const coupon_code = link.getParameterByName('CC') || ''
    const sub4 = link.getParameterByName('S4') || ''
    const sub5 = link.getParameterByName('S5') || ''
    const source_id = link.getQueryParameter('source_id') || ''
    const ttclid_id = link.getQueryParameter('ttclid') || ''
    const ersParam = link.getParameterByName('ers') || ''
    const gtmTrackingID = window.GtmIDTracking || ''

    if (!domain) {
      const zdn1 = window.ctrwowUtils.handleParam.getQueryParameter('zdn1')
      zdn1 && (domain = getEFDomain(zdn1))
    }

    if ((ersParam && ersParam.toLowerCase() === 'y') || (!domain && !gtmTrackingID)) {
      return null
    }

    domain &&
      conversionTrackingData.push({
        type: 'everflow',
        data: {
          domain: domain,
          offer_id: sub4,
          transaction_id: sub5,
          coupon_code: coupon_code,
          source_id: source_id,
          amount: window.localStorage.getItem('conventionTrackingPrice') || '0',
          checkoutUrl: window.location.search.replace('?', ''),
          parameters: {
            ttclid: ttclid_id
          }
        }
      })

    gtmTrackingID &&
      conversionTrackingData.push({
        type: 'gtm',
        data: {
          price: window.localStorage.getItem('conventionTrackingPrice') || '0',
          gtmId: gtmTrackingID
        }
      })
  } catch (err) {
    console.log('getDataConversionForShopifyGroup error: ', { err })
  }

  return conversionTrackingData
}
