// [source : ctrwowlib > fireFbPixel]
export default function fireFbPixel(orderInfo) {
  try {
    if (orderInfo && orderInfo.orderNumber) {
      if (typeof fbq !== 'undefined') {
        if (!localStorage.getItem('isFbPurchaseFired')) {
          // eslint-disable-next-line no-undef
          fbq(
            'track',
            'Purchase',
            {
              value: orderInfo.orderTotalFull,
              currency: window.localStorage.getItem('currencyCode') || ''
            },
            {
              eventID: orderInfo.orderNumber
            }
          )
          localStorage.setItem('isFbPurchaseFired', true)
        }
      }
    }
  } catch (err) {
    console.log('fireFbPixel error: ', { err })
  }
}
