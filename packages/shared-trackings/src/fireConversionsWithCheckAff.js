export default function fireConversionsWithCheckAff(conversionsParams) {
  try {
    const { countryCode, handleTrackConversions, trackingLink = window.location.host } = conversionsParams
    if (typeof handleTrackConversions !== 'function') {
      throw 'handleTrackConversions is not a function'
    }

    const affParam = window.ctrwowUtils.link.getQueryParameter('Affid')
    window.ctrwowUtils.tracking
      .checkAff(affParam, trackingLink, { countryCode })
      .then(function (rs) {
        if (rs) {
          handleTrackConversions()
        }
      })
      .catch((e) => {
        handleTrackConversions()
      })
  } catch (e) {
    return false
  }
  return true
}
