import { TRACKING_EVENTS } from './constants'

function sha256(ascii) {
  function rightRotate(value, amount) {
    return (value >>> amount) | (value << (32 - amount))
  }

  var mathPow = Math.pow
  var maxWord = mathPow(2, 32)
  var lengthProperty = 'length'
  var i, j // Used as a counter across the whole file
  var result = ''

  var words = []
  var asciiBitLength = ascii[lengthProperty] * 8

  //* caching results is optional - remove/add slash from front of this line to toggle
  // Initial hash value: first 32 bits of the fractional parts of the square roots of the first 8 primes
  // (we actually calculate the first 64, but extra values are just ignored)
  var hash = (sha256.h = sha256.h || [])
  // Round constants: first 32 bits of the fractional parts of the cube roots of the first 64 primes
  var k = (sha256.k = sha256.k || [])
  var primeCounter = k[lengthProperty]
  /* /
  var hash = [], k = [];
  var primeCounter = 0;
  // */

  var isComposite = {}
  for (var candidate = 2; primeCounter < 64; candidate++) {
    if (!isComposite[candidate]) {
      for (i = 0; i < 313; i += candidate) {
        isComposite[i] = candidate
      }
      hash[primeCounter] = (mathPow(candidate, 0.5) * maxWord) | 0
      k[primeCounter++] = (mathPow(candidate, 1 / 3) * maxWord) | 0
    }
  }

  ascii += '\x80'
  while ((ascii[lengthProperty] % 64) - 56) ascii += '\x00' // More zero padding
  for (i = 0; i < ascii[lengthProperty]; i++) {
    j = ascii.charCodeAt(i)
    if (j >> 8) return // ASCII check: only accept characters in range 0-255
    words[i >> 2] |= j << (((3 - i) % 4) * 8)
  }
  words[words[lengthProperty]] = (asciiBitLength / maxWord) | 0
  words[words[lengthProperty]] = asciiBitLength

  // process each chunk
  for (j = 0; j < words[lengthProperty]; ) {
    var w = words.slice(j, (j += 16)) // The message is expanded into 64 words as part of the iteration
    var oldHash = hash
    // This is now the undefinedworking hash", often labelled as variables a...g
    // (we have to truncate as well, otherwise extra entries at the end accumulate
    hash = hash.slice(0, 8)

    for (i = 0; i < 64; i++) {
      // Expand the message into 64 words
      // Used below if
      var w15 = w[i - 15]
      var w2 = w[i - 2]

      // Iterate
      var a = hash[0]
      var e = hash[4]
      var temp1 =
        hash[7] +
        (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25)) + // S1
        ((e & hash[5]) ^ (~e & hash[6])) + // ch
        k[i] +
        // Expand the message schedule if needed
        (w[i] =
          i < 16
            ? w[i]
            : (w[i - 16] +
                (rightRotate(w15, 7) ^ rightRotate(w15, 18) ^ (w15 >>> 3)) + // s0
                w[i - 7] +
                (rightRotate(w2, 17) ^ rightRotate(w2, 19) ^ (w2 >>> 10))) | // s1
              0)
      // This is only used once, so *could* be moved below, but it only saves 4 bytes and makes things unreadble
      var temp2 =
        (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22)) + // S0
        ((a & hash[1]) ^ (a & hash[2]) ^ (hash[1] & hash[2])) // maj

      hash = [(temp1 + temp2) | 0].concat(hash) // We don't bother trimming off the extra ones, they're harmless as long as we're truncating when we do the slice()
      hash[4] = (hash[4] + temp1) | 0
    }

    for (i = 0; i < 8; i++) {
      hash[i] = (hash[i] + oldHash[i]) | 0
    }
  }

  for (i = 0; i < 8; i++) {
    for (j = 3; j + 1; j--) {
      var b = (hash[i] >> (j * 8)) & 255
      result += (b < 16 ? 0 : '') + b.toString(16)
    }
  }
  return result
}

function getEFDomain(zdn1) {
  let domain = ''
  const objDomain = {
    1: 'www.dapidata.com',
    2: 'vervedirect.servecvr.com',
    3: 'www.sdwetrk.com',
    4: 'www.esplma.com',
    5: 'www.eldrflwr.com',
    6: 'www.frnchprl.com',
    7: 'www.frstbte.com',
    8: 'www.flrtni.com',
    9: 'www.flrdra.com',
    10: 'www.emrldisle.com',
    11: 'www.edlwss.com',
    12: 'www.whtrsn.com',
    13: 'www.esprssmrtn.com',
    14: 'www.whskysr.com',
    15: 'www.finegizmos.com',
    16: 'www.selectgizmos.com',
    17: 'www.techselectgadgets.com',
    18: 'www.newtechgizmo.com',
    19: 'www.englshrs.com',
    20: 'www.elprsdnt.com',
    21: 'www.frscosr.com',
    22: 'www.frnchsprkl.com',
    23: 'www.bldycsr.com',
    24: 'www.smsmrtni.com',
    25: 'www.allclear-skin.com',
    26: 'www.allcleartools.com',
    27: 'www.oobots.com',
    28: 'www.djpcraze.com',
    29: 'www.freedomcharming.com',
    30: 'www.dealthem.com',
    31: 'www.imyellowpages.com',
    32: 'www.altoacre.com',
    33: 'www.veldbrand.com',
    34: 'www.tmtsub.com',
    35: 'track.shopnovawave.com',
    36: 'track.mynovawave.com',
    37: 'www.phlifeplus.com',
    38: 'www.peoplegrow.com',
    39: 'www.amasasky.com',
    40: 'www.beautifultruly.com',
    41: 'www.biggestdealsonline.com',
    42: 'www.earlytechadopter.com',
    43: 'www.producttrustedreviews.com',
    44: 'www.oggadget.com',
    45: 'www.onlythebiggestdeals.com',
    46: 'www.nanotechloop.com',
    47: 'www.smartermoneysaver.com',
    48: 'www.healthyglowmag.com',
    49: 'www.specialdreamdeals.com',
    50: 'www.dealsforyoudaily.com',
    51: 'www.supersavingsbuddy.com',
    52: 'www.techmonkeypost.com',
    53: 'www.thetechadopter.com',
    54: 'www.theweeklyloop.com',
    55: 'www.truesurvivorsonly.com',
    56: 'www.picbalancer.com',
    57: 'www.firescoops.com',
    58: 'www.honestitemsreviews.com',
    59: 'www.hypebuzzed.com',
    60: 'www.earn1m.com',
    61: 'www.shredzcrush.com',
    62: 'www.02good4u.com'
  }
  if (objDomain[zdn1]) domain = objDomain[zdn1]
  return domain
}

// [source : ctrwowlib > fireEverFlow]
export default function fireEverFlow(orderInfo) {
  const {
    ctrwowUtils: { link }
  } = window

  // Ref: https://dfoglobal.atlassian.net/browse/CSB-1785
  // const everFlowUrl = 'https://#DOMAIN/?nid=#NETWORK_ID&oid=#OFFER_ID&transaction_id=#TRANSACTION_ID&adv1=#ADV1&coupon_code=#CC&sub1=#S1&sub2=#S2&sub3=#S3&sub4=#S4&sub5=#S5&source_id=#SOURCE_ID'
  try {
    // [Tracking - EF] Do not fire conversion to EF if param ERS=Y exists on URL
    // ref: https://dfoglobal.atlassian.net/browse/CTR-1201
    const ersParam = link.getParameterByName('ers') || ''
    if (ersParam && ersParam.toLowerCase() === 'y') {
      return
    }

    const domain2 = link.getParameterByName(TRACKING_EVENTS.everflowDomainParams2) || ''
    let domain = link.getParameterByName(TRACKING_EVENTS.everflowDomainParams) || domain2
    const domainKa = link.getParameterByName(TRACKING_EVENTS.everflowDomainParamsKa) || ''

    const coupon_code = link.getParameterByName('CC') || ''
    const sub4 = link.getParameterByName('S4') || ''
    const sub5 = link.getParameterByName('S5') || ''
    const source_id = link.getQueryParameter('source_id') || ''
    const ttclid_id = link.getQueryParameter('ttclid') || ''
    const qtySelected = localStorage.getItem('qtySelected')

    const getScriptCode = (sub4, sub5, coupon_code, source_id, ttclid) => {
      const sourceConfigShopifyPayment = window.__ctrPageConfiguration?.sourceConfig?.source
      let EFPayload

      const treatMent = window.ctrwowUtils.handleParam.getQueryParameter('treatment') || null

      if (sourceConfigShopifyPayment === 'SHOPIFY') {
        // Payload for Shopify payment
        EFPayload = `EF.conversion({
          offer_id: "${sub4}",
          transaction_id: "${sub5}",
          adv1: "${orderInfo.cusEmailPP ? orderInfo.cusEmailPP : orderInfo.cusEmail ? orderInfo.cusEmail : ''}",
          adv2: "${treatMent || (orderInfo.cusFirstName ? sha256(orderInfo.cusFirstName) : '')}",
          adv3: "${orderInfo.cusLastName ? sha256(orderInfo.cusLastName) : ''}",
          adv4: "${orderInfo.cusEmailPP ? sha256(orderInfo.cusEmailPP) : orderInfo.cusEmail ? sha256(orderInfo.cusEmail) : ''}",
          adv5: "${orderInfo.cusPhone ? sha256(Number(orderInfo.cusPhone.match(/\d/g).join(''))) : ''}",
          coupon_code: "${coupon_code}",
          order_id: "${orderInfo.orderNumber}",
          source_id: "${source_id}",
          amount: "${orderInfo.orderTotalFull}",
          parameters: {
            ttclid: "${ttclid}",
            flowparams: "${window.location.search.replace('?', '')}"
          }
        });`
      } else {
        // Default Payload for all payments
        EFPayload = `EF.conversion({
          offer_id: "${sub4}",
          transaction_id: "${sub5}",
          adv1: "${qtySelected || orderInfo.orderNumber}",
          adv2: "${treatMent || (orderInfo.cusFirstName ? sha256(orderInfo.cusFirstName) : '')}",
          adv3: "${orderInfo.cusLastName ? sha256(orderInfo.cusLastName) : ''}",
          adv4: "${orderInfo.cusEmailPP ? sha256(orderInfo.cusEmailPP) : orderInfo.cusEmail ? sha256(orderInfo.cusEmail) : ''}",
          adv5: "${orderInfo.cusPhone ? sha256(Number(orderInfo.cusPhone.match(/\d/g).join(''))) : ''}",
          coupon_code: "${coupon_code}",
          order_id: "${orderInfo.orderNumber}",
          source_id: "${source_id}",
          amount: "${orderInfo.orderTotalFull}",
          parameters: {
            ttclid: "${ttclid}",
            flowparams: "${window.location.search.replace('?', '')}"
          }
        });`
      }
      return EFPayload
    }

    const checkRunEFKa = () => {
      if (!domainKa) {
        return
      }

      const coupon_codeKa = link.getParameterByName('CC-ka') || ''
      const sub4Ka = link.getParameterByName('S4-ka') || ''
      const sub5Ka = link.getParameterByName('S5-ka') || ''
      const source_idKa = link.getQueryParameter('source_id-ka') || ''
      const ttclid_idKa = link.getQueryParameter('ttclid-ka') || ''

      const scriptUrlKa = document.createElement('script')
      scriptUrlKa.type = 'text/javascript'
      scriptUrlKa.src = `https://${domainKa}/scripts/sdk/everflow.js`
      document.head.appendChild(scriptUrlKa)

      scriptUrlKa.onload = () => {
        const scriptCodeKa = getScriptCode(sub4Ka, sub5Ka, coupon_codeKa, source_idKa, ttclid_idKa)
        const scriptEFKa = document.createElement('script')
        scriptEFKa.type = 'text/javascript'
        scriptEFKa.innerHTML = scriptCodeKa
        document.head.appendChild(scriptEFKa)
        localStorage.setItem('isEverFlowFired', 'true')
      }
    }

    if (orderInfo && orderInfo.orderNumber) {
      if (!domain) {
        const zdn1 = window.ctrwowUtils.handleParam.getQueryParameter('zdn1')
        zdn1 && (domain = getEFDomain(zdn1))
      }

      if (domain) {
        // Domain 1 or 2
        const scriptUrl = document.createElement('script')
        scriptUrl.type = 'text/javascript'
        scriptUrl.src = `https://${domain}/scripts/sdk/everflow.js`
        document.head.appendChild(scriptUrl)

        scriptUrl.onload = () => {
          const scriptCode = getScriptCode(sub4, sub5, coupon_code, source_id, ttclid_id)
          const scriptEF = document.createElement('script')
          scriptEF.type = 'text/javascript'
          scriptEF.innerHTML = scriptCode
          document.head.appendChild(scriptEF)
          localStorage.setItem('isEverFlowFired', 'true')
          checkRunEFKa()
        }
      } else {
        checkRunEFKa()
      }
    }
  } catch (err) {
    console.log('fireEverFlow error: ', { err })
  }
}

export function checkAndGetAltAffId(resolve, reject) {
  try {
    const domain2 = window.ctrwowUtils.handleParam.getQueryParameter(TRACKING_EVENTS.everflowDomainParams2) || ''
    const domain = window.ctrwowUtils.handleParam.getQueryParameter(TRACKING_EVENTS.everflowDomainParams) || domain2
    const affId = window.ctrwowUtils.handleParam.getQueryParameter('affid')
    const offerId = window.ctrwowUtils.handleParam.getQueryParameter('s4')
    const currentURL = location.href.toLocaleLowerCase()
    if (
      !window.dfoef ||
      !domain ||
      !affId ||
      (currentURL.indexOf('www.getuvbrite.com') === -1 &&
        currentURL.indexOf('www.zenfluffsleep.com') === -1 &&
        currentURL.indexOf('www.getchargecard.com') === -1)
    ) {
      resolve(null)
      return
    }

    // STEP 1: check and get Alt AffId from server
    // recommit code
    window.dfoef.order
      .checkAndGetAltAffId(affId, offerId)
      .then((result) => {
        if (result && result.success && result.isSpecialFire) {
          let param = window.location.href.split('?')[1]
          const paramArr = param.split('&')
          const newParamArr = []
          paramArr.forEach((p) => {
            if (p.toLowerCase() === `affid=${affId}`) {
              p = `affId=${result.altAffId}`
            }
            newParamArr.push(p)
          })

          param = newParamArr.join('&')
          result.landingUrl = `${window.location.href.split('?')[0]}?${param}`
          window.landingUrlAlt = result.landingUrl
          localStorage.setItem('altUrlVisible', true)
        }
        resolve(result)
      })
      .catch((e) => {
        resolve(null)
      })
  } catch (error) {
    console.log('check affid fail')
    resolve(null)
  }
}

export function fireEverFlow2Server() {
  try {
    let orderInfo = localStorage.getItem('orderInfo')
    if (!orderInfo) return

    const domain2 = window.ctrwowUtils.handleParam.getQueryParameter(TRACKING_EVENTS.everflowDomainParams2) || ''
    let domain = window.ctrwowUtils.handleParam.getQueryParameter(TRACKING_EVENTS.everflowDomainParams) || domain2

    orderInfo = JSON.parse(orderInfo)

    if (!domain) {
      const zdn1 = window.ctrwowUtils.handleParam.getQueryParameter('zdn1')
      zdn1 && (domain = getEFDomain(zdn1))
    }
    if (!domain || !orderInfo.orderNumber) return

    const isSpecialFire = window.dfoef.upsell.isSpecialFire()
    if (isSpecialFire && localStorage.getItem('altUrlVisible') === 'true') {
      const scriptUrl = document.createElement('script')
      scriptUrl.type = 'text/javascript'
      scriptUrl.src = `https://www.frnchsprkl.com/scripts/sdk/everflow.js`
      scriptUrl.onload = sendData2EverFlow
      document.head.appendChild(scriptUrl)
    } else {
      fireEverFlow(orderInfo)
      sendData2EverFlow()
    }
  } catch (error) {
    console.log('Fired but failed.')
  }
}

function sendData2EverFlow() {
  const offerId = window.ctrwowUtils.handleParam.getQueryParameter('s4')
  const affId = window.ctrwowUtils.handleParam.getQueryParameter('affId')
  const coupon_code = window.ctrwowUtils.handleParam.getQueryParameter('coupon_code')
  const orderInfo = JSON.parse(localStorage.getItem('orderInfo'))
  const data = {
    orderStatus: 'success',
    amount: orderInfo.orderTotalFull,
    couponCode: coupon_code,
    orderNumber: orderInfo.orderNumber,
    customerEmail: orderInfo.cusEmail,
    orderUrl: localStorage.getItem('ctr_checkout_url')
  }
  window.dfoef.upsell
    .fireAndSaveOrder(affId, offerId, data)
    .then((data) => {
      localStorage.setItem('isEverFlowFired', 'true')
      localStorage.removeItem('altUrlVisible')
      console.log(data)
    })
    .catch((e) => {
      console.log('Fired but failed.')
    })
}

function loadLibrary(isUpsellPage) {
  const script = document.createElement('script')
  script.src = 'https://d16hdrba6dusey.cloudfront.net/sitecommon/js/commons/dfoef.min.js'
  script.defer = true
  if (isUpsellPage) {
    script.onload = fireEverFlow2Server
  }
  document.body.appendChild(script)
}

export function initDFOLib(isUpsellPage, delayLoad) {
  try {
    if (delayLoad) {
      let firstLoad = true
      const events = ['touchstart', 'click', 'scroll']
      events.forEach(function (eventName) {
        window.addEventListener(
          eventName,
          function () {
            if (!firstLoad) return
            loadLibrary(isUpsellPage)
            firstLoad = false
          },
          {
            once: true
          }
        )
      })
    } else {
      loadLibrary(isUpsellPage)
    }
  } catch (error) {
    console.log('init dfo lib error ')
  }
}
