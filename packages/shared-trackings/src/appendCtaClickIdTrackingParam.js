export default function appendCtaClickIdTrackingParam(url, $currentTarget) {
  let urlWithTrackingParam = url
  try {
    if (!urlWithTrackingParam) {
      throw 'Missing url'
    }
    if (!$currentTarget) {
      throw 'Missing $currentTarget'
    }
    const trackingAttrParamMap = {
      'ctr-tracking-click-id': 'ctr_tracking__click_id',
      'ctr-tracking-original-click-id': 'ctr_tracking__original_click_id',
      'ctr_cssid': 'ctr_cssid',
      'ctr_ppid': 'ctr_ppid',
      'ctr_psid': 'ctr_psid',
      'ctr_ppu': 'ctr_ppu',
      'ctr_io': 'ctr_io'
    }

    Object.keys(trackingAttrParamMap).forEach(atrr => {
      if($currentTarget.attr(atrr)) {
        urlWithTrackingParam = window.ctrwowUtils.commonActions.updateURLParameter(
          urlWithTrackingParam,
          trackingAttrParamMap[atrr],
          $currentTarget.attr(atrr)
        )
      }
    })
  } catch (err) {
    console.log('appendCtaClickIdTrackingParam error: ', err)
  }
  return urlWithTrackingParam
}
