import appendCtaClickIdTrackingParam from './appendCtaClickIdTrackingParam'
/**
 *
 * @param url Ex: http://test.com?p1=1
 * @param $currentTarget (Jquery Object)
 * @returns {string} Ex: ?p1=1&ctr_tracking__click_id=test&ctr_tracking__original_click_id=test
 */
export default function mergeCurrentParamsWithCtaClickIdTrackingParam($currentTarget) {
  const url = window.location.href
  try {
    if (!$currentTarget) {
      throw 'Missing $currentTarget'
    }
    const newUrl = appendCtaClickIdTrackingParam(url, $currentTarget)
    const queryString = newUrl.split('?')[1] || ''
    return `?${queryString}`;
  } catch (err) {
    console.log('mergeCurrentParamsWithCtaClickIdTrackingParam error: ', err)
    return `?${url.split('?')[1] || ''}`
  }
}
