import { countrylist } from './countrylist'
export function setPatternZipCode(value, element) {
  if (value) {
    const regex = decodeURIComponent(value)
    element.setAttribute('postcode-pattern', regex)
  } else {
    element.removeAttribute('postcode-pattern')
  }
}
export function loadStates(countryDdl) {
  const form = countryDdl.closest('form')
  let stateUrl = `//cdn-sgn.dfowebsys-h01.com/states/${countryDdl.value.toLowerCase()}.json`
  if (countryDdl.value.toLowerCase() === 'us') {
    stateUrl = `//cdn-sgn.dfowebsys-h01.com/states/sio/${countryDdl.value.toLowerCase()}.json`
  }
  const stateDdl = form.querySelector('select[name="state"]')
  let listOptions = '<option value="">----</option>'
  fetch(stateUrl)
    .then((res) => res.json())
    .then((data) => {
      data.forEach((state) => {
        listOptions += `<option value="${state.StateCode}">${state.StateName}</option>`
      })
      stateDdl.innerHTML = listOptions
      if (data.length) {
        stateDdl.setAttribute('required', '')
      } else {
        // remove attr required with empty states
        stateDdl.removeAttribute('required')
        stateDdl.click()
      }
      window.ctrwowUtils.events.emit('stateLoaded')
    })
    .catch((err) => {
      stateDdl.innerHTML = listOptions
      // remove attr required with empty states
      stateDdl.removeAttribute('required')
      stateDdl.click()
      window.ctrwowUtils.events.emit('stateLoaded')
      console.log(err)
    })
}
export function loadCountries(countries, { countryCode }) {
  const countryDdls = document.querySelectorAll('[name="countryCode"]')
  if (!Array.isArray(countries)) {
    return
  }
  countryDdls.forEach((countryDdl) => {
    let listOptions = '<option value="">----</option>'
    countries.forEach((country) => {
      countrylist.forEach((item) => {
        if (item.ISO === country.iso_2) {
          country.postalCodeRegex = item.Regex
        }
      })
      listOptions += `<option value="${country.iso_2}" regex="${country.postalCodeRegex}">${country.name}</option>`
    })
    countryDdl.innerHTML = listOptions
    countryDdl.addEventListener('change', (e) => {
      loadStates(e.currentTarget)

      if (e.currentTarget.selectedIndex > -1) {
        const regex = e.currentTarget.options[e.currentTarget.selectedIndex].getAttribute('regex')
        const zipCodeElm = e.currentTarget.closest('form').querySelector('input[name="zipCode"]')
        zipCodeElm && setPatternZipCode(regex, zipCodeElm)
      }
    })

    // ! Set Current country
    if (countries.length === 1) {
      countryCode = countries[0].iso_2
    }
    if (countryDdl.querySelector(`option[value="${countryCode}"]`)) {
      countryDdl.value = countryCode
      countryDdl.dispatchEvent(new Event('change'))
    }

    window.ctrwowUtils.events.emit('countriesLoaded')
  })

  window.countries = countries.map((item) => {
    return {
      ...item,
      countryCode: item.iso_2
    }
  })
}
