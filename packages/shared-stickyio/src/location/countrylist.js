export const countrylist = [
  {
    Country: 'Afghanistan',
    ISO: 'AF',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Åland Islands',
    ISO: 'AX',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Albania',
    ISO: 'AL',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Algeria',
    ISO: 'DZ',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'American Samoa',
    ISO: 'AS',
    Regex: '^\\d{5}(-{1}\\d{4,6})$'
  },
  {
    Country: 'Andorra',
    ISO: 'AD',
    Regex: '^[Aa][Dd]\\d{3}$'
  },
  {
    Country: 'Angola',
    ISO: 'AO',
    Regex: ''
  },
  {
    Country: 'Anguilla',
    ISO: 'AI',
    Regex: '^[Aa][I][-][2][6][4][0]$'
  },
  {
    Country: 'Antigua and Barbuda',
    ISO: 'AG',
    Regex: ''
  },
  {
    Country: 'Argentina',
    ISO: 'AR',
    Regex: '^\\d{4}|[A-Za-z]\\d{4}[a-zA-Z]{3}$'
  },
  {
    Country: 'Armenia',
    ISO: 'AM',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Aruba',
    ISO: 'AW',
    Regex: ''
  },
  {
    Country: 'Ascension island',
    ISO: 'AC',
    Regex: '^[Aa][Ss][Cc][Nn]\\s{0,1}[1][Zz][Zz]$'
  },
  {
    Country: 'Australia',
    ISO: 'AU',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Austria',
    ISO: 'AT',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Azerbaijan',
    ISO: 'AZ',
    Regex: '^[Aa][Zz]\\d{4}$'
  },
  {
    Country: 'Bahamas',
    ISO: 'BS',
    Regex: ''
  },
  {
    Country: 'Bahrain',
    ISO: 'BH',
    Regex: '^\\d{3,4}$'
  },
  {
    Country: 'Bangladesh',
    ISO: 'BD',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Barbados',
    ISO: 'BB',
    Regex: '^[Aa][Zz]\\d{5}$'
  },
  {
    Country: 'Belarus',
    ISO: 'BY',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Belgium',
    ISO: 'BE',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Belize',
    ISO: 'BZ',
    Regex: ''
  },
  {
    Country: 'Benin',
    ISO: 'BJ',
    Regex: ''
  },
  {
    Country: 'Bermuda',
    ISO: 'BM',
    Regex: '^[A-Za-z]{2}\\s([A-Za-z]{2}|\\d{2})$'
  },
  {
    Country: 'Bhutan',
    ISO: 'BT',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Bolivia',
    ISO: 'BO',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Bonaire, Sint Eustatius and Saba',
    ISO: 'BQ',
    Regex: ''
  },
  {
    Country: 'Bosnia and Herzegovina',
    ISO: 'BA',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Botswana',
    ISO: 'BW',
    Regex: ''
  },
  {
    Country: 'Brazil',
    ISO: 'BR',
    Regex: '^\\d{5}-\\d{3}$'
  },
  {
    Country: 'British Antarctic Territory',
    ISO: '',
    Regex: '^[Bb][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'British Indian Ocean Territory',
    ISO: 'IO',
    Regex: '^[Bb]{2}[Nn][Dd]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'British Virgin Islands',
    ISO: 'VG',
    Regex: '^[Vv][Gg]\\d{4}$'
  },
  {
    Country: 'Brunei',
    ISO: 'BN',
    Regex: '^[A-Za-z]{2}\\d{4}$'
  },
  {
    Country: 'Bulgaria',
    ISO: 'BG',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Burkina Faso',
    ISO: 'BF',
    Regex: ''
  },
  {
    Country: 'Burundi',
    ISO: 'BI',
    Regex: ''
  },
  {
    Country: 'Cambodia',
    ISO: 'KH',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Cameroon',
    ISO: 'CM',
    Regex: ''
  },
  {
    Country: 'Canada',
    ISO: 'CA',
    Regex: '^(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\d(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\s{0,1}\\d(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\d$'
  },
  {
    Country: 'Cape Verde',
    ISO: 'CV',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Cayman Islands',
    ISO: 'KY',
    Regex: '^[Kk][Yy]\\d[-\\s]{0,1}\\d{4}$'
  },
  {
    Country: 'Central African Republic',
    ISO: 'CF',
    Regex: ''
  },
  {
    Country: 'Chad',
    ISO: 'TD',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Chile',
    ISO: 'CL',
    Regex: '^\\d{7}\\s\\(\\d{3}-\\d{4}\\)$'
  },
  {
    Country: 'China',
    ISO: 'CN',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Christmas Island',
    ISO: 'CX',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Cocos (Keeling) Island',
    ISO: 'CC',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Colombia',
    ISO: 'CO',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Comoros',
    ISO: 'KM',
    Regex: ''
  },
  {
    Country: 'Congo (Brazzaville)',
    ISO: 'CG',
    Regex: ''
  },
  {
    Country: 'Congo, Democratic Republic',
    ISO: 'CD',
    Regex: '^[Cc][Dd]$'
  },
  {
    Country: 'Cook Islands',
    ISO: 'CK',
    Regex: ''
  },
  {
    Country: 'Costa Rica',
    ISO: 'CR',
    Regex: '^\\d{4,5}$'
  },
  {
    Country: 'Côte d"Ivoire (Ivory Coast)',
    ISO: 'CI',
    Regex: ''
  },
  {
    Country: 'Croatia',
    ISO: 'HR',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Cuba',
    ISO: 'CU',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Curaçao',
    ISO: 'CW',
    Regex: ''
  },
  {
    Country: 'Cyprus',
    ISO: 'CY',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Czech Republic',
    ISO: 'CZ',
    Regex: '^\\d{5}\\s\\(\\d{3}\\s\\d{2}\\)$'
  },
  {
    Country: 'Denmark',
    ISO: 'DK',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Djibouti',
    ISO: 'DJ',
    Regex: ''
  },
  {
    Country: 'Dominica',
    ISO: 'DM',
    Regex: ''
  },
  {
    Country: 'Dominican Republic',
    ISO: 'DO',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'East Timor',
    ISO: 'TL',
    Regex: ''
  },
  {
    Country: 'Ecuador',
    ISO: 'EC',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'El Salvador',
    ISO: 'SV',
    Regex: '^1101$'
  },
  {
    Country: 'Egypt',
    ISO: 'EG',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Equatorial Guinea',
    ISO: 'GQ',
    Regex: ''
  },
  {
    Country: 'Eritrea',
    ISO: 'ER',
    Regex: ''
  },
  {
    Country: 'Estonia',
    ISO: 'EE',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Ethiopia',
    ISO: 'ET',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Falkland Islands',
    ISO: 'FK',
    Regex: '^[Ff][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Faroe Islands',
    ISO: 'FO',
    Regex: '^\\d{3}$'
  },
  {
    Country: 'Fiji',
    ISO: 'FJ',
    Regex: ''
  },
  {
    Country: 'Finland',
    ISO: 'FI',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'France',
    ISO: 'FR',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'French Guiana',
    ISO: 'GF',
    Regex: '^973\\d{2}$'
  },
  {
    Country: 'French Polynesia',
    ISO: 'PF',
    Regex: '^987\\d{2}$'
  },
  {
    Country: 'French Southern and Antarctic Territories',
    ISO: 'TF',
    Regex: ''
  },
  {
    Country: 'Gabon',
    ISO: 'GA',
    Regex: '^\\d{2}\\s[a-zA-Z-_ ]\\s\\d{2}$'
  },
  {
    Country: 'Gambia',
    ISO: 'GM',
    Regex: ''
  },
  {
    Country: 'Georgia',
    ISO: 'GE',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Germany',
    ISO: 'DE',
    Regex: '^\\d{2}$'
  },
  {
    Country: 'Germany',
    ISO: 'DE',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Germany',
    ISO: 'DE',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Ghana',
    ISO: 'GH',
    Regex: ''
  },
  {
    Country: 'Gibraltar',
    ISO: 'GI',
    Regex: '^[Gg][Xx][1]{2}\\s{0,1}[1][Aa]{2}$'
  },
  {
    Country: 'Greece',
    ISO: 'GR',
    Regex: '^\\d{3}\\s{0,1}\\d{2}$'
  },
  {
    Country: 'Greenland',
    ISO: 'GL',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Grenada',
    ISO: 'GD',
    Regex: ''
  },
  {
    Country: 'Guadeloupe',
    ISO: 'GP',
    Regex: '^971\\d{2}$'
  },
  {
    Country: 'Guam',
    ISO: 'GU',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Guatemala',
    ISO: 'GT',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Guernsey',
    ISO: 'GG',
    Regex: '^[A-Za-z]{2}\\d\\s{0,1}\\d[A-Za-z]{2}$'
  },
  {
    Country: 'Guinea',
    ISO: 'GN',
    Regex: ''
  },
  {
    Country: 'Guinea Bissau',
    ISO: 'GW',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Guyana',
    ISO: 'GY',
    Regex: ''
  },
  {
    Country: 'Haiti',
    ISO: 'HT',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Heard and McDonald Islands',
    ISO: 'HM',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Honduras',
    ISO: 'HN',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Hong Kong',
    ISO: 'HK',
    Regex: ''
  },
  {
    Country: 'Hungary',
    ISO: 'HU',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Iceland',
    ISO: 'IS',
    Regex: '^\\d{3}$'
  },
  {
    Country: 'India',
    ISO: 'IN',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Indonesia',
    ISO: 'ID',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Iran',
    ISO: 'IR',
    Regex: '^\\d{5}-\\d{5}$'
  },
  {
    Country: 'Iraq',
    ISO: 'IQ',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Ireland',
    ISO: 'IE',
    Regex: ''
  },
  {
    Country: 'Isle of Man',
    ISO: 'IM',
    Regex: '^[Ii[Mm]\\d{1,2}\\s\\d\\[A-Z]{2}$'
  },
  {
    Country: 'Israel',
    ISO: 'IL',
    Regex: '^\\b\\d{5}(\\d{2})?$'
  },
  {
    Country: 'Italy',
    ISO: 'IT',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Jamaica',
    ISO: 'JM',
    Regex: '^\\d{2}$'
  },
  {
    Country: 'Japan',
    ISO: 'JP',
    Regex: '^\\d{7}\\s\\(\\d{3}-\\d{4}\\)$'
  },
  {
    Country: 'Jersey',
    ISO: 'JE',
    Regex: '^[Jj][Ee]\\d\\s{0,1}\\d[A-Za-z]{2}$'
  },
  {
    Country: 'Jordan',
    ISO: 'JO',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Kazakhstan',
    ISO: 'KZ',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Kenya',
    ISO: 'KE',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Kiribati',
    ISO: 'KI',
    Regex: ''
  },
  {
    Country: 'Korea, North',
    ISO: 'KP',
    Regex: ''
  },
  {
    Country: 'Korea, South',
    ISO: 'KR',
    Regex: '^\\d{6}\\s\\(\\d{3}-\\d{3}\\)$'
  },
  {
    Country: 'Kosovo',
    ISO: 'XK',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Kuwait',
    ISO: 'KW',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Kyrgyzstan',
    ISO: 'KG',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Latvia',
    ISO: 'LV',
    Regex: '^[Ll][Vv][- ]{0,1}\\d{4}$'
  },
  {
    Country: 'Laos',
    ISO: 'LA',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Lebanon',
    ISO: 'LB',
    Regex: '^\\d{4}\\s{0,1}\\d{4}$'
  },
  {
    Country: 'Lesotho',
    ISO: 'LS',
    Regex: '^\\d{3}$'
  },
  {
    Country: 'Liberia',
    ISO: 'LR',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Libya',
    ISO: 'LY',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Liechtenstein',
    ISO: 'LI',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Lithuania',
    ISO: 'LT',
    Regex: '^[Ll][Tt][- ]{0,1}\\d{5}$'
  },
  {
    Country: 'Luxembourg',
    ISO: 'LU',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Macau',
    ISO: 'MO',
    Regex: ''
  },
  {
    Country: 'Macedonia',
    ISO: 'MK',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Madagascar',
    ISO: 'MG',
    Regex: '^\\d{3}$'
  },
  {
    Country: 'Malawi',
    ISO: 'MW',
    Regex: ''
  },
  {
    Country: 'Maldives',
    ISO: 'MV',
    Regex: '^\\d{4,5}$'
  },
  {
    Country: 'Malaysia',
    ISO: 'MY',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Mali',
    ISO: 'ML',
    Regex: ''
  },
  {
    Country: 'Malta',
    ISO: 'MT',
    Regex: '^[A-Za-z]{3}\\s{0,1}\\d{4}$'
  },
  {
    Country: 'Marshall Islands',
    ISO: 'MH',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Mauritania',
    ISO: 'MR',
    Regex: ''
  },
  {
    Country: 'Mauritius',
    ISO: 'MU',
    Regex: ''
  },
  {
    Country: 'Martinique',
    ISO: 'MQ',
    Regex: '^972\\d{2}$'
  },
  {
    Country: 'Mayotte',
    ISO: 'YT',
    Regex: '^976\\d{2}$'
  },
  {
    Country: 'Micronesia',
    ISO: 'FM',
    Regex: '^\\d{5}(-{1}\\d{4})$'
  },
  {
    Country: 'Mexico',
    ISO: 'MX',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Micronesia',
    ISO: 'FM',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Moldova',
    ISO: 'MD',
    Regex: '^[Mm][Dd][- ]{0,1}\\d{4}$'
  },
  {
    Country: 'Monaco',
    ISO: 'MC',
    Regex: '^980\\d{2}$'
  },
  {
    Country: 'Mongolia',
    ISO: 'MN',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Montenegro',
    ISO: 'ME',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Montserrat',
    ISO: 'MS',
    Regex: '^[Mm][Ss][Rr]\\s{0,1}\\d{4}$'
  },
  {
    Country: 'Morocco',
    ISO: 'MA',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Mozambique',
    ISO: 'MZ',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Myanmar',
    ISO: 'MM',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Namibia',
    ISO: 'NA',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Nauru',
    ISO: 'NR',
    Regex: ''
  },
  {
    Country: 'Nepal',
    ISO: 'NP',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Netherlands',
    ISO: 'NL',
    Regex: '^\\d{4}\\s{0,1}[A-Za-z]{2}$'
  },
  {
    Country: 'New Caledonia',
    ISO: 'NC',
    Regex: '^988\\d{2}$'
  },
  {
    Country: 'New Zealand',
    ISO: 'NZ',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Nicaragua',
    ISO: 'NI',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Niger',
    ISO: 'NE',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Nigeria',
    ISO: 'NG',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Niue',
    ISO: 'NU',
    Regex: ''
  },
  {
    Country: 'Norfolk Island',
    ISO: 'NF',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Northern Mariana Islands',
    ISO: 'MP',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Norway',
    ISO: 'NO',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Oman',
    ISO: 'OM',
    Regex: '^\\d{3}$'
  },
  {
    Country: 'Pakistan',
    ISO: 'PK',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Palau',
    ISO: 'PW',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Panama',
    ISO: 'PA',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Papua New Guinea',
    ISO: 'PG',
    Regex: '^\\d{3}$'
  },
  {
    Country: 'Paraguay',
    ISO: 'PY',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Peru',
    ISO: 'PE',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Philippines',
    ISO: 'PH',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Pitcairn Islands',
    ISO: 'PN',
    Regex: '^[Pp][Cc][Rr][Nn]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Poland',
    ISO: 'PL',
    Regex: '^\\d{2}[- ]{0,1}\\d{3}$'
  },
  {
    Country: 'Portugal',
    ISO: 'PT',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Portugal',
    ISO: 'PT',
    Regex: '^\\d{4}[- ]{0,1}\\d{3}$'
  },
  {
    Country: 'Puerto Rico',
    ISO: 'PR',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Qatar',
    ISO: 'QA',
    Regex: ''
  },
  {
    Country: 'Réunion',
    ISO: 'RE',
    Regex: '^974\\d{2}$'
  },
  {
    Country: 'Romania',
    ISO: 'RO',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Russia',
    ISO: 'RU',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Saint Barthélemy',
    ISO: 'BL',
    Regex: '^97133$'
  },
  {
    Country: 'Saint Helena',
    ISO: 'SH',
    Regex: '^[Ss][Tt][Hh][Ll]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Saint Kitts and Nevis',
    ISO: 'KN',
    Regex: ''
  },
  {
    Country: 'Saint Lucia',
    ISO: 'LC',
    Regex: ''
  },
  {
    Country: 'Saint Martin',
    ISO: 'MF',
    Regex: '^97150$'
  },
  {
    Country: 'Saint Pierre and Miquelon',
    ISO: 'PM',
    Regex: '^97500$'
  },
  {
    Country: 'Saint Vincent and the Grenadines',
    ISO: 'VC',
    Regex: '^[Vv][Cc]\\d{4}$'
  },
  {
    Country: 'San Marino',
    ISO: 'SM',
    Regex: '^4789\\d$'
  },
  {
    Country: 'Sao Tome and Principe',
    ISO: 'ST',
    Regex: ''
  },
  {
    Country: 'Saudi Arabia',
    ISO: 'SA',
    Regex: '^\\d{5}(-{1}\\d{4})?$'
  },
  {
    Country: 'Senegal',
    ISO: 'SN',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Serbia',
    ISO: 'RS',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Serbia',
    ISO: 'RS',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Seychelles',
    ISO: 'SC',
    Regex: ''
  },
  {
    Country: 'Sint Maarten',
    ISO: 'SX',
    Regex: ''
  },
  {
    Country: 'Sierra Leone',
    ISO: 'SL',
    Regex: ''
  },
  {
    Country: 'Singapore',
    ISO: 'SG',
    Regex: '^\\d{2}$'
  },
  {
    Country: 'Singapore',
    ISO: 'SG',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Singapore',
    ISO: 'SG',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Slovakia',
    ISO: 'SK',
    Regex: '^\\d{5}\\s\\(\\d{3}\\s\\d{2}\\)$'
  },
  {
    Country: 'Slovenia',
    ISO: 'SI',
    Regex: '^([Ss][Ii][- ]{0,1}){0,1}\\d{4}$'
  },
  {
    Country: 'Solomon Islands',
    ISO: 'SB',
    Regex: ''
  },
  {
    Country: 'Somalia',
    ISO: 'SO',
    Regex: ''
  },
  {
    Country: 'South Africa',
    ISO: 'ZA',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'South Georgia and the South Sandwich Islands',
    ISO: 'GS',
    Regex: '^[Ss][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'South Korea',
    ISO: 'KR',
    Regex: '^\\d{6}\\s\\(\\d{3}-\\d{3}\\)$'
  },
  {
    Country: 'Spain',
    ISO: 'ES',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Sri Lanka',
    ISO: 'LK',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Sudan',
    ISO: 'SD',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Suriname',
    ISO: 'SR',
    Regex: ''
  },
  {
    Country: 'Swaziland',
    ISO: 'SZ',
    Regex: '^[A-Za-z]\\d{3}$'
  },
  {
    Country: 'Sweden',
    ISO: 'SE',
    Regex: '^\\d{3}\\s*\\d{2}$'
  },
  {
    Country: 'Switzerland',
    ISO: 'CH',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Svalbard and Jan Mayen',
    ISO: 'SJ',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Syria',
    ISO: 'SY',
    Regex: ''
  },
  {
    Country: 'Taiwan',
    ISO: 'TW',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Tajikistan',
    ISO: 'TJ',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Tanzania',
    ISO: 'TZ',
    Regex: ''
  },
  {
    Country: 'Thailand',
    ISO: 'TH',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Togo',
    ISO: 'TG',
    Regex: ''
  },
  {
    Country: 'Tokelau',
    ISO: 'TK',
    Regex: ''
  },
  {
    Country: 'Tonga',
    ISO: 'TO',
    Regex: ''
  },
  {
    Country: 'Trinidad and Tobago',
    ISO: 'TT',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Tristan da Cunha',
    ISO: 'SH',
    Regex: '^[Tt][Dd][Cc][Uu]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Tunisia',
    ISO: 'TN',
    Regex: '^\\d{4}$'
  },
  {
    Country: 'Turkey',
    ISO: 'TR',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Turkmenistan',
    ISO: 'TM',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Turks and Caicos Islands',
    ISO: 'TC',
    Regex: '^[Tt][Kk][Cc][Aa]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Tuvalu',
    ISO: 'TV',
    Regex: ''
  },
  {
    Country: 'Uganda',
    ISO: 'UG',
    Regex: ''
  },
  {
    Country: 'Ukraine',
    ISO: 'UA',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'United Arab Emirates',
    ISO: 'AE',
    Regex: ''
  },
  {
    Country: 'United Kingdom',
    ISO: 'GB',
    Regex: '^[A-Z]{1,2}[0-9R][0-9A-Z]?\\s*[0-9][A-Z-[CIKMOV]]{2}'
  },
  {
    Country: 'United States',
    ISO: 'US',
    Regex: '^\\b\\d{5}\\b(?:[- ]{1}\\d{4})?$'
  },
  {
    Country: 'Uruguay',
    ISO: 'UY',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'U.S. Virgin Islands',
    ISO: 'VI',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Uzbekistan',
    ISO: 'UZ',
    Regex: '^\\d{3} \\d{3}$'
  },
  {
    Country: 'Vanuatu',
    ISO: 'VU',
    Regex: ''
  },
  {
    Country: 'Vatican',
    ISO: 'VA',
    Regex: '^120$'
  },
  {
    Country: 'Venezuela',
    ISO: 'VE',
    Regex: '^\\d{4}(\\s[a-zA-Z]{1})?$'
  },
  {
    Country: 'Vietnam',
    ISO: 'VN',
    Regex: '^\\d{6}$'
  },
  {
    Country: 'Wallis and Futuna',
    ISO: 'WF',
    Regex: '^986\\d{2}$'
  },
  {
    Country: 'Yemen',
    ISO: 'YE',
    Regex: ''
  },
  {
    Country: 'Zambia',
    ISO: 'ZM',
    Regex: '^\\d{5}$'
  },
  {
    Country: 'Zimbabwe',
    ISO: 'ZW',
    Regex: ''
  }
]
