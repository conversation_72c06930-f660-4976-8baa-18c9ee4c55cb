export function definePriceObj(billing_model, fullPrice, discount, fe_quantity, isTrial = 0, trialPrice) {
  let discountedPrice = Number((fullPrice * discount).toFixed(2))
  if (isTrial) {
    discountedPrice = Number(trialPrice)
  }
  const savePrice = Number((fullPrice - discountedPrice).toFixed(2))
  const discountObj = billing_model.discount ? billing_model.discount : { percent: 0, amount: 0 }
  return {
    Discount: { ...discountObj },
    DiscountedPrice: {
      FormattedValue: window.ctrwowUtils.number.convertNumberToCurrency(discountedPrice),
      Value: discountedPrice,
      GlobalCurrencyCode: window.currencyCode
    },
    SavePrice: {
      FormattedValue: window.ctrwowUtils.number.convertNumberToCurrency(savePrice),
      Value: savePrice,
      GlobalCurrencyCode: window.currencyCode
    },
    FullRetailPrice: {
      FormattedValue: window.ctrwowUtils.number.convertNumberToCurrency(fullPrice),
      Value: fullPrice,
      GlobalCurrencyCode: window.currencyCode
    },
    UnitDiscountRate: {
      FormattedValue: window.ctrwowUtils.number.convertNumberToCurrency(discountedPrice / fe_quantity),
      Value: Number((discountedPrice / fe_quantity).toFixed(2)),
      GlobalCurrencyCode: window.currencyCode
    }
  }
}

function detectOneTime(billing_models) {
  let haveOnetime = false
  billing_models.forEach((billing_model) => {
    if (typeof billing_model.days === 'undefined') {
      haveOnetime = true
    }
  })

  return haveOnetime
}

export function generatePriceObj(billing_models, price, fe_quantity, trialPrice) {
  const pricing = []
  const fullPrice = Number(price.price)
  if (window.currency && window.currency.symbol_left) {
    window.formatedPrice = window.currency.symbol_left + fullPrice.toFixed(2)
  }
  if (window.currency && window.currency.symbol_right) {
    window.formatedPrice = fullPrice.toFixed(2) + window.currency.symbol_right
  }

  const haveOnetime = detectOneTime(billing_models)

  billing_models.forEach((billing_model) => {
    const days = billing_model.days ? '_' + billing_model.days : ''
    const fieldName = 'productPrices' + days

    let discount = 1
    if (billing_model.discount && billing_model.discount.percent) {
      discount = 1 - billing_model.discount.percent / 100
    }
    pricing[fieldName] = definePriceObj(billing_model, fullPrice, discount, fe_quantity, price.is_trial_product, trialPrice)
    if (fieldName !== 'productPrices' && typeof pricing.productPrices === 'undefined' && !haveOnetime) {
      pricing.productPrices = { ...pricing[fieldName] }
    }
    if (!billing_model.days && !pricing.productPrices_root) {
      pricing.productPrices_root = { ...pricing[fieldName] }
    }
  })
  return pricing
}
