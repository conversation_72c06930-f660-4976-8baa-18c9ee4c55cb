export function generateSubDomainAndKey(Builder, model) {
  try {
    const config = Builder.getConfig().data.settings
    if (!config.stickyio) {
      alert('Please Setup Sub Domain, Sticky API Account!')
      return
    }
    model.addAttributes({ stickyIODomain: config.stickyio.subDomain, xid: config.stickyio.key })
  } catch (e) {
    alert('Please Setup Sub Domain, Sticky API Account!')
    console.log(e)
  }
}
