export function errorHandler(form) {
  const firstError = form.querySelector('.error')
  firstError && firstError.scrollIntoView({ block: 'center', behavior: 'smooth' })
}
export function getFormData(form) {
  const formData = {}
  const inputs = $(form).find('input, textarea, select')
  if (inputs.length) {
    for (let i = 0; i < inputs.length; i++) {
      const value = $(inputs[i]).val()
      const type = $(inputs[i]).attr('type')
      if (type === 'radio' && !$(inputs[i]).prop('checked')) {
        break
      }
      if (value && inputs[i].name !== '') {
        formData[inputs[i].name] = value
      }
    }
  }
  return formData
}
export function submitHandler(forms) {
  let submitData = {}
  for (let i = 0; i < forms.length; i++) {
    const validator = $(forms[i]).validate()
    if ($(forms[i]).css('display') === 'none') {
      continue
    }
    const isValid = validator && validator.form()
    if (isValid && submitData) {
      const asyncValidator = window.ctrwowUtils.form.asyncValidateForm()
      if (!asyncValidator.isValid) {
        submitData = null
        alert(asyncValidator.message)
      } else {
        submitData[$(forms[i]).attr('name')] = getFormData(forms[i])
      }
    } else if (submitData) {
      submitData = null
      errorHandler(forms[i])
    }
  }
  return submitData || {}
}
