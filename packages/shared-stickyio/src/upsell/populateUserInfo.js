import { populateDataToNodeContent } from 'shared-checkout-flow/src/canReplaceContentWithPlaceholder'
import * as orderInfo from 'shared-checkout-flow/src/orderInfo/orderInfo'

function populateData() {
  const firstName = orderInfo.getUserFirstName()
  const lastName = orderInfo.getUserLastName()
  if (!firstName && !lastName) {
    return
  }

  populateDataToNodeContent((textContent) => textContent.replace(/\{firstname\}/gi, firstName).replace(/\{lastname\}/gi, lastName))
}

export function populateUserInfo() {
  if (/complete|interactive|loaded/.test(document.readyState)) {
    populateData()
  } else {
    document.addEventListener('DOMContentLoaded', populateData, false)
  }
}
