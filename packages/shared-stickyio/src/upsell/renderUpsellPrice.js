import { getDescendantProp } from '../helper/getDescendantProp'
import { getShippingFee } from '../helper/getShippingFee'

export function renderUpsellPrice(product, fieldObj) {
  Object.keys(fieldObj).forEach((key) => {
    const value = fieldObj[key]
    const itemEl = document.querySelector('body')
    const fields = itemEl.querySelectorAll(value)
    Array.prototype.slice.call(fields).forEach((field) => {
      if (product) {
        let textContent = ''
        textContent = getDescendantProp(product, key)
        if (key === 'shippingFee') {
          textContent = getShippingFee(product)
        }

        field.textContent = textContent
      }
    })
  })
}
