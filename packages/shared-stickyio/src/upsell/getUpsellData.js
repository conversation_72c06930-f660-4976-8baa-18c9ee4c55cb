export function getSelectedProduct() {
  return window.upsells[window.upsell_productindex || 0]
}
export function getBillingModelID(billing_models, orderInfo) {
  window.billingModelIndex = window.billingModelIndex || 0
  for (let i = 0, n = billing_models.length; i < n; i++) {
    if (billing_models[i].index === window.billingModelIndex) {
      return billing_models[i].id
    }
  }

  // Capture billingmode from checkout page
  if (window.billingModelIndex === 101) {
    return orderInfo.billingmode
  }
}
export function getUpsellData(orderInfo) {
  const product = getSelectedProduct()
  const url = window.location.href
  const path = url.substring(0, url.lastIndexOf('/')) + '/'

  let data = {
    previousOrderId: orderInfo.orderNumber,
    shippingId: product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex || 0].shippingMethodId : null,
    ipAddress: orderInfo.ipAddress,
    campaignId: product.campaignId,
    offers: [
      {
        offer_id: product.offerId,
        product_id: product.productId,
        billing_model_id: getBillingModelID(product.billing_models, orderInfo),
        quantity: product.quantity
      }
    ],
    xid: window.xid
  }
  if (window.localStorage.getItem('userPaymentType') === 'paypal') {
    data = {
      ...data,
      creditCardType: 'paypal',
      alt_pay_return_url: path + orderInfo.successUrl + window.location.search,
      tranType: 'Sale'
    }
  }
  return data
}
