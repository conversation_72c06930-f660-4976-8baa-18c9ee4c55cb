import { toggleClass } from '../helper/toggleClass'
import { findProductById } from './findProductById'

export function resetSingleStatus(list, index) {
  if (list.inputs[index] && list.inputs[index].checked) {
    const productItem = list.inputs[index].closest('.js-list-item')
    const product = findProductById(productItem.dataset.id)
    if (!product) {
      return
    }
    const productTitleEl = productItem.querySelector('.js-title')
    product.productName = productTitleEl ? productTitleEl.textContent : product.productName
    list.selectedItem = productItem
    product && window.ctrwowCheckout.checkoutData.setProduct(product)
  }
  toggleClass(list.items[index], 'list-item--checked', list.inputs[index] ? list.inputs[index].checked : false)
}

export function resetCheckedStatus(list) {
  for (var i = 0; i < list.items.length; i++) {
    resetSingleStatus(list, i)
  }
}

export function resetFocusStatus(list, index, bool) {
  toggleClass(list.items[index], 'list-item--focus', bool)
}
