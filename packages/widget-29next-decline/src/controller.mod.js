/**
 * <AUTHOR> <<EMAIL>>
 * widget-29next-decline - Widget controller
 * @param element<DomElement>
 * @param props<Object>
 */
import * as paymentInfo from 'shared-29next/src/payment/paymentInfo'
import * as decline from 'shared-29next/src/decline/'

export default function initComponent(element, props) {
  const elm = element
  let orderInfo = window.localStorage.getItem('orderInfo')
  // let orderNumber = window.ctrwowUtils.link.queryURLParameter('orderNumber') || ''
  window.localStorage.removeItem('orderOnePageFlag')
  const paymentType = paymentInfo.getUserPaymentType()

  if (orderInfo) {
    orderInfo = JSON.parse(orderInfo)
    // orderNumber = orderInfo.orderNumber
  }
  function displayMessage() {
    try {
      let paymentName = ''
      switch (paymentType) {
        case paymentInfo.PAYMENT_METHOD.IDEAL:
          paymentName = 'Ideal'
          break
        case paymentInfo.PAYMENT_METHOD.SOFORT:
          paymentName = 'Sofort'
          break
        case paymentInfo.PAYMENT_METHOD.STRIPE:
          paymentName = 'Stripe'
          break
      }

      if (!paymentInfo.isPaidByCreditCard()) {
        const paymentSection = elm.querySelector('.payment-section')
        if (paymentSection && paymentName) {
          paymentSection.innerHTML = paymentSection.innerHTML.replace(/Paypal/g, paymentName)
        }
        window.q('.cc').display('none')
        window.q('.others').display('block')
      }
    } catch (e) {
      console.log(e)
    }
  }
  displayMessage()

  function init() {
    if (orderInfo && orderInfo.upsellIndex > 0) {
      decline.redirectToNextPage(orderInfo.confirmUrl)
    }

    try {
      elm.querySelector('.go-back-btn').addEventListener('click', (e) => {
        e.preventDefault()
        decline.goBackFromDeclinePage()
      })
    } catch (e) {
      console.log(e)
    }
  }

  init()
}
