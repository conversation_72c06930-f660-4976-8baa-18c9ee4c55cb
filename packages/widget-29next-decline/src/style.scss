/** widget-decline-sticky **/
#id {
  font-family: Helvetica;
}
.go-back-btn {
  padding: 15px 35px;
  border: 0;
  background: 0 0;
  background-color: #7ad712;
  background-image: linear-gradient(to bottom, #7ad712 0, #5fa80e 100%);
  border-bottom: 4px #4d870b solid;
  font-size: 19px;
  font-weight: 500;
  border-radius: 3px;
  &:hover {
    background: linear-gradient(to bottom,#a8ff0f 0,#a8ff0f 100%);
  }
}
.title {
  text-align: center;
  font-size: 24px;
  padding: 0;
  margin: 0;
  .text {
    padding: 0;
    margin: 0;
  }
  @media (max-width: 767px) {
    font-size: 18px;
  }
}
.desc {
  text-align: center;
  font-size: 24px;
  color: #808080;
  padding: 0;
  margin: 0;
  @media (max-width: 767px) {
    font-size: 18px;
  }
}
.button-group {
  margin-top: 50px;
  text-align: center;
}
.others {
  display: none;
  &[data-gjs-type] {
    display: block;
  }
}
