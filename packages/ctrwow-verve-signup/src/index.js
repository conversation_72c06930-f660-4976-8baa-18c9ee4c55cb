;(function () {
  class SignupPage {
    constructor() {
      this.urlApi = 'https://vervedirect-dev-api.azurewebsites.net'
      if (window.location.hostname === 'www.vervedirect.com') {
        this.urlApi = 'https://vervedirect-prod-api.azurewebsites.net'
      }
    }

    fetchAM() {
      $.ajax({
        url: `${this.urlApi}/api/public/affiliateManagers`,
        method: 'GET',
        success: function (response) {
          const data = response.data
          if (response.success === true && data.length > 0) {
            const ByAMHTML = document.querySelector('#ByAM')
            data.map((item) => {
              const selected = ''
              ByAMHTML.innerHTML += `<option ${selected} value="${item.id}">${item.name}</option>`
            })
          }
        },
        error: function (response) {
          const dataError = response.responseJSON.errors
          let errorMess = ''
          dataError.map((item) => {
            errorMess += `${item.message}\n`
          })
          alert(`${errorMess}`)
        }
      })
    }

    createReturnUrl() {
      // TODO
      // $('div[name="retURL"]').val(document.location.toString() + (document.location.search.indexOf('?') > -1 ? '&' : '?') + 'thankyou=1')
    }

    affiliateForm() {
      const $this = this
      $('#submitFormSignUp').click(function (e) {
        // const captchaResponse = grecaptcha.getResponse()
        const captchaResponse = 1
        if (captchaResponse === 0) {
          alert('Please prove that you are a not a robot by selecting the checkbox!!')
          return false
        } else {
          const txtReferralNotes = $('#ReferralNotes').val()
          const txtCompanyName = $('#company').val()
          const txtAddress = $('#street').val()
          const txtCity = $('#city').val()
          const txtState = $('#stateAjax option:selected').val()
          const txtZip = $('#zip').val()
          const cbCountry = $('#countriesAjax option:selected').val()
          const txtWebsite = $('#URL').val()
          const cbPaymentModel = $('#PaymentModel option:selected').val()
          const cbPriCategory = $('#PriCategory option:selected').val()
          const txtLegalEntity = $('#LegalEntity option:selected').val()
          const txtComments = $('#description').val()
          const tbMainFirst = $('#first_name').val()
          const tbMainLast = $('#last_name').val()
          const tbMainTitle = $('#title').val()
          const tbMainPhone = $('#phone').val()
          const tbMainPhone2 = $('#mobile').val()
          const tbMainEmail = $('#email').val()
          const tbMainIM = $('#IM').val()
          const cbMainIMService = $('#IMService option:selected').val()
          const cbPaymentTo = $('#PaymentTo option:selected').val()
          const cbCurrency = $('#currency option:selected').val()
          const cbTaxClass = $('#TaxClass option:selected').val()
          const txtTaxID = $('#TaxID').val()

          if (
            txtCompanyName === '' ||
            txtAddress === '' ||
            txtCity === '' ||
            txtState === '' ||
            txtZip === '' ||
            cbCountry === '' ||
            tbMainFirst === '' ||
            tbMainLast === '' ||
            tbMainPhone === '' ||
            tbMainEmail === '' ||
            txtTaxID === '' ||
            tbMainIM === ''
          ) {
            // alert('Please complete all the required fields .')
            return false
          }

          $('[required]').attr('role', 'checked')
          if ($this.validateForm()) {
            const fromForm = window.ctrwowUtils.link.getParameterByName('type')
            let ByAffiliate = ''
            let ByAM = 'David'
            let ByAMID = '34'
            if ($('#ByAM').val() !== '') {
              ByAMID = $('#ByAM').val()
              ByAM = $('#ByAM  option:selected').text()
            } else if ($('#ByAffiliate').val() !== '') {
              ByAffiliate = $('#ByAffiliate').val()
            }
            window.ctrwowFormUtils.showAjaxLoading()
            $.ajax({
              url: `${this.urlApi}/api/public/affiliates`,
              contentType: 'application/json;charset=UTF-8',
              data: JSON.stringify({
                source: fromForm,
                company: {
                  company: txtCompanyName.toString(),
                  street: txtAddress.toString(),
                  city: txtCity.toString(),
                  state: txtState.toString(),
                  country: cbCountry.toString(),
                  postCode: txtZip.toString(),
                  website: txtWebsite.toString()
                },
                marketing: {
                  paymentModel: cbPaymentModel.toString(),
                  primaryCategory: cbPriCategory.toString(),
                  comments: txtComments.toString(),
                  paymentTo: cbPaymentTo.toString(),
                  currency: cbCurrency.toString(),
                  taxClass: cbTaxClass.toString(),
                  taxId: txtTaxID.toString()
                },
                contact: {
                  firstName: tbMainFirst.toString(),
                  lastName: tbMainLast.toString(),
                  jobTitle: tbMainTitle.toString(),
                  workPhone: tbMainPhone.toString(),
                  cellPhone: tbMainPhone2.toString(),
                  email: tbMainEmail.toString(),
                  imService: cbMainIMService.toString(),
                  im: tbMainIM.toString(),
                  legalEntityType: txtLegalEntity.toString()
                },
                referral: {
                  affiliateName: ByAffiliate,
                  affiliateManagerName: ByAM,
                  affiliateManagerID: ByAMID,
                  referralNotes: txtReferralNotes.toString()
                }
              }),
              type: 'POST',
              success: function (response) {
                if (response.success === true) {
                  window.location.href = 'thank-you.html'
                } else {
                  alert('Something went wrong!')
                }
                $('.preloading-wrapper').css({
                  display: 'none',
                  opacity: 1
                })
              },
              error: function (response) {
                if (response.responseJSON && response.responseJSON.errors) {
                  const dataError = response.responseJSON.errors
                  let errorMess = ''
                  if (dataError) {
                    dataError.map((item) => {
                      errorMess += `${item.message}\n`
                    })
                    alert(`${errorMess}`)
                  }
                }
                $('.preloading-wrapper').css({
                  display: 'none',
                  opacity: 1
                })
              }
            })
          } else {
            alert('Please complete all the required fields!')
            e.preventDefault()

            return false
          }
        }
      })
    }

    validateEmail(email) {
      const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return re.test(email)
    }

    validatePhone(phone) {
      const re = /^[+]*[(]{0,1}[0-9]{1,3}[)]{0,1}[-\s\./0-9]*$/g
      return re.test(phone)
    }

    validateForm() {
      var isValid = true
      let countFalse = 0

      $('#affiliate-signup-form [required]').each(function (index, elm) {
        var inputVal = $(elm).val().trim()
        var type = $(elm).attr('type') ? $(elm).attr('type') : $(elm).prop('tagName').toLowerCase()

        elm = $(elm)
        switch (type) {
          case 'email':
            if (!this.validateEmail(inputVal)) {
              this.showError(elm, true)
              isValid = false
            } else {
              this.showError(elm, false)
            }
            break
          case 'tel':
            if (!this.validatePhone(inputVal)) {
              this.showError(elm, true)
              isValid = false
            } else {
              this.showError(elm, false)
            }
            break
          case 'checkbox':
            if (!elm.prop('checked')) {
              this.showError(elm, true)
              isValid = false
            } else {
              this.showError(elm, false)
            }
            break
          default:
            if (!inputVal.length) {
              this.showError(elm, true)
              isValid = false
            } else {
              this.showError(elm, false)
            }
        }
        if (isValid === false && countFalse === 0) {
          const IdAtt = $(elm).attr('id')
          $([document.documentElement, document.body]).animate(
            {
              scrollTop: $(`#${IdAtt}`).offset().top - 30
            },
            2000
          )
          countFalse = 1
        }
      })

      return isValid
    }

    showError(elm, isShow) {
      if (isShow) {
        // is Valid
        elm.addClass('error').closest('.fieldset').addClass('errors')
      } else {
        // Not valid
        elm.removeClass('error').closest('.fieldset').removeClass('errors')
      }
    }

    init() {
      document.addEventListener('DOMContentLoaded', () => {
        this.createReturnUrl()
        window.ctrwowFormUtils.loadCountries('countriesAjax')
        this.fetchAM()
        this.affiliateForm()
      })
    }
  }
  const signupPage = new SignupPage()
  signupPage.init()
})()

// affiliate.globalFct()
