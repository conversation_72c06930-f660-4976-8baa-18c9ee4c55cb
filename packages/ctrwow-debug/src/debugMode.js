const CTRWOW_DEBUG_NAME = 'ctr__debugMode'

const updateDebugModeData = (host) => {
  const d = new Date()
  d.setTime(d.getTime() + 24 * 60 * 60 * 1000 * (host ? 1 : -1))
  document.cookie = `${CTRWOW_DEBUG_NAME}=${host};${'expires=' + d.toUTCString()};path=/`
}

export const start = (host = 'http://localhost:9000') => {
  console.log('host' + host)
  console.log('host' + host)
  console.log('host' + host)
  console.log('host' + host)
  console.log('host' + host)
  fetch([`${host}/debuggingPackages.json`])
    .then((res) => res.json())
    .then((res) => {
      updateDebugModeData([host, ...res].join('a11a'))
      console.log(`======== \n Debugging Host: ${host} \n Debugging Packages: ${res} \n================ `)
    })
}

export const stop = (_) => updateDebugModeData()

export const getDebugInfo = () => {
  const found = (document.cookie.split(';') || []).find((item) => item.trim().indexOf(CTRWOW_DEBUG_NAME) === 0)

  return (found && found.split('=')[1].trim()) || ''
}

export const getDebugHost = (info = getDebugInfo()) => info && info.split('a11a')[0]

const isDebugging = (packageName) => getDebugInfo().indexOf(packageName) > 0

//
export const isRun = (el, packageName) => {
  let retry = null

  if (!packageName || !isDebugging(packageName)) {
    return false
  }

  runComponent(el)

  if (packageName) {
    window.ctrwowUtils.getDependencies([`${getDebugHost()}/${packageName}.js`]).catch((e) => {
      console.log(e)
      console.log('[ctrwowUtils][debugMode] load js code fail')
    })
    return true
  }

  function runComponent(el) {
    if (window[packageName]) {
      window[packageName] && window[packageName](el)
      retry && clearInterval(retry)
      return
    }

    if (!retry) {
      retry = setInterval(() => runComponent(el), 20)
    }
  }
}

export const run = isRun
