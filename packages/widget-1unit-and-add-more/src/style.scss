$color_1: #fff;
$color_2: #898989;
$color_3: #1f8305;
$color_4: #1F8305;
$color_5: #2db4ed;
$color_6: #000;
$color_7: #ed5214;
$color_8: #1a1a1a;
$background-color_1: #29af5c;
$background-color_2: #fff7b2;
$background-color_3: unset;
$background-color_4: #fff2e0;

* {
	box-sizing: border-box;
}
.hidden {
	display: none !important;
}
.js-hidden {
	display: none !important;
}
.one-unit-widget {
	min-height: 50px;
	position: relative;
	border: 4px solid #30bd51;
	box-shadow: 0 3px 9px 2px #0000001f;
	margin: 0 0 25px;
	border-radius: 5px 5px 5px 5px;
	padding: 4px;
	.loading {
		&:before {
			content: "";
			background-image: url(https://d16hdrba6dusey.cloudfront.net/sitecommon/images/loading-price-v1.gif);
			display: inline-block;
			width: 20px;
			height: 10px;
			background-size: contain;
			background-repeat: no-repeat;
		}
	}
	[type="radio"] {
		&:checked {
			+ {
				span {
					line-height: 36px;
					padding-right: 80px;
					padding-left: 5px;
					&:after {
						box-shadow: 0 3px 9px 2px rgba(0,0,0,0.12);
						width: 24px;
						height: 24px;
						top: 5px;
						left: unset;
						right: 8px;
						background: #ffffff;
					}
					&:before {
						content: 'ON';
						width: 55px;
						background: #1F80EF;
						color: $color_1;
						text-align: left;
						line-height: 34px;
						padding-left: 15px;
						font-size: 16px;
						font-weight: 600;
						font-style: unset;
						position: absolute;
						height: 30px;
						border: none;
						border-radius: 15px;
						right: 5px;
						left: unset;
						top: 0px;
					}
				}
			}
		}
		&:not(:checked) {
			+ {
				span {
					line-height: 36px;
					padding-right: 80px;
					padding-left: 5px;
					&:after {
						box-shadow: 0 3px 9px 2px rgba(0,0,0,0.12);
						opacity: 1;
						-webkit-transform: unset;
						transform: unset;
						width: 24px;
						height: 24px;
						top: 3px;
						left: unset;
						right: 47px;
						background: #ffffff;
					}
					&:before {
						content: 'OFF';
						background: #cccccc;
						width: 60px;
						color: $color_2;
						text-align: right;
						line-height: 34px;
						padding-right: 8px;
						font-size: 16px;
						font-weight: 600;
						font-style: unset;
						position: absolute;
						height: 30px;
						border: none;
						border-radius: 15px;
						right: 5px;
						left: unset;
						top: 0px;
					}
				}
			}
		}
	}
	[type=radio] {
		&:not(:checked)+span {
			font-style: italic;
			font-weight: 600;
		}
	}
	.toggle-desc {
		width: calc(100% - 85px);
	}
	.add-more-unit.active {
		font-weight: 600;
		font-style: italic;
		font-weight: 600;
		font-style: italic;
		background-color: $background-color_3;
	}
	.add-more-unit {
		min-height: 50px;
		padding: 10px 0 10px 13px;
		margin: 0 0 10px;
		position: relative;
		background-color: $background-color_2;
		border-radius: 5px 5px 5px 5px;
		display: flex;
		align-items: center;
		cursor: pointer;
		.toggle-desc {
			margin: 0;
			font-weight: 600;
		}
		.toggle {
			margin: 0;
			font-size: 17px;
			line-height: 20px;
			width: 65px;
			height: 30px;
			position: relative;
			span {
				pointer-events: none;
				height: 30px;
			}
			input {
				&:not(:checked) {
					position: absolute;
					left: -9999px;
				}
				&:checked {
					position: absolute;
					left: -9999px;
				}
				&:not(:checked)+span {
					font-style: normal;
					font-weight: 600;
					pointer-events: none;
					height: 30px;
					position: relative;
					color: $color_6;
					line-height: 36px;
					padding-right: 80px;
					padding-left: 5px;
					font-style: normal;
					&:before {
						content: 'OFF';
						background: #cccccc;
						width: 60px;
						color: $color_2;
						text-align: right;
						line-height: 33px;
						padding-right: 8px;
						font-size: 14px;
						font-weight: 600;
						font-style: unset;
						position: absolute;
						height: 30px;
						border: none;
						border-radius: 15px;
						right: 5px;
						left: unset;
						top: 0px;
					}
					&:after {
						font-weight: 600;
						line-height: 36px;
						box-shadow: 0 3px 9px 2px rgba(0, 0, 0, 0.12);
						width: 24px;
						height: 24px;
						top: 3px;
						left: unset;
						right: 45px;
						background: #ffffff;
					}
				}
				&:checked+span {
					padding-left: 5px;
					padding-right: 80px;
					line-height: 36px;
					position: relative;
					font-style: normal;
					&:before {
						content: 'ON';
						width: 52px;
						background: #1F80EF;
						color: $color_1;
						text-align: left;
						line-height: 33px;
						padding-left: 15px;
						font-size: 14px;
						font-weight: 600;
						font-style: unset;
						position: absolute;
						height: 30px;
						border: none;
						border-radius: 15px;
						right: 5px;
						left: unset;
						top: 0px;
					}
					&:after {
						content: '';
						-webkit-transform: scale(1);
						transform: scale(1);
						box-shadow: 0 3px 9px 2px rgba(0, 0, 0, 0.12);
						width: 24px;
						height: 24px;
						top: 3px;
						left: unset;
						right: 8px;
						background: #ffffff;
					}
				}
			}
			input+span {
				display: block;
				&:after {
					opacity: 1;
					transform: unset;
					content: '';
					position: absolute;
					border-radius: 100%;
					transition: all .2s ease;
				}
			}
		}
	}
}
.one-unit-content {
	flex: 1 0 0px;
	display: flex;
	width: 100%;
	align-items: flex-start;
	justify-content: flex-start;
	margin: 0;
	padding: 15px;
	flex-direction: column;
	.order-now-active {
		display: block;
		text-decoration: none;
		text-align: center;
		width: 100%;
		border-radius: 11px 11px 11px 11px;
		font-weight: 700;
		line-height: 20px;
		color: $color_6;
		max-width: 100%;
		font-size: 18px;
		padding: 15px 5px;
		border-radius: 5px 5px 5px 5px;
		background-color: $background-color_1;
		margin: 10px auto 0;
	}
}
.one-unit-desc {
	padding: 0;
	max-width: 100%;
	margin: 0;
	width: 100%;
	text-align: left;
	display: flex;
	flex-direction: column;
	.headline {
		margin: 0 0 16px;
		.product-name {
			text-align: center;
			font-size: 18px;
			line-height: 20px;
			margin: 0;
			font-weight: 700;
		}
		.limited-time {
			padding: 6px 0 0;
			margin: 0;
			color: $color_3;
			font-size: 16px;
			line-height: 30px;
			text-align: center;
		}
		.js-coupon-text {
			font-size: 18px;
			font-weight: 600;
			line-height: 27px;
			color: $color_4;
		}
	}
	.product-price-and-img {
		margin: 0;
		width: 100%;
	}
	.product-img {
		display: flex;
		align-items: center;
		text-align: center;
		margin: 15px 0;
		position: static;
		width: 100%;
		right: 0;
		min-height: auto;
		img {
			margin: 0 auto;
		}
	}
	.product-price {
		max-width: 100%;
		display: flex;
		align-items: flex-start;
		flex-direction: column;
		font-size: 16px;
		margin: 0;
		.js-fullprice-1unit, .js-current-fullprice {
			margin: 0 auto;
			text-decoration: line-through;
			line-height: 18px;
			height: 18px;
		}
		.discount-price {
			text-align: center;
			font-size: 24px;
			margin: 0;
			font-size: 20px;
			line-height: 24px;
			display: inline;
			color: $color_5;
			font-weight: 700;
			width: 100%;
		}
		.js-discount-price-1unit, .js-current-discount-price, .js-current-unit-price {
			font-size: 30px;
			line-height: 36px;
			font-weight: 800;
		}
	}
}
.fire-group {
	padding: 0 10px 10px;
	background-color: $background-color_4;
	width: 100%;
	.headline {
		min-height: 50px;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		color: $color_7;
		font-weight: 700;
		font-size: 17px;
		line-height: 20px;
		letter-spacing: -.5px;
		.sellingout {
			margin-top: 5px;
			margin-left: 5px;
		}
	}
	.fire-desc {
		line-height: 19px;
		color: $color_8;
		font-style: italic;
		font-weight: 600;
		margin: 0;
		text-align: left;
		font-size: 16px;
	}
}
