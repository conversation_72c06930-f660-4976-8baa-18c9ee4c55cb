/**
 * @widgetName widget-1unit-and-add-more
 * @version 1.0.0
 * @see WIDGET_STORY
 * <AUTHOR> <PERSON>ran <<EMAIL>>
 */

// import addChildComponents from './helpers' // OPTIONAL

import { widgetControllerWrapper } from 'shared-gjs-utils/src/widgetControllerWrapper'
import componentController from './controller.mod'
// import getViewDefinition from './viewDefinition'
import widgetTemplate from './template.html'
import widgetStyle from './style.scss'
import { WIDGET_SETTINGS } from './settings'

export default (editor, config) => {
  const { widgetName, widgetTypeId, packageName } = config

  // add helpers - in any - which used as parts of main widget
  // addChildComponents(editor, config, widgetTypeId)

  // ADD MAIN COMPONENT
  const domComponents = editor.DomComponents
  domComponents.addType(widgetTypeId, {
    model: {
      defaults: {
        name: widgetName,
        css: widgetStyle.toString(),
        ...WIDGET_SETTINGS,
        components: widgetTemplate,
        // components: widgetTemplate.replaceAll(/PARENT_TYPE_ID/g, widgetTypeId),
        // DEV-NOTE - must defined [script-props] to support binding script as a function
        'script-props': [],
        script: widgetControllerWrapper(componentController, { packageName })
      }
    }

    // view: getViewDefinition(editor, config)
  })
}
