/**
 * <AUTHOR> <<EMAIL>>
 * widget-1unit-and-add-more - settings
 */

export const WIDGET_SETTINGS = {
  // classes: [], // class for main component wrapper
  attributes: {
    yourAtt: 'default-value' // trait without [changeProp]
  },
  yourProp: 'default-value', // trait with [changeProp] = 1
  traits: [
    {
      label: 'Map Unit to Pid',
      name: 'map-unit-to-pid',
      type: 'text'
    }
  ],
  'script-props': []
}
