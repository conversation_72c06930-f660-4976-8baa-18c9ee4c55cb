/**
 * <AUTHOR> <<EMAIL>>
 * widget-1unit-and-add-more - Widget controller
 * @param element<DomElement>
 * @param props<Object>
 */

export default function initComponent(element, props) {
  let mapUnit2Pid = element.getAttribute('map-unit-to-pid') || null
  let firstPro = null

  function addLoading() {
    if (window.ctrwowUtils.isBuilderMode()) return
    const elmPrices = element.querySelectorAll(`.one-unit-widget .price-elm`)
    elmPrices.forEach((elm) => {
      elm.innerText = ''
      elm.classList.add('loading')
    })
  }
  addLoading()

  function showAddMoreOption() {
    if (window.ctrwowUtils.isBuilderMode()) {
      const elmAddMoreUnit = element.querySelectorAll('.add-more-unit')
      elmAddMoreUnit.forEach((item) => {
        item.classList.remove('js-hidden')
      })
    }
  }
  showAddMoreOption()

  function bindProductPrice(pro, firstPro, prevProduct) {
    if (!pro || window.ctrwowUtils.isBuilderMode()) return
    const qty = pro.quantity
    const elmFullPrices = element.querySelectorAll(`.one-unit-widget .js-fullprice-${qty}unit`)
    elmFullPrices.forEach((item) => {
      item.innerText = pro.productPrices.FullRetailPrice.FormattedValue
      item.classList.remove('loading')
    })

    const elmDiscountPrices = element.querySelectorAll(`.one-unit-widget .js-discount-price-${qty}unit`)
    elmDiscountPrices.forEach((item) => {
      let discountFormat = pro.productPrices.DiscountedPrice.FormattedValue
      if (firstPro) {
        let discount = pro.productPrices.DiscountedPrice.Value
        if (prevProduct) {
          discount = discount - prevProduct.productPrices.DiscountedPrice.Value
        }
        discountFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(discount, discountFormat)
        // discount = discount - firstPro.productPrices.DiscountedPrice.Value
        // discountFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(discount, discountFormat)
      }
      item.innerText = discountFormat
      item.classList.remove('loading')

      const elmAddMoreParent = item.closest('.add-more-unit')
      if (elmAddMoreParent) elmAddMoreParent.setAttribute('data-pid', pro.productId)
    })

    const elmSavePrices = element.querySelectorAll(`.one-unit-widget .js-save-price-${qty}unit`)
    elmSavePrices.forEach((item) => {
      const savePrice = pro.productPrices.FullRetailPrice.Value - pro.productPrices.DiscountedPrice.Value
      const savePriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(savePrice, pro.productPrices.DiscountedPrice.FormattedValue)
      item.innerText = savePriceFormat
      item.classList.remove('loading')
    })

    const elmUnitPrices = element.querySelectorAll(`.one-unit-widget .js-unit-price-${qty}unit`)
    elmUnitPrices.forEach((item) => {
      if (pro.productPrices.UnitDiscountRate) {
        item.innerText = pro.productPrices.UnitDiscountRate.FormattedValue
      } else {
        const unitPrice = pro.productPrices.DiscountedPrice.Value / pro.productPrices.quantity
        const unitPriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(unitPrice, pro.productPrices.DiscountedPrice.FormattedValue)
        item.innerText = unitPriceFormat
      }
      item.classList.remove('loading')

      const elmAddMoreParent = item.closest('.add-more-unit')
      if (elmAddMoreParent) elmAddMoreParent.setAttribute('data-pid', pro.productId)
    })
  }

  function getProductPrice(prices) {
    try {
      if (mapUnit2Pid) {
        mapUnit2Pid = JSON.parse(mapUnit2Pid)
      }

      if (!prices || prices.length === 0) return
      let prevProduct = null
      if (mapUnit2Pid) {
        // const qtyS = Object.keys(mapUnit2Pid)
        const pidS = Object.values(mapUnit2Pid)

        pidS.forEach((pid) => {
          const pro = prices.find((item) => {
            return item.productId.toString() === pid.toString()
          })

          bindProductPrice(pro, firstPro, prevProduct)
          prevProduct = pro
          if (!firstPro) firstPro = pro
        })
      } else {
        const elmAddOns = element.querySelectorAll('.add-more-unit')
        const totalQty = (elmAddOns && elmAddOns.length + 1) || 0
        for (let i = 1; i <= totalQty; i++) {
          const pro = prices.find((item) => {
            return item.quantity === i
          })
          bindProductPrice(pro, firstPro, prevProduct)
          prevProduct = pro
          if (!firstPro) firstPro = pro
        }
      }
    } catch (error) {
      console.log('1unit and add more widget: bind price error' + error)
    }
  }

  function updatePriceAndImg(unitIndex = null) {
    // Update image
    if (unitIndex) {
      const elmImgFirst = element.querySelector('.product-price-and-img .product-img-first')
      const elmImgSecond = element.querySelector('.product-price-and-img .product-img-second')
      const elmImgThird = element.querySelector('.product-price-and-img .product-img-third')
      const elmImgFourth = element.querySelector('.product-price-and-img .product-img-fourth')
      elmImgFirst && elmImgFirst.classList.add('js-hidden')
      elmImgSecond && elmImgSecond.classList.add('js-hidden')
      elmImgThird && elmImgThird.classList.add('js-hidden')
      elmImgFourth && elmImgFourth.classList.add('js-hidden')
      switch (unitIndex) {
        case 1:
          elmImgFirst && elmImgFirst.classList.remove('js-hidden')
          break
        case 2:
          elmImgSecond && elmImgSecond.classList.remove('js-hidden')
          break
        case 3:
          elmImgThird && elmImgThird.classList.remove('js-hidden')
          break
        case 4:
          elmImgFourth && elmImgFourth.classList.remove('js-hidden')
          break
        default:
      }

      if (!elmImgSecond && !elmImgThird && !elmImgFourth) {
        const proImg = element.querySelector('.product-price-and-img .product-img')
        proImg && proImg.classList.remove('js-hidden')
      }
    }
    // End Update image

    // Update Price
    const pro = window.ctrwowCheckout.checkoutData.getProduct()
    const elmCurrentFullPrices = element.querySelectorAll('.product-price-and-img .js-current-fullprice')
    elmCurrentFullPrices.forEach((item) => {
      item.innerText = pro.productPrices.FullRetailPrice.FormattedValue
      item.classList.remove('loading')
    })
    const elmCurrentDiscountPrices = element.querySelectorAll('.product-price-and-img .js-current-discount-price')
    elmCurrentDiscountPrices.forEach((item) => {
      item.innerText = pro.productPrices.DiscountedPrice.FormattedValue
      item.classList.remove('loading')
    })
    const elmCurrentSavePrices = element.querySelectorAll('.product-price-and-img .js-current-save-price')
    elmCurrentSavePrices.forEach((item) => {
      const savePrice = pro.productPrices.FullRetailPrice.Value - pro.productPrices.DiscountedPrice.Value
      const savePriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(savePrice, pro.productPrices.DiscountedPrice.FormattedValue)
      item.innerText = savePriceFormat
      item.classList.remove('loading')
    })
    const elmCurrentUnitPrices = element.querySelectorAll('.product-price-and-img .js-current-unit-price')
    elmCurrentUnitPrices.forEach((item) => {
      if (pro.productPrices.UnitDiscountRate) {
        item.innerText = pro.productPrices.UnitDiscountRate.FormattedValue
      } else {
        const unitPrice = pro.productPrices.DiscountedPrice.Value / pro.productPrices.quantity
        const unitPriceFormat = window.ctrwowUtils.number.formaterNumberByFormattedValue(unitPrice, pro.productPrices.DiscountedPrice.FormattedValue)
        item.innerText = unitPriceFormat
      }
      item.classList.remove('loading')
    })

    const elmCurrentQtys = element.querySelectorAll('.one-unit-widget .current-qty')
    elmCurrentQtys.forEach((item) => {
      item.innerText = pro.quantity
      item.classList.remove('loading')
    })

    const elmPlus = element.querySelector('.one-unit-widget .plus')
    if (pro.quantity > 1) {
      elmPlus && elmPlus.classList.remove('js-hidden')
    } else {
      elmPlus && elmPlus.classList.add('js-hidden')
    }
    // End Update Price
  }

  function showMoreOption(unitIndex, isON) {
    const elmAddOnSecondPro = element.querySelector('.one-unit-widget .second-pro')
    const elmAddOnThridPro = element.querySelector('.one-unit-widget .third-pro')
    const elmInputThridPro = elmAddOnThridPro ? elmAddOnThridPro.querySelector('input') : null
    const elmAddOnFourthPro = element.querySelector('.one-unit-widget .fourth-pro')
    const elmInputFourthPro = elmAddOnFourthPro ? elmAddOnFourthPro.querySelector('input') : null
    switch (unitIndex) {
      case 2:
        if (isON) {
          elmAddOnThridPro && elmAddOnThridPro.classList.remove('js-hidden')
          const pid = elmAddOnSecondPro.getAttribute('data-pid')
          const productItem = document.querySelector(`.js-list-item[data-id='${pid}']`)
          productItem && productItem.click()
          updatePriceAndImg(unitIndex)
        } else {
          elmAddOnThridPro && elmAddOnThridPro.classList.add('js-hidden')
          elmAddOnFourthPro && elmAddOnFourthPro.classList.add('js-hidden')
          elmInputThridPro && (elmInputThridPro.checked = false)
          elmInputFourthPro && (elmInputFourthPro.checked = false)
          elmAddOnThridPro && elmAddOnThridPro.classList.remove('active')
          elmAddOnFourthPro && elmAddOnFourthPro.classList.remove('active')

          // first product
          const productItem = document.querySelector(`.js-list-item[data-id='${firstPro.productId}']`)
          productItem && productItem.click()
          updatePriceAndImg(unitIndex - 1)
        }
        break
      case 3:
        if (isON) {
          elmAddOnFourthPro && elmAddOnFourthPro.classList.remove('js-hidden')

          // 3 unit
          const pid = elmAddOnThridPro.getAttribute('data-pid')
          const productItem = document.querySelector(`.js-list-item[data-id='${pid}']`)
          productItem && productItem.click()
          updatePriceAndImg(unitIndex)
        } else {
          elmAddOnFourthPro && elmAddOnFourthPro.classList.add('js-hidden')
          elmInputFourthPro && (elmInputFourthPro.checked = false)
          elmAddOnFourthPro && elmAddOnFourthPro.classList.remove('active')

          // 2 unit
          const pid = elmAddOnSecondPro.getAttribute('data-pid')
          const productItem = document.querySelector(`.js-list-item[data-id='${pid}']`)
          productItem && productItem.click()
          updatePriceAndImg(unitIndex - 1)
        }
        break
      case 4:
        if (isON) {
          // 4 unit
          const pid = elmAddOnFourthPro.getAttribute('data-pid')
          const productItem = document.querySelector(`.js-list-item[data-id='${pid}']`)
          productItem && productItem.click()
          updatePriceAndImg(unitIndex)
        } else {
          // 3 unit
          const pid = elmAddOnThridPro.getAttribute('data-pid')
          const productItem = document.querySelector(`.js-list-item[data-id='${pid}']`)
          productItem && productItem.click()
          updatePriceAndImg(unitIndex - 1)
        }
        break
    }
  }

  function triggerToggleClick() {
    if (window.ctrwowUtils.isBuilderMode()) return
    const elmToggles = element.querySelectorAll('.add-more-unit')
    elmToggles.forEach((item) => {
      item.addEventListener('click', function (e) {
        const currentElm = e.currentTarget
        currentElm.classList.toggle('active')
        const input = currentElm.querySelector('input')
        let unitIndex = 1
        if (currentElm.classList.contains('second-pro')) {
          unitIndex = 2
        } else if (currentElm.classList.contains('third-pro')) {
          unitIndex = 3
        } else if (currentElm.classList.contains('fourth-pro')) {
          unitIndex = 4
        }

        let toggleStatus = true
        if (currentElm.classList.contains('active')) {
          input && (input.checked = true)
          toggleStatus = true
        } else {
          input && (input.checked = false)
          toggleStatus = false
        }
        showMoreOption(unitIndex, toggleStatus)
      })
    })
  }

  window.ctrwowUtils.getDependencies([window.ctrwowUtils.getCtrLibLink('ctrwowCheckout')], { delayUntilInteract: false }).then(() =>
    window.ctrwowCheckout.ready().then(() => {
      window.ctrwowUtils.events.on('onAfterActivePopup', function () {
        firstPro = null
        const prices = JSON.parse(localStorage.getItem('products'))
        mapUnit2Pid = element.getAttribute('map-unit-to-pid') || null
        getProductPrice(prices)
        const elmLimitedTime = element.querySelector('.limited-time')
        elmLimitedTime && elmLimitedTime.classList.remove('js-hidden')

        updatePriceAndImg()
      })
      window.ctrwowCheckout.productListData.onProductListChange((products) => {
        getProductPrice(products.prices)
        triggerToggleClick()
      })
      window.ctrwowCheckout.checkoutData.onProductChange(() => {
        updatePriceAndImg()
      })
    })
  )
}
