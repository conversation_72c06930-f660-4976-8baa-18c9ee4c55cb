<div class="one-unit-widget">
  <div class="one-unit-content">
    <div class="one-unit-desc">
      <h3 class="headline">
        <div class="product-name"><span class="current-qty">1</span><span>X PRODUCT NAME</span><span class="plus">S</span></div>
        <div class="limited-time js-hidden">10% OFF — Limited Time!</div>
      </h3>
      <div id="i0wjqux" class="product-price-and-img">
        <div class="product-img product-img-first">
          <img
            alt=""
            src="https://test2.ctrwow.com/getuvbrite/en/assets/image/aa6d2ce0-f3c4-4c9d-9f7c-3b61f962d1c0/64055d9361818810406f6cfc/1f3a83dd-0b5a-4564-a486-72a63aae6bfc/black-1.webp"
          />
        </div>
        <div class="product-img product-img-second js-hidden">
          <img
            alt=""
            src="https://test2.ctrwow.com/getuvbrite/en/assets/image/aa6d2ce0-f3c4-4c9d-9f7c-3b61f962d1c0/64055d9361818810406f6cfc/1f3a83dd-0b5a-4564-a486-72a63aae6bfc/black-1.webp"
          />
        </div>
        <div class="product-img product-img-third js-hidden">
          <img
            alt=""
            src="https://test2.ctrwow.com/getuvbrite/en/assets/image/aa6d2ce0-f3c4-4c9d-9f7c-3b61f962d1c0/64055d9361818810406f6cfc/1f3a83dd-0b5a-4564-a486-72a63aae6bfc/black-1.webp"
          />
        </div>
        <div class="product-img product-img-fourth js-hidden">
          <img
            alt=""
            src="https://test2.ctrwow.com/getuvbrite/en/assets/image/aa6d2ce0-f3c4-4c9d-9f7c-3b61f962d1c0/64055d9361818810406f6cfc/1f3a83dd-0b5a-4564-a486-72a63aae6bfc/black-1.webp"
          />
        </div>
        <div class="product-price">
          <div class="js-fullprice-1unit price-elm">$XX.XX</div>
          <div class="discount-price"><span>Just </span><span class="js-discount-price-1unit price-elm">$XX.XX</span><span> each</span></div>
          <div class="save-price hidden"><span>Savings </span><span class="js-save-price-1unit price-elm">$XX.XX</span></div>
        </div>
      </div>
    </div>
    <a href="javascript:;" class="order-now-active">ORDER NOW</a>
  </div>
  <div class="add-more-unit add-more-2unit second-pro">
    <div class="toggle-desc" data-gjs-editable="true">
      <span>Add another for just </span><span class="js-discount-price-2unit price-elm">$XX.XX</span><span>! </span>
    </div>
    <div class="toggle"><input type="radio" /><span> </span></div>
  </div>
  <div class="add-more-unit add-more-3unit js-hidden third-pro">
    <div class="toggle-desc" data-gjs-editable="true">
      <span>Keep going! Add a 3rd unit for only </span><span class="js-discount-price-3unit price-elm">$XX.XX</span><span>! </span>
    </div>
    <div class="toggle"><input type="radio" /><span> </span></div>
  </div>
  <div class="add-more-unit add-more-4unit js-hidden fourth-pro">
    <div class="toggle-desc" data-gjs-editable="true">
      <span>Get a 4th unit for just </span><span class="js-discount-price-4unit price-elm">$XX.XX</span><span>! </span>
    </div>
    <div class="toggle"><input type="radio" /><span> </span></div>
  </div>
  <div class="fire-group">
    <div class="headline">
      <img
        alt="image"
        src="https://test2.ctrwow.com/getuvbrite/en/assets/image/aa6d2ce0-f3c4-4c9d-9f7c-3b61f962d1c0/64055d9361818810406f6cfc/33e7bce7-f570-4085-a23c-2bd77d0bd276/flames.png"
        width="35"
        height="42"
      />
      <div class="sellingout">HURRY, SELLING OUT FAST!</div>
    </div>
    <div class="fire-desc">Add more units now for guaranteed savings!</div>
  </div>
</div>

<!-- <style>
    *{
        box-sizing: border-box;
    }
.hidden,  .js-hidden{
  display: none !important;
}
.one-unit-widget {
  min-height: 50px;
  position: relative;
  border: 4px solid #30bd51;
  box-shadow: 0 3px 9px 2px #0000001f;
  margin: 0 0 25px;
  border-radius: 5px 5px 5px 5px;
  padding: 4px;
}
.one-unit-widget .loading:before {
    content: "";
    background-image: url(https://d16hdrba6dusey.cloudfront.net/sitecommon/images/loading-price-v1.gif);
    display: inline-block;
    width: 20px;
    height: 10px;
    background-size: contain;
    background-repeat: no-repeat;
}
.one-unit-widget [type="radio"]:checked + span,.one-unit-widget [type="radio"]:not(:checked) + span {
  line-height:36px;
}

.one-unit-widget [type="radio"]:checked + span:after,.one-unit-widget [type="radio"]:not(:checked) + span:after {
  box-shadow: 0 3px 9px 2px rgba(0,0,0,0.12)
}

.one-unit-widget [type="radio"]:not(:checked) + span:after {
  opacity: 1;
  -webkit-transform: unset;
  transform: unset;
}

.one-unit-widget [type="radio"]:checked + span:before {
  content: 'ON';
  width: 55px;
  background: #1F80EF;
  color: #fff;
  text-align: left;
  line-height: 34px;
  padding-left: 15px;
  font-size: 16px;
  font-weight: 600;
  font-style: unset
}

.one-unit-widget [type="radio"]:not(:checked) + span:before {
  content: 'OFF';
  background: #cccccc;
  width: 60px;
  color: #898989;
  text-align: right;
  line-height: 34px;
  padding-right: 8px;
  font-size: 16px;
  font-weight: 600;
  font-style: unset
}

.one-unit-widget [type="radio"]:checked + span:after {
  width: 24px;
  height: 24px;
  top: 5px;
  left: unset;
  right: 8px;
  background: #ffffff
}

.one-unit-widget [type="radio"]:not(:checked) + span:after {
  width: 24px;
  height: 24px;
  top: 3px;
  left: unset;
  right: 47px;
  background: #ffffff;
}

.one-unit-widget [type="radio"]:checked + span,.one-unit-widget [type="radio"]:not(:checked) + span {
  padding-right: 80px;
}

.one-unit-widget [type="radio"]:checked + span,.one-unit-widget [type="radio"]:not(:checked) + span {
  padding-left:5px;
}

.one-unit-widget [type="radio"]:checked + span:before,.one-unit-widget [type="radio"]:not(:checked) + span:before {
  position: absolute;
  height: 30px;
  border: none;
  border-radius: 15px;
  right: 5px;
  left: unset;
  top: 0px;
}

.one-unit-widget [type=radio]:not(:checked)+span {
  font-style: italic;
  font-weight: 600;
}

.one-unit-widget .toggle-desc {
  width: calc(100% - 85px)
}

.one-unit-widget .add-more-unit.active {
  font-weight: 600;
  font-style: italic
}

.one-unit-widget .add-more-unit span {
  pointer-events: none;
  height: 30px;
  font-style: normal
}

.one-unit-content{
    flex: 1 0 0px;
    display: flex;
    width: 100%;
    align-items: flex-start;
    justify-content: flex-start;
    margin: 0;
    padding: 15px 10px 15px 15px;
    flex-direction: column;
}

.one-unit-desc{
  padding: 0;
  max-width: 250px;
  margin: 0;
  width: 100%;
  text-align: left;
  display: flex;
  flex-direction: column;
}

.one-unit-desc .headline{
  margin: 0 0 16px;
}

.one-unit-desc .headline .product-name{
  text-align: left;
  font-size: 18px;
  line-height: 20px;
  margin: 0;
  font-weight: 700;
}
.one-unit-desc .headline .limited-time{
  padding: 6px 0 0;
  margin: 0;
  color: #1f8305;
  font-size: 16px;
  line-height: 30px;
}

.one-unit-desc .headline .js-coupon-text{
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: #1F8305;
}

.one-unit-desc .product-price-and-img{
  margin: 0;
  width: 100%;
}

.one-unit-desc .product-img{
  display: flex;
  align-items: center;
  text-align: center;
  margin: 0;
  position: absolute;
  top: 50px;
  width: 20%;
  right: 0;
  min-height: 120px;
}

.one-unit-desc .product-img img{
    max-height: 115px;
    margin: 0 auto;
}

.one-unit-desc .product-price{
  max-width: 230px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 16px;
  margin: 0;
}

.one-unit-desc .product-price .js-fullprice-1unit{
  margin: 0;
  text-decoration: line-through;
  line-height: 18px;
  height: 18px;
}

.one-unit-desc .product-price .discount-price{
  text-align: left;
  font-size: 24px;
  margin: 0;
  font-size: 20px;
  line-height: 24px;
  display: inline;
  color: #2db4ed;
  font-weight: 700;
}

.one-unit-desc .product-price .js-discount-price-1unit{
  font-size: 30px;
  line-height: 36px;
  font-weight: 800;
}

.one-unit-content .order-now-active{
  display: block;
  text-decoration: none;
  text-align: center;
  width: 100%;
  border-radius: 11px 11px 11px 11px;
  font-weight: 700;
  line-height: 20px;
  color: #000;
  max-width: 150px;
  font-size: 18px;
  padding: 15px 5px;
  border-radius: 5px 5px 5px 5px;
  background-color: #29af5c;
  margin-top: 10px;
}

.one-unit-widget .add-more-unit{
  min-height: 50px;
  padding: 10px 0 10px 13px;
  margin: 0 0 10px;
  position: relative;
  background-color: #fff7b2;
  border-radius: 5px 5px 5px 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.one-unit-widget .add-more-unit.active{
  font-weight: 600;
  font-style: italic;
  background-color:unset;
}

.one-unit-widget .add-more-unit .toggle-desc{
  margin: 0;
  font-weight: 600;
}

.one-unit-widget .add-more-unit .toggle{
  margin: 0;
  font-size: 17px;
  line-height: 20px;
  width: 65px;
  height: 30px;
  position: relative;
}

.one-unit-widget .add-more-unit .toggle input:not(:checked),
.one-unit-widget .add-more-unit .toggle input:checked{
  position: absolute;
  left: -9999px;
}

.one-unit-widget .add-more-unit .toggle input+span{
  display: block;
}

.one-unit-widget .add-more-unit .toggle input:not(:checked)+span {
  font-style: normal;
  font-weight: 600;
  pointer-events: none;
  height: 30px;
  position: relative;
  color: #000;
  line-height: 36px;
  padding-right: 80px;
  padding-left: 5px;
}
.one-unit-widget .add-more-unit .toggle input:checked+span{
  padding-left: 5px;
  padding-right: 80px;
  line-height: 36px;
  position: relative;
}
.one-unit-widget .add-more-unit .toggle input:not(:checked)+span:before{
    content: 'OFF';
    background: #cccccc;
    width: 60px;
    color: #898989;
    text-align: right;
    line-height: 34px;
    padding-right: 8px;
    font-size: 16px;
    font-weight: 600;
    font-style: unset;
    position: absolute;
    height: 30px;
    border: none;
    border-radius: 15px;
    right: 5px;
    left: unset;
    top: 0px;
}
.one-unit-widget .add-more-unit .toggle input:checked+span:before{
  content: 'ON';
  width: 55px;
  background: #1F80EF;
  color: #fff;
  text-align: left;
  line-height: 34px;
  padding-left: 15px;
  font-size: 16px;
  font-weight: 600;
  font-style: unset;
  position: absolute;
  height: 30px;
  border: none;
  border-radius: 15px;
  right: 5px;
  left: unset;
  top: 0px;
}
.one-unit-widget .add-more-unit .toggle input+span:after{
  opacity: 1;
  transform: unset;
  content: '';
  position: absolute;
  border-radius: 100%;
  transition: all .2s ease;
}
.one-unit-widget .add-more-unit .toggle input:not(:checked)+span:after{
  font-weight: 600;
  line-height: 36px;
  box-shadow: 0 3px 9px 2px rgba(0, 0, 0, 0.12);
  width: 24px;
  height: 24px;
  top: 3px;
  left: unset;
  right: 47px;
  background: #ffffff;
}
.one-unit-widget .add-more-unit .toggle input:checked+span:after{
  content: '';
  -webkit-transform: scale(1);
  transform: scale(1);
  box-shadow: 0 3px 9px 2px rgba(0, 0, 0, 0.12);
  width: 24px;
  height: 24px;
  top: 3px;
  left: unset;
  right: 8px;
  background: #ffffff;
}

.fire-group{
  padding: 0 10px 10px;
  background-color: #fff2e0;
  width: 100%;
}

.fire-group .headline{
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #ed5214;
  font-weight: 700;
  font-size: 17px;
  line-height: 20px;
  letter-spacing: -.5px;
}

.fire-group .fire-desc{
  line-height: 19px;
  color: #1a1a1a;
  font-style: italic;
  font-weight: 600;
  margin: 0;
  text-align: left;
  font-size: 16px;
}
.fire-group .headline .sellingout{
  margin-top: 5px;
  margin-left: 5px;
}
 </style> -->
