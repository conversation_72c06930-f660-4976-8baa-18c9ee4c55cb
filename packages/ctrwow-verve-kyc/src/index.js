;(function () {
  class KYCPage {
    constructor() {
      this.urlApi = 'https://vervedirect-dev-api.azurewebsites.net'
      if (window.location.hostname === 'www.vervedirect.com') {
        this.urlApi = 'https://vervedirect-prod-api.azurewebsites.net'
      }
    }

    validateEmail(email) {
      const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return re.test(email)
    }

    validatePhone(phone) {
      const re = /^[+]*[(]{0,1}[0-9]{1,3}[)]{0,1}[-\s\./0-9]*$/g
      return re.test(phone)
    }

    validateURL(url) {
      var re = /^(http[s]?:\/\/){0,1}(www\.){0,1}[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}[\.]{0,1}/
      return re.test(url)
    }

    setValues(value) {
      const $this = this
      $('[data-field]').each(function () {
        const $element = $(this)
        if ($element !== 'undefined') {
          const elementType = $element.attr('type')
          const fieldName = $element.attr('data-field')
          const propNames = fieldName.split('.')
          const propValue = propNames.reduce((obj, propName) => (typeof obj === 'undefined' || obj === null ? null : obj[propName]), value)
          if (elementType === 'date') {
            $element.val($this.formatDate(propValue))
          } else if ($element.prop('tagName') === 'SELECT' && $element.prop('multiple')) {
            if (propValue !== null && typeof propValue !== 'undefined') {
              const optionValues = propValue.split(',')
              $element
                .find('option')
                .prop('selected', false)
                .each(function () {
                  if (optionValues.includes(this.value)) {
                    this.selected = true
                  }
                })
            }
          } else if (elementType === 'checkbox') {
            $element.prop('checked', propValue)
          } else {
            $element.val(propValue)
          }
        }
      })
    }

    getValues() {
      const result = {}
      const $this = this
      $('[data-field]').each(function () {
        const $element = $(this)
        const elementType = $element.attr('type')
        const fieldName = $element.attr('data-field')
        const propNames = fieldName.split('.')
        const lastProp = propNames.pop()
        const targetObj = propNames.reduce((obj, propName) => {
          if (!(propName in obj)) {
            obj[propName] = {}
          }
          return obj[propName]
        }, result)
        let propValue = $element.val()
        if (elementType === 'date') {
          propValue = $this.formatDate(propValue, 1)
        } else if (elementType === 'checkbox' || elementType === 'radio') {
          if ($element.prop('checked')) {
            propValue = true
          } else {
            propValue = false
          }
        } else if ($element.prop('tagName') === 'SELECT' && $element.prop('multiple')) {
          const propValues = []
          $element.find('option:selected').each(function () {
            propValues.push(this.value)
          })
          propValue = propValues.join(',')
        }

        targetObj[lastProp] = propValue
      })
      return result
    }

    getUrlParameter(parameter) {
      const url = window.location.href
      parameter = parameter.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]')
      var regex = new RegExp('[\\?|&]' + parameter.toLowerCase() + '=([^&#]*)')
      var results = regex.exec('?' + url.toLowerCase().split('?')[1])
      return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '))
    }

    formatDate(current_datetime, type = 0) {
      let date = new Date()
      if (current_datetime !== null) {
        date = new Date(current_datetime)
      }

      if (type === 1) {
        return date.toISOString()
      } else {
        return `${date.getFullYear().toString().padStart(4, '0')}-${(date.getMonth() + 1)
          .toString()
          .padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
      }
    }

    toggleRequiredFields(isCompany) {
      $('[data-required-for]').removeAttr('required')
      if (isCompany) {
        $('[data-required-for=company]').attr('required', 'required')
      } else {
        $('[data-required-for=individual]').attr('required', 'required')
      }
    }

    clickHandleEntity() {
      if ($('#individualEntity').is(':checked')) {
        $('#companyContainer').hide()
        $('#companyContainer [required]').each(function (index, elm) {
          $(elm).removeAttr('required')
        })
      } else {
        $('#companyContainer').show()
        $('#companyContainer [required]').each(function (index, elm) {
          $(elm).is('required')
        })
      }
    }

    checkData(data) {
      if (data.isCompany === true) {
        $('#companyEntity').prop('checked', true)
        $('#companyContainer').show()
        $('#docs_coirotac_container').show()
      } else {
        $('#individualEntity').prop('checked', true)
        $('#companyContainer').hide()
        $('#docs_coirotac_container').hide()
      }
      this.toggleRequiredFields(data.isCompany)
      if (data.companyDetail && data.companyDetail.socialMediaHandlesOthers && data.companyDetail.socialMediaHandlesOthers !== null) {
        $('#company_other_sm').prop('checked', true)
      }
      if (data.personalDetail && data.personalDetail.socialMediaHandlesOthers && data.personalDetail.socialMediaHandlesOthers !== null) {
        $('#individual_other_sm').prop('checked', true)
      }
      if (data.declaration.proofOfAddress !== null && data.declaration.proofOfAddress.otherProof !== null) {
        $('#docs_3_other').prop('checked', true)
      }
      if (data.declaration.documents && data.declaration.documents.length > 0) {
        const docs = data.declaration.documents

        docs.forEach((item) => {
          if (item.type && item.url) {
            if (item.type === 'certificateOfIncorporation' && item.url != null) {
              $('#docs_coirotac').removeAttr('required')
              $('.urlDoc1').append('')
              $('.urlDoc1').append(`<a href='${item.url}'>${item.url}</a>`)
              $('#docs_coirotac_value').val(item.url)
            }
            if (item.type === 'governmentIssuedId' && item.url != null) {
              $('#docs_siiotaicpoc').removeAttr('required')
              $('.urlDoc2').append('')
              $('.urlDoc2').append(`<a href='${item.url}'>${item.url}</a>`)
              $('#docs_siiotaicpoc_value').val(item.url)
            }
            if (item.type === 'proofOfAddress' && item.url != null) {
              $('#docs_poaocaoi').removeAttr('required')
              $('.urlDoc3').append('')
              $('.urlDoc3').append(`<a href='${item.url}'>${item.url}</a>`)
              $('#docs_poaocaoi_value').val(item.url)
            }
            if (item.type === 'signature' && item.url != null) {
              $('#signatureImage').show()
              $('#signArea').hide()
              $('#signatureImage').append(`<img src='${item.url}' id="imageSign" />`)
              $('#signatureValue').val(item.url)
              $('#updateSignature').prop('checked', false)
            } else {
              $('#signatureImage').hide()
              $('#signArea').show()
              $('#updateSignature').prop('checked', true)
            }
          }
        })
      } else {
        $('#updateSignature').prop('checked', true)
        $('#signatureImage').hide()
      }
    }

    fetchKyc() {
      const caseID = this.getUrlParameter('code')
      const $this = this
      $.ajax({
        url: `${this.urlApi}/api/public/kyc/${caseID}`,
        method: 'GET',
        success: function (response) {
          if (response.success === true) {
            $this.setValues(response.data)
            $this.checkData(response.data)
          }
        },
        error: function (response) {
          try {
            const dataError = response.responseJSON.errors
            let errorMess = ''
            dataError.map((item) => {
              errorMess += `${item.message}\n`
            })
            console.log(`${errorMess}`)
          } catch (e) {
            // console.log(e);
          }
        }
      })
      $('.preloading-wrapper').css({
        display: 'none',
        opacity: 1
      })
    }

    uploadFile(file, type) {
      if (file.size < 5000000) {
        const caseID = this.getUrlParameter('code')
        const fd = new FormData()
        fd.append('file', file)

        return $.ajax({
          url: `${this.urlApi}/api/public/kyc/${caseID}/docs/${type}`,
          data: fd,
          contentType: false,
          processData: false,
          cache: false,
          method: 'post',
          success: function (response) {
            return response
          },
          error: function (response) {
            const dataError = response.responseJSON.errors
            let errorMess = ''
            dataError.map((item) => {
              errorMess += `${item.message}\n`
            })
            console.log(`${errorMess} - file`)
          }
        })
      } else {
        alert(`Max size < 5MB `)
        $('.preloading-wrapper').css({
          display: 'none',
          opacity: 1
        })
        return false
      }
    }

    inputHandler() {
      $('[required]').on('change.changeValue keyup.changeValue', function () {
        if (!$(this).is(':visible')) return
        var inputVal = $(this).val().trim()
        var type = $(this).attr('type')
        var elm = $(this)

        if ($(this).attr('role')) {
          switch (type) {
            case 'email':
              if (!$(this).validateEmail(inputVal)) {
                $(this).showError(elm, true)
              } else {
                $(this).showError(elm, false)
              }
              break
            case 'url':
              if (!$(this).validateURL(inputVal)) {
                $(this).showError(elm, true)
              } else {
                $(this).showError(elm, false)
              }
              break
            case 'checkbox':
              if (!elm.prop('checked')) {
                $(this).showError(elm, true)
              } else {
                $(this).showError(elm, false)
              }
              break
            default:
              if (!inputVal.length) {
                $(this).showError(elm, true)
              } else {
                $(this).showError(elm, false)
              }
          }
        }
      })
    }

    validateForm() {
      let isValid = true
      const $this = this
      let countFalse = 0
      $('#affiliate-signup-form [required]').each(function (index, elm) {
        const inputVal = ($(elm).val() || '').trim()
        let type = null
        if ($(elm).attr('type') !== null) {
          type = $(elm).attr('type')
        } else {
          type = $(elm).prop('tagName').toLowerCase()
        }

        elm = $(elm)
        switch (type) {
          case 'email':
            if (!$this.validateEmail(inputVal)) {
              $this.showError(elm, true)

              isValid = false
            } else {
              $this.showError(elm, false)
            }
            break
          case 'tel':
            if (!$this.validatePhone(inputVal)) {
              $this.showError(elm, true)

              isValid = false
            } else {
              $this.showError(elm, false)
            }
            break
          case 'url':
            if (!$this.validateURL(inputVal)) {
              $this.showError(elm, true)

              isValid = false
            } else {
              $this.showError(elm, false)
            }
            break
          case 'checkbox':
            if (!elm.prop('checked')) {
              $this.showError(elm, true)
              isValid = false
            } else {
              $this.showError(elm, false)
            }
            break
          default:
            if (!inputVal.length) {
              $this.showError(elm, true)
              isValid = false
            } else {
              $this.showError(elm, false)
            }
        }
        if (isValid === false && countFalse === 0) {
          const IdAtt = $(elm).attr('id')
          $([document.documentElement, document.body]).animate(
            {
              scrollTop: $(`#${IdAtt}`).offset().top - 30
            },
            0
          )
          countFalse = 1
        }
      })

      return isValid
    }

    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(',')
      var mime = arr[0].match(/:(.*?);/)[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }

      return new File([u8arr], filename, { type: mime })
    }

    updateArray(data = [], type = '', url) {
      let flag = true
      for (const i in data) {
        if (data[i].type === type) {
          data[i].url = url
          flag = false
        }
      }
      if (flag) {
        data.push({
          type: type,
          url: url
        })
      }
      return data
    }

    returnSignFile() {
      // const $this = this
      return new Promise(function (resolve, reject) {
        // TODO
        /* eslint-disable */
        html2canvas([document.getElementById('sign-pad')], {
          onrendered: function (canvas) {
            const canvas_img_data = canvas.toDataURL('image/png')
            const signature_file = $this.dataURLtoFile(canvas_img_data, 'signature_file.png')
            return resolve(signature_file)
          }
        })
      })
    }

    updatePromiseAllData(tasksInpo, dataInput, caseID) {
      const $this = this
      if (tasksInpo.length > 0) {
        Promise.all(tasksInpo).then((files) => {
          if (files && files.length > 0) {
            files.forEach((file) => {
              $this.updateArray(dataInput.declaration.documents, file.data.type, file.data.url)
            })
          }
          $this.updateData(dataInput, caseID)
        })
      } else {
        $this.updateData(dataInput, caseID)
      }
    }

    affiliateForm() {
      const $this = this

      $('#submit').click(function (e) {
        if ($this.validateForm()) {
          // section for editing kyc
          const caseID = $this.getUrlParameter('code')
          if (!caseID) {
            alert('Something is missing!!')
            return false
          }
          //  const captchaResponse = grecaptcha.getResponse()
          const captchaResponse = null
          if (captchaResponse.length === 0) {
            alert('Please prove that you are a not a robot by selecting the checkbox!!')
            return false
          } else {
            window.ctrwowFormUtils.showAjaxLoading()
            const dataInput = $this.getValues()

            if (dataInput.isCompany === false) {
              dataInput.companyDetail = null
            }

            if ($('#individualYesSameOfficeResidential').is(':checked')) {
              dataInput.personalDetail.officeAddress.street = dataInput.personalDetail.residentialAddress.street
              dataInput.personalDetail.officeAddress.city = dataInput.personalDetail.residentialAddress.city
              dataInput.personalDetail.officeAddress.state = dataInput.personalDetail.residentialAddress.state
              dataInput.personalDetail.officeAddress.postCode = dataInput.personalDetail.residentialAddress.postCode
              dataInput.personalDetail.officeAddress.country = dataInput.personalDetail.residentialAddress.country
            }
            if ($('#billingYesSameAsContact').is(':checked')) {
              dataInput.billingInfo.name = dataInput.personalDetail.name
              dataInput.billingInfo.position = dataInput.personalDetail.position
              dataInput.billingInfo.cellPhone = dataInput.personalDetail.cellPhone
              dataInput.billingInfo.email = dataInput.personalDetail.email
            }
            dataInput.declaration.documents = []
            if ($('#docs_coirotac_value').val() !== '') {
              const docs_coirotac_value = {
                type: 'certificateOfIncorporation',
                url: $('#docs_coirotac_value').val()
              }
              dataInput.declaration.documents.push(docs_coirotac_value)
            }
            if ($('#docs_siiotaicpoc_value').val() !== '') {
              const docs_siiotaicpoc_value = {
                type: 'governmentIssuedId',
                url: $('#docs_siiotaicpoc_value').val()
              }
              dataInput.declaration.documents.push(docs_siiotaicpoc_value)
            }
            if ($('#docs_poaocaoi_value').val() !== '') {
              const docs_poaocaoi_value = {
                type: 'proofOfAddress',
                url: $('#docs_poaocaoi_value').val()
              }
              dataInput.declaration.documents.push(docs_poaocaoi_value)
            }
            if ($('#signatureValue').val() !== '') {
              const signatureValue = {
                type: 'signature',
                url: $('#signatureValue').val()
              }
              dataInput.declaration.documents.push(signatureValue)
            }

            if (dataInput.declaration.documents.length > 0) {
              const tasksInpo = []
              if ($('#docs_coirotac').prop('files')[0]) {
                tasksInpo.push($this.uploadFile($('#docs_coirotac').prop('files')[0], 'certificateOfIncorporation'))
              }
              if ($('#docs_siiotaicpoc').prop('files')[0]) {
                tasksInpo.push($this.uploadFile($('#docs_siiotaicpoc').prop('files')[0], 'governmentIssuedId'))
              }

              if ($('#docs_poaocaoi').prop('files')[0]) {
                tasksInpo.push($this.uploadFile($('#docs_poaocaoi').prop('files')[0], 'proofOfAddress'))
              }
              if ($('#updateSignature').is(':checked')) {
                $this.returnSignFile().then((res) => {
                  tasksInpo.push($this.uploadFile(res, 'signature'))
                  $this.updatePromiseAllData(tasksInpo, dataInput, caseID)
                })
              } else {
                $this.updatePromiseAllData(tasksInpo, dataInput, caseID)
              }
            } else {
              // TODO
              /* eslint-disable */
              html2canvas([document.getElementById('sign-pad')], {
                onrendered: function (canvas) {
                  const canvas_img_data = canvas.toDataURL('image/png')
                  const signature_file = $this.dataURLtoFile(canvas_img_data, 'signature_file.png')
                  const tasks = [
                    $this.uploadFile($('#docs_siiotaicpoc').prop('files')[0]),
                    $this.uploadFile($('#docs_poaocaoi').prop('files')[0]),
                    $this.uploadFile(signature_file)
                  ]
                  if ($('#docs_coirotac').prop('files')[0]) {
                    tasks.push(
                      $this.uploadFile($('#docs_coirotac').prop('files')[0]).then((files) => {
                        const certificateOfIncorporation = {
                          type: 'certificateOfIncorporation',
                          url: files.data.url
                        }
                        dataInput.declaration.documents.push(certificateOfIncorporation)
                      })
                    )
                  }
                  Promise.all(tasks).then((files) => {
                    const governmentIssuedId = {
                      type: 'governmentIssuedId',
                      url: files[0].data.url
                    }
                    const proofOfAddress = {
                      type: 'proofOfAddress',
                      url: files[1].data.url
                    }
                    const signature = {
                      type: 'signature',
                      url: files[2].data.url
                    }

                    dataInput.declaration.documents.push(governmentIssuedId, proofOfAddress, signature)
                    $this.updateData(dataInput, caseID)
                  })
                }
              }) 
            }
          }
        } else {
          e.preventDefault()
          return false
        }
      })
    }

    updateData(dataInput, caseID) {
      $.ajax({
        url: `${this.urlApi}/api/public/kyc/${caseID}`,
        method: 'POST',
        dataType: 'json',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify(dataInput),
        success: function (response) {
          if (response.success === true) {
            window.location.href = 'thank-you.html'
            // window.location.reload(true);
          }
        },
        error: function (response) {
          console.log(response)
          const dataError = response.responseJSON.errors
          let errorMess = ''
          dataError.map((item) => {
            errorMess += `${item.message}\n`
          })
          alert(`${errorMess}`)
          $('.preloading-wrapper').css({
            display: 'none',
            opacity: 1
          })
        }
      })
    }

    showError(elm, isShow) {
      if (isShow) {
        // is Valid
        console.log(elm, isShow)
        elm.addClass('error').closest('.fieldset').addClass('errors')
      } else {
        // Not valid
        elm.removeClass('error').closest('.fieldset').removeClass('errors')
      }
    }

    toggleRequiredFieldsOfficeAddress(isAddress) {
      $('[data-required-for-address]').removeAttr('required')
      if (isAddress) {
        $('[data-required-for-address=officeAddress]').attr('required', 'required')
      }
    }

    toggleRequiredFieldsBillingContact(isBill) {
      $('[data-required-for-billing]').removeAttr('required')
      if (isBill) {
        $('[data-required-for-billing=billing]').attr('required', 'required')
      }
    }

    showNameFile1() {
      const imageUpload = document.getElementById('docs_coirotac')
      imageUpload.onchange = function () {
        const input = this.files[0]
        if (input) {
          document.querySelector('.nameDoc1').innerHTML = ''
          document.querySelector('.nameDoc1').innerHTML = `Selected: <strong>${input.name}</strong>`
        }
      }
    }

    showNameFile2() {
      const imageUpload = document.getElementById('docs_siiotaicpoc')
      imageUpload.onchange = function () {
        const input = this.files[0]
        if (input) {
          document.querySelector('.nameDoc2').innerHTML = ''
          document.querySelector('.nameDoc2').innerHTML = `Selected: <strong>${input.name}</strong>`
        }
      }
    }

    showNameFile3() {
      const imageUpload = document.getElementById('docs_poaocaoi')
      imageUpload.onchange = function () {
        const input = this.files[0]
        if (input) {
          document.querySelector('.nameDoc3').innerHTML = ''
          document.querySelector('.nameDoc3').innerHTML = `Selected: <strong>${input.name}</strong>`
        }
      }
    }

    init() {
      const $this = this

      document.addEventListener('DOMContentLoaded', () => {
        this.affiliateForm()
        this.inputHandler()
        this.fetchKyc()
        this.showNameFile1()
        this.showNameFile2()
        this.showNameFile3()

        // indiviudal Click
        $('#individualEntity').click(function (e) {
          $('#companyContainer').hide()
          $('#docs_coirotac_container').hide()
          $this.toggleRequiredFields(false)

          if ($('#docs_coirotac_value').val() === '') {
            $('#docs_coirotac').removeAttr('required')
            // $('#docs_coirotac_container').find('font').css('display','none')
            $('#docs_coirotac').closest('.errors').removeClass('errors')
          }

          if ($('#docs_siiotaicpoc_value').val() === '') {
            $('#docs_siiotaicpoc_container').find('font').css('display','inline')
            $('#docs_siiotaicpoc').attr('required', 'required')
          }
        })

        // company Click 
        $('#companyEntity').click(function (e) {
          $('#companyContainer').show()
          $('#docs_coirotac_container').show()
          
          if ($('#docs_coirotac_value').val() === '') {
            // if (!$('#docs_coirotac_container').find('font').length) {
            //   $('#docs_coirotac_container').find('font').css('display','none')
            // }
            $('#docs_coirotac').attr('required', 'required')
          }

          if ($('#docs_siiotaicpoc_value').val() === '') {
            $('#docs_siiotaicpoc').removeAttr('required')
            $('#docs_siiotaicpoc_container').find('font').css('display', 'none')
            $('#docs_siiotaicpoc').closest('.errors').removeClass('errors')
          }

          $this.toggleRequiredFields(true)
        })
        $('#individualYesSameOfficeResidential').click(function (e) {
          if ($('#individualYesSameOfficeResidential').is(':checked')) {
            $('#individualOfficeAddressContainer').hide()

            $this.toggleRequiredFieldsOfficeAddress(false)
          } else {
            $('#individualOfficeAddressContainer').show()
            $this.toggleRequiredFieldsOfficeAddress(true)
          }
        })
        $('#updateSignature').click(function (e) {
          if ($('#updateSignature').is(':checked')) {
            $('#signArea').show()
            $('#signatureImage').hide()
          } else {
            $('#signatureImage').show()
            $('#signArea').hide()
          }
        })
        $('#billingYesSameAsContact').click(function (e) {
          if ($('#billingYesSameAsContact').is(':checked')) {
            $('#billingContainer').hide()
            $this.toggleRequiredFieldsBillingContact(false)
          } else {
            $('#billingContainer').show()
            $this.toggleRequiredFieldsBillingContact(true)
          }
        })
      })
    }
  }
  const kycPage = new KYCPage()
  kycPage.init()
})()
