import getPageSettings from 'shared-checkout-flow/src/getPageSettings'

let crmIntance = null

export default function getCrmInstance() {
  if (crmIntance) {
    return crmIntance
  }

  const pageSettings = getPageSettings()
  const { ctrwowUtils, EmanageCRMJS } = window

  crmIntance = new EmanageCRMJS({
    webkey: pageSettings.webKey,
    cid: pageSettings.cid,
    // DEV-NOTE: use default value from lib
    // baseEndpoint is different for GET/POST request
    // baseAPIEndpoint: pageSettings.crmEndpoint,
    lang: pageSettings.lang,
    declineUrl: pageSettings.declineUrl,
    successUrl: pageSettings.successUrl,
    isTest: !!ctrwowUtils.link.getParameterByName('isCardTest')
  })

  return crmIntance
}
