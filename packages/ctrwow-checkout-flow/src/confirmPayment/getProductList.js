// source: https://dfo-aws-pubasset-cdn.s3.us-east-2.amazonaws.com/sitecommon/js/pages/confirm-v2.page.js
export default function getProductList(orderInfo, data) {
  const utils = window.ctrwowUtils
  const product_charges_statement_confirm_page =
    'Charges on your statement will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}'

  // const fvalue = data.receipts[0].formattedAmount.replace(/[,|.]/g, '')
  // const pValue = data.receipts[0].amount.toFixed(2).toString().replace(/\./, '')
  // const fCurrency = fvalue.replace(pValue, '######')
  const shippingPriceFormatted = data.shippingPriceFormatted

  let shippingLabel = 'Shipping'
  let totalLabel = 'Total'
  let charges_statement = product_charges_statement_confirm_page
  const js_translate = window.js_translate || {}

  // DEV - [js_translate] is injected to handle translation by crm - will use other-way to handle translate for ctrwow's site
  if (window.js_translate) {
    shippingLabel = js_translate.shipping
    totalLabel = js_translate.total
    charges_statement = js_translate.product_charges_statement_confirm_page
  }

  if (utils.localStorage().get('preOrderUpsell') === 'true') {
    charges_statement =
      js_translate.pre_order_product_charges_statement_confirm_page ||
      'Your deposit will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}. You will be charged the price of the products when they ship.'
  }

  let productItemTmp = `<li class="content__itemsList__item">
                                    <div class="content__itemsList__item--inner">
                                        <span>{productName}</span>
                                        <span>{productPrice}</span>
                                    </div>
                                    <div class="content__itemsList__item--inner">
                                        <span>${shippingLabel}</span>
                                        <span>{shippingPrice}</span>
                                    </div>
                                    {tax}
                                    <div class="content__itemsList__item--inner">
                                        <span>${totalLabel}</span>
                                        <span>{productTotal}</span>
                                    </div>
                                    <div class="content__itemsList__item--inner"><span>${charges_statement}</span></div>
                                </li>`

  let productItemMainTmp = productItemTmp

  const productItemTmpWarranty = `<li class="content__itemsList__item">
                                    <div class="content__itemsList__item--inner">
                                        <span>{productName}</span>
                                        <span>{productPrice}</span>
                                    </div>
                                    <div class="content__itemsList__item--inner">
                                        <span>${shippingLabel}</span>
                                        <span>{shippingPrice}</span>
                                    </div>
                                    {tax}
                                    <div class="content__itemsList__item--inner">
                                        <span>${totalLabel}</span>
                                        <span>{productTotal}</span>
                                    </div>
                                    <div class="content__itemsList__item--inner"><span>${product_charges_statement_confirm_page}</span></div>
                                </li>`

  if (utils.localStorage().get('preOrder') === 'true') {
    let pre_order_product_charges_statement_confirm_page =
      js_translate.pre_order_product_charges_statement_confirm_page ||
      'Your deposit will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}. You will be charged the price of the products when they ship.'
    pre_order_product_charges_statement_confirm_page = pre_order_product_charges_statement_confirm_page.replace(
      /\{productTotal\}/gi,
      '{productTotalPreOrder}'
    )

    productItemMainTmp = `<li class="content__itemsList__item">
                                    <div class="content__itemsList__item--inner">
                                        <span>{productName}</span>
                                        <span>{productPrice}</span>
                                    </div>
                                    <div class="content__itemsList__item--inner">
                                        <span>${shippingLabel}</span>
                                        <span>{shippingPrice}</span>
                                    </div>
                                    {tax}
                                    <div class="content__itemsList__item--inner">
                                        <span>${totalLabel}</span>
                                        <span>{productTotalPreOrder}</span>
                                    </div>
                                    <div class="content__itemsList__item--inner"><span>${pre_order_product_charges_statement_confirm_page}</span></div>
                                </li>`
  }

  if (utils.localStorage().get('preOrderUpsell') === 'true') {
    productItemTmp = productItemTmp.replace(/\{productTotal\}/gi, '{productTotalPreOrder}')
  }

  // Installment Payment : only for Brazil
  let installmentText = ''
  if (orderInfo.installmentValue && orderInfo.installmentValue !== '') {
    const mainPrice = (data.orderPrice / orderInfo.installmentValue).toFixed(2)
    installmentText =
      ' (' +
      orderInfo.installmentText
        .replace(/N/, orderInfo.installmentValue)
        .replace(/\$price/, utils.number.formaterNumberByFormattedValue(mainPrice, shippingPriceFormatted)) +
      ')'
  }

  // eslint-disable-next-line no-undef
  const mainProductNames = typeof mainProducts !== 'undefined' ? mainProducts : false
  // eslint-disable-next-line no-undef
  const upsellProductNames = typeof upsellProducts !== 'undefined' ? upsellProducts : false

  let taxLine = ''
  const taxMainValue = parseFloat(data.orderPrice) - parseFloat(data.orderProductPrice) - parseFloat(data.shippingPrice)
  if (taxMainValue > 0.1 && utils.localStorage().get('preOrder') !== 'true') {
    taxLine = `
                <div class="content__itemsList__item--inner">
                    <span>Tax</span>
                    <span>${'$' + taxMainValue.toFixed(2)}</span>
                </div>
                `
  }

  let listProduct = productItemMainTmp
    .replace('{productName}', data.productName)
    .replace(/\{productPrice\}/g, data.orderProductPriceFormatted)
    .replace(/\{tax\}/g, taxLine)
    .replace(/\{productTotal\}/g, `${data.orderPriceFormatted}<em>${installmentText}</em>`)
    .replace(/\{productTotalPreOrder\}/g, `${data.orderProductPriceFormatted}<em>${installmentText}</em>`)
    .replace('{shippingPrice}', data.shippingPriceFormatted)
    .replace('{midDescriptor}', data.receipts[0].midDescriptor ? data.receipts[0].midDescriptor : 'Paypal')
    .replace(/\{orderNumber\}/g, data.orderNumber)

  if (mainProductNames) {
    for (let i = 0; i < mainProductNames.length; i++) {
      if (data.productName.trim() === mainProductNames[i].split(',')[0].trim()) {
        listProduct = listProduct.replace(
          mainProductNames[i].replace(/\,/, '::').split('::')[0].trim(),
          mainProductNames[i].replace(/\,/, '::').split('::')[1].trim()
        )
        break
      }
    }
  }

  for (let i = 0; i < data.relatedOrders.length; i++) {
    if (data.relatedOrders[i].orderStatus === 'Cancel') continue

    if (orderInfo.installmentValue && orderInfo.installmentValue !== '') {
      const mainPrice = (data.relatedOrders[i].orderPrice / orderInfo.installmentValue).toFixed(2)
      installmentText =
        ' (' +
        orderInfo.installmentText
          .replace(/N/, orderInfo.installmentValue)
          .replace(/\$price/, utils.number.formaterNumberByFormattedValue(mainPrice, shippingPriceFormatted)) +
        ')'
    }

    let taxUpsellLine = ''
    const taxUpsellValue =
      parseFloat(data.relatedOrders[i].orderPrice) -
      parseFloat(data.relatedOrders[i].orderProductPrice) -
      parseFloat(data.relatedOrders[i].shippingPrice)
    if (taxUpsellValue > 0 && utils.localStorage().get('preOrderUpsell') !== 'true') {
      taxUpsellLine = `
                    <div class="content__itemsList__item--inner">
                        <span>Tax</span>
                        <span>${'$' + taxUpsellValue.toFixed(2)}</span>
                    </div>
                    `
    }

    let itemTmp = ''
    if (data.relatedOrders[i].productName.toLowerCase().indexOf('warranty') > -1) {
      itemTmp = productItemTmpWarranty
        .replace('{productName}', data.relatedOrders[i].productName)
        .replace(/\{productPrice\}/g, data.relatedOrders[i].orderProductPriceFormatted)
        .replace(/\{tax\}/g, taxUpsellLine)
        .replace(/\{productTotal\}/g, `${data.relatedOrders[i].orderPriceFormatted}<em>${installmentText}</em>`)
        .replace('{shippingPrice}', data.relatedOrders[i].shippingPriceFormatted)
        .replace('{midDescriptor}', data.relatedOrders[i].receipts[0].midDescriptor ? data.relatedOrders[i].receipts[0].midDescriptor : 'Paypal')
        .replace(/\{orderNumber\}/g, data.relatedOrders[i].orderNumber)
    } else {
      itemTmp = productItemTmp
        .replace('{productName}', data.relatedOrders[i].productName)
        .replace(/\{productPrice\}/g, data.relatedOrders[i].orderProductPriceFormatted)
        .replace(/\{tax\}/g, taxUpsellLine)
        .replace(/\{productTotal\}/g, `${data.relatedOrders[i].orderPriceFormatted}<em>${installmentText}</em>`)
        .replace(/\{productTotalPreOrder\}/g, `${data.relatedOrders[i].orderProductPriceFormatted}<em>${installmentText}</em>`)
        .replace('{shippingPrice}', data.relatedOrders[i].shippingPriceFormatted)
        .replace('{midDescriptor}', data.relatedOrders[i].receipts[0].midDescriptor ? data.relatedOrders[i].receipts[0].midDescriptor : 'Paypal')
        .replace(/\{orderNumber\}/g, data.relatedOrders[i].orderNumber)
    }

    if (upsellProductNames) {
      for (let j = 0; j < upsellProductNames.length; j++) {
        itemTmp = itemTmp.replace(
          upsellProductNames[j].replace(/\,/, '::').split('::')[0].trim(),
          upsellProductNames[j].replace(/\,/, '::').split('::')[1].trim()
        )
      }
    }
    listProduct += itemTmp
  }

  return `<ul class="content__itemsList">${listProduct}</ul>`

  // const ul = document.createElement('ul')
  // ul.innerHTML = listProduct
  // const receiptList = _q('.receipt-list')
  // if (receiptList) {
  //   receiptList.appendChild(ul)
  // }

  // if (utils.localStorage().get('additionTextConfirmName')) {
  //   _q('.receipt-list ul .item').querySelector('.inner span').insertAdjacentText(
  //     'beforeend', ' ' + utils.localStorage().get('additionTextConfirmName'))
  // }
}
