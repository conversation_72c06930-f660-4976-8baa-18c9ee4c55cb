import getEmanageCRMJS from 'shared-checkout-flow/src/getEmanageCRMJS'
import * as tracking from 'shared-checkout-flow/src/tracking'
export { tracking }
export { default as populateConfirmedInfoByOrder } from './confirmPayment'

if (!window.__ctrConfirmPayment) {
  window.__ctrConfirmPayment = {
    isReady: false
  }
  window.__ctrConfirmPayment.processing = new Promise((resolve) => (window.__ctrConfirmPayment.resolve = resolve))

  window.__ctrConfirmPayment.processing.then(() => {
    console.log('__ctrConfirmPayment get ready !!!')
    window.__ctrConfirmPayment.isReady = true
  })

  getEmanageCRMJS()
    .then(() => {
      console.log('start to int [EmanageCRMJS] instance')
      window.__ctrConfirmPayment.resolve(true)
    })
    .catch((e) => {
      console.error('cannot create ctrwow instance')
    })
}

export const ready = () => window.__ctrConfirmPayment.processing
