export const getDisplayName = ({ firstName, lastName }) => `${firstName} ${lastName}`
const isPaid = (order) => ['Paid', 'Delivered'].indexOf(order.orderStatus) > -1

const getOrderTotal = ({ orderPrice, relatedOrders = [] }) =>
  relatedOrders.reduce((total, order) => total + (isPaid(order) ? order.orderPrice : 0), orderPrice || 0)

export const getFormattedSavedTotal = (checkoutInfo, order) => {
  const savedTotal = checkoutInfo.savedTotal || 0
  return window.ctrwowUtils.number.formaterNumberByFormattedValue(savedTotal, order.receipts[0].formattedAmount)
}

export const getFormattedOrderTotal = (order) => {
  const total = getOrderTotal(order)
  return window.ctrwowUtils.number.formaterNumberByFormattedValue(total, order.receipts[0].formattedAmount)
}

const getFormattedAddress = (address) =>
  [getDisplayName(address), address.address1 || '', address.city || '', address.state || '', address.countryCode || '', address.zipCode || ''].join(
    '<br/>'
  )

export const getFormattedShippingAddress = ({ shippingAddress: address }) => getFormattedAddress(address)
export const getFormattedBillingAddress = ({ billingAddress: address }) => getFormattedAddress(address)

export const getFormattedProductTotal = (order, checkoutInfo) => {
  // Installment Payment : only for Brazil
  let installmentText = ''
  if (checkoutInfo.installmentValue && checkoutInfo.installmentValue !== '') {
    const mainPrice = (order.orderPrice / checkoutInfo.installmentValue).toFixed(2)
    installmentText =
      ' (' + checkoutInfo.installmentText.replace(/N/, checkoutInfo.installmentValue).replace(/\$price/, 'R$' + mainPrice.replace(/\./, ',')) + ')'
  }

  return `${order.orderPriceFormatted}${installmentText}`
}

export const getMidDescriptor = (order) => (order.receipts[0].midDescriptor ? order.receipts[0].midDescriptor : 'Paypal')
