import { getOrderInfo } from 'shared-checkout-flow/src/orderInfo/orderInfo'
import getFormattedDateNow from 'shared-formatter/src/getFormattedDateNow'
import * as orderDetailsDomainData from './orderDetails.domainData'
import getCrmInstance from './getCrmInstance'
import getProductList from './getProductList'
// import { isPaidByPaypal } from 'shared-checkout-flow/src/payment/userPaymentType'
import { isPaidByCreditCard } from 'shared-checkout-flow/src/payment/userPaymentType'


const updateUpsellsStatus = (orderNumber) =>
  new Promise((resolve) => {
    getCrmInstance().Order.updateUpsellsStatus(orderNumber, function (result) {
      if (result) {
        console.log('upsells status is updated')
        console.log(JSON.stringify(result || {}))
      }
      resolve(result)
    })
  })

const getRelatedOrders = (orderNumber) => new Promise((resolve) => getCrmInstance().Order.getRelatedOrders(orderNumber, (result) => resolve(result)))

// const getItemsOrderedDetails = (checkoutInfo, order) => {
//   const PRODUCT_DETAILS_TEMPLATE = `
//     <li class="content__itemsList__item">
//                                 <div class="content__itemsList__item--inner">
//                                     <span>{productName}</span>
//                                     <span>{productPrice}</span>
//                                 </div>
//                                 <div class="content__itemsList__item--inner">
//                                     <span>{shippingLabel}</span>
//                                     <span>{shippingPrice}</span>
//                                 </div>
//                                 <div class="content__itemsList__item--inner">
//                                     <span>{totalLabel}</span>
//                                     <span>{productTotal}</span>
//                                 </div>
//                                 <div class="content__itemsList__item--inner"><span>{charges_statement}</span></div>
//                             </li>`
//
//   const messageStatement =
//     confirm.orderInfo && confirm.orderInfo.paymentProcessorId === '5'
//       ? 'Charges on your statement will be processed for {productPrice} ({orderNumber}) and will appear as {midDescriptor}'
//       : 'Your deposit will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}. You will be charged the price of the products when they ship.'
//
//   const messageStatementUpsell = 'Charges on your statement will be processed for {productPrice} ({orderNumber}) and will appear as {midDescriptor}'
//   const messageStatementTranslateUpsell =
//     'Charges on your statement will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}'
//   const messageStatementTranslate =
//     confirm.orderInfo && confirm.orderInfo.paymentProcessorId === '5'
//       ? 'Charges on your statement will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}'
//       : 'Your deposit will be processed for {productTotal} ({orderNumber}) and will appear as {midDescriptor}. You will be charged the price of the products when they ship.'
//
//   // init data config data_en.json
//   const js_translate = {
//     shipping: 'Shipping',
//     total: 'Total',
//     product_charges_statement_confirm_page: messageStatementTranslate,
//     product_charges_statement_upsell_confirm_page: messageStatementTranslateUpsell,
//     free: 'free',
//     fomoExtraText: 'JUST NOW'
//   }
//
//   let shippingLabel = 'Shipping'
//   const totalLabel = 'Total'
//   let charges_statement = messageStatement
//   let chargesStatementUpsell = messageStatementUpsell
//
//   if (window.js_translate) {
//     shippingLabel = js_translate.shipping
//     shippingLabel = js_translate.total
//     charges_statement = js_translate.product_charges_statement_confirm_page
//     chargesStatementUpsell = js_translate.product_charges_statement_upsell_confirm_page
//   }
//
//   const getDetailsByItem = (order, charges_statement) =>
//     PRODUCT_DETAILS_TEMPLATE.replaceAll('{charges_statement}', charges_statement)
//       .replaceAll('{productName}', order.productName)
//       .replaceAll('{productPrice}', order.orderProductPriceFormatted)
//       .replaceAll('{shippingLabel}', shippingLabel)
//       .replaceAll('{shippingPrice}', order.shippingPriceFormatted)
//       .replaceAll('{totalLabel}', totalLabel)
//       .replaceAll('{productTotal}', orderDetailsDomainData.getFormattedProductTotal(order, checkoutInfo))
//       .replaceAll('{midDescriptor}', orderDetailsDomainData.getMidDescriptor(order))
//       .replaceAll('{orderNumber}', order.orderNumber)
//
//   const items = order.relatedOrders.reduce((acc, relatedOrderItem) => {
//     if (['Cancel', 'New'].includes(relatedOrderItem.orderStatus)) {
//       return acc
//     }
//
//     return acc + getDetailsByItem(relatedOrderItem, chargesStatementUpsell)
//   }, getDetailsByItem(order, charges_statement))
//
//   return `<ul class="content__itemsList">${items}</ul>`
// }

const populateData = (checkoutInfo, elm, orderData) => {
  console.log(elm)
  console.log(orderData)
  if (!orderData || !orderData.orderNumber || !elm || !elm.innerHTML) {
    throw 'Invalid Data'
  }

  elm.innerHTML = elm.innerHTML
    .replaceAll('{orderNumber}', orderData.orderNumber)
    .replaceAll('{customerName}', orderDetailsDomainData.getDisplayName(orderData))
    .replaceAll('{customerEmail}', `${orderData.customerEmail}`)
    .replaceAll('{orderDate}', getFormattedDateNow())
    .replaceAll('{orderTotal}', orderDetailsDomainData.getFormattedOrderTotal(orderData))
    .replaceAll('{savedTotal}', orderDetailsDomainData.getFormattedSavedTotal(checkoutInfo, orderData))
    .replaceAll('{shippingAddress}', orderDetailsDomainData.getFormattedShippingAddress(orderData))
    .replaceAll('{billingDetails}', orderDetailsDomainData.getFormattedBillingAddress(orderData))
    .replaceAll('{itemsOrdered__title}', getItemsOrderedTitle(orderData))
    .replaceAll('{itemsOrdered__details}', getProductList(checkoutInfo, orderData))

  function getItemsOrderedTitle(order) {
    return order.paymentProcessorId === '31' ? 'ITEMS PRE-ORDERED' : 'ITEMS ORDERED'
  }
}

export default function populateConfirmedInfoByOrder(elm) {
  const { ctrwowUtils } = window

  const checkoutInfo = getOrderInfo() || {}
  const { orderNumber } = checkoutInfo

  console.log(`used field useCreditCard ${checkoutInfo.useCreditCard}`)

  ctrwowUtils.showGlobalLoading()

  // update upsells status in CRM from NEW status to PAID
  // Ref: https://dfoglobal.atlassian.net/browse/CTR-1875
  // const processing = isPaidByPaypal() || paymentProcessorId === '31' ? Promise.resolve(true) : updateUpsellsStatus(orderNumber)

  // no update upsells status for offline payment
  console.log('no update upsells status for offline payment')
  const processing = isPaidByCreditCard() ? updateUpsellsStatus(orderNumber) : Promise.resolve(true)

  processing
    .then(() => getRelatedOrders(orderNumber))
    .then((result) => populateData(checkoutInfo, elm, result))
    .catch((e) => {
      console.log(e)
    })
    .finally(ctrwowUtils.hideGlobalLoading)
}
