import * as productList from 'shared-checkout-flow/src/orderInfo/mainProductList'
import createCrmInstanceBase from 'shared-checkout-flow/src/createCrmInstance'

export default function createCrmInstance(pageSettings) {
  const newEmanageCRM = createCrmInstanceBase(pageSettings)

  // START overwrite getProduct list in EmanageCRM
  const old_getProducts = newEmanageCRM.Campaign.getProducts
  newEmanageCRM.Campaign.getProducts = function (callback) {
    return old_getProducts.call(newEmanageCRM.Campaign, (response) => {
      // set product list
      if (response) {
        productList.setProductList(response)
      }
      callback(response)
    })
  }

  return newEmanageCRM
}
