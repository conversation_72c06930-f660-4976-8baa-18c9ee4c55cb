import getEmanageCRMJS from 'shared-checkout-flow/src/getEmanageCRMJS'
export { redirectToConfirmPage as populateDeclinedInfoByOrder, goBackFromDeclinePage } from './declinePayment'
export { isPaidByPaypal, isPaidByCreditCard } from 'shared-checkout-flow/src/payment/userPaymentType'

if (!window.__ctrDeclinePayment) {
  window.__ctrDeclinePayment = {
    isReady: false
  }
  window.__ctrDeclinePayment.processing = new Promise((resolve) => (window.__ctrDeclinePayment.resolve = resolve))

  window.__ctrDeclinePayment.processing.then(() => {
    console.log('__ctrDeclinePayment get ready !!!')
    window.__ctrDeclinePayment.isReady = true
  })

  getEmanageCRMJS()
    .then(() => {
      console.log('start to int [EmanageCRMJS] instance')
      window.__ctrDeclinePayment.resolve(true)
    })
    .catch((e) => {
      console.error('cannot create ctrwow instance')
    })
}

export const ready = () => window.__ctrDeclinePayment.processing
