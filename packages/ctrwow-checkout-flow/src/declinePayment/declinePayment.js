import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import { updateCurrentBLTrackingParam } from 'shared-trackings'

// when paypal redirect to decline page, if has a order successfully then will redirect to confirm page
export function redirectToConfirmPage() {
  const utils = window.ctrwowUtils
  const { confirmUrl: successUrl } = getPageSettings()

  if (utils.localStorage().get('paypal_isMainOrder') && utils.localStorage().get('paypal_isMainOrder') === 'upsell') {
    let redirectUrl = successUrl + window.ctrwowUtils.link.getCustomPathName() // filter path name with special character "#"
    // let redirectUrl = successUrl + location.search
    // remove gtm upsell purchas event
    const upParams = utils.localStorage().get('fireUpsellForGTMPurchase')

    utils.localStorage().remove('fireUpsellForGTMPurchase')

    if (utils.link.getQueryParameter(upParams)) {
      redirectUrl = utils.link.updateURLParameter(redirectUrl, upParams, '0')
    }

    location.href = updateCurrentBLTrackingParam(redirectUrl)
  } else {
    // remove paypal params
    // check if is redirected from Paypal
    if (utils.link.getQueryParameter('paymentId') || utils.link.getQueryParameter('token')) {
      const hrefSplit = location.href.split('?')
      if (hrefSplit.length > 1) {
        let declineUrl = hrefSplit[0]
        let urlParams = hrefSplit[1]
        urlParams = utils.link.removeParamFromUrl('paymentId', urlParams)
        urlParams = utils.link.removeParamFromUrl('token', urlParams)
        urlParams = utils.link.removeParamFromUrl('PayerID', urlParams)
        declineUrl = urlParams !== '' ? declineUrl + '?' + urlParams : declineUrl
        location.href = updateCurrentBLTrackingParam(declineUrl, null, ['ctr_io'])
      }
    }
  }
}

export const goBackFromDeclinePage = () => {
  window.ctrwowUtils.handleParam.clearParameter('redirect_status')
  window.ctrwowUtils.handleParam.clearParameter('payment_intent_client_secret')
  window.ctrwowUtils.handleParam.clearParameter('payment_intent')
  window.ctrwowUtils.handleParam.clearParameter('trackingNumber')
  const mainOrderLink = localStorage.getItem('mainOrderLink')
  mainOrderLink && window.ctrwowUtils.link.redirectPage(updateCurrentBLTrackingParam(mainOrderLink))
}
