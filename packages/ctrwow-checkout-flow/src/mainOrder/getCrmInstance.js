import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import createCrmInstance from 'shared-checkout-flow/src/createCrmInstance'

let crmInstance = null

export default function getCrmInstance() {
  if (crmInstance) {
    const pageSettings = getPageSettings() || {}

    if (crmInstance.webkey !== pageSettings.webKey || crmInstance.cid !== pageSettings.cid) {
      crmInstance = createCrmInstance(pageSettings)
      console.log('need to init - change config', crmInstance)
    }
    return crmInstance
  }

  crmInstance = createCrmInstance(getPageSettings())
  return crmInstance
}
