export default function clearLocalStorage() {
  if (!window.ctrwowUtils.isLiveMode()) {
    return
  }
  if (typeof window.ctrwowUtils.handleParam === 'object') {
    window.ctrwowUtils.handleParam.clearParameter('10pop', '15pop', 'clickid', 'banner', 'shippro', 'dpop')
  }

  // clear local storage
  // utils.localStorage().remove('orderInfo');
  // utils.localStorage().remove('mainOrderLink');
  // utils.localStorage().remove('user_firstname');
  // utils.localStorage().remove('user_lastname');
  // utils.localStorage().remove('paypal_isMainOrder');
  // utils.localStorage().remove('countryCode');
  // utils.localStorage().remove('currencyCode');
  // utils.localStorage().remove('isSpecialOffer');
  // utils.localStorage().remove('webkey_to_check_paypal');
  // utils.localStorage().remove('userPaymentType');
  // utils.localStorage().remove('loggingInfo');

  // Save Listicle Referrer Url  before clear all
  const listicleReferrerUrl = window.localStorage.getItem('referrerUrl')

  const ctrWowSurvey__id = window.localStorage.getItem('ctrWowSurvey__id')

  // Keep for Ecom
  const ctr__ecom_cart = window.localStorage.getItem('ctr__ecom_cart')
  const ctr__ecom_campaigns = window.localStorage.getItem('ctr__ecom_campaigns')
  const ctr__ecom_order_info = window.localStorage.getItem('ctr__ecom_order_info')

  // Keep for Funnel
  const ctr__funnel_savedinfo = window.localStorage.getItem('ctr__funnel_savedinfo')
  const ctr__countdown_endtime = window.localStorage.getItem('ctr__countdown_endtime')

  // Keep for case check duplicate order with email (CSB-8966)
  const customerForm = window.localStorage.getItem('customerForm')

  // Keep 29NextApiKey
  const ctr_29NextApiKey = window.ctrwowUtils.localStorage().get('29NextApiKey')
  const customerLocationWidget = window.ctrwowUtils.localStorage().get('customerLocationWidget')

  // Keep hosting info for woocommence
  const ctr_endpointURL = window.localStorage.getItem('endpointURL')
  const ctr_apiKey = window.localStorage.getItem('apiKey')
  const ctr_apiSecretKey = window.localStorage.getItem('apiSecretKey')

  const campProducts = window.localStorage.getItem('campproducts')
  if (window.location.pathname.indexOf('/special-offer') === -1) {
    window.localStorage.clear() // clear all items
    window.ctrwowUtils.events.emit('onCompleteClearLS')
  }

  window.sessionStorage.removeItem('cvvdata')

  // Add Listicle Referrer Url  after clear all
  if (listicleReferrerUrl) {
    window.localStorage.setItem('referrerUrl', listicleReferrerUrl)
  }

  if (ctrWowSurvey__id) {
    window.localStorage.setItem('ctrWowSurvey__id', ctrWowSurvey__id)
  }

  if (campProducts) {
    window.localStorage.setItem('campproducts', campProducts)
  }

  if (ctr__ecom_cart) {
    window.localStorage.setItem('ctr__ecom_cart', ctr__ecom_cart)
  }

  if (ctr__ecom_campaigns) {
    window.localStorage.setItem('ctr__ecom_campaigns', ctr__ecom_campaigns)
  }

  if (ctr__ecom_order_info) {
    window.localStorage.setItem('ctr__ecom_order_info', ctr__ecom_order_info)
  }

  if (ctr__funnel_savedinfo) {
    window.localStorage.setItem('ctr__funnel_savedinfo', ctr__funnel_savedinfo)
  }

  if (ctr__countdown_endtime) {
    window.localStorage.setItem('ctr__countdown_endtime', ctr__countdown_endtime)
  }

  if (customerForm) {
    window.localStorage.setItem('customerForm', customerForm)
  }

  if (ctr_endpointURL) {
    window.localStorage.setItem('endpointURL', ctr_endpointURL)
  }

  if (ctr_apiKey) {
    window.localStorage.setItem('apiKey', ctr_apiKey)
  }

  if (ctr_apiSecretKey) {
    window.localStorage.setItem('apiSecretKey', ctr_apiSecretKey)
  }

  if (ctr_29NextApiKey) {
    window.localStorage.setItem('29NextApiKey', ctr_29NextApiKey)
  }

  if (customerLocationWidget) {
    window.localStorage.setItem('customerLocationWidget', customerLocationWidget)
    window.localStorage.setItem('location', customerLocationWidget)
  }
}
