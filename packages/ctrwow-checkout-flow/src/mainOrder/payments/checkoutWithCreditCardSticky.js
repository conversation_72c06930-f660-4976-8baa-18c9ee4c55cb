import { submitCheckoutInfoSticky, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from './../order/mainOrderInfo'

import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getBillingModelID(billing_models) {
  window.billingModelIndex = window.billingModelIndex || 0
  for (let i = 0, n = billing_models.length; i < n; i++) {
    if (billing_models[i].index === window.billingModelIndex) {
      return billing_models[i].id
    }
  }
}
function getExpirationDate(expiration) {
  const str = expiration.split('/')
  return str[0] + str[1].substr(2, 4)
}
function getCreditCardNumber(cardNumberStr) {
  const card = cardNumberStr.replace(/[^0-9]/g, '')
  return card
}
function getOrderDataSticky({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {} } = {}) {
  const product = mainOrderInfo.getProduct()

  window.shippingIndex = window.shippingIndex || 0
  const expirationDate = getExpirationDate(payment.expiration)
  const creditCardNumber = getCreditCardNumber(payment.creditcard)
  const shippingId = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null
  const shippingPrice = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : null
  const shippingFormatPrice =
    product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].formattedPrice : null
  const commonAddressInfo = {
    firstName: customer.firstName,
    lastName: customer.lastName,
    phone: customer.phoneNumber || '',
    phoneNumber: customer.phoneNumber || ''
  }
  const shippingInfomation = {
    shippingFirstName: shippingAddress.firstName || customer.firstName,
    shippingLastName: shippingAddress.lastName || customer.lastName,
    shippingAddress1: shippingAddress.address1,
    shippingAddress2: shippingAddress.address2 || '',
    shippingCity: shippingAddress.city,
    shippingState: shippingAddress.state,
    shippingZip: shippingAddress.zipCode,
    shippingCountry: shippingAddress.countryCode
  }
  const billingInfomation = {
    billingFirstName: billingAddress.firstName || customer.firstName,
    billingLastName: billingAddress.lastName || customer.lastName,
    billingAddress1: billingAddress.address1 || shippingAddress.address1,
    billingAddress2: billingAddress.address2 || shippingAddress.address2 || '',
    billingCity: billingAddress.city || shippingAddress.city,
    billingState: billingAddress.state || shippingAddress.state,
    billingZip: billingAddress.zipCode || shippingAddress.zipCode,
    billingCountry: billingAddress.countryCode || shippingAddress.countryCode
  }
  let chosenProducts = [
    {
      offer_id: product.offerId,
      product_id: product && product.productId,
      billing_model_id: getBillingModelID(product.billing_models),
      quantity: product && product.quantity
    }
  ]
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    chosenProducts = [
      ...chosenProducts,
      {
        offer_id: lw_product.offerId,
        product_id: lw_product.productId,
        billing_model_id: 2,
        quantity: lw_product.quantity
      }
    ]
  }
  if (product.is_trial_product === 1) {
    chosenProducts[0].trial = {
      product_id: product.productId
    }
  }

  return {
    ...commonAddressInfo,
    email: customer.email,
    ...shippingInfomation,
    billingSameAsShipping: billingAddress && billingAddress.address1 ? 'NO' : 'YES',
    ...billingInfomation,
    creditCardType: payment.creditCardBrand,
    creditCardNumber: creditCardNumber,
    expirationDate: expirationDate,
    CVV: payment.cvv,
    shippingId: shippingId,
    shippingPrice: shippingPrice,
    shippingFormatPrice: shippingFormatPrice,
    shippingMethodId: shippingId,
    campaignId: product.campaignId,
    offers: chosenProducts,
    // comment: '',
    // funnelBoxId: mainOrderInfo.getFunnelBoxId(),
    // productId: product && product.productId,

    customer: {
      ...commonAddressInfo
    },
    payment: {
      name: '',
      creditcard: creditCardNumber,
      creditCardBrand: payment.creditCardBrand,
      expiration: payment.expiration,
      cvv: payment.cvv
    },
    useShippingAddressForBilling: !(billingAddress && billingAddress.address1),
    shippingAddress: shippingInfomation,
    billingAddress: billingInfomation,
    tranType: 'Sale',
    ipAddress: window.ipAddress
  }
}
// detect cardType
function GetCardType(number) {
  // visa
  var re = new RegExp('^4')
  if (number.match(re) != null) return 'Visa'

  // Mastercard
  // Updated for Mastercard 2017 BINs expansion
  if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(number)) return 'Mastercard'

  // AMEX
  re = new RegExp('^3[47]')
  if (number.match(re) != null) return 'AMEX'

  // Discover
  re = new RegExp('^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)')
  if (number.match(re) != null) return 'Discover'

  // Diners
  re = new RegExp('^36')
  if (number.match(re) != null) return 'Diners'

  // Diners - Carte Blanche
  re = new RegExp('^30[0-5]')
  if (number.match(re) != null) return 'Diners - Carte Blanche'

  // JCB
  re = new RegExp('^35(2[89]|[3-8][0-9])')
  if (number.match(re) != null) return 'JCB'

  // Visa Electron
  re = new RegExp('^(4026|417500|4508|4844|491(3|7))')
  if (number.match(re) != null) return 'Visa Electron'

  return 'Credit Card'
}
export default function checkoutWithCreditCardSticky(paymentInfo, flag = false) {
  console.log(paymentInfo)
  const { ctrwowUtils } = window

  const $checkoutWithCreditCardButtonV1 = $('button[name="checkoutWithCreditCardSticky"]')
  const $checkoutWithCreditCardButton = $checkoutWithCreditCardButtonV1.length
    ? $checkoutWithCreditCardButtonV1
    : $('button[name="checkoutWithCreditCard"]')

  const orderData = getOrderDataSticky(paymentInfo)
  if (document.getElementById('installpaymentDdl')) {
    orderData.payment.Instalments = document.getElementById('installpaymentDdl').value
  }
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

  return submitCheckoutInfoSticky(orderData, PAYMENT_METHOD.CREDIT_CARD)
    .then(async (result) => {
      console.log('credit card checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder')
      // window.localStorage.setItem('mainOrderLink', location.pathname)
      // window.localStorage.setItem('userPaymentType', 'paypal')

      // window.localStorage.setItem('paypal_isMainOrder', 'main')
      // window.localStorage.setItem('webkey_to_check_paypal', getCrmInstance().webkey)
      // save cardType
      if (result.useCreditCard) {
        const cardType = GetCardType(orderData.payment.creditcard)
        window.ctrwowUtils.localStorage().set('cardType', cardType)
      }

      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('credit card checkout - success - go to next page', nextLink)

      const delayFirstStep = typeof window.timeDelayFirstStep === 'number' ? window.timeDelayFirstStep : 0
      await delay(delayFirstStep)

      window._q('.paymentProccessing_cc .processing') && window._q('.paymentProccessing_cc .processing').classList.add('hidden')
      window._q('.paymentProccessing_cc .successed') && window._q('.paymentProccessing_cc .successed').classList.remove('hidden')

      const timeDelay = typeof window.timeDelayBeforeRedirect === 'number' ? window.timeDelayBeforeRedirect : 300
      await delay(timeDelay)

      if (flag) {
        return result
      }

      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithCreditCardButton)
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithCreditCardButton)
        ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
    .catch((e) => {
      console.log(e)
      console.log('credit card checkout - fail')
      // const isUsedPaymentDecline = arguments.length > 2 ? arguments[2].isUsedPaymentDecline : null
      // if (isUsedPaymentDecline) {
      //   throw e
      // }

      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithCreditCardButton)
      ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
