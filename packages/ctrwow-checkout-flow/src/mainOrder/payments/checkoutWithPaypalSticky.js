import { submitCheckoutInfoSticky, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam, mergeCurrentParamsWithCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getBillingModelID(billing_models) {
  window.billingModelIndex = window.billingModelIndex || 0
  for (let i = 0, n = billing_models.length; i < n; i++) {
    if (billing_models[i].index === window.billingModelIndex) {
      return billing_models[i].id
    }
  }
}
// function getOrderDataSticky({ customer = {}, shippingAddress = {}, billingAddress = {} } = {}) {
function getOrderDataSticky() {
  const product = mainOrderInfo.getProduct()

  window.shippingIndex = window.shippingIndex || 0
  const shippingId = product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null
  const shippingPrice = product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : null
  const shippingFormatPrice = product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].formattedPrice : null
  // const commonAddressInfo = {
  //   firstName: customer.firstName || '',
  //   lastName: customer.lastName || '',
  //   phone: customer.phoneNumber || '',
  //   phoneNumber: customer.phoneNumber || ''
  // }
  // const shippingInfomation = {
  //   shippingAddress1: shippingAddress.address1 || '',
  //   shippingAddress2: shippingAddress.address2 || '',
  //   shippingCity: shippingAddress.city || '',
  //   shippingState: shippingAddress.state || '',
  //   shippingZip: shippingAddress.zipCode || '',
  //   shippingCountry: shippingAddress.countryCode || ''
  // }
  // const billingInfomation = {
  //   billingFirstName: customer.firstName || '',
  //   billingLastName: customer.lastName || '',
  //   billingAddress1: billingAddress.address1 || shippingAddress.address1,
  //   billingAddress2: billingAddress.address2 || shippingAddress.address2 || '',
  //   billingCity: billingAddress.city || shippingAddress.city,
  //   billingState: billingAddress.state || shippingAddress.state,
  //   billingZip: billingAddress.zipCode || shippingAddress.zipCode,
  //   billingCountry: billingAddress.countryCode || shippingAddress.countryCode
  // }
  let chosenProducts = [
    {
      offer_id: product.offerId,
      product_id: product && product.productId,
      billing_model_id: getBillingModelID(product.billing_models),
      quantity: product && product.quantity
    }
  ]
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    chosenProducts = [
      ...chosenProducts,
      {
        offer_id: lw_product.offerId,
        product_id: lw_product.productId,
        billing_model_id: 2,
        quantity: lw_product.quantity
      }
    ]
  }

  const miniUpsellItems = window.ctrwowCheckout.checkoutData.getMiniUpsell() || []
  if (miniUpsellItems.length) {
    for (let i = 0, n = miniUpsellItems.length; i < n; i++) {
      chosenProducts = [
        ...chosenProducts,
        {
          offer_id: miniUpsellItems[i].offerId,
          product_id: miniUpsellItems[i].productId,
          billing_model_id: 2,
          quantity: miniUpsellItems[i].quantity
        }
      ]
    }
  }

  const url = window.location.href
  const path = url.substring(0, url.lastIndexOf('/')) + '/'
  return {
    creditCardType: 'paypal',
    alt_pay_return_url: path + window.pageUrls.successUrl + window.location.search,
    // ...commonAddressInfo,
    // email: customer.email || '',
    // ...shippingInfomation,
    // billingSameAsShipping: billingAddress && billingAddress.address1 ? 'NO' : 'YES',
    // ...billingInfomation,
    shippingId: shippingId,
    shippingPrice: shippingPrice,
    shippingFormatPrice: shippingFormatPrice,
    shippingMethodId: shippingId,
    campaignId: product.campaignId,
    offers: chosenProducts,
    // customer: {
    //   ...commonAddressInfo
    // },
    // useShippingAddressForBilling: !(billingAddress && billingAddress.address1),
    // shippingAddress: { ...commonAddressInfo, ...shippingInfomation },
    // billingAddress: { ...commonAddressInfo, ...billingInfomation },
    tranType: 'Sale',
    ipAddress: window.ipAddress
  }
}

// export default function checkoutWithPaypalSticky(paymentInfo, flag = false) {
export default function checkoutWithPaypalSticky(flag = false) {
  const $checkoutWithPaypalButton = $('button.ctaSubmitPP')
  // const orderData = getOrderDataSticky(paymentInfo)
  const orderData = getOrderDataSticky()
  const params = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutWithPaypalButton)
  orderData.alt_pay_return_url += params
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrderPaypal')
  return submitCheckoutInfoSticky(orderData, PAYMENT_METHOD.PAYPAL)
    .then((result) => {
      if (!result || (result && !result.success)) {
        // eslint-disable-next-line no-throw-literal
        throw 'paypal checkout - fail'
      }
      console.log('paypal checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrderPaypal')

      window.localStorage.setItem('paypal_isMainOrder', 'main')
      // window.localStorage.setItem('webkey_to_check_paypal', '')

      // Use in Diggy Popup
      if (flag) return result

      // Get paypal link then redirect
      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('Go to next page', nextLink)

      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithPaypalButton)
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithPaypalButton)
        window.ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
    .catch((e) => {
      console.log(e)
      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithPaypalButton)
      window.ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
