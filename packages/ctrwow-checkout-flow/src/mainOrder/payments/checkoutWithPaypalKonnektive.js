import { submitCheckoutInfoKonnektive, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam, mergeCurrentParamsWithCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getOrderDataKonnektive({ customer = {}, shippingAddress = {}, billingAddress = {} } = {}) {
  const product = mainOrderInfo.getProduct()

  const commonUserInfo = {
    first_name: customer.firstName,
    last_name: customer.lastName,
    email: customer.email,
    phone_number: customer.phoneNumber || ''
  }
  const shippingInfomation = {
    line1: shippingAddress.address1,
    line2: shippingAddress.address2 || '',
    line4: shippingAddress.city,
    state: shippingAddress.state,
    country: shippingAddress.countryCode,
    postcode: shippingAddress.zipCode
  }
  const billingInfomation = {
    line1: billingAddress.address1 || shippingAddress.address1,
    line2: billingAddress.address2 || shippingAddress.address2 || '',
    line4: billingAddress.city || shippingAddress.city,
    state: billingAddress.state || shippingAddress.state,
    country: billingAddress.countryCode || shippingAddress.countryCode,
    postcode: billingAddress.zipCode || shippingAddress.zipCode
  }

  window.shippingIndex = window.shippingIndex || 0
  const shippingPrice = product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : null
  const shippingFormatPrice = product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].formattedPrice : null
  let chosenProducts = [
    {
      type: 'main',
      campaignId: product.campaignId,
      productId: product && product.productId,
      sku: product.sku,
      price: product.productPrices.DiscountedPrice.Value,
      currency: product.productPrices.DiscountedPrice.GlobalCurrencyCode,
      quantity: product && product.quantity,
      is_upsell: false
    }
  ]
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    chosenProducts = [
      ...chosenProducts,
      {
        type: 'lw',
        campaignId: lw_product.campaignId,
        productId: lw_product.productId,
        sku: lw_product.sku,
        price: lw_product.productPrices.DiscountedPrice.Value,
        currency: lw_product.productPrices.DiscountedPrice.GlobalCurrencyCode,
        quantity: lw_product.quantity,
        is_upsell: true
      }
    ]
  }

  const miniUpsellItems = window.ctrwowCheckout.checkoutData.getMiniUpsell() || []
  if (miniUpsellItems.length) {
    for (let i = 0, n = miniUpsellItems.length; i < n; i++) {
      chosenProducts = [
        ...chosenProducts,
        {
          type: 'upsell',
          campaignId: miniUpsellItems[i].campaignId,
          productId: miniUpsellItems[i].productId,
          sku: miniUpsellItems[i].sku,
          price: miniUpsellItems[i].productPrices.DiscountedPrice.Value,
          currency: miniUpsellItems[i].productPrices.DiscountedPrice.GlobalCurrencyCode,
          quantity: miniUpsellItems[i].quantity,
          is_upsell: true
        }
      ]
    }
  }

  const url = window.location.href
  const path = url.substring(0, url.lastIndexOf('/')) + '/'

  return {
    creditCardType: 'paypal',
    user: commonUserInfo,
    salesUrl: path + window.pageUrls.successUrl,
    // redirectsTo: path + window.pageUrls.successUrl,
    redirectsTo: path + window.upsellUrls[0].upsellUrl,
    errorRedirectsTo: path + window.pageUrls.declineUrl,
    shippingPrice: shippingPrice,
    shippingFormatPrice: shippingFormatPrice,
    shipping_address: shippingInfomation,
    billing_same_as_shipping_address: !(billingAddress && billingAddress.address1),
    billing_address: billingAddress && billingAddress.address1 ? billingInfomation : null,
    campaignId: product.campaignId,
    chosenProducts: chosenProducts,
    ipAddress: window.ipAddress
  }
}

// export default function checkoutWithPaypalKonnektive(paymentInfo, flag = false) {
export default function checkoutWithPaypalKonnektive(paymentInfo, flag = false) {
  const $checkoutWithPaypalButton = $('button.ctaSubmitPP')
  const orderData = getOrderDataKonnektive(paymentInfo)
  const params = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutWithPaypalButton)
  orderData.salesUrl += params
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrderPaypal')
  return submitCheckoutInfoKonnektive(orderData, PAYMENT_METHOD.PAYPAL)
    .then((result) => {
      // !TODO result === "MERC_REDIRECT" && message.url
      if (!result || (result && result.result !== 'MERC_REDIRECT')) {
        // eslint-disable-next-line no-throw-literal
        throw 'paypal checkout - fail'
      }
      console.log('paypal checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrderPaypal')

      window.localStorage.setItem('paypal_isMainOrder', 'main')
      // window.localStorage.setItem('webkey_to_check_paypal', '')

      // Use in Diggy Popup
      if (flag) return result

      // Get paypal link then redirect
      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('Go to next page', nextLink)

      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithPaypalButton)
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithPaypalButton)
        window.ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
    .catch((e) => {
      console.log(e)
      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithPaypalButton)
      window.ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
