import { submitCheckoutInfo, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from './../order/mainOrderInfo'
import getCrmInstance from './../getCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getOrderData({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {} } = {}) {
  console.log('start get order data !')

  const product = mainOrderInfo.getProduct()
  const commonAddressInfo = {
    firstName: customer.firstName,
    lastName: customer.lastName,
    phoneNumber: customer.phoneNumber
  }

  window.shippingIndex = window.shippingIndex || 0

  return {
    comment: '',
    funnelBoxId: mainOrderInfo.getFunnelBoxId(),
    productId: product && product.productId,
    shippingMethodId: product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null,
    customer: customer,
    payment: {
      paymentProcessorId: 41
    },
    useShippingAddressForBilling: !(billingAddress && billingAddress.address1),
    shippingAddress: shippingAddress && shippingAddress.address1 ? { ...commonAddressInfo, ...shippingAddress } : null,
    billingAddress: null
  }
}

export default function checkoutWithSofort(paymentInfo, flag = false) {
  console.log(paymentInfo)
  window.ctrwowUtils.events.emit('beforeGetOrderData')

  const { ctrwowUtils } = window
  const $checkoutWithSofortButton = $('button[name="checkoutWithSofort"]')

  const orderData = getOrderData(paymentInfo)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')

  return submitCheckoutInfo(orderData, PAYMENT_METHOD.STRIPE)
    .then((result) => {
      console.log('sofort checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder', result)

      window.localStorage.setItem('ideal_isMainOrder', 'main')
      window.localStorage.setItem('webkey_to_check_sofort', getCrmInstance().webkey)
      window.localStorage.setItem('webkey_for_success_page', getCrmInstance().webkey)

      // success page will use this trackingNumber to call comfirm payment api
      window.localStorage.setItem('trackingNumber', result.trackingNumber)
      if (flag) {
        return result
      }

      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('sofort checkout - success - go to next page', nextLink)
      console.log(nextLink)

      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithSofortButton)
      } else {
        ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(getPageSettings().confirmUrl, $checkoutWithSofortButton))
      }
    })
    .catch((e) => {
      console.log(e)
      console.log('sofort checkout - fail')
      ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(getCrmInstance().declineUrl, $checkoutWithSofortButton))
    })
}
