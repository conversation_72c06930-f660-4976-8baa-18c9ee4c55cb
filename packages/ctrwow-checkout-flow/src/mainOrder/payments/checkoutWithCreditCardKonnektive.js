import { submitCheckoutInfoKonnektive, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'

import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getCreditCardNumber(cardNumberStr) {
  const card = cardNumberStr.replace(/[^0-9]/g, '')
  return card
}
function convertMonthYearObj(my) {
  return {
    month: Number(my.split('/')[0]),
    year: Number(my.split('/')[1])
  }
}
function getOrderDataKonnektive({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {} } = {}) {
  const product = mainOrderInfo.getProduct()
  window.shippingIndex = window.shippingIndex || 0
  const creditCardNumber = getCreditCardNumber(payment.creditcard)
  const shippingCode = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null
  const shippingPrice = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : null
  const shippingFormatPrice =
    product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].formattedPrice : null
  const commonUserInfo = {
    first_name: customer.firstName,
    last_name: customer.lastName,
    phone_number: customer.phoneNumber || ''
  }
  const shippingInfomation = {
    ...commonUserInfo,
    line1: shippingAddress.address1,
    line2: shippingAddress.address2 || '',
    line4: shippingAddress.city,
    state: shippingAddress.state,
    country: shippingAddress.countryCode,
    postcode: shippingAddress.zipCode
  }
  const billingInfomation = {
    ...commonUserInfo,
    line1: billingAddress.address1 || shippingAddress.address1,
    line2: billingAddress.address2 || shippingAddress.address2 || '',
    line4: billingAddress.city || shippingAddress.city,
    state: billingAddress.state || shippingAddress.state,
    country: billingAddress.countryCode || shippingAddress.countryCode,
    postcode: billingAddress.zipCode || shippingAddress.zipCode
  }
  const paymentDetails = {
    card_name: `${customer.firstName} ${customer.lastName}`,
    card_number: creditCardNumber,
    creditCardBrand: payment.creditCardBrand,
    card_expiry_month: convertMonthYearObj(payment.expiration).month,
    card_expiry_year: convertMonthYearObj(payment.expiration).year,
    card_cvv: payment.cvv
  }
  let chosenProducts = [
    {
      type: 'main',
      campaignId: product.campaignId,
      productId: product.productId,
      sku: product.sku,
      price: product.productPrices.DiscountedPrice.Value,
      currency: product.productPrices.DiscountedPrice.GlobalCurrencyCode,
      quantity: product && product.quantity,
      is_upsell: false
    }
  ]

  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    chosenProducts = [
      ...chosenProducts,
      {
        type: 'lw',
        campaignId: lw_product.campaignId,
        productId: lw_product.productId,
        sku: lw_product.sku,
        price: lw_product.productPrices.DiscountedPrice.Value,
        currency: lw_product.productPrices.DiscountedPrice.GlobalCurrencyCode,
        quantity: lw_product.quantity,
        is_upsell: true
      }
    ]
  }

  return {
    campaignId: product.campaignId,
    shipping_code: shippingCode,
    shippingPrice: shippingPrice,
    shippingFormatPrice: shippingFormatPrice,

    chosenProducts: chosenProducts,
    user: {
      ...commonUserInfo,
      email: customer.email
    },
    creditCardType: payment.creditCardBrand,
    payment_method: 'bankcard',
    payment_details: paymentDetails,
    billing_same_as_shipping_address: !(billingAddress && billingAddress.address1),
    shipping_address: shippingInfomation,
    billing_address: billingAddress && billingAddress.address1 ? billingInfomation : null,
    useCreditCard: true,
    ipAddress: window.ipAddress
  }
}
// detect cardType
function GetCardType(number) {
  // visa
  var re = new RegExp('^4|^7')
  if (number.match(re) != null) return 'Visa'

  // Mastercard
  // Updated for Mastercard 2017 BINs expansion
  if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(number)) return 'Mastercard'

  // AMEX
  re = new RegExp('^3[47]')
  if (number.match(re) != null) return 'AMEX'

  // Discover
  re = new RegExp('^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)')
  if (number.match(re) != null) return 'Discover'

  // Diners
  re = new RegExp('^36')
  if (number.match(re) != null) return 'Diners'

  // Diners - Carte Blanche
  re = new RegExp('^30[0-5]')
  if (number.match(re) != null) return 'Diners - Carte Blanche'

  // JCB
  re = new RegExp('^35(2[89]|[3-8][0-9])')
  if (number.match(re) != null) return 'JCB'

  // Visa Electron
  re = new RegExp('^(4026|417500|4508|4844|491(3|7))')
  if (number.match(re) != null) return 'Visa Electron'

  return 'Credit Card'
}
export default function checkoutWithCreditCardKonnektive(paymentInfo, flag = false) {
  console.log(paymentInfo)
  const { ctrwowUtils } = window

  const $checkoutWithCreditCardButtonV1 = $('button[name="checkoutWithCreditCardKonnektive"]')
  const $checkoutWithCreditCardButton = $checkoutWithCreditCardButtonV1.length
    ? $checkoutWithCreditCardButtonV1
    : $('button[name="checkoutWithCreditCardKonnektive"]')

  const orderData = getOrderDataKonnektive(paymentInfo)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

  return submitCheckoutInfoKonnektive(orderData, PAYMENT_METHOD.CREDIT_CARD)
    .then(async (result) => {
      console.log('credit card checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder')

      // save cardType
      const cardType = GetCardType(orderData.payment_details.card_number)
      window.ctrwowUtils.localStorage().set('cardType', cardType)

      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('credit card checkout - success - go to next page', nextLink)

      const delayFirstStep = typeof window.timeDelayFirstStep === 'number' ? window.timeDelayFirstStep : 0
      await delay(delayFirstStep)

      window._q('.paymentProccessing_cc .processing') && window._q('.paymentProccessing_cc .processing').classList.add('hidden')
      window._q('.paymentProccessing_cc .successed') && window._q('.paymentProccessing_cc .successed').classList.remove('hidden')

      const timeDelay = typeof window.timeDelayBeforeRedirect === 'number' ? window.timeDelayBeforeRedirect : 300
      await delay(timeDelay)

      if (flag) {
        return result
      }

      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithCreditCardButton)
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithCreditCardButton)
        ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
    .catch((e) => {
      console.log('credit card checkout - fail')

      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithCreditCardButton)
      ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
