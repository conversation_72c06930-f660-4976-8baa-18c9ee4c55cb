import { submitCheckoutInfo29Next, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'

import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function generateSupscription(chosenProducts) {
  if (typeof window.subscription === 'object') {
    chosenProducts = [
      {
        ...{ ...chosenProducts[0] },
        subscription: {
          interval: window.subscription.type,
          interval_count: window.subscription.value
        }
      }
    ]
  }
}
// function getCreditCardNumber(cardNumberStr) {
//   const card = cardNumberStr.replace(/[^0-9]/g, '')
//   return card
// }
// function convertMonthYearObj(my) {
//   return {
//     month: Number(my.split('/')[0]),
//     year: Number(my.split('/')[1])
//   }
// }
function getOrderData29Next({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {}, payment_detail = {} } = {}) {
  const product = mainOrderInfo.getProduct()
  window.shippingIndex = window.shippingIndex || 0
  // const creditCardNumber = getCreditCardNumber(payment.creditcard)
  const shippingCode = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null
  const shippingPrice = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : null
  const shippingFormatPrice =
    product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].formattedPrice : null
  const commonUserInfo = {
    first_name: customer.firstName,
    last_name: customer.lastName,
    phone_number: customer.phoneNumber || ''
  }
  const shippingInfomation = {
    ...commonUserInfo,
    line1: shippingAddress.address1,
    line2: shippingAddress.address2 || '',
    line4: shippingAddress.city,
    state: shippingAddress.state,
    country: shippingAddress.countryCode,
    postcode: shippingAddress.zipCode
  }
  const billingInfomation = {
    ...commonUserInfo,
    line1: billingAddress.address1 || shippingAddress.address1,
    line2: billingAddress.address2 || shippingAddress.address2 || '',
    line4: billingAddress.city || shippingAddress.city,
    state: billingAddress.state || shippingAddress.state,
    country: billingAddress.countryCode || shippingAddress.countryCode,
    postcode: billingAddress.zipCode || shippingAddress.zipCode
  }
  const paymentDetails = {
    // card_name: `${customer.firstName} ${customer.lastName}`,
    // card_number: creditCardNumber,
    // creditCardBrand: payment.creditCardBrand,
    // creditCardType: payment.creditCardBrand,
    // card_expiry_month: convertMonthYearObj(payment.expiration).month,
    // card_expiry_year: convertMonthYearObj(payment.expiration).year,
    // card_cvv: payment.cvv
    ...payment_detail
  }
  let chosenProducts = [
    {
      // type: 'main',
      // sku: product.sku,
      // price: product.productPrices.DiscountedPrice.Value,
      // currency: product.productPrices.DiscountedPrice.GlobalCurrencyCode,
      // quantity: product && product.quantity,
      // is_upsell: false,
      // quantity: product.quantity, // product.quantity = product quantity in package
      package_id: product.productId,
      quantity: 1,
      is_upsell: false
    }
  ]
  generateSupscription(chosenProducts)
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    chosenProducts = [
      ...chosenProducts,
      {
        // type: 'lw',
        // sku: lw_product.sku,
        // price: lw_product.productPrices.DiscountedPrice.Value,
        // currency: lw_product.productPrices.DiscountedPrice.GlobalCurrencyCode,
        // quantity: lw_product.quantity,
        // is_upsell: true
        package_id: lw_product.productId,
        quantity: 1,
        is_upsell: true
      }
    ]
  }

  const url = window.location.href
  const pathname = window.location.pathname
  const curentUrlPath = pathname.substring(pathname.lastIndexOf('/') + 1)
  const arrUrlLocaton = url.split(curentUrlPath)

  const getDomain = () => {
    let url = location.href
    try {
      const param = url.split('?')

      if (param.length === 1) return url

      const arrParam = param[1].split('&')
      const tempParam = []
      arrParam.forEach((p) => {
        const oneParam = p.toLocaleLowerCase()
        const arrOne = oneParam.split('=')
        if (arrOne[0].indexOf('ctr_') === -1) {
          tempParam.push(p)
        }
      })
      param[1] = tempParam.join('&')
      url = `${param[0]}?${param[1]}`
    } catch (e) {
      console.log('parse url error')
    }

    return url
  }
  const attribution = {
    funnel: window.funnelID,
    gclid: window.ctrwowUtils.handleParam.getQueryParameter('gclid') || '',
    metadata: {
      everflow_transaction_id: window.ctrwowUtils.handleParam.getQueryParameter('s5') || '',
      domain: getDomain(),
      device: window.ctrwowUtils.getDevice().desktop() ? 'desktop' : 'mobile',
      offer_id: window.ctrwowUtils.handleParam.getQueryParameter('s4') || ''
    },
    subaffiliate1: window.ctrwowUtils.handleParam.getQueryParameter('s1') || '',
    subaffiliate2: window.ctrwowUtils.handleParam.getQueryParameter('s2') || '',
    subaffiliate3: window.ctrwowUtils.handleParam.getQueryParameter('s3') || '',
    subaffiliate4: window.ctrwowUtils.handleParam.getQueryParameter('s4') || '',
    subaffiliate5: window.ctrwowUtils.handleParam.getQueryParameter('s5') || '',
    affid: window.ctrwowUtils.handleParam.getQueryParameter('affid') || '',
    network_id: window.ctrwowUtils.handleParam.getQueryParameter('network_id') || '',
    ttclid: window.ctrwowUtils.handleParam.getQueryParameter('ttclid') || ''
  }

  return {
    campaignId: '',
    shipping_code: shippingCode,
    shippingPrice: shippingPrice,
    shippingFormatPrice: shippingFormatPrice,
    shipping_method: product.shippings[window.shippingIndex].ref_id,
    lines: chosenProducts,
    user: {
      ...commonUserInfo,
      email: customer.email
    },
    payment_method: 'bankcard',
    payment_detail: paymentDetails,
    // billing_same_as_shipping_address: !(billingAddress && billingAddress.address1),
    shipping_address: shippingInfomation,
    billing_address: billingAddress && billingAddress.address1 ? billingInfomation : shippingInfomation,
    useCreditCard: true,
    ipAddress: window.ipAddress,
    success_url: `${arrUrlLocaton[0]}${window.ctrwowUtils.link.mergeWithCurrentQueryString(window.pageUrls.successUrl)}`,
    payment_failed_url: `${arrUrlLocaton[0]}${window.ctrwowUtils.link.mergeWithCurrentQueryString(window.pageUrls.declineUrl)}`,
    attribution: attribution
  }
}
// detect cardType
// function GetCardType(number) {
//   // visa
//   var re = new RegExp('^4')
//   if (number.match(re) != null) return 'Visa'

//   // Mastercard
//   // Updated for Mastercard 2017 BINs expansion
//   if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(number)) return 'Mastercard'

//   // AMEX
//   re = new RegExp('^3[47]')
//   if (number.match(re) != null) return 'AMEX'

//   // Discover
//   re = new RegExp('^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)')
//   if (number.match(re) != null) return 'Discover'

//   // Diners
//   re = new RegExp('^36')
//   if (number.match(re) != null) return 'Diners'

//   // Diners - Carte Blanche
//   re = new RegExp('^30[0-5]')
//   if (number.match(re) != null) return 'Diners - Carte Blanche'

//   // JCB
//   re = new RegExp('^35(2[89]|[3-8][0-9])')
//   if (number.match(re) != null) return 'JCB'

//   // Visa Electron
//   re = new RegExp('^(4026|417500|4508|4844|491(3|7))')
//   if (number.match(re) != null) return 'Visa Electron'

//   return 'Credit Card'
// }
export default function checkoutWithCreditCard29Next(paymentInfo, flag = false) {
  console.log(paymentInfo)
  const { ctrwowUtils } = window

  const $checkoutWithCreditCardButtonV1 = $('button[name="checkoutWithCreditCard29Next"]')
  const $checkoutWithCreditCardButton = $checkoutWithCreditCardButtonV1.length
    ? $checkoutWithCreditCardButtonV1
    : $('button[name="checkoutWithCreditCard29Next"]')

  const orderData = getOrderData29Next(paymentInfo)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

  return submitCheckoutInfo29Next(orderData, PAYMENT_METHOD.CREDIT_CARD)
    .then(async (result) => {
      console.log('credit card checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder')

      // save cardType
      // const cardType = GetCardType(orderData.payment_details?.card_number || 0)
      const cardType = orderData.payment_detail?.payment_method === 'card_token' ? 'Credit Card' : ''
      window.ctrwowUtils.localStorage().set('cardType', cardType)

      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('credit card checkout - success - go to next page', nextLink)

      const delayFirstStep = typeof window.timeDelayFirstStep === 'number' ? window.timeDelayFirstStep : 0
      await delay(delayFirstStep)

      window._q('.paymentProccessing_cc .processing') && window._q('.paymentProccessing_cc .processing').classList.add('hidden')
      window._q('.paymentProccessing_cc .successed') && window._q('.paymentProccessing_cc .successed').classList.remove('hidden')

      const timeDelay = typeof window.timeDelayBeforeRedirect === 'number' ? window.timeDelayBeforeRedirect : 300
      await delay(timeDelay)

      // 3DS case
      if (result && result.callBackUrl) {
        window.ctrwowUtils.link.redirectPage(result.callBackUrl)
      } else {
        if (flag) {
          return result
        }

        if (nextLink) {
          location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithCreditCardButton)
        } else {
          const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithCreditCardButton)
          ctrwowUtils.link.redirectPage(redirectUrl)
        }
      }
    })
    .catch((e) => {
      console.log('credit card checkout - fail')

      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithCreditCardButton)
      ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
