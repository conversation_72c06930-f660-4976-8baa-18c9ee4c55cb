import { submitCheckoutInfo29Next, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam, mergeCurrentParamsWithCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function generateSupscription(chosenProducts) {
  if (typeof window.subscription === 'object') {
    chosenProducts = [
      {
        ...{ ...chosenProducts[0] },
        subscription: {
          interval: window.subscription.type,
          interval_count: window.subscription.value
        }
      }
    ]
  }
}

function getOrderData29Next() {
  const product = mainOrderInfo.getProduct()
  window.shippingIndex = window.shippingIndex || 0
  const shippingCode = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null
  const shippingPrice = product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : null
  const shippingFormatPrice =
    product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].formattedPrice : null
  let chosenProducts = [
    {
      // type: 'main',
      // sku: product.sku,
      // price: product.productPrices.DiscountedPrice.Value,
      // currency: product.productPrices.DiscountedPrice.GlobalCurrencyCode,
      // quantity: product && product.quantity,
      // is_upsell: false
      package_id: product.productId,
      // quantity: product.quantity, // product.quantity = product quantity in package
      quantity: 1,
      is_upsell: false
    }
  ]
  generateSupscription(chosenProducts)
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    chosenProducts = [
      ...chosenProducts,
      {
        // type: 'lw',
        // sku: lw_product.sku,
        // price: lw_product.productPrices.DiscountedPrice.Value,
        // currency: lw_product.productPrices.DiscountedPrice.GlobalCurrencyCode,
        // quantity: lw_product.quantity,
        // is_upsell: true
        package_id: lw_product.productId,
        quantity: 1,
        is_upsell: true
      }
    ]
  }

  const miniUpsellItems = window.ctrwowCheckout.checkoutData.getMiniUpsell() || []
  if (miniUpsellItems.length) {
    for (let i = 0, n = miniUpsellItems.length; i < n; i++) {
      chosenProducts = [
        ...chosenProducts,
        {
          // type: 'upsell',
          // sku: miniUpsellItems[i].sku,
          // price: miniUpsellItems[i].productPrices.DiscountedPrice.Value,
          // currency: miniUpsellItems[i].productPrices.DiscountedPrice.GlobalCurrencyCode,
          // quantity: miniUpsellItems[i].quantity,
          // is_upsell: true
          package_id: miniUpsellItems[i].productId,
          quantity: 1,
          is_upsell: true
        }
      ]
    }
  }

  const url = window.location.href
  const pathname = window.location.pathname
  const curentUrlPath = pathname.substring(pathname.lastIndexOf('/') + 1)
  const arrUrlLocaton = url.split(curentUrlPath)
  const getDomain = () => {
    let url = location.href
    try {
      const param = url.split('?')

      if (param.length === 1) return url

      const arrParam = param[1].split('&')
      const tempParam = []
      arrParam.forEach((p) => {
        const oneParam = p.toLocaleLowerCase()
        const arrOne = oneParam.split('=')
        if (arrOne[0].indexOf('ctr_') === -1) {
          tempParam.push(p)
        }
      })
      param[1] = tempParam.join('&')
      url = `${param[0]}?${param[1]}`
    } catch (e) {
      console.log('parse url error')
    }

    return url
  }
  const attribution = {
    funnel: window.funnelID,
    gclid: window.ctrwowUtils.handleParam.getQueryParameter('gclid') || '',
    metadata: {
      everflow_transaction_id: window.ctrwowUtils.handleParam.getQueryParameter('s5') || '',
      domain: getDomain(),
      device: window.ctrwowUtils.getDevice().desktop() ? 'desktop' : 'mobile',
      offer_id: window.ctrwowUtils.handleParam.getQueryParameter('s4') || ''
    },
    subaffiliate1: window.ctrwowUtils.handleParam.getQueryParameter('s1') || '',
    subaffiliate2: window.ctrwowUtils.handleParam.getQueryParameter('s2') || '',
    subaffiliate3: window.ctrwowUtils.handleParam.getQueryParameter('s3') || '',
    subaffiliate4: window.ctrwowUtils.handleParam.getQueryParameter('s4') || '',
    subaffiliate5: window.ctrwowUtils.handleParam.getQueryParameter('s5') || '',
    affid: window.ctrwowUtils.handleParam.getQueryParameter('affid') || '',
    network_id: window.ctrwowUtils.handleParam.getQueryParameter('network_id') || '',
    ttclid: window.ctrwowUtils.handleParam.getQueryParameter('ttclid') || ''
  }
  // const apiCallback = 'https://ctrwow-dev-ecommerceplatformintegrationpublic-microservice.azurewebsites.net/api/orders/completed'
  return {
    campaignId: '',
    shipping_code: shippingCode,
    shippingPrice: shippingPrice,
    shippingFormatPrice: shippingFormatPrice,
    shipping_method: product.shippings[window.shippingIndex].ref_id,
    lines: chosenProducts,
    payment_method: 'paypal',
    payment_detail: {
      // payment_return_url: apiCallback + `?curl=${arrUrlLocaton[0]}${window.pageUrls.declineUrl}`
      payment_method: 'paypal'
    },
    useCreditCard: false,
    ipAddress: window.ipAddress,
    success_url: `${arrUrlLocaton[0]}${window.ctrwowUtils.link.mergeWithCurrentQueryString(window.pageUrls.successUrl)}`,
    payment_failed_url: `${arrUrlLocaton[0]}${window.ctrwowUtils.link.mergeWithCurrentQueryString(window.pageUrls.declineUrl)}`,
    attribution: attribution
  }
}

export default function checkoutWithPaypal29Next(flag = false) {
  const $checkoutWithPaypalButton = $('button.ctaSubmitPP')
  const orderData = getOrderData29Next()
  const params = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutWithPaypalButton)
  console.log(params)
  // orderData.payment_details.payment_return_url += params
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.paymentProcessorId = 5
  window.ctrwowUtils.events.emit('beforeSubmitOrderPaypal')
  return submitCheckoutInfo29Next(orderData, PAYMENT_METHOD.PAYPAL)
    .then((result) => {
      if (!result || (result && !result.callBackUrl)) {
        // eslint-disable-next-line no-throw-literal
        throw 'paypal checkout - fail'
      }
      console.log('paypal checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrderPaypal')

      window.localStorage.setItem('paypal_isMainOrder', 'main')
      // window.localStorage.setItem('webkey_to_check_paypal', '')

      // Use in Diggy Popup
      if (flag) return result

      // Get paypal link then redirect
      const nextLink = getNextLinkFromOrderResponse(result)
      console.log('Go to next page', nextLink)

      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithPaypalButton)
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithPaypalButton)
        window.ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
    .catch((e) => {
      console.log(e)
      window.ctrwowUtils.events.emit('afterSubmitOrderFail')
      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithPaypalButton)
      window.ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
