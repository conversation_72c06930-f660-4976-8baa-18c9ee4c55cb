import { submitCheckoutInfo } from './helpers'
import * as mainOrderInfo from './../order/mainOrderInfo'
import getCrmInstance from './../getCrmInstance'
import { PAYMENT_TYPE, getPaymentName } from 'shared-checkout-flow/src/payment/paymentType.constants'
import { checkoutAfterSuccess, checkoutAfterFail } from './checkoutWithMainOrder'

function getOrderData({ customFields = {}, customer = {}, payment = {}, shippingAddress = {}, billingAddress = {}, mid = {}, successUrl } = {}) {
  console.log('start get order data !')

  const product = mainOrderInfo.getProduct()
  const commonAddressInfo = {
    firstName: customer.firstName,
    lastName: customer.lastName,
    phoneNumber: customer.phoneNumber
  }
  window.shippingIndex = window.shippingIndex || 0

  var orderData = {
    ...customFields,
    comment: '',
    funnelBoxId: mainOrderInfo.getFunnelBoxId(),
    productId: product && product.productId,
    shippingMethodId: product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null,
    customer: customer,
    payment: payment,
    useShippingAddressForBilling: !(billingAddress && billingAddress.address1),
    shippingAddress: shippingAddress && shippingAddress.address1 ? { ...commonAddressInfo, ...shippingAddress } : null,
    billingAddress: billingAddress && billingAddress.address1 ? { ...commonAddressInfo, ...billingAddress } : null,
    successUrl
  }
  if (mid && mid.midId) {
    orderData = { ...orderData, mid: { midId: mid.midId } }
  }
  return orderData
}

export default function checkoutWithStripe(paymentInfo, flag = false) {
  console.log(paymentInfo)
  window.ctrwowUtils.events.emit('beforeGetOrderData')

  let $checkoutWithStripeButton

  let paymentType = getPaymentName(paymentInfo.payment.paymentProcessorId)
  // detect payment type for listicle
  switch (paymentType) {
    case PAYMENT_TYPE.AFTERPAY: {
      $checkoutWithStripeButton = $('button[name="checkoutWithAfterPay"]')
      paymentType = PAYMENT_TYPE.STRIPE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.SEZZLE: {
      $checkoutWithStripeButton = $('button[name="checkoutWithSezzle"]')
      paymentType = PAYMENT_TYPE.STRIPE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.SOFORT: {
      $checkoutWithStripeButton = $('button[name="checkoutWithSofort"]')
      paymentType = PAYMENT_TYPE.STRIPE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.IDEAL: {
      $checkoutWithStripeButton = $('button[name="checkoutWithIdeal"]')
      paymentType = PAYMENT_TYPE.STRIPE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.BANCONTACT: {
      $checkoutWithStripeButton = $('button[name="checkoutWithBanContact"]')
      paymentType = PAYMENT_TYPE.STRIPE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.BRAINTREE: {
      $checkoutWithStripeButton = $('button[name="checkoutWithBraintree"]')
      paymentType = PAYMENT_TYPE.BRAINTREE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.GAP: {
      paymentType = PAYMENT_TYPE.GAP.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.SEPA: {
      paymentType = PAYMENT_TYPE.SEPA.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.BLUESNAPGOOGLE: {
      paymentType = PAYMENT_TYPE.BLUESNAPGOOGLE.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.AMAZON_PAY: {
      paymentType = PAYMENT_TYPE.AMAZON_PAY.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.COIN: {
      paymentType = PAYMENT_TYPE.COIN.toLocaleLowerCase()
      break
    }
    case PAYMENT_TYPE.KLARNA: {
      paymentType = PAYMENT_TYPE.KLARNA.toLocaleLowerCase()
      break
    }
  }

  const orderData = getOrderData(paymentInfo)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')

  return submitCheckoutInfo(orderData, paymentType)
    .then((result) => {
      console.log('stripe checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder', result)

      window.localStorage.setItem('webkey_for_success_page', getCrmInstance().webkey)
      window.localStorage.setItem('trackingNumber', result.trackingNumber)

      if (flag) {
        return result
      }

      if (getPaymentName(orderData.payment.paymentProcessorId) === PAYMENT_TYPE.SEPA) {
        result.callBackUrl = null
      }

      // setTimeout(() => {
      //   checkoutAfterSuccess(result, $checkoutWithStripeButton)
      // }, 1000)
      checkoutAfterSuccess(result, $checkoutWithStripeButton)
    })
    .catch((e) => {
      console.log(e)
      checkoutAfterFail($checkoutWithStripeButton)
    })
}
