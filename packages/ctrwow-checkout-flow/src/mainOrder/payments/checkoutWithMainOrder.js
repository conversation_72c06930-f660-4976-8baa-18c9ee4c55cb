import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import getCrmInstance from './../getCrmInstance'
import { getNextLinkFromOrderResponse } from './helpers'

export const checkoutAfterSuccess = (result, $checkoutButton) => {
  const { ctrwowUtils } = window
  const nextLink = getNextLinkFromOrderResponse(result)
  console.log('checkout - success - go to next page', nextLink)
  console.log(nextLink)

  if (nextLink) {
    location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutButton)
  } else {
    ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(getPageSettings().confirmUrl, $checkoutButton))
  }
}

export const checkoutAfterFail = ($checkoutButton) => {
  const { ctrwowUtils } = window
  console.log('ideal checkout - fail')
  ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(getCrmInstance().declineUrl, $checkoutButton))
}
