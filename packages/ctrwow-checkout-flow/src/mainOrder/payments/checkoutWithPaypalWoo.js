import { submitCheckoutInfoWoo } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam, mergeCurrentParamsWithCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'
import { createPaypalCharge } from 'shared-woocommerce/src/helper'
import { saveWooCheckoutInfo } from 'shared-woocommerce/src/order'

function getOrderDataWoo({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {} } = {}) {
  const product = mainOrderInfo.getProduct()
  window.shippingIndex = window.shippingIndex || 0
  const shippingMethodId = product && product?.shippings.length > 0 ? product.shippings[window.shippingIndex].methodId : ''
  const shippingPrice = product && product?.shippings.length > 0 ? product.shippings[window.shippingIndex].price : 0
  const shippingTitle = product && product?.shippings.length > 0 ? product.shippings[window.shippingIndex].title : ''
  const paymentDetails = {
    payment_method: PAYMENT_METHOD.PAYPAL,
    payment_method_title: PAYMENT_METHOD.PAYPAL,
    set_paid: false
  }

  const shippingMethod = [
    {
      method_id: shippingMethodId,
      method_title: shippingTitle,
      total: shippingPrice.toFixed(2)
    }
  ]
  const line_items = []
  line_items.push({
    product_id: product?.productId,
    quantity: product?.quantity
  })

  const miniUpsellItems = window.ctrwowCheckout.checkoutData.getMiniUpsell() || []
  if (miniUpsellItems.length) {
    for (let i = 0, n = miniUpsellItems.length; i < n; i++) {
      line_items.push({
        product_id: miniUpsellItems[i]?.productId,
        quantity: miniUpsellItems[i]?.quantity
      })
    }
  }

  return {
    ...paymentDetails,
    line_items,
    shipping_lines: shippingMethod
  }
}

export default async function checkoutWithPaypalWoo(paymentInfo, flag = false) {
  const $checkoutWithPaypalButton = $('button.ctaSubmitPP')
  const orderData = getOrderDataWoo(paymentInfo)
  const params = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutWithPaypalButton)
  console.log(params)
  // orderData.payment_details.payment_return_url += params
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrderPaypal')
  return submitCheckoutInfoWoo(orderData, PAYMENT_METHOD.PAYPAL)
    .then(async (result) => {
      const productSelected = mainOrderInfo.getProduct()
      console.log('paypal checkout - success', result)
      window.ctrwowUtils.events.emit('afterSubmitOrderPaypal')

      window.localStorage.setItem('paypal_isMainOrder', 'main')
      // window.localStorage.setItem('webkey_to_check_paypal', '')

      // Use in Diggy Popup
      if (flag) return
      // create Paypal charge
      try {
        const { total } = result
        const { links } = await createPaypalCharge(Number(total), window.currencyCode, window.pageUrls)

        // Get paypal link then redirect
        const nextLink = links.find((link) => link.rel === 'approve')?.href
        console.log('Go to next page', nextLink)

        // Save checkout info
        saveWooCheckoutInfo(result, productSelected)

        if (nextLink) {
          location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithPaypalButton)
        } else {
          const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithPaypalButton)
          window.ctrwowUtils.link.redirectPage(redirectUrl)
        }
      } catch (error) {
        const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithPaypalButton)
        window.ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
    .catch((e) => {
      console.log(e)
      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithPaypalButton)
      window.ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
