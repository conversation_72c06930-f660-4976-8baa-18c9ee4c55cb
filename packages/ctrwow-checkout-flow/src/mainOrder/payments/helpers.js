import getCrmInstance from './../getCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import * as mainOrderInfo from './../order/mainOrderInfo'
import * as lifeTimeWarrantyHandler from './../lifeTimeWarrantyHandler'
import { setOrderInfo } from 'shared-checkout-flow/src/orderInfo/orderInfo'
import addAddedInfoToOrderPayload from 'shared-checkout-flow/src/orderInfo/addAddedInfoToOrderPayload'
import { WEB_SERVICE_URL } from 'shared-checkout-flow/src/configurable.constants'
import { getParamAffiliate } from 'shared-checkout-flow/src/getParamAffiliate'

import { PAYMENT_METHOD, setUserPaymentType } from 'shared-checkout-flow/src/payment/userPaymentType'
import { getDataConversionForShopifyGroup } from 'shared-trackings/index'
import { connectWooCommerce } from 'shared-woocommerce/src/helper/'

// Save data callback paypal
export function saveCheckoutInfo(orderResponse, orderData) {
  const selectedProduct = mainOrderInfo.getProduct()

  const _localStorage = localStorage

  // Update total product quantity if checkout multiple product
  const totalProductQty = Number(window.ctrwowUtils.localStorage().get('totalProductQty'))
  if (orderResponse.token) {
    window.sessionStorage.setItem('orderToken', orderResponse.token)
  }

  /**
   * totalPriceMiniUpsell => using for fireGtmEventForUpsell
   */
  const miniUpsell = window.ctrwowCheckout.checkoutData.getMiniUpsell() || null
  const totalPriceMiniUpsell =
    miniUpsell !== null && miniUpsell !== undefined && miniUpsell.length > 0 ? miniUpsell.reduce((totalPrice, item) => totalPrice + item.price, 0) : 0

  // support activetion code to submit register app
  const activationCode = []
  if (window.isActivationCode) {
    activationCode.push({
      type: 'main',
      orderNumber: orderResponse.orderNumber
    })
  }

  var orderInfo = {
    orderParams: window.ctrwowUtils.link.getCustomPathName().substr(1),
    upsells: orderResponse.upsells,
    upsellIndex: window.upsellIndex || 0,
    countryCode: getCrmInstance().countryCode,
    campaignName: getPageSettings().offerName || _localStorage.getItem('mainCampaignName'),
    campaignWebKey: getPageSettings().webKey,
    orderNumber: orderResponse.orderNumber,
    cusEmail: orderData.customer ? orderData.customer.email : '', // recommit
    cardId: orderResponse.cardId,
    paymentProcessorId: orderResponse.paymentProcessorId,
    addressId: orderResponse.customerResult ? orderResponse.customerResult.shippingAddressId : '',
    customerId: orderResponse.customerResult ? orderResponse.customerResult.customerId : '',
    orderTotal: selectedProduct.productPrices.DiscountedPrice.Value,
    formattedNumber: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
    orderTotalFull: window.localStorage.getItem('conventionTrackingPrice'),
    totalPriceMiniUpsell,
    savedTotal: selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value,
    quantity: totalProductQty || selectedProduct.quantity,
    orderedProducts: [
      {
        sku: selectedProduct.sku,
        pid: selectedProduct.productId,
        name: selectedProduct.productName
      }
    ],
    useCreditCard: orderResponse.useCreditCard ? orderResponse.useCreditCard : false,
    activationCode: activationCode
  }
  try {
    orderInfo = {
      ...orderInfo,
      cusPhone: orderData.shippingAddress.phoneNumber || '',
      cusFirstName: orderData.shippingAddress.firstName || '',
      cusLastName: orderData.shippingAddress.lastName || '',
      cusCity: orderData.shippingAddress.city || '',
      cusState: orderData.shippingAddress.state || '',
      cusCountry: orderData.shippingAddress.countryCode || '',
      cusZip: orderData.shippingAddress.zipCode || '',
      address1: orderData.shippingAddress.address1 || '',
      address2: orderData.shippingAddress.address2 || ''
    }
  } catch (e) {
    console.log(e)
  }

  if ((orderResponse.useCreditCard || orderResponse.paymentProcessorId === 31) && document.getElementById('installpaymentDdl')) {
    orderInfo.installmentValue = document.getElementById('installpaymentDdl').value
    orderInfo.installmentText = window.optionText || 'Nx de $price sem juros'
  }
  // if (document.getElementById('installpaymentDdl')) {
  //   orderInfo.installmentValue = document.getElementById('installpaymentDdl').value
  //   orderInfo.installmentText = window.optionText || 'Nx de $price sem juros'
  // }

  const giftCard = window.ctrwowCheckout.checkoutData.getGiftCard()
  if (giftCard) {
    giftCard.Balance = giftCard.NewBalance
    window.localStorage.setItem('giftCard', JSON.stringify(giftCard))
  }

  setOrderInfo(orderInfo)
  // _localStorage.setItem('webkey', JSON.stringify(getCrmInstance().webkey))
  // TODO: DELETE
  const loggingInfo = {
    orderNumber: orderResponse.orderNumber,
    trackingNumber: orderResponse.trackingNumber,
    callBackUrl: orderResponse.callBackUrl
  }

  _localStorage.setItem('loggingInfo', JSON.stringify(loggingInfo))
}

export const submitCheckoutInfo = (orderData, paymenttype) =>
  window.__CTR_FP_TRACKING.getFingerPrintId().then((fpid) => {
    // Submit fingerPrint's payload (including customer info & shipping address)
    if (orderData.shippingAddress) {
      const fingerPrintPayload = {
        ...orderData.customer,
        ...orderData.shippingAddress,
        address: orderData.shippingAddress.address1,
        country: orderData.shippingAddress.countryCode
      }

      delete fingerPrintPayload.address1
      delete fingerPrintPayload.countryCode

      window.__CTR_FP_TRACKING &&
        window.__CTR_FP_TRACKING.pushUserInfo(fingerPrintPayload, function (rs) {
          console.log('__CTR_FP_TRACKING.trackUserInfo success', rs)
        })
    }

    orderData.fingerPrintId = fpid

    if (!orderData.couponCode) {
      orderData.couponCode = mainOrderInfo.getCouponCode() || window.ctrwowUtils.link.getParameterByName('couponCode') || ''
    }

    if (window.landingUrlAlt) {
      orderData.landingUrl = window.landingUrlAlt
    }

    // This case is used for product with multi variants in  linebundle page
    if (window.Variants !== undefined) {
      orderData.Variants = window.Variants
    }

    if (mainOrderInfo.getMessageCard()) {
      orderData.AdditionalInfo = mainOrderInfo.getMessageCard() || []
    }

    // Detect cashback param to append on url after checkout successfully
    // @paramAfterSuccess { name & value}
    if (window.ctrwowUtils.localStorage().get('cusParamAfterSuccess')) {
      const cusParamAfterSuccess = window.ctrwowUtils.localStorage().get('cusParamAfterSuccess')
      const urlSearch = window.location.search

      if (orderData.payment.callBackParam) {
        // Paypal method
        orderData.payment.callBackParam = `${orderData.payment.callBackParam}&${cusParamAfterSuccess}`
      } else {
        // Credit card method
        if (urlSearch.indexOf('?') > -1) {
          orderData.payment.callBackParam = `${urlSearch}&${cusParamAfterSuccess}`
        } else {
          orderData.payment.callBackParam = `?${cusParamAfterSuccess}`
        }
      }

      // Clear LocalStorage's Parameter
      window.ctrwowUtils.localStorage().remove('cusParamAfterSuccess')
    }

    localStorage.setItem('userPaymentProcessorId', orderData.payment.paymentProcessorId) // use display payment type in decline page

    mainOrderInfo.addMiniUpsellToOrderData(orderData)
    addAddedInfoToOrderPayload(orderData)
    mainOrderInfo.addGiftCardToOrderData(orderData)

    // Submit conversion tracking data for shopify group order
    const delayedCheckoutShopify = window.localStorage.getItem('delayedCheckoutShopify')
    if (delayedCheckoutShopify === 'true') {
      orderData.conversionTrackings = getDataConversionForShopifyGroup()
    }

    const processing = new Promise((resolve, reject) => {
      if (window.isWebSaleParam) {
        const url = `${window.__ctrPageConfiguration.crmEndpoint}/orders/${window.__ctrPageConfiguration.webKey}?newWebSales=true`
        getCrmInstance().Order.placeOrderWithUrl(url, orderData, paymenttype, function (result) {
          console.log(result)
          console.log('inside crm - success')
          localStorage.setItem('mainOrderLink', location.pathname)
          localStorage.setItem('userPaymentType', paymenttype)
          setUserPaymentType(paymenttype)
          localStorage.setItem('user_firstname', orderData.customer ? orderData.customer.firstName : '')
          localStorage.setItem('user_lastname', orderData.customer ? orderData.customer.lastName : '')
          if (result && result.success) {
            resolve(result)
          } else {
            reject(result)
          }
        })
      } else {
        // update new API salespci for checkout api
        getCrmInstance().Order.baseAPIEndpoint = window.ctrwowUtils.getSalesPciCRMBaseUrl(getCrmInstance().Order)
        getCrmInstance().Order.placeOrder(orderData, paymenttype, function (result) {
          console.log(result)
          console.log('inside crm - success')
          localStorage.setItem('mainOrderLink', location.pathname)
          localStorage.setItem('userPaymentType', paymenttype)
          setUserPaymentType(paymenttype)
          localStorage.setItem('user_firstname', orderData.customer ? orderData.customer.firstName : '')
          localStorage.setItem('user_lastname', orderData.customer ? orderData.customer.lastName : '')
          // save cvv to sessionStorage - use for shopify
          if (orderData.payment && orderData.payment.cvv) {
            sessionStorage.setItem('cvvdata', orderData.payment.cvv)
          }
          if (result && result.success) {
            resolve(result)
          } else {
            reject(result)
          }
        })
      }
    })

    return processing.then((result) => {
      const dataAfterCheckingUpsell = window.miniUpselUrl ? mainOrderInfo.handleDataSkipMiniUpsell(result) : result
      const skipUpsellWarranty = mainOrderInfo.getLifetimeWarrantyConfig__skipUpsell()
      const slugUrl = mainOrderInfo.getLifetimeWarrantyConfig__slugURL()
      // const data = skipUpsellWarranty ? lifeTimeWarrantyHandler.handleDataSkipUpsellWarranty(result, slugUrl) : result
      const data = skipUpsellWarranty
        ? lifeTimeWarrantyHandler.handleDataSkipUpsellWarranty(dataAfterCheckingUpsell, slugUrl)
        : dataAfterCheckingUpsell
      saveCheckoutInfo(data, orderData)
      return data
    })
  })

export const getNextLinkFromOrderResponse = (orderData) => {
  const getUpsellLink = () => {
    try {
      const upsellIndex = window.upsellIndex || 0
      const upsellLink = orderData.upsells[upsellIndex].upsellUrl

      const splitLink = upsellLink.split('?')
      if (splitLink.length <= 1) {
        return upsellLink.substr(upsellLink.lastIndexOf('/') + 1)
      } else {
        splitLink[0] = splitLink[0].substr(splitLink[0].lastIndexOf('/') + 1)
        const newLink = splitLink.join('?')

        return newLink
      }
    } catch (e) {
      return undefined
    }
  }

  const getActionUrl = () => {
    try {
      return orderData.paymentContinueResult.actionUrl
    } catch (e) {
      return undefined
    }
  }

  return orderData.callBackUrl || getActionUrl() || getUpsellLink()
}

// ! Sticky
function getOrderedStickyProducts(selectedProduct, orderResponse, orderData, orderTotal, subscription) {
  const mainProduct = [
    {
      type: 'main',
      sku: selectedProduct.sku,
      pid: selectedProduct.productId,
      name: selectedProduct.productName + subscription,
      price: selectedProduct.productPrices.DiscountedPrice.Value,
      formatedPrice: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
      quantity: selectedProduct.quantity,
      is_trial_product: selectedProduct.is_trial_product,
      shippingPrice: orderData.shippingPrice,
      shippingFormatPrice: orderData.shippingFormatPrice,
      shippingId: orderData.shippingId,
      children: orderData.children,
      orderNumber: orderResponse.order_id,
      orderTotal: orderTotal ? Number(orderTotal) : '',
      orderTotalFormated: orderTotal ? window.ctrwowUtils.number.convertNumberToCurrency(orderTotal) : '',
      resp_msg: orderResponse.resp_msg || '',
      url: location.pathname
    }
  ]
  const upsellUrls = []
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    const total = lw_product.productPrices.DiscountedPrice.Value
    upsellUrls.push({
      name: lw_product.productName,
      formatedPrice: lw_product.productPrices.DiscountedPrice.FormattedValue,
      orderTotalFormated: window.ctrwowUtils.number.convertNumberToCurrency(total),
      orderTotal: total,
      shippingFormatPrice: window.ctrwowUtils.number.convertNumberToCurrency(0),
      gatewayDescriptor: orderResponse.gatewayDescriptor || orderResponse.paymentType,
      orderNumber: orderResponse.orderNumber,
      isPaid: orderResponse.isPaid || false
    })
  }
  return {
    mainProduct: mainProduct,
    upsellUrls: upsellUrls
  }
}
export function saveCheckoutInfoSticky(orderResponse, orderData) {
  const selectedProduct = mainOrderInfo.getProduct()

  const _localStorage = localStorage

  // Update total product quantity if checkout multiple product
  const totalProductQty = Number(window.ctrwowUtils.localStorage().get('totalProductQty'))
  if (orderResponse.token) {
    window.sessionStorage.setItem('orderToken', orderResponse.token)
  }
  const orderTotal = selectedProduct.productPrices.DiscountedPrice.Value + orderData.shippingPrice
  let subscription = ''
  switch (orderData.offers[0].billing_model_id) {
    case 3:
      subscription = ' Subscription 30 days'
      break
    case 4:
      subscription = ' Subscription 60 days'
      break
    case 6:
      subscription = ' Subscription 45 days'
      break
  }
  try {
    const orderedProducts = getOrderedStickyProducts(selectedProduct, orderResponse, orderData, orderTotal, subscription)
    var orderInfo = {
      stickyIODomain: window.stickyIODomain,
      xid: window.xid,
      billingmode: orderData.offers[0].billing_model_id,
      orderParams: window.ctrwowUtils.link.getCustomPathName().substr(1),
      upsells: orderResponse.upsells,
      upsellIndex: window.upsellIndex || 0,
      countryCode: orderData.shippingCountry || '',
      campaignName: _localStorage.getItem('mainCampaignName'),
      campaignWebKey: orderData.campaignId,
      campaignId: orderData.campaignId,
      orderNumber: orderResponse.orderNumber,
      cardId: orderResponse.cardId,
      creditCardType: orderData.creditCardType,
      paymentProcessorId: orderResponse.paymentProcessorId,
      addressId: orderResponse.customerResult ? orderResponse.customerResult.shippingAddressId : '',
      customerId: orderResponse.customerResult ? orderResponse.customerResult.customerId : '',
      orderTotal: selectedProduct.productPrices.DiscountedPrice.Value,
      formattedNumber: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
      orderTotalFull: Number(window.localStorage.getItem('conventionTrackingPrice')),
      savedTotal: selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value,
      quantity: totalProductQty || selectedProduct.fe_quantity || selectedProduct.quantity,
      orderedProducts: orderedProducts.mainProduct,
      isPaid: orderResponse.isPaid || false,
      ipAddress: orderData.ipAddress,
      shippingAddress: {
        firstName: (orderData.shippingAddress && orderData.shippingAddress.shippingFirstName) || '',
        lastName: (orderData.shippingAddress && orderData.shippingAddress.shippingLastName) || '',
        middleName: '',
        phoneNumber: orderData.phoneNumber || '',
        address1: (orderData.shippingAddress && orderData.shippingAddress.shippingAddress1) || '',
        address2: (orderData.shippingAddress && orderData.shippingAddress.shippingAddress2) || '',
        city: (orderData.shippingAddress && orderData.shippingAddress.shippingCity) || '',
        state: (orderData.shippingAddress && orderData.shippingAddress.shippingState) || '',
        countryCode: (orderData.shippingAddress && orderData.shippingAddress.shippingCountry) || '',
        countryName: (orderData.shippingAddress && orderData.shippingAddress.shippingCountry) || '',
        zipCode: (orderData.shippingAddress && orderData.shippingAddress.shippingZip) || ''
      },
      billingAddress: {
        firstName: (orderData.billingAddress && orderData.billingAddress.billingFirstName) || '',
        lastName: (orderData.billingAddress && orderData.billingAddress.billingLastName) || '',
        middleName: '',
        phoneNumber: (orderData.billingAddress && orderData.billingAddress.phoneNumber) || '',
        address1: (orderData.billingAddress && orderData.billingAddress.billingAddress1) || '',
        address2: (orderData.billingAddress && orderData.billingAddress.billingAddress2) || '',
        city: (orderData.billingAddress && orderData.billingAddress.billingCity) || '',
        state: (orderData.billingAddress && orderData.billingAddress.billingState) || '',
        countryCode: (orderData.billingAddress && orderData.billingAddress.billingCountry) || '',
        countryName: (orderData.billingAddress && orderData.billingAddress.billingCountry) || '',
        zipCode: (orderData.billingAddress && orderData.billingAddress.billingZip) || ''
      },
      gatewayDescriptor: orderResponse.gatewayDescriptor || orderResponse.paymentType,
      useCreditCard: orderResponse.useCreditCard ? orderResponse.useCreditCard : false
    }
    orderInfo = {
      ...orderInfo,
      ...window.pageUrls,
      firstName: (orderData.shippingAddress && orderData.shippingAddress.shippingFirstName) || '',
      lastName: (orderData.shippingAddress && orderData.shippingAddress.shippingLastName) || '',
      cusPhone: orderData.phoneNumber || '',
      cusFirstName: (orderData.shippingAddress && orderData.shippingAddress.shippingFirstName) || '',
      cusLastName: (orderData.shippingAddress && orderData.shippingAddress.shippingLastName) || '',
      cusEmail: orderData.email || '',
      cusCity: (orderData.shippingAddress && orderData.shippingAddress.shippingCity) || '',
      cusState: (orderData.shippingAddress && orderData.shippingAddress.shippingState) || '',
      cusCountry: (orderData.shippingAddress && orderData.shippingAddress.shippingCountry) || '',
      cusZip: (orderData.shippingAddress && orderData.shippingAddress.shippingZip) || ''
    }
    if (orderedProducts.upsellUrls && orderedProducts.upsellUrls.length) {
      orderInfo.orderedProducts = [...orderInfo.orderedProducts, ...orderedProducts.upsellUrls]
      orderInfo.upsellUrls = orderedProducts.upsellUrls
    }
  } catch (e) {
    console.log(e)
  }

  if (orderResponse.useCreditCard && document.getElementById('installpaymentDdl')) {
    orderInfo.installmentValue = document.getElementById('installpaymentDdl').value
    orderInfo.installmentText = window.optionText || 'Nx de $price sem juros'
  }

  setOrderInfo(orderInfo)
}
export const submitCheckoutInfoSticky = (orderData, paymenttype) =>
  window.__CTR_FP_TRACKING.getFingerPrintId().then((fpid) => {
    orderData.fingerPrintId = fpid
    setUserPaymentType(paymenttype)

    const processing = new Promise((resolve, reject) => {
      // Submit Order
      window.xid = window.xid || 'YmVhdXR5c3RhdF81MDYwOjJjMzM2ZDIwNTNjOGNh'
      let url = `//${window.stickyIODomain}.sticky.io/api/v1/new_order`
      if (paymenttype === PAYMENT_METHOD.PAYPAL) {
        url = '//e52nk3009l.execute-api.us-east-1.amazonaws.com/prod/placeOrderwithPP'

        // update data submit with request url and api key
        orderData.request_url = `https://${window.stickyIODomain}.sticky.io/api/v1/new_order`
        orderData.api_key = window.xid
      }

      const settings = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Basic ' + window.xid
        },
        body: JSON.stringify(orderData),
        redirect: 'follow'
      }
      window.ctrwowUtils
        .callAjax(url, settings)
        .then((result) => {
          console.log(result)
          localStorage.setItem('mainOrderLink', location.pathname)
          if (orderData.customer) {
            localStorage.setItem('user_firstname', orderData.customer.firstName)
            localStorage.setItem('user_lastname', orderData.customer.lastName)
          }

          if (result && (result.response_code === '100' || result.success)) {
            resolve(result)
          } else {
            reject(result)
          }
        })
        .catch((e) => {
          localStorage.setItem('mainOrderLink', location.pathname)
          reject(e)
        })
    })

    return processing
      .then((result) => {
        if (window.upsellUrls && window.upsellUrls.length > 0) {
          window.upsellUrls.map((item) => {
            item.upsellUrl += location.search
            return item
          })
        }
        let data = {
          upsells: window.upsellUrls || [],
          orderNumber: '',
          cardId: '',
          paymentProcessorId: '',
          customerResult: {
            shippingAddressId: '',
            customerId: ''
          },
          paymentType: paymenttype,
          useCreditCard: paymenttype === PAYMENT_METHOD.CREDIT_CARD
        }
        if (typeof result === 'object') {
          data.orderNumber = result.order_id || ''
          data.customerResult.customerId = result.customerId || ''
          if (paymenttype === PAYMENT_METHOD.CREDIT_CARD) {
            data.isPaid = true
          }
          if (result.paypalUrl) {
            data.callBackUrl = result.paypalUrl
          }
          data = {
            ...data,
            ...result
          }
        }

        saveCheckoutInfoSticky(data, orderData)
        return data
      })
      .catch((e) => console.log(e))
  })

// ! 29Next
function getOrdered29NextProducts(selectedProduct, orderResponse, orderData, orderTotal, subscription) {
  const formatedPrice = selectedProduct.productPrices.DiscountedPrice.FormattedValue
  const mainProduct = [
    {
      type: 'main',
      sku: selectedProduct.sku,
      price: selectedProduct.productPrices.DiscountedPrice.Value,
      formatedPrice: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
      currency: selectedProduct.productPrices.DiscountedPrice.GlobalCurrencyCode,
      name: selectedProduct.productName + subscription,
      quantity: selectedProduct.fe_quantity,
      shippingPrice: orderData.shippingPrice,
      shippingId: orderData.shipping_code,
      shippingFormatPrice: orderData.shippingFormatPrice,
      orderNumber: orderResponse.orderNumber,
      orderTotal: orderTotal ? Number(orderTotal) : '',
      orderTotalFormated: orderTotal ? window.ctrwowUtils.number.formaterNumberByFormattedValue(orderTotal, formatedPrice) : '',
      url: location.pathname
    }
  ]

  const upsellUrls = []
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    const lwPrice = lw_product.productPrices.DiscountedPrice.Value
    upsellUrls.push({
      type: 'upsell',
      name: lw_product.productName,
      sku: lw_product.sku,
      price: lwPrice,
      quantity: lw_product.fe_quantity,
      formatedPrice: lw_product.productPrices.DiscountedPrice.FormattedValue,
      orderTotal: lwPrice,
      orderTotalFormated: lw_product.productPrices.DiscountedPrice.FormattedValue,
      shippingFormatPrice: window.ctrwowUtils.number.formaterNumberByFormattedValue(0, formatedPrice)
    })
  }
  const miniUpsell = window.ctrwowCheckout.checkoutData.getMiniUpsell() || null
  if (miniUpsell && miniUpsell.length > 0) {
    miniUpsell.forEach((miniItem) => {
      const miniPrice = miniItem.productPrices.DiscountedPrice.Value
      upsellUrls.push({
        type: 'upsell',
        name: miniItem.productName,
        sku: miniItem.sku,
        price: miniPrice,
        quantity: miniItem.fe_quantity,
        formatedPrice: miniItem.productPrices.DiscountedPrice.FormattedValue,
        orderTotal: miniPrice,
        orderTotalFormated: miniItem.productPrices.DiscountedPrice.FormattedValue,
        shippingFormatPrice: window.ctrwowUtils.number.formaterNumberByFormattedValue(0, formatedPrice)
      })
    })
  }
  return {
    mainProduct: mainProduct,
    upsellUrls: upsellUrls
  }
}
export function saveCheckoutInfo29Next(orderResponse, orderData) {
  const selectedProduct = mainOrderInfo.getProduct()
  const _localStorage = localStorage
  const totalProductQty = Number(window.ctrwowUtils.localStorage().get('totalProductQty'))
  const orderTotal = selectedProduct.productPrices.DiscountedPrice.Value + orderData.shippingPrice

  let subscription = ''
  if (typeof window.subscription === 'object') {
    switch (window.subscription.type) {
      case 'day':
        subscription = ` Subscription ${window.subscription.value} day(s)`
        break
      case 'month':
        subscription = ` Subscription ${window.subscription.value} month(s)`
        break
    }
  }

  try {
    const orderedProducts = getOrdered29NextProducts(selectedProduct, orderResponse, orderData, orderTotal, subscription)
    var orderInfo = {
      _29NextDomain: window._29NextDomain,
      xid: window.xid,
      orderParams: window.ctrwowUtils.link.getCustomPathName().substr(1),
      upsells: orderResponse.upsells,
      upsellIndex: window.upsellIndex || 0,
      countryCode: (orderData.shipping_address && orderData.shipping_address.country) || '',
      campaignName: _localStorage.getItem('mainCampaignName'),
      campaignWebKey: '',
      orderNumber: orderResponse.orderNumber || '',
      ref_id: orderResponse.ref_id,
      // creditCardType: orderData.payment_details.creditCardType,
      productId: selectedProduct.productId,
      orderTotal: selectedProduct.productPrices.DiscountedPrice.Value,
      formattedNumber: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
      orderTotalFull: Number(window.localStorage.getItem('conventionTrackingPrice')),
      savedTotal: selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value,
      quantity: totalProductQty || selectedProduct.quantity,
      fe_quantity: totalProductQty || selectedProduct.fe_quantity,
      orderedProducts: orderedProducts.mainProduct,
      ipAddress: orderData.ipAddress,
      customerId: (orderResponse.user && orderResponse.user.id) || '',
      shippingAddress: {
        firstName: (orderData.shipping_address && orderData.shipping_address.first_name) || '',
        lastName: (orderData.shipping_address && orderData.shipping_address.last_name) || '',
        phoneNumber: (orderData.shipping_address && orderData.shipping_address.phone_number) || '',
        address1: (orderData.shipping_address && orderData.shipping_address.line1) || '',
        address2: (orderData.shipping_address && orderData.shipping_address.line2) || '',
        city: (orderData.shipping_address && orderData.shipping_address.line4) || '',
        state: (orderData.shipping_address && orderData.shipping_address.state) || '',
        countryCode: (orderData.shipping_address && orderData.shipping_address.country) || '',
        countryName: (orderData.shipping_address && orderData.shipping_address.country) || '',
        zipCode: (orderData.shipping_address && orderData.shipping_address.postcode) || ''
      },
      billingAddress: !orderData.billing_address
        ? {
            firstName: (orderData.shipping_address && orderData.shipping_address.first_name) || '',
            lastName: (orderData.shipping_address && orderData.shipping_address.last_name) || '',
            phoneNumber: (orderData.shipping_address && orderData.shipping_address.phone_number) || '',
            address1: (orderData.shipping_address && orderData.shipping_address.line1) || '',
            address2: (orderData.shipping_address && orderData.shipping_address.line2) || '',
            city: (orderData.shipping_address && orderData.shipping_address.line4) || '',
            state: (orderData.shipping_address && orderData.shipping_address.state) || '',
            countryCode: (orderData.shipping_address && orderData.shipping_address.country) || '',
            countryName: (orderData.shipping_address && orderData.shipping_address.country) || '',
            zipCode: (orderData.shipping_address && orderData.shipping_address.postcode) || ''
          }
        : {
            firstName: orderData.billing_address.first_name || '',
            lastName: orderData.billing_address.last_name || '',
            phoneNumber: orderData.billing_address.phone_number || '',
            address1: orderData.billing_address.line1 || '',
            address2: orderData.billing_address.line2 || '',
            city: orderData.billing_address.line4 || '',
            state: orderData.billing_address.state || '',
            countryCode: orderData.billing_address.country || '',
            countryName: orderData.billing_address.country || '',
            zipCode: orderData.billing_address.postcode || ''
          },
      useCreditCard: orderResponse.useCreditCard || false
    }

    orderInfo = {
      ...orderInfo,
      ...window.pageUrls,
      cusEmail: (orderData.user && orderData.user.email) || '',
      cusPhone: (orderData.shipping_address && orderData.shipping_address.phone_number) || '',
      firstName: (orderData.shipping_address && orderData.shipping_address.first_name) || '',
      cusFirstName: (orderData.shipping_address && orderData.shipping_address.first_name) || '',
      lastName: (orderData.shipping_address && orderData.shipping_address.last_name) || '',
      cusLastName: (orderData.shipping_address && orderData.shipping_address.last_name) || '',
      cusCity: (orderData.shipping_address && orderData.shipping_address.line4) || '',
      cusState: (orderData.shipping_address && orderData.shipping_address.state) || '',
      cusCountry: (orderData.shipping_address && orderData.shipping_address.country) || '',
      cusZip: (orderData.shipping_address && orderData.shipping_address.postcode) || ''
    }

    if (orderedProducts.upsellUrls && orderedProducts.upsellUrls.length) {
      orderInfo.orderedProducts = [...orderInfo.orderedProducts, ...orderedProducts.upsellUrls]
      orderInfo.upsellUrls = orderedProducts.upsellUrls
    }
  } catch (e) {
    console.log(e)
  }

  setOrderInfo(orderInfo)
}
export const submitCheckoutInfo29Next = (orderData, paymenttype) =>
  window.__CTR_FP_TRACKING.getFingerPrintId().then((fpid) => {
    orderData.fingerPrintId = fpid
    setUserPaymentType(paymenttype)

    if (!orderData.vouchers) {
      const couponCode = mainOrderInfo.getCouponCode() || window.ctrwowUtils.link.getParameterByName('couponCode') || ''
      orderData.vouchers = !!couponCode && couponCode !== '' ? [couponCode] : []
    }

    const processing = new Promise((resolve, reject) => {
      // Submit Order
      const url = `https://campaigns.apps.29next.com/api/v1/orders/`
      const settings = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: window.ctrwowUtils.localStorage().get('29NextApiKey')
        },
        body: JSON.stringify(orderData)
      }
      window.ctrwowUtils
        .callAjax(url, settings)
        .then((result) => {
          console.log(result)
          localStorage.setItem('mainOrderLink', location.pathname)
          if (orderData.user) {
            localStorage.setItem('user_firstname', orderData.user.first_name)
            localStorage.setItem('user_lastname', orderData.user.last_name)
          }

          // if (result && (result.status === 'confirmed' || result.payment_complete_url)) {
          if (result && result.ref_id) {
            resolve(result)
          } else {
            reject(result)
          }
        })
        .catch((e) => {
          localStorage.setItem('mainOrderLink', location.pathname)
          reject(e)
        })
    })

    return processing
      .then((result) => {
        if (window.upsellUrls && window.upsellUrls.length > 0) {
          window.upsellUrls.map((item) => {
            item.upsellUrl += location.search
            return item
          })
        }
        let data = {
          upsells: window.upsellUrls || [],
          orderNumber: result.number || '',
          ref_id: result.ref_id,
          paymentType: paymenttype,
          useCreditCard: paymenttype === PAYMENT_METHOD.CREDIT_CARD
        }
        if (result.payment_complete_url) {
          data.callBackUrl = result.payment_complete_url
        } else if (typeof result === 'object' && result.status === 'confirmed') {
          data.orderNumber = result.number || ''
          data = {
            ...data,
            ...result
          }
        }

        saveCheckoutInfo29Next(data, orderData)
        return data
      })
      .catch((e) => console.log(e))
  })

// WooCommerce
export const submitCheckoutInfoWoo = (orderData, paymenttype) =>
  window.__CTR_FP_TRACKING.getFingerPrintId().then((fpid) => {
    orderData.fingerPrintId = fpid
    setUserPaymentType(paymenttype)
    const processing = new Promise((resolve, reject) => {
      // Submit Order

      const wooCommerce = connectWooCommerce()

      wooCommerce
        .post('orders', orderData)
        .then((result) => {
          const data = result?.data
          console.log(data)
          localStorage.setItem('mainOrderLink', location.pathname)
          if (orderData.user) {
            localStorage.setItem('user_firstname', orderData.user.first_name)
            localStorage.setItem('user_lastname', orderData.user.last_name)
          }

          if (data && data.id) {
            resolve(data)
          } else {
            reject(data)
          }
        })
        .catch((e) => {
          localStorage.setItem('mainOrderLink', location.pathname)
          reject(e)
        })
    })

    return processing
      .then((result) => {
        if (window.upsellUrls && window.upsellUrls.length > 0) {
          window.upsellUrls.map((item) => {
            item.upsellUrl += location.search
            return item
          })
        }
        const data = {
          upsells: window.upsellUrls || [],
          orderNumber: result.number || '',
          paymentType: result.payment_method,
          useCreditCard: paymenttype === PAYMENT_METHOD.CREDIT_CARD,
          confirmUrl: window.pageUrls.confirmUrl,
          successUrl: window.pageUrls.successUrl,
          declineUrl: window.pageUrls.declineUrl
        }
        return {
          ...data,
          ...result
        }
      })
      .catch((e) => console.log(e))
  })

// ! Konnektive
function getOrderedKonnektiveProducts(selectedProduct, orderResponse, orderData, orderTotal, subscription) {
  const formatedPrice = selectedProduct.productPrices.DiscountedPrice.FormattedValue
  const mainProduct = [
    {
      type: 'main',
      sku: selectedProduct.sku,
      productId: selectedProduct.productId,
      price: selectedProduct.productPrices.DiscountedPrice.Value,
      formatedPrice: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
      currency: selectedProduct.productPrices.DiscountedPrice.GlobalCurrencyCode,
      name: selectedProduct.productName + subscription,
      quantity: selectedProduct.fe_quantity,
      shippingPrice: orderData.shippingPrice,
      shippingId: orderData.shipping_code,
      shippingFormatPrice: orderData.shippingFormatPrice,
      orderNumber: orderResponse.orderNumber,
      orderTotal: orderTotal ? Number(orderTotal) : '',
      orderTotalFormated: orderTotal ? window.ctrwowUtils.number.formaterNumberByFormattedValue(orderTotal, formatedPrice) : '',
      url: location.pathname
    }
  ]

  const upsellUrls = []
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    const lwPrice = lw_product.productPrices.DiscountedPrice.Value
    upsellUrls.push({
      type: 'upsell',
      name: lw_product.productName,
      sku: lw_product.sku,
      price: lwPrice,
      quantity: lw_product.fe_quantity,
      formatedPrice: lw_product.productPrices.DiscountedPrice.FormattedValue,
      orderTotal: lwPrice,
      orderTotalFormated: lw_product.productPrices.DiscountedPrice.FormattedValue,
      shippingFormatPrice: window.ctrwowUtils.number.formaterNumberByFormattedValue(0, formatedPrice)
    })
  }
  return {
    mainProduct: mainProduct,
    upsellUrls: upsellUrls
  }
}
export function saveCheckoutInfoKonnektive(orderResponse, orderData) {
  const selectedProduct = mainOrderInfo.getProduct()
  const _localStorage = localStorage
  const totalProductQty = Number(window.ctrwowUtils.localStorage().get('totalProductQty'))
  const orderTotal = selectedProduct.productPrices.DiscountedPrice.Value + orderData.shippingPrice

  let subscription = ''
  if (typeof window.subscription === 'object') {
    switch (window.subscription.type) {
      case 'day':
        subscription = ` Subscription ${window.subscription.value} day(s)`
        break
      case 'month':
        subscription = ` Subscription ${window.subscription.value} month(s)`
        break
    }
  }

  try {
    const orderedProducts = getOrderedKonnektiveProducts(selectedProduct, orderResponse, orderData, orderTotal, subscription)
    var orderInfo = {
      konnektiveUserName: window.konnektiveUserName,
      konnektivePassword: window.konnektivePassword,
      orderParams: window.ctrwowUtils.link.getCustomPathName().substr(1),
      upsells: orderResponse.upsells,
      upsellIndex: window.upsellIndex || 0,
      countryCode: (orderData.shipping_address && orderData.shipping_address.country) || '',
      campaignName: _localStorage.getItem('mainCampaignName'),
      campaignWebKey: orderData.campaignId,
      orderNumber: orderResponse.orderNumber,
      creditCardType: orderData.creditCardType,
      orderTotal: selectedProduct.productPrices.DiscountedPrice.Value,
      formattedNumber: selectedProduct.productPrices.DiscountedPrice.FormattedValue,
      orderTotalFull: Number(window.localStorage.getItem('conventionTrackingPrice')),
      savedTotal: selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value,
      quantity: totalProductQty || selectedProduct.fe_quantity || selectedProduct.quantity,
      orderedProducts: orderedProducts.mainProduct,
      ipAddress: orderData.ipAddress,
      customerId: orderResponse.message.customerId,
      shippingAddress: {
        firstName: (orderData.shipping_address && orderData.shipping_address.first_name) || '',
        lastName: (orderData.shipping_address && orderData.shipping_address.last_name) || '',
        phoneNumber: (orderData.shipping_address && orderData.shipping_address.phone_number) || '',
        address1: (orderData.shipping_address && orderData.shipping_address.line1) || '',
        address2: (orderData.shipping_address && orderData.shipping_address.line2) || '',
        city: (orderData.shipping_address && orderData.shipping_address.line4) || '',
        state: (orderData.shipping_address && orderData.shipping_address.state) || '',
        countryCode: (orderData.shipping_address && orderData.shipping_address.country) || '',
        countryName: (orderData.shipping_address && orderData.shipping_address.country) || '',
        zipCode: (orderData.shipping_address && orderData.shipping_address.postcode) || ''
      },
      billingAddress: !orderData.billing_address
        ? {
            firstName: (orderData.shipping_address && orderData.shipping_address.first_name) || '',
            lastName: (orderData.shipping_address && orderData.shipping_address.last_name) || '',
            phoneNumber: (orderData.shipping_address && orderData.shipping_address.phone_number) || '',
            address1: (orderData.shipping_address && orderData.shipping_address.line1) || '',
            address2: (orderData.shipping_address && orderData.shipping_address.line2) || '',
            city: (orderData.shipping_address && orderData.shipping_address.line4) || '',
            state: (orderData.shipping_address && orderData.shipping_address.state) || '',
            countryCode: (orderData.shipping_address && orderData.shipping_address.country) || '',
            countryName: (orderData.shipping_address && orderData.shipping_address.country) || '',
            zipCode: (orderData.shipping_address && orderData.shipping_address.postcode) || ''
          }
        : {
            firstName: orderData.billing_address.first_name || '',
            lastName: orderData.billing_address.last_name || '',
            phoneNumber: orderData.billing_address.phone_number || '',
            address1: orderData.billing_address.line1 || '',
            address2: orderData.billing_address.line2 || '',
            city: orderData.billing_address.line4 || '',
            state: orderData.billing_address.state || '',
            countryCode: orderData.billing_address.country || '',
            countryName: orderData.billing_address.country || '',
            zipCode: orderData.billing_address.postcode || ''
          },
      useCreditCard: orderResponse.useCreditCard || false
    }

    orderInfo = {
      ...orderInfo,
      ...window.pageUrls,
      cusEmail: orderData.user.email,
      cusPhone: orderData.user.phone_number,
      firstName: orderData.user.first_name,
      cusFirstName: orderData.user.first_name,
      lastName: orderData.user.last_name,
      cusLastName: orderData.user.last_name,
      cusCity: (orderData.shipping_address && orderData.shipping_address.line4) || '',
      cusState: (orderData.shipping_address && orderData.shipping_address.state) || '',
      cusCountry: (orderData.shipping_address && orderData.shipping_address.country) || '',
      cusZip: (orderData.shipping_address && orderData.shipping_address.postcode) || ''
    }

    if (orderedProducts.upsellUrls && orderedProducts.upsellUrls.length) {
      orderInfo.orderedProducts = [...orderInfo.orderedProducts, ...orderedProducts.upsellUrls]
      orderInfo.upsellUrls = orderedProducts.upsellUrls
    }
  } catch (e) {
    console.log(e)
  }

  setOrderInfo(orderInfo)
}
function generateKonnektiveAPIUrl(orderData, paymenttype) {
  // let baseAPI = `${WEB_SERVICE_URL}/konnektive/order/import?`
  let baseAPI = `${WEB_SERVICE_URL}/konnektive/order/import?loginId=${window.konnektiveUserName}&password=${window.konnektivePassword}`
  const billShipSame = orderData.billing_same_as_shipping_address ? 1 : 0

  const affParam = getParamAffiliate('affId')
  const sourceValue1 = getParamAffiliate('s1', 'sourceValue1') || getParamAffiliate('c1', 'sourceValue1')
  const sourceValue2 = getParamAffiliate('s2', 'sourceValue2') || getParamAffiliate('c2', 'sourceValue2')
  const sourceValue3 = getParamAffiliate('s3', 'sourceValue3') || getParamAffiliate('c3', 'sourceValue3')
  const sourceValue4 = getParamAffiliate('s4', 'sourceValue4') || getParamAffiliate('c4', 'sourceValue4')
  const sourceValue5 = getParamAffiliate('s5', 'sourceValue5') || getParamAffiliate('c5', 'sourceValue5')
  // Campaign And Product
  const campaignId = orderData.chosenProducts[0].campaignId
  baseAPI += `&campaignId=${campaignId}${affParam}${sourceValue1}${sourceValue2}${sourceValue3}${sourceValue4}${sourceValue5}`

  for (let i = 0, n = orderData.chosenProducts.length; i < n; i++) {
    baseAPI += `&product${i + 1}_id=${orderData.chosenProducts[i].productId}&product${i + 1}_qty=${orderData.chosenProducts[0].quantity}`
  }

  // Customer Name
  const firstName = orderData.user.first_name.replace(' ', '+')
  const lastName = orderData.user.last_name.replace(' ', '+')
  const emailAddress = orderData.user.email.replace(' ', '+')
  const phoneNumber = orderData.user.phone_number
  baseAPI += `&firstName=${firstName}&lastName=${lastName}&emailAddress=${emailAddress}&phoneNumber=${phoneNumber}`

  if (paymenttype === PAYMENT_METHOD.CREDIT_CARD) {
    // Payment
    const cardNumber = orderData.payment_details.card_number
    const cardMonth = orderData.payment_details.card_expiry_month
    const cardYear = orderData.payment_details.card_expiry_year
    const cardSecurityCode = orderData.payment_details.card_cvv
    baseAPI += `&paySource=CREDITCARD&cardNumber=${cardNumber}&cardMonth=${cardMonth}&cardYear=${cardYear}&cardSecurityCode=${cardSecurityCode}`
  }

  // Shipping Address
  const address1 = orderData.shipping_address.line1.replace(' ', '+')
  const address2 = orderData.shipping_address.line2.replace(' ', '+')
  const postalCode = orderData.shipping_address.postcode.replace(' ', '+')
  const city = orderData.shipping_address.line4.replace(' ', '+')
  const state = orderData.shipping_address.state.replace(' ', '+')
  const country = orderData.shipping_address.country.replace(' ', '+')
  baseAPI += `&address1=${address1}&address2=${address2}&postalCode=${postalCode}&city=${city}&state=${state}&country=${country}&billShipSame=${billShipSame}`

  // Billing Address
  if (billShipSame === 0) {
    // Shipping Address
    const shipFirstName = orderData.user.first_name.replace(' ', '+')
    const shipLastName = orderData.user.last_name.replace(' ', '+')
    const shipAddress1 = orderData.billing_address.line1.replace(' ', '+')
    const shipAddress2 = orderData.billing_address.line2.replace(' ', '+')
    const shipPostalCode = orderData.billing_address.postcode.replace(' ', '+')
    const shipCity = orderData.billing_address.line4.replace(' ', '+')
    const shipState = orderData.billing_address.state.replace(' ', '+')
    const shipCountry = orderData.billing_address.country.replace(' ', '+')
    baseAPI += `&shipFirstName=${shipFirstName}&shipLastName=${shipLastName}&shipAddress1=${shipAddress1}&shipAddress2=${shipAddress2}&shipPostalCode=${shipPostalCode}&shipCity=${shipCity}&shipState=${shipState}&shipCountry=${shipCountry}`
  }

  if (paymenttype === PAYMENT_METHOD.PAYPAL) {
    // !TODO
    // const salesUrl = orderData.salesUrl

    // baseAPI += `&paySource=PAYPAL&salesUrl=${salesUrl}`

    /**
     * after approve payment from paypal => redirect to upsell
     */
    const redirectsTo = orderData.redirectsTo
    const errorRedirectsTo = orderData.errorRedirectsTo

    baseAPI += `&paySource=PAYPAL&redirectsTo=${redirectsTo}&errorRedirectsTo=${errorRedirectsTo}`
    return baseAPI
  }

  return baseAPI
}
export const submitCheckoutInfoKonnektive = (orderData, paymenttype) =>
  window.__CTR_FP_TRACKING.getFingerPrintId().then((fpid) => {
    orderData.fingerPrintId = fpid
    setUserPaymentType(paymenttype)

    const processing = new Promise((resolve, reject) => {
      // Submit Order
      const url = generateKonnektiveAPIUrl(orderData, paymenttype)
      const settings = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
      window.ctrwowUtils
        .callAjax(url, settings)
        .then((result) => {
          console.log(result)
          localStorage.setItem('mainOrderLink', location.pathname)
          if (orderData.user) {
            localStorage.setItem('user_firstname', orderData.user.first_name)
            localStorage.setItem('user_lastname', orderData.user.last_name)
          }

          // if (result && result.result === 'SUCCESS') {
          if (result && result.result === 'MERC_REDIRECT') {
            resolve(result)
          } else {
            reject(result)
          }
        })
        .catch((e) => {
          localStorage.setItem('mainOrderLink', location.pathname)
          reject(e)
        })
    })

    return processing
      .then((result) => {
        if (window.upsellUrls && window.upsellUrls.length > 0) {
          window.upsellUrls.map((item) => {
            item.upsellUrl += location.search
            return item
          })
        }
        let data = {
          upsells: window.upsellUrls || [],
          orderNumber: result.message.orderId,
          paymentType: paymenttype,
          useCreditCard: paymenttype === PAYMENT_METHOD.CREDIT_CARD
        }
        // if (typeof result === 'object' && result.result === 'SUCCESS') {
        if (typeof result === 'object' && result.result === 'MERC_REDIRECT') {
          data = {
            ...data,
            ...result,
            callBackUrl: result.message.url
          }
          if (result.message && result.message.paypalUrl) {
            data.callBackUrl = result.message.paypalUrl
          }
        }

        saveCheckoutInfoKonnektive(data, orderData)
        return data
      })
      .catch((e) => console.log(e))
  })
