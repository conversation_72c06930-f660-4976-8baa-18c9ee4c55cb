import { submitCheckoutInfoWoo, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'

import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'
import { checkStatus, createStripeCharge } from 'shared-woocommerce/src/helper'
import { saveWooCheckoutInfo, updateOrderData } from 'shared-woocommerce/src/order'
// import { createStripeSessionCheckout } from 'shared-woocommerce/src/helper'

function getCreditCardNumber(cardNumberStr) {
  const card = cardNumberStr.replace(/[^0-9]/g, '')
  return card
}

function convertMonthYearObj(my) {
  return {
    month: Number(my.split('/')[0]),
    year: Number(my.split('/')[1])
  }
}

function getOrderDataWoo({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {} } = {}) {
  const product = mainOrderInfo.getProduct()
  window.shippingIndex = window.shippingIndex || 0
  const shippingMethodId = product && product?.shippings.length > 0 ? product.shippings[window.shippingIndex].methodId : ''
  const shippingPrice = product && product?.shippings.length > 0 ? product.shippings[window.shippingIndex].price : 0
  const shippingTitle = product && product?.shippings.length > 0 ? product.shippings[window.shippingIndex].title : ''
  const commonUserInfo = {
    first_name: customer.firstName,
    last_name: customer.lastName
  }
  const shippingInfomation = {
    ...commonUserInfo,
    address_1: shippingAddress.address1,
    address_2: shippingAddress.address2 || '',
    city: shippingAddress.city,
    state: shippingAddress.state,
    country: shippingAddress.countryCode,
    postcode: shippingAddress.zipCode
  }
  const billingInfomation = {
    ...commonUserInfo,
    address_1: billingAddress.address1 || shippingAddress.address1,
    address_2: billingAddress.address2 || shippingAddress.address2 || '',
    city: billingAddress.city || shippingAddress.city,
    state: billingAddress.state || shippingAddress.state,
    country: billingAddress.countryCode || shippingAddress.countryCode,
    postcode: billingAddress.zipCode || shippingAddress.zipCode,
    phone: customer.phoneNumber || '',
    email: customer.email
  }
  const paymentDetails = {
    payment_method: PAYMENT_METHOD.STRIPE,
    payment_method_title: PAYMENT_METHOD.STRIPE,
    set_paid: false
  }

  const shippingMethod = [
    {
      method_id: shippingMethodId,
      method_title: shippingTitle,
      total: shippingPrice.toFixed(2)
    }
  ]

  return {
    ...paymentDetails,
    shipping: shippingInfomation,
    billing: billingInfomation,
    line_items: [
      {
        product_id: product?.productId,
        quantity: product?.quantity
      }
    ],
    shipping_lines: shippingMethod
  }
}

function getCardInfo({ payment }) {
  const cardInfo = {
    number: getCreditCardNumber(payment.creditcard),
    exp_month: convertMonthYearObj(payment.expiration).month,
    exp_year: convertMonthYearObj(payment.expiration).year,
    cvc: payment.cvv
  }
  return cardInfo
}

export default function checkoutWithCreditCardWoo(paymentInfo, flag = false) {
  console.log(paymentInfo)
  // const { ctrwowUtils } = window

  const $checkoutWithCreditCardButtonV1 = $('button[name="checkoutWithCreditCardWoo"]')
  const $checkoutWithCreditCardButton = $checkoutWithCreditCardButtonV1.length
    ? $checkoutWithCreditCardButtonV1
    : $('button[name="checkoutWithCreditCardWoo"]')

  const orderData = getOrderDataWoo(paymentInfo)
  const cardInfo = getCardInfo(paymentInfo)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')
  // const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

  return submitCheckoutInfoWoo(orderData, PAYMENT_METHOD.CREDIT_CARD)
    .then(async (result) => {
      console.log('credit card checkout - success')
      const productSelected = mainOrderInfo.getProduct()
      const { currency, total, orderNumber, billing } = result
      const chargeDescription = {
        orderNumber: `ORDER NUMBER: ${orderNumber}`,
        customerInfo: `${billing.first_name} (${billing.email})`
      }
      const { status, source } = await createStripeCharge(total, currency, cardInfo, chargeDescription)
      if (checkStatus(status)) {
        const { status: orderStatus } = await updateOrderData(orderNumber, { status: 'completed' })
        if (checkStatus(orderStatus)) {
          window.ctrwowUtils.events.emit('afterSubmitOrder')
          // save cardType
          const cardType = source?.brand?.toLowerCase()
          window.ctrwowUtils.localStorage().set('cardType', cardType)

          const nextLink = getNextLinkFromOrderResponse(result)
          console.log('credit card checkout - success - go to next page', nextLink)

          // const delayFirstStep = typeof window.timeDelayFirstStep === 'number' ? window.timeDelayFirstStep : 0
          // await delay(delayFirstStep)

          window._q('.paymentProccessing_cc .processing') && window._q('.paymentProccessing_cc .processing').classList.add('hidden')
          window._q('.paymentProccessing_cc .successed') && window._q('.paymentProccessing_cc .successed').classList.remove('hidden')

          // const timeDelay = typeof window.timeDelayBeforeRedirect === 'number' ? window.timeDelayBeforeRedirect : 300
          // await delay(timeDelay)

          // Save Woo Checkout Info
          saveWooCheckoutInfo(result, productSelected)
          if (flag) {
            return result
          }

          if (nextLink) {
            location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithCreditCardButton)
          } else {
            const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.confirmUrl, $checkoutWithCreditCardButton)
            window.ctrwowUtils.link.redirectPage(redirectUrl)
          }
        } else {
          throw orderStatus
        }
      }
    })
    .catch((e) => {
      console.log('credit card checkout - fail')
      console.warn(e)
      const redirectUrl = appendCtaClickIdTrackingParam(window.pageUrls.declineUrl, $checkoutWithCreditCardButton)
      window.ctrwowUtils.link.redirectPage(redirectUrl)
    })
}
