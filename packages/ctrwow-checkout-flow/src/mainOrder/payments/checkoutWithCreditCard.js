import { submitCheckoutInfo, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from './../order/mainOrderInfo'
import getCrmInstance from './../getCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'

import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'
import { ctrwow__Stripe3Ds, loading3Ds } from 'shared-checkout-flow/src/payment/stripe.helpers'

function getOrderData({ customer = {}, payment = {}, shippingAddress = {}, billingAddress = {}, threeDSSuccessUrl, threeDSFailUrl } = {}) {
  const product = mainOrderInfo.getProduct()

  const commonAddressInfo = {
    firstName: customer.firstName,
    lastName: customer.lastName,
    phoneNumber: customer.phoneNumber
  }

  window.shippingIndex = window.shippingIndex || 0

  return {
    comment: '',
    funnelBoxId: mainOrderInfo.getFunnelBoxId(),
    productId: product && product.productId,
    shippingMethodId: product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null,

    customer: customer,
    payment: {
      name: '',
      // creditcard: payment.creditcard,
      creditcard:
        window.__ctrPageConfiguration.sourceConfig && window.__ctrPageConfiguration.sourceConfig.source
          ? payment.creditcard.replace(/-/g, '')
          : payment.creditcard, // Format card number for Shopify
      creditCardBrand: payment.creditCardBrand,
      expiration: payment.expiration,
      cvv: payment.cvv
    },
    useShippingAddressForBilling: !(billingAddress && billingAddress.address1),
    shippingAddress: shippingAddress && shippingAddress.address1 ? { ...commonAddressInfo, ...shippingAddress } : null,
    billingAddress: billingAddress && billingAddress.address1 ? { ...commonAddressInfo, ...billingAddress } : null,
    threeDSSuccessUrl,
    threeDSFailUrl
    // ...mainOrderInfo.getMiniUpsellInfo()
  }
}
// detect cardType
// function GetCardType(number) {
//   // visa
//   var re = new RegExp('^4')
//   if (number.match(re) != null) return 'Visa'

//   // Mastercard
//   // Updated for Mastercard 2017 BINs expansion
//   if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(number)) return 'Mastercard'

//   // AMEX
//   re = new RegExp('^3[47]')
//   if (number.match(re) != null) return 'AMEX'

//   // Discover
//   re = new RegExp('^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)')
//   if (number.match(re) != null) return 'Discover'

//   // Diners
//   re = new RegExp('^36')
//   if (number.match(re) != null) return 'Diners'

//   // Diners - Carte Blanche
//   re = new RegExp('^30[0-5]')
//   if (number.match(re) != null) return 'Diners - Carte Blanche'

//   // JCB
//   re = new RegExp('^35(2[89]|[3-8][0-9])')
//   if (number.match(re) != null) return 'JCB'

//   // Visa Electron
//   re = new RegExp('^(4026|417500|4508|4844|491(3|7))')
//   if (number.match(re) != null) return 'Visa Electron'

//   return 'Credit Card'
// }

// update func GetCardType to match GetCardType in widget creditcard form
function GetCardType(number) {
  // visa
  var re = new RegExp('^4')
  if (number.match(re) != null) return 'Visa'

  // Mastercard
  // Updated for Mastercard 2017 BINs expansion
  if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(number)) return 'Mastercard'

  // AMEX
  re = new RegExp('^3[47]')
  if (number.match(re) != null) return 'AMEX'

  // Discover
  re = new RegExp('^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)')
  if (number.match(re) != null) return 'Discover'

  // Diners
  re = new RegExp('^36')
  if (number.match(re) != null) return 'Diners'

  // Diners - Carte Blanche
  re = new RegExp('^30[0-5]')
  if (number.match(re) != null) return 'Diners Carte Blanche'

  // JCB
  re = new RegExp('^35(2[89]|[3-8][0-9])')
  if (number.match(re) != null) return 'JCB'

  // MASTER CARD
  re = new RegExp('^5[1-5]')
  if (number.match(re) != null) return 'Mastercard'

  // Visa Electron
  re = new RegExp('^(4026|417500|4508|4844|491(3|7))')
  if (number.match(re) != null) return 'Visa Electron'

  return 'Default'
}

function checkAndRedirectPage(ctrwowUtils, result, $checkoutWithCreditCardButton) {
  let nextLink = getNextLinkFromOrderResponse(result)

  if (nextLink && nextLink.indexOf('?') === -1 && location.href.split('?').length > 1) {
    nextLink = `${nextLink}?${location.href.split('?')[1]}`
  }
  console.log('credit card checkout - success - go to next page', nextLink)
  console.log(nextLink)

  // Detect variable pauseCheckoutProcessing to redirect url when success
  if (window.pauseCheckoutProcessing) {
    setTimeout(function () {
      if (nextLink) {
        location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithCreditCardButton)
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(getPageSettings().confirmUrl, $checkoutWithCreditCardButton)
        ctrwowUtils.link.redirectPage(redirectUrl)
      }
    }, window.pauseCheckoutProcessing.delay)
  } else {
    if (nextLink) {
      location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithCreditCardButton)
    } else {
      const redirectUrl = appendCtaClickIdTrackingParam(getPageSettings().confirmUrl, $checkoutWithCreditCardButton)
      ctrwowUtils.link.redirectPage(redirectUrl)
    }
  }
}

export default function checkoutWithCreditCard(paymentInfo, flag = false) {
  console.log(paymentInfo)
  window.ctrwowUtils.events.emit('beforeGetOrderData')

  const { ctrwowUtils } = window

  const $checkoutWithCreditCardButtonV1 = $('button[name="checkoutWithCreditCardV1"]')
  const $checkoutWithCreditCardButton = $checkoutWithCreditCardButtonV1.length
    ? $checkoutWithCreditCardButtonV1
    : $('button[name="checkoutWithCreditCard"]')

  const orderData = getOrderData(paymentInfo)

  // check and switch product when case more products
  if (window.multiMainProducts && window.multiMainProducts.length > 0) {
    orderData.productId = 0
    orderData.products = window.multiMainProducts
  }

  if (document.getElementById('installpaymentDdl')) {
    orderData.payment.Instalments = document.getElementById('installpaymentDdl').value
  }
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')

  if (ctrwowUtils.link.getQueryParameter('iscardtest') === '1' && ctrwowUtils.link.getQueryParameter('card_number')) {
    orderData.payment.creditcard = ctrwowUtils.link.getQueryParameter('card_number')
  }

  window.__loading3Ds = null
  loading3Ds && (window.__loading3Ds = loading3Ds())
  return submitCheckoutInfo(orderData, PAYMENT_METHOD.CREDIT_CARD)
    .then(async (result) => {
      console.log('credit card checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder', result)

      // === start check 3Ds
      let __TIMEOUT = 0
      const __continueResult = result.paymentContinueResult?.items.reduce((objResult, item) => {
        objResult[item.key] = item.value
        return objResult
      }, {})

      if (__continueResult && __continueResult.requires_action === 'true') {
        window.ctrwowUtils.localStorage().set('stripe_3Ds', true)
        window.__loading3Ds.waiting()
        const __ctrwow__Stripe3Ds = await ctrwow__Stripe3Ds()
        const { requires_action_publishkey, requires_action_client_secret } = __continueResult
        const rsVerify3Ds = await __ctrwow__Stripe3Ds.init({ publicKey: requires_action_publishkey, clientSecrect: requires_action_client_secret })
        if (rsVerify3Ds && rsVerify3Ds.success) {
          const __confirm3Ds = await __ctrwow__Stripe3Ds.confirmOrder(result)
          if (__confirm3Ds.success) {
            // window.isUseSuccessAndFailPopup defined in creditcardSB widget
            window.isUseSuccessAndFailPopup && window.__loading3Ds.success()

            // window.timeout4SuccessPopup defined in creditcardSB widget
            __TIMEOUT = window.timeout4SuccessPopup
          } else {
            // window.isUseSuccessAndFailPopup defined in creditcardSB widget
            window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
            return
          }
        } else {
          // window.isUseSuccessAndFailPopup defined in creditcardSB widget
          window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
          return
        }
      }
      // === end check 3Ds

      // save cardTyoe
      if (result.useCreditCard) {
        const cardType = GetCardType(orderData.payment.creditcard)
        window.ctrwowUtils.localStorage().set('cardType', cardType)
      }

      if (flag) {
        return result
      }

      setTimeout(() => {
        if (window.walletInfo) {
          window.ctrwowUtils.events.on('submitNFTDataToGoogleFinished', function (data) {
            // Detect variable pauseCheckoutProcessing to display custom loading with success message
            if (window.pauseCheckoutProcessing) {
              const customPopup = document.querySelector('.custom-loading')
              customPopup.classList.add('successed')
            }

            checkAndRedirectPage(data.ctrwowUtils, data.result, data.checkoutWithCreditCardButton)
          })
          window.walletInfo.isCallSubmit = true
          window.ctrwowUtils.events.emit('callSubmitNFTDataToGoogle', {
            result: result,
            ctrwowUtils: ctrwowUtils,
            checkoutWithCreditCardButton: $checkoutWithCreditCardButton
          })
        } else {
          // Detect variable pauseCheckoutProcessing to display custom loading with success message
          if (window.pauseCheckoutProcessing) {
            const customPopup = document.querySelector('.custom-loading')
            customPopup.classList.add('successed')
          }
          checkAndRedirectPage(ctrwowUtils, result, $checkoutWithCreditCardButton)
        }
      }, Number(__TIMEOUT))

      // checkAndRedirectPage(ctrwowUtils, nextLink, $checkoutWithCreditCardButton)
    })
    .catch((e) => {
      console.log(e)
      console.log('credit card checkout - fail')
      window.ctrwowUtils.events.emit('declinePayment')
      window.isDeclineCC = true
      if ((!e.success && e.type === 'STRIPE_3DS_FAIL') || (window.__loading3Ds.checkFailForm() && window.isUseFailPopup)) {
        window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
      } else {
        const isUsedPaymentDecline = arguments.length > 2 ? arguments[2].isUsedPaymentDecline : null
        if (isUsedPaymentDecline) {
          throw e
        }

        if (window.isDeclinePopup) {
          return
        }
        const redirectUrl = appendCtaClickIdTrackingParam(getCrmInstance().declineUrl, $checkoutWithCreditCardButton)

        // Detect variable pauseCheckoutProcessing to redirect url when decline
        if (window.pauseCheckoutProcessing) {
          setTimeout(function () {
            ctrwowUtils.link.redirectPage(redirectUrl)
          }, window.pauseCheckoutProcessing.delay)
        } else {
          ctrwowUtils.link.redirectPage(redirectUrl)
        }
      }
    })
}
