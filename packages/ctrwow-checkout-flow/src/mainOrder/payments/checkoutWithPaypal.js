import { submitCheckoutInfo, getNextLinkFromOrderResponse } from './helpers'
import * as mainOrderInfo from './../order/mainOrderInfo'
import getCrmInstance from './../getCrmInstance'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { appendCtaClickIdTrackingParam, mergeCurrentParamsWithCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getOrderData(paymentProcessorId) {
  const product = mainOrderInfo.getProduct()

  window.shippingIndex = window.shippingIndex || 0

  var orderData = {
    comment: '',
    useShippingAddressForBilling: true,
    customer: {
      email: null,
      phoneNumber: null,
      firstName: null,
      lastName: null
    },
    payment: {
      paymentProcessorId: paymentProcessorId
    },
    shippingAddress: null,
    billingAddress: null,
    funnelBoxId: mainOrderInfo.getFunnelBoxId(),
    productId: product && product.productId,
    shippingMethodId: product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null
    // ...mainOrderInfo.getMiniUpsellInfo()
  }


  const shopifySourceConfig = window.__ctrPageConfiguration.sourceConfig ? window.__ctrPageConfiguration.sourceConfig.source : null
  if (shopifySourceConfig === 'SHOPIFY') {
    window.ctrwowUtils.localStorage().set('SOURCE_CONFIG', 'SHOPIFY')
    window.ctrwowUtils.localStorage().set('XCID', window.__ctrPageConfiguration.cid)
    orderData = {...orderData, customer: null}
    orderData = {...orderData, successUrl: window.__ctrPageConfiguration.successUrl, declineUrl: window.__ctrPageConfiguration.declineUrl}
  }

  return orderData
}

function __continueAfterSubmitOrder({result, $checkoutWithPaypalButton, flag}) {
  // window.localStorage.setItem('paypal_isMainOrder', 'main')
  // window.localStorage.setItem('webkey_to_check_paypal', getCrmInstance().webkey)

  // if (flag) {
  //   return result
  // }

  const nextLink = getNextLinkFromOrderResponse(result)
  console.log('paypal checkout - success - go to next page', nextLink)
  console.log(nextLink)

  if (nextLink) {
    location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithPaypalButton)
  } else {
    const redirectUrl = appendCtaClickIdTrackingParam(getPageSettings().confirmUrl, $checkoutWithPaypalButton)
    window.ctrwowUtils.link.redirectPage(redirectUrl)
  }
}

export default function checkoutWithPaypal(paymentProcessorId, flag = false) {
  window.ctrwowUtils.events.emit('beforeGetOrderData')
  const { ctrwowUtils } = window
  const $checkoutWithPaypalButton = $('button.checkoutWithPaypal')
  const orderData = getOrderData(paymentProcessorId)

  // check and switch product when case more products
  if (window.multiMainProducts && window.multiMainProducts.length > 0) {
    orderData.productId = 0
    orderData.products = window.multiMainProducts
  }

  if (!orderData.payment) {
    orderData.payment = {}
  }
  orderData.payment.callBackParam = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutWithPaypalButton)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')
  window.ctrwowUtils.events.emit('onBeforeSubmitOrderPaypal')

  return submitCheckoutInfo(orderData, PAYMENT_METHOD.PAYPAL)
    .then((result) => {
      console.log('paypal checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder', result)
      // window.localStorage.setItem('mainOrderLink', location.pathname)
      // window.localStorage.setItem('userPaymentType', 'paypal')

      window.localStorage.setItem('paypal_isMainOrder', 'main')
      window.localStorage.setItem('webkey_to_check_paypal', getCrmInstance().webkey)

      if (flag) {
        return result
      }

      if (window.walletInfo) {
        window.ctrwowUtils.events.on('submitNFTDataToGoogleFinished', function (data) {
          if (!window.isPauseProcess__Paypal) {
            __continueAfterSubmitOrder({ result: data.result, $checkoutWithPaypalButton: data.checkoutWithPaypalButton, flag: data.flag })
          } else {
            window.ctrwowUtils.events.emit('onAfterPlaceOrderPaypal', result)
            window.ctrwowUtils.events.on('continuePlaceOrderProcessPaypal', (result) => {
              __continueAfterSubmitOrder({ result: data.result, $checkoutWithPaypalButton: data.checkoutWithPaypalButton, flag: data.flag })
            })
          }
        })
        window.ctrwowUtils.events.emit('callSubmitNFTDataToGoogle', {
          result: result,
          checkoutWithPaypalButton: $checkoutWithPaypalButton,
          flag: flag
        })
      } else {
        if (!window.isPauseProcess__Paypal) {
          __continueAfterSubmitOrder({ result: result, $checkoutWithPaypalButton: $checkoutWithPaypalButton, flag: flag })
        } else {
          window.ctrwowUtils.events.emit('onAfterPlaceOrderPaypal', result)
          window.ctrwowUtils.events.on('continuePlaceOrderProcessPaypal', (result) => {
            __continueAfterSubmitOrder({ result: result, $checkoutWithPaypalButton: $checkoutWithPaypalButton, flag: flag })
          })
        }
      }
    })
    .catch((e) => {
      console.log(e)
      console.log('paypal checkout - fail')
      if (window.hasDeclinePaymentPopup) {
        window.ctrwowUtils.events.emit('afterSubmitOrderFail')
      } else {
        const redirectUrl = appendCtaClickIdTrackingParam(getCrmInstance().declineUrl, $checkoutWithPaypalButton)
        ctrwowUtils.link.redirectPage(redirectUrl)
      }
    })
}
