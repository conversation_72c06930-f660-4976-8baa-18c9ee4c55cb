import { submitCheckoutInfo } from './helpers'
import * as mainOrderInfo from '../order/mainOrderInfo'
import getCrmInstance from '../getCrmInstance'
// import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import { PAYMENT_METHOD } from 'shared-checkout-flow/src/payment/userPaymentType'
import { mergeCurrentParamsWithCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking'

function getOrderData(paymentProcessorId) {
  const product = mainOrderInfo.getProduct()

  window.shippingIndex = window.shippingIndex || 0

  return {
    comment: '',
    useShippingAddressForBilling: true,
    customer: {
      email: null,
      phoneNumber: null,
      firstName: null,
      lastName: null
    },
    payment: {
      paymentProcessorId: paymentProcessorId
    },
    shippingAddress: null,
    billingAddress: null,
    funnelBoxId: mainOrderInfo.getFunnelBoxId(),
    productId: product && product.productId,
    shippingMethodId: product && product.shippings && product.shippings.length > 0 ? product.shippings[window.shippingIndex].shippingMethodId : null,
    successUrl: window.__ctrPageConfiguration.successUrl,
    declineUrl: window.__ctrPageConfiguration.declineUrl,
    // ...mainOrderInfo.getMiniUpsellInfo()
  }
}

export default function checkoutWithPaypalShopify(paymentProcessorId = 5, flag = false) {
  window.ctrwowUtils.events.emit('beforeGetOrderData')
  const $checkoutWithPaypalButton = $('button.checkoutWithPaypal')
  const orderData = getOrderData(paymentProcessorId)
  if (!orderData.payment) {
    orderData.payment = {}
  }
  orderData.payment.callBackParam = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutWithPaypalButton)
  if (!orderData || orderData === '') {
    return Promise.reject()
  }

  window.ctrwowUtils.events.emit('beforeSubmitOrder')

  return submitCheckoutInfo(orderData, PAYMENT_METHOD.PAYPAL)
    .then((result) => {
      console.log('paypal checkout - success')
      window.ctrwowUtils.events.emit('afterSubmitOrder', result)
      // window.localStorage.setItem('mainOrderLink', location.pathname)
      // window.localStorage.setItem('userPaymentType', 'paypal')

      window.localStorage.setItem('paypal_isMainOrder', 'main')
      window.localStorage.setItem('webkey_to_check_paypal', getCrmInstance().webkey)

      return result
      
      // if (flag) {
      //   return result
      // }

      // const nextLink = getNextLinkFromOrderResponse(result)
      // console.log('paypal checkout - success - go to next page', nextLink)
      // console.log(nextLink)

      // if (nextLink) {
      //   location.href = appendCtaClickIdTrackingParam(nextLink, $checkoutWithPaypalButton)
      // } else {
      //   const redirectUrl = appendCtaClickIdTrackingParam(getPageSettings().confirmUrl, $checkoutWithPaypalButton)
      //   ctrwowUtils.link.redirectPage(redirectUrl)
      // }
    })
    .catch((e) => {
      console.log(e)
      console.log('paypal checkout - fail')
      // if (window.hasDeclinePaymentPopup) {
      //   window.ctrwowUtils.events.emit('afterSubmitOrderFail')
      // } else {
      //   const redirectUrl = appendCtaClickIdTrackingParam(getCrmInstance().declineUrl, $checkoutWithPaypalButton)
      //   ctrwowUtils.link.redirectPage(redirectUrl)
      // }
    })
}
