import * as checkoutData from './order/mainOrderInfo'

const parseValues = (elm) => {
  let values = elm.getAttribute('life-time-warranty') || ''
  values = values.trim().split('__p')
  if (values.length < 2) {
    return []
  }

  const funnelId = values[0]
  const percent = parseInt(values[1])

  return funnelId && percent ? [funnelId, percent] : []
}

const getSlugPage = (elm) => {
  if (elm.hasAttribute('slug-page-name-skip')) {
    return elm.getAttribute('slug-page-name-skip') || ''
  }
  return false
}

const upParam = (elm) => {
  if (elm.hasAttribute('params-checked-append-url')) {
    return elm.getAttribute('params-checked-append-url') || ''
  }
  return false
}

const calculatePrice = (product, percent) => {
  return product.productPrices.DiscountedPrice.Value * (percent / 100)
}

export default function addLifetimeWarrantyHandler(elm) {
  const [funnelId, percent] = parseValues(elm)
  const slugUrl = getSlugPage(elm)
  const params = upParam(elm)
  const checkbox = elm.querySelector('input[type=checkbox]')
  let warrantyPrice = 0

  checkoutData.onProductChange((product) => {
    warrantyPrice = calculatePrice(product, percent)
    elm.querySelector('.warranty_price').innerHTML = window.ctrwowUtils.number.formaterNumberByFormattedValue(
      warrantyPrice,
      product.productPrices.DiscountedPrice.FormattedValue
    )
    checkbox && checkoutData.setLifetimeWarrantyConfig('type', checkbox.checked ? { funnelId, warrantyPrice } : { funnelId: 0, warrantyPrice: 0 })
  })

  checkbox &&
    checkbox.addEventListener('change', (event) => {
      checkoutData.setLifetimeWarrantyConfig('type', event.target.checked ? { funnelId, warrantyPrice } : { funnelId: 0, warrantyPrice: 0 })
      checkoutData.setLifetimeWarrantyConfig('skipUpsell', event.target.checked)
    })
  checkoutData.setLifetimeWarrantyConfig('skipUpsell', checkbox.checked)
  slugUrl && checkoutData.setLifetimeWarrantyConfig('slugURL', slugUrl)
  params && checkoutData.setLifetimeWarrantyConfig('upParam', params)
}
export function getUpsellNameFromUrl(upsellURL) {
  let parts = upsellURL.split('special-offer-')
  if (parts.length > 1) {
    parts = parts[1].split('.html')[0].split('-')

    // remove last word which length = 24 (could be - guid of page)
    if (parts[parts.length - 1].length === 24) {
      parts.pop()
    }

    return parts.join('-')
  }

  return null
}

export function getUpParam(upsellURL) {
  const upsellName = getUpsellNameFromUrl(upsellURL)
  let upParam = ''

  if (upsellName) {
    upParam = 'up_' + upsellName + '=1'
  }
  return upParam
}

export const appendParamIntoUrl = (upsellURL, paramURL) => {
  if (paramURL === '') return
  const currentUrl = upsellURL

  if (currentUrl.indexOf(paramURL) > -1) {
    return currentUrl
  }
  if (currentUrl.indexOf('?') > -1) {
    paramURL = `&${paramURL}`
  } else {
    paramURL = `?${paramURL}`
  }
  const newurl = currentUrl + paramURL
  return newurl
}

export const handleDataSkipUpsellWarranty = (data, slugURL) => {
  const orderInfo = data
  const filterValue = (item) =>
    slugURL ? item.upsellUrl.indexOf(slugURL) <= -1 : item.upsellUrl.indexOf('warranty') <= -1 && item.upsellUrl.indexOf('koreprotect') <= -1
  if (orderInfo.upsells && orderInfo.upsells.length > 0) {
    const upsells = orderInfo.upsells.filter((item) => filterValue(item))
    var difference = orderInfo.upsells.filter((item) => upsells.indexOf(item) === -1)
    let upParam = checkoutData.getLifetimeWarrantyConfig__upParam()
    if (difference.length > 0) {
      upParam = upParam || getUpParam(difference[0].upsellUrl)
      if (upsells.length > 0 && upParam) {
        upsells[0].upsellUrl = appendParamIntoUrl(upsells[0].upsellUrl, upParam)
      }
    }
    orderInfo.upsells = upsells
  }

  return orderInfo
}
