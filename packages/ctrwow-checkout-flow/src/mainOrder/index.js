import './initPage'

export { default as getPageSettings } from 'shared-checkout-flow/src/getPageSettings'
export { default as getCheckoutConfig } from 'shared-checkout-flow/src/getPageSettings'
import * as payment from './payments'
export { payment }

import * as mainOrderInfo from './order/mainOrderInfo'
export { mainOrderInfo as checkoutData }

import * as mainProductList from 'shared-checkout-flow/src/orderInfo/mainProductList'
export { mainProductList as productListData }

import getCrmInstance from './getCrmInstance'
export { getCrmInstance }

export { default as addLifetimeWarrantyHandler } from './lifeTimeWarrantyHandler'

import * as additionalInfo from 'shared-checkout-flow/src/orderInfo/addAddedInfoToOrderPayload'
export { additionalInfo }

import trackingPlaceOrder from 'shared-checkout-flow/src/tracking/trackingPlaceOrder'
export { trackingPlaceOrder }

import { appendCtaClickIdTrackingParam } from 'shared-checkout-flow/src/tracking/index'
export { appendCtaClickIdTrackingParam }

export const ready = () => (window.__ctrCheckoutFlow.isReady ? Promise.resolve(true) : window.__ctrCheckoutFlow.processing)
