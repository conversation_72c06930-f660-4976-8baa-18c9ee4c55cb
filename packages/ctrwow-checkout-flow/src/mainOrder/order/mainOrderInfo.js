import loadStatistical, { saveMainOrderGtmID, saveMainOrderPageId } from './loadStatisticalByProduct'

class MainOrderData {
  constructor() {
    console.log('init MainOrderData')
    this.data = {
      product: null
    }
    this.checkoutDataHandler = {}
  }

  get product() {
    return this.data.product
  }

  set product(newVal) {
    const oldVal = this.data.product

    this.data.product = newVal

    // for tracking purpose
    loadStatistical(newVal, this.lifetimeWarrantyConfig || {})

    // for tracking gtmID
    saveMainOrderGtmID()

    // for tracking main order
    saveMainOrderPageId()

    this.executeHandler('product', oldVal, newVal)
    // const handlers = (this.checkoutDataHandler && this.checkoutDataHandler.product) || []
    // handlers.forEach((fn) => fn(newVal, oldVal))
  }

  get lifetimeWarrantyConfig() {
    return this.data.lifetimeWarrantyConfig
  }

  set lifetimeWarrantyConfig({ key, values }) {
    const oldVal = this.data.lifetimeWarrantyConfig

    this.data.lifetimeWarrantyConfig = {
      ...(oldVal || {}),
      [key]: values
    }

    // for tracking purpose
    loadStatistical(this.product, this.data.lifetimeWarrantyConfig)

    this.executeHandler('lifetimeWarrantyConfig', oldVal, this.data.lifetimeWarrantyConfig)

    // const handlers = (this.checkoutDataHandler && this.checkoutDataHandler.lifetimeWarrantyConfig) || []
    // handlers.forEach((fn) => fn(newVal, oldVal))
  }

  get miniUpsell() {
    return this.data.miniUpsell
  }

  set miniUpsell(newVal) {
    const oldVal = this.data.miniUpsell

    this.data.miniUpsell = newVal
    this.executeHandler('miniUpsell', oldVal, newVal)
  }

  get taxPercent() {
    return this.data.taxPercent
  }

  set taxPercent(newVal) {
    const oldVal = this.data.taxPercent

    this.data.taxPercent = newVal
    this.executeHandler('taxPercent', oldVal, newVal)
  }

  get giftCard() {
    return this.data.giftCard
  }

  set giftCard(newVal) {
    const oldVal = this.data.giftCard

    this.data.giftCard = newVal
    this.executeHandler('giftCard', oldVal, newVal)
  }

  addHandler(key, handler) {
    if (!this.checkoutDataHandler[key]) {
      this.checkoutDataHandler[key] = []
    }

    const handlers = this.checkoutDataHandler[key]
    handlers.push(handler)

    return () => {
      const index = handlers.indexOf(handler)
      handlers.splice(index, 1)
    }
  }

  executeHandler(key, oldVal, newVal) {
    const handlers = (this.checkoutDataHandler && this.checkoutDataHandler[key]) || []
    handlers.forEach((fn) => fn(newVal, oldVal))
  }
}

// only support one instance per page
let checkoutData
if (window.__checkoutData) {
  checkoutData = window.__checkoutData
} else {
  checkoutData = window.__checkoutData = new MainOrderData()
}

export const initData = () => {
  console.log('mainOrderInfo::initData')
  checkoutData = new MainOrderData()
}

const getData = (dataKey) => dataKey && checkoutData[dataKey]
const setData = (dataKey, dataValue) => {
  checkoutData[dataKey] = dataValue
}
export const selectedProductItem = () => {
  const selectedProduct = window.ctrwowCheckout.checkoutData.getProduct()
  return document.querySelector(`.js-list-item[data-id="${selectedProduct.productId}"]`)
}
export const setProduct = (val) => setData('product', val)
export const getProduct = () => getData('product')
export const onProductChange = (handler) => {
  console.log('onProductChange')
  checkoutData.addHandler('product', handler)
}

export const onLifetimeWarrantyConfigChange = (handler) => {
  console.log('onLifetimeWarrantyConfigChange')
  checkoutData.addHandler('lifetimeWarrantyConfig', handler)
}

export const setCouponCode = (val) => {
  setData('couponCode', val)
  if (!val) {
    delete window.discountTypeCoupOn
    delete window.discountCoupOn
  }
}
export const getCouponCode = () => getData('couponCode')

export const setMessageCard = (val) => setData('messageCard', val)
export const getMessageCard = () => getData('messageCard')

export const setMiniUpsell = (val) => setData('miniUpsell', val)
export const getMiniUpsell = () => getData('miniUpsell')
export const onMiniUpsellChange = (handler) => {
  console.log('onMiniUpsellChange')
  checkoutData.addHandler('miniUpsell', handler)
}
export const addMiniUpsellToOrderData = (orderData) => {
  const miniUpsell = getMiniUpsell()

  if (!miniUpsell) {
    return
  }

  if (Array.isArray(miniUpsell)) {
    orderData.multipleMiniUpsells = miniUpsell
  } else {
    orderData.miniUpsell = miniUpsell
  }

  /* let grandTotal = Number(window.localStorage.getItem('conventionTrackingPrice'))
  if (miniUpsell.length > 0) {
    for (const miniItem of miniUpsell) {
      if (miniItem.price) {
        grandTotal += miniItem.price
      }
    }
  }

  window.localStorage.setItem('conventionTrackingPrice', grandTotal.toFixed(2)) */
}

export const setTaxPercent = (val) => setData('taxPercent', val)
export const getTaxPercent = () => getData('taxPercent')
export const onTaxPercentChange = (handler) => {
  console.log('onTaxPercentChange')
  checkoutData.addHandler('taxPercent', handler)
}

// Gift Card
export const setGiftCard = (val) => setData('giftCard', val)
export const getGiftCard = () => getData('giftCard')
export const onGiftCardChange = (handler) => {
  console.log('onGiftCardChange')
  checkoutData.addHandler('giftCard', handler)
}
export const addGiftCardToOrderData = (orderData) => {
  const giftCard = getGiftCard()

  if (!giftCard) return

  orderData.GiftCardNumber = giftCard.GiftCardNumber
  if (window.additionPriceValue && window.additionPriceValue > giftCard.NewBalance) {
    giftCard.CanPayAll = false
    window.localStorage.setItem('giftCard', JSON.stringify(giftCard))
    return
  }
  if (giftCard.CanPayAll) {
    orderData.payment = {
      paymentProcessorId: 62
    }
  }
}

export const checkCamp = (webKey) => {
  let isExisted = true
  let campProducts = window.localStorage.getItem('campproducts')
  if (campProducts) {
    try {
      campProducts = JSON.parse(campProducts)
      const camps = campProducts.camps.filter((item) => item[webKey])

      if (camps.length > 0) {
        const beforeDate = new Date(camps[0][webKey].timestamp)
        const newDate = new Date()
        const res = Math.abs(newDate - beforeDate) / 1000
        const minutes = Math.floor(res / 60)
        // console.log('check time of keeping prices in local storage: ', minutes);
        if (minutes >= 0) isExisted = false
      } else {
        isExisted = false
      }
    } catch (err) {
      console.log(err)
      isExisted = false
    }
  } else {
    isExisted = false
  }
  return isExisted
}

// TODO - need to verify how to get this value - copy source from [ctrwowWeb > paypal component]
export const setLifetimeWarrantyConfig = (key, values) => setData(`lifetimeWarrantyConfig`, { key, values })
const getLifetimeWarrantyConfigLocal = () => getData('lifetimeWarrantyConfig') || {}
export const getLifetimeWarrantyConfig__type = () => {
  const lifetimeWarrantyConfig = getLifetimeWarrantyConfigLocal()
  return lifetimeWarrantyConfig.type || { warrantyPrice: 0, funnelId: 0 }
}
export const getLifetimeWarrantyConfig__skipUpsell = () => {
  const lifetimeWarrantyConfig = getLifetimeWarrantyConfigLocal()
  return lifetimeWarrantyConfig.skipUpsell || false
}
export const getLifetimeWarrantyConfig = getLifetimeWarrantyConfig__type
export const getLifetimeWarrantyConfig__slugURL = () => {
  const lifetimeWarrantyConfig = getLifetimeWarrantyConfigLocal()
  return lifetimeWarrantyConfig.slugURL || false
}
export const getLifetimeWarrantyConfig__upParam = () => {
  const lifetimeWarrantyConfig = getLifetimeWarrantyConfigLocal()
  return lifetimeWarrantyConfig.upParam || false
}

export const getFunnelBoxId = () => {
  const { funnelId } = getLifetimeWarrantyConfig__type() || {}
  return funnelId
}
export const handleDataSkipUpsellWarranty = (data) => {
  const orderInfo = data
  if (orderInfo.upsells && orderInfo.upsells.length > 0) {
    const upsells = orderInfo.upsells.filter((item) => !item.upsellUrl.includes('special-offer-warranty'))
    orderInfo.upsells = upsells
  }
  return orderInfo
}

export const handleDataSkipMiniUpsell = (data) => {
  const orderInfo = data
  if (orderInfo.upsells && orderInfo.upsells.length > 0) {
    const upsells = orderInfo.upsells.filter((item) => !item.upsellUrl.includes(window.miniUpselUrl))
    orderInfo.upsells = upsells
  }
  return orderInfo
}
// // TODO - need to verify how to get this value - copy source from [ctrwowWeb > paypal component]
// // for paypal - preorder - do not support
// export const getMiniUpsellInfo = () => {
//   const miniUpsell = document.getElementById('txtMiniUpsellPID')
//   if (!miniUpsell) {
//     return null
//   }
//   if (miniUpsell.checked) {
//     return {
//       productId: Number(miniUpsell.dataset.id),
//       shippingMethodId: Number(document.getElementById('txtMiniUpsellShippingID').dataset.id)
//     }
//   }
// }
