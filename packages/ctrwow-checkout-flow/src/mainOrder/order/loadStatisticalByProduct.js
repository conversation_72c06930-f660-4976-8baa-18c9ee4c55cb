/* eslint-disable */
function getWarrantyPrice(warrantyPrice, taxes, selectedProduct) {
  let wPrice = 0

  if (!!document.getElementById('txtProductWarrantyLabel') && document.getElementById('txtProductWarrantyLabel').checked) {
    const warrantyRate = [0.1, 0.2, 0.2, 0.2, 0.2, 0.3, 0.5, 0.15, 0.25, 0.35, 0.4, 0.45, 0.55, 0.6]
    const funnelId = document.getElementById('txtProductWarrantyLabel')
      ? document.getElementById('txtProductWarrantyLabel').checked
        ? document.getElementById('txtProductWarrantyLabel').value
        : 0
      : 0
    const funnelPrice = warrantyRate[parseInt(funnelId) - 1]

    const warrantyPrice = (Math.round(100 * selectedProduct.productPrices.DiscountedPrice.Value * funnelPrice) / 100).toFixed(2)
    wPrice = Number(warrantyPrice)
  }

  return { wPrice }
}

// source: ctrwow_web/app/gjs/blocks/order/components/orderInfoV3.js
export default function loadStatistical(product, { warrantyPrice = 0 }) {
  if (!product) { return }
  // const fValue = product.productPrices.DiscountedPrice.FormattedValue.replace(/[,|.]/g, '')
  // const pValue = product.productPrices.DiscountedPrice.Value.toString().replace(/\./, '')
  // const fCurrency = fValue.replace(pValue, '######').replace(/\d/g, '')

  const productItem = product
  // const shippingFee = productItem.shippings[window.shippingIndex || 0].formattedPrice;

  // const taxes = productItem.productPrices.Surcharge ? productItem.productPrices.Surcharge.FormattedValue : '0.0'
  // const warranty = warrantyPrice
  const shippingPrice = productItem.shippings[window.shippingIndex || 0] ? productItem.shippings[window.shippingIndex || 0].price : 0
  const grandTotal = shippingPrice + productItem.productPrices.DiscountedPrice.Value + warrantyPrice

  window.localStorage.setItem('conventionTrackingPrice', grandTotal.toFixed(2))
  window.localStorage.setItem('ctr_checkout_url', window.location.href)
  saveCtrPublishVersion()

  if (productItem.productTypeName && productItem.productTypeName.toLocaleLowerCase() === 'coupon') {
    window.localStorage.setItem('isActivationCode', 'true')
  } else {
    window.localStorage.setItem('isActivationCode', 'false')
  }


  function saveCtrPublishVersion() {
    try {
      window.localStorage.setItem('ctr_checkout_page_publishVersion', [window.__CTRWOW_CONFIG.publishedVersion || '', location.href].join("_"))
    } catch {
      // DO NOTHING
    }
  }
}

export const saveMainOrderGtmID = () => {
  const { ctrwowUtils } = window
  const pageConfig = window.__ctrPageConfiguration
  if (!pageConfig) return
  const gtmId = pageConfig.gtmID
  if (!gtmId) return
  return ctrwowUtils.localStorage().set('mainOrder__gtmID', gtmId)
}

export const saveMainOrderPageId = () => {
  const ctrwowConfig = window.__CTRWOW_CONFIG
  if (!ctrwowConfig) return
  const pageId = ctrwowConfig.pageId
  if (!pageId) return
  window.localStorage.setItem('ctr_checkout_page_id', pageId)
  saveCtrPublishVersion()

  function saveCtrPublishVersion() {
    try {
      window.localStorage.setItem('ctr_checkout_page_publishVersion', window.__CTRWOW_CONFIG.publishedVersion || '')
    } catch {
      // DO NOTHING
    }
  }
}