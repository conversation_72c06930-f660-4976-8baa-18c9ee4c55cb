function _kountScriptLoaded() {
  // eslint-disable-next-line no-undef
  const client = new ka.ClientSDK()
  client.setupCallback({
    // 'collect-end': function (params) {
    //     console.log('end: ', params);
    // },

    // fires when collection has started.
    'collect-begin': function (params) {
      // console.log('Get directly from Kount: ', params);
      console.log('get kount-v2 directly')
      // _initIframe(params)

      // add to localStorage to use for placing order
      const obj = {
        merchantId: params.MerchantId,
        sessionId: params.MercSessId
      }
      const { ctrwowUtils: utils } = window
      const isEcomSite = !!window.__CTR_ECOM_CONFIG

      // If use Signifyd Fraud, this variable from custom script: Signifyd Fraud Init
      if (window.useSignifydFraud === true) {
        window.__CTR_FP_TRACKING.getFingerPrintId().then((fpid) => {
          obj.sessionId = fpid
          if (isEcomSite) {
            utils.localStorage().set('antiFraud', fpid)
          } else {
            utils.localStorage().set('antiFraud', JSON.stringify(obj))
          }
        })
      } else {
        if (isEcomSite) {
          utils.localStorage().set('antiFraud', params.MercSessId)
        } else {
          utils.localStorage().set('antiFraud', JSON.stringify(obj))
        }
      }
    }
  })

  client.autoLoadEvents()
}

// March 25 2022 - remove loading this iframe from Eduardo's request //
// function _initIframe(params) {
//   try {
//     const iframe = document.createElement('iframe')
//     iframe.src = `https://additional.tryemanagecrm.com/Home/NewOffer?m=${params.MerchantId}&s=${params.MercSessId}`
//     iframe.setAttribute('frameborder', 0)
//     iframe.setAttribute('scrolling', 'no')
//     iframe.style = 'width: 1px; height: 1px'
//     document.body.appendChild(iframe)
//   } catch (err) {
//     console.log('Can not init Kount')
//   }
// }

function loadKount() {
  // add class "kaxsdc" and data-event="load" to body tag
  document.body.classList.add('kaxsdc')
  document.body.setAttribute('data-event', 'load')

  // load script
  // ClientID default: 700000
  const clientId = window.__CTRWOW_CONFIG.kountId ? window.__CTRWOW_CONFIG.kountId : '700000'
  const script = document.createElement('script')
  script.src = `https://ssl.kaptcha.com/collect/sdk?m=${clientId}`
  script.defer = true
  script.onload = _kountScriptLoaded
  document.body.appendChild(script)
}

export default function loadKountWrapper() {
  // recommit
  const myTimeout = setTimeout(loadKount, window.ctrDevDebugger__UtilsTesting__delayTimeDependencies || 1000 * 5)
  try {
    ['touchstart', 'click', 'scroll'].forEach(function (eventName) {
      window.addEventListener(
        eventName,
        function () {
          const { ctrwowUtils: utils } = window
          if (!utils.localStorage().get('antiFraud')) {
            clearTimeout(myTimeout)
            loadKount()
          }
        },
        {
          once: true
        }
      )
    })
  } catch (e) {
    console.log(e)
  }
}
