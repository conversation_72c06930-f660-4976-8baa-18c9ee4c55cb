import { getPageSettingsAsync } from 'shared-checkout-flow/src/getPageSettings'
import clearLocalStorage from './clearLocalStorage'
import * as mainOrderInfo from './order/mainOrderInfo'
import loadKount from './loadKount'
import loadStripeRadar from './loadStripeRadar'
import { initDFOLib } from 'shared-trackings/src/fireEverFlow'
import getEmanageCRMJS from 'shared-checkout-flow/src/getEmanageCRMJS'

if (!window.__ctrCheckoutFlow) {
  window.__ctrCheckoutFlow = {
    isReady: false
  }
  window.__ctrCheckoutFlow.processing = new Promise((resolve) => (window.__ctrCheckoutFlow.resolve = resolve))

  window.__ctrCheckoutFlow.processing.then(() => {
    console.log('CtrCheckout get ready !!!')
    window.__ctrCheckoutFlow.isReady = true

    if (window.ctrwowUtils.localStorage().get('orderOnePageFlag') === 'true') {
      console.log('order not clear cache!')
    } else {
      clearLocalStorage()
    }

    mainOrderInfo.initData()
    loadKount()

    if (window.ctrwowUtils.handleParam.getQueryParameter('isstriperadar') === '1') {
      loadStripeRadar()
    }

    try {
      initDFOLib(null, true)
    } catch (e) {
      console.log('load dfo lib fail ')
    }
  })

  getEmanageCRMJS()
    // make sure pageSettings have value
    .then(getPageSettingsAsync)
    .then(() => {
      console.log('start to int [EmanageCRMJS] instance')
      window.__ctrCheckoutFlow.resolve(true)
    })
    .catch((e) => {
      console.error('Cannot create ctrwow instance')
      console.log(e)
    })
} else {
  console.log('WARNING: Tried to load [ctrwowCheckout] more than once.')
}
