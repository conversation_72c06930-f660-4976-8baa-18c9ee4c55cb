function _stripeRadarScriptLoaded() {
  try {
    // const publishableKey = 'pk_test_ynLC2aXSWUMqaQsQNfiCrL0Z'
    // const stripeAccount = 'acct_19e9QTIqcrfj6K9e'
    let stripeAccount = 'acct_1MYvx9H45UqwdnbL'
    let publishableKey = 'pk_live_51LZjI2HY4pTWX9Mn2RyLVUjCkl0a3jnbvRJ6GNy0i04qY1FHwtWsaCQQJLzdHqtzL3siRZJlmSFNE5UsKdgxdrS900qmFx5ZtZ'
    if (window.ctrwowUtils.handleParam.getQueryParameter('iscardtest') === '1') {
      // test account
      stripeAccount = 'acct_19e9QTIqcrfj6K9e'
      publishableKey = 'pk_test_ynLC2aXSWUMqaQsQNfiCrL0Z'
    }

    if (!publishableKey) return

    let stripeAccountJson = {}
    if (stripeAccount) {
      stripeAccountJson = { stripeAccount: stripeAccount }
    }

    // eslint-disable-next-line no-undef
    const stripe = Stripe(publishableKey, stripeAccountJson)

    stripe.createRadarSession().then(function (result) {
      console.log(result)
      const { ctrwowUtils: utils } = window
      utils.localStorage().set('stripeRadar', JSON.stringify(result))
    })
  } catch (error) {
    console.log('createRadarSession error:' + error)
  }
}

function loadStripeRadar() {
  const script = document.createElement('script')
  script.src = 'https://js.stripe.com/v3/'
  script.defer = true
  script.onload = _stripeRadarScriptLoaded
  document.body.appendChild(script)
}

export default function loadStripeRadarWrapper() {
  // recommit
  const myTimeout = setTimeout(loadStripeRadar, window.ctrDevDebugger__UtilsTesting__delayTimeDependencies || 1000 * 5)
  try {
    ['touchstart', 'click', 'scroll'].forEach(function (eventName) {
      window.addEventListener(
        eventName,
        function () {
          const { ctrwowUtils: utils } = window
          if (!utils.localStorage().get('stripeRadar')) {
            clearTimeout(myTimeout)
            loadStripeRadar()
          }
        },
        {
          once: true
        }
      )
    })
  } catch (e) {
    console.log(e)
  }
}
