import getEmanageCRMJS from 'shared-checkout-flow/src/getEmanageCRMJS'
export { populateSuccessInfoByOrder } from './success.page'

if (!window.__ctrSuccessPayment) {
  window.__ctrSuccessPayment = {
    isReady: false
  }
  window.__ctrSuccessPayment.processing = new Promise((resolve) => (window.__ctrSuccessPayment.resolve = resolve))

  window.__ctrSuccessPayment.processing.then(() => {
    console.log('__ctrSuccessPayment get ready !!!')
    window.__ctrSuccessPayment.isReady = true
  })

  getEmanageCRMJS()
    .then(() => {
      console.log('start to int [EmanageCRMJS] instance')
      window.__ctrSuccessPayment.resolve(true)
    })
    .catch((e) => {
      console.error('cannot create ctrwow instance')
    })
}

export const ready = () => window.__ctrSuccessPayment.processing
