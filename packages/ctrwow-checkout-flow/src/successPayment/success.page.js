import { getOrderInfo, setOrderInfo } from 'shared-checkout-flow/src/orderInfo/orderInfo'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'
import { getPaymentName, PAYMENT_TYPE } from 'shared-checkout-flow/src/payment/paymentType.constants'
import { updateCurrentBLTrackingParam } from 'shared-trackings'
import { SOURCE_CONFIG } from 'shared-checkout-flow/src/configurable.constants'

// source from [https://d16hdrba6dusey.cloudfront.net/sitecommon/js/pages/success.page.js]
export function populateSuccessInfoByOrder() {
  const { ctrwowUtils } = window
  const checkoutConfig = getPageSettings()
  var siteSetting = {
    declineUrl: checkoutConfig.declineUrl,
    successUrl: checkoutConfig.confirmUrl,
    webKey: window.localStorage.getItem('webkey_to_check_paypal') ? window.localStorage.getItem('webkey_to_check_paypal') : '',
    CID: checkoutConfig.cid
  }

  if (localStorage.getItem('webkey_for_success_page') !== null) {
    siteSetting.webKey = localStorage.getItem('webkey_for_success_page')
  }

  const successPage = {
    orderInfo: getOrderInfo()
  }

  // case shopify
  if (window.ctrwowUtils.localStorage().get('SOURCE_CONFIG') === 'SHOPIFY') {
    siteSetting = {
      ...siteSetting,
      CID: window.ctrwowUtils.localStorage().get('XCID'), // recommit
      sourceConfig: {
        source: window.ctrwowUtils.localStorage().get('SOURCE_CONFIG')
      }
    }
    console.log(siteSetting)
  }
  const { sourceConfig: { source } = {} } = siteSetting
  const eCRM = new window.EmanageCRMJS({
    ...(SOURCE_CONFIG[source] || {}),
    webkey: siteSetting.webKey,
    cid: siteSetting.CID,
    lang: '',
    isTest: !!ctrwowUtils.link.getParameterByName('isCardTest')
  })
  if (window.ctrwowUtils.localStorage().get('SOURCE_CONFIG') !== 'SHOPIFY') {
    eCRM.Order.baseAPIEndpoint = window.ctrwowUtils.getSalesPciCRMBaseUrl(eCRM.Order)
  }

  const upsellIndex = successPage.orderInfo.upsellIndex

  let upsellRedirectUrl = ''
  try {
    upsellRedirectUrl = successPage.orderInfo.upsells[upsellIndex].upsellUrl
  } catch (err) {
    upsellRedirectUrl = siteSetting.successUrl
  }
  if (ctrwowUtils.localStorage().get('upsellListicleURL')) {
    upsellRedirectUrl = ctrwowUtils.localStorage().get('upsellListicleURL')
  }

  // // just use for stripe checkout
  // const stripe_redirect_status_param = ctrwowUtils.link.getQueryParameter('redirect_status')
  // if (stripe_redirect_status_param && stripe_redirect_status_param === 'failed') {
  //   location.href = updateCurrentBLTrackingParam(siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName())
  //   return
  // }

  const userPaymentType = localStorage.getItem('userPaymentType')
  const payment = {
    checkPaypalApprove: () => {
      // just use for stripe checkout
      const stripe_redirect_status_param = ctrwowUtils.link.getQueryParameter('redirect_status')
      if (stripe_redirect_status_param && stripe_redirect_status_param === 'failed') {
        location.href = updateCurrentBLTrackingParam(siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName())
        return
      }

      // check trackingNumber
      const trackingNumber = localStorage.trackingNumber ? localStorage.trackingNumber : null
      if (!trackingNumber) {
        console.log('Missing trackingNumber')
        return
      }
      var postData = {
        PaymentProcessorId: successPage.orderInfo.paymentProcessorId
      }
      // if (successPage.orderInfo.paymentProcessorId === 40) {
      //   postData = {
      //     paymentProcessorId: successPage.orderInfo.paymentProcessorId,
      //     orderNumber: successPage.orderInfo.orderNumber
      //   }
      // } else {
      //   postData = {
      //     trackingCountryCode: '',
      //     trackingLanguageCountryCode: '',
      //     paymentProcessorId: successPage.orderInfo.paymentProcessorId
      //   }
      // }

      // approve payment
      if (getPaymentName(postData.PaymentProcessorId) === PAYMENT_TYPE.IDEAL) {
        eCRM.Order.checkPaypalApprove(trackingNumber, postData, (result) => {
          if (result && result.success) {
            if (upsellRedirectUrl.lastIndexOf('/') >= 0) {
              // for upsell link

              const splitLink = upsellRedirectUrl.split('?')
              let redirectUrl = ''
              if (splitLink.length <= 1) {
                redirectUrl = upsellRedirectUrl.substring(
                  upsellRedirectUrl.lastIndexOf('/') + 1,
                  upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
                )
              } else {
                redirectUrl = splitLink[0].substring(splitLink[0].lastIndexOf('/') + 1, splitLink[0].length)
              }

              location.href = updateCurrentBLTrackingParam(redirectUrl + window.ctrwowUtils.link.getCustomPathName())
            } else {
              // redirect to confirm page
              location.href = updateCurrentBLTrackingParam(upsellRedirectUrl + window.ctrwowUtils.link.getCustomPathName())
            }
          } else {
            // remove gtm upsell purchas event
            localStorage.removeItem('fireUpsellForGTMPurchase')

            if (localStorage.getItem('isMainOrder') === 'upsell') {
              location.href = updateCurrentBLTrackingParam(siteSetting.successUrl + window.ctrwowUtils.link.getCustomPathName())
            } else {
              // redirect to decline page
              location.href = updateCurrentBLTrackingParam(siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName())
            }
          }
        })
      } else {
        eCRM.Order.checkPaypalApprove(trackingNumber, postData, (result) => {
          /* if (result && result.success) {
            if (upsellRedirectUrl.lastIndexOf('/') >= 0) {
              // for upsell link
              const redirectUrl = upsellRedirectUrl.substring(
                upsellRedirectUrl.lastIndexOf('/') + 1,
                upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
              )
              location.href = redirectUrl + window.ctrwowUtils.link.getCustomPathName()
            } else {
              // redirect to confirm page
              location.href = upsellRedirectUrl + window.ctrwowUtils.link.getCustomPathName()
            }
          } else {
            // remove gtm upsell purchas event
            localStorage.removeItem('fireUpsellForGTMPurchase')

            if (localStorage.getItem('isMainOrder') === 'upsell') {
              location.href = siteSetting.successUrl + window.ctrwowUtils.link.getCustomPathName()
            } else {
              // redirect to decline page
              location.href = siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName()
            }
          } */
          console.log(result)
        })
        setTimeout(() => {
          if (upsellRedirectUrl.lastIndexOf('/') >= 0) {
            // for upsell link
            // let redirectUrl = upsellRedirectUrl.substring(
            //   upsellRedirectUrl.lastIndexOf('/') + 1,
            //   upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
            // )

            const splitLink = upsellRedirectUrl.split('?')
            let redirectUrl = ''
            if (splitLink.length <= 1) {
              redirectUrl = upsellRedirectUrl.substring(
                upsellRedirectUrl.lastIndexOf('/') + 1,
                upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
              )
            } else {
              redirectUrl = splitLink[0].substring(splitLink[0].lastIndexOf('/') + 1, splitLink[0].length)
            }

            redirectUrl = redirectUrl + window.ctrwowUtils.link.getCustomPathName()
            location.href = updateCurrentBLTrackingParam(redirectUrl)
          } else {
            // redirect to confirm page
            location.href = updateCurrentBLTrackingParam(upsellRedirectUrl + window.ctrwowUtils.link.getCustomPathName())
          }
        }, 1000)
      }
    }
  }

  const paypal = {
    getTransactionNumber: () => {
      if (window.ctrwowUtils.localStorage().get('SOURCE_CONFIG') === 'SHOPIFY') {
        return ctrwowUtils.handleParam.getQueryParameter('paymentId') || ctrwowUtils.handleParam.getQueryParameter('transactionCode') // paypal
      } else {
        return ctrwowUtils.link.getParameterByName('paymentId') || ctrwowUtils.link.getParameterByName('transactionCode') // paypal
      }
    },
    // getToken: () => {
    getToken: () => {
      // return ctrwowUtils.link.getParameterByName('token')
      return ctrwowUtils.handleParam.getQueryParameter('token')
    },
    checkPaypalApprove: () => {
      const postData = {
        trackingCountryCode: '',
        trackingLanguageCountryCode: '',
        paymentProcessorId: successPage.orderInfo.paymentProcessorId
      }

      let trackingNumber = ''
      // eslint-disable-next-line eqeqeq
      if (successPage.orderInfo.paymentProcessorId == '31') {
        trackingNumber = paypal.getToken()
      } else {
        trackingNumber = paypal.getTransactionNumber()
        if (!trackingNumber || trackingNumber === '') {
          trackingNumber = paypal.getToken()
        }
      }

      eCRM.Order.checkPaypalApprove(trackingNumber, postData, (result) => {
        function afterApprovePayment(result) {
          if (result && result.success) {
            // use firstname and lastname in upsell
            localStorage.setItem('user_firstname', result.address.firstName)
            localStorage.setItem('user_lastname', result.address.lastName)
  
            // eslint-disable-next-line eqeqeq
            const orderInfo = getOrderInfo()
            /*
            if (result.address.email) {
              if (!orderInfo.cusEmail) {
                orderInfo.cusEmailPP = result.address.email
              }
            }
            */
            if (
              window.ctrwowUtils.localStorage().get('SOURCE_CONFIG') === 'SHOPIFY' &&
              result.orderId &&
              window.ctrwowUtils.localStorage().get('isUpdateOrderNumber') !== 'true'
            ) {
              window.ctrwowUtils.localStorage().set('isUpdateOrderNumber', true)
              orderInfo.orderNumber = result.orderId
            }
            if (result.address) {
              if (result.address.email) {
                orderInfo.cusEmailPP = result.address.email || ''
              }
              if (result.address.firstName) {
                orderInfo.cusFirstName = result.address.firstName || ''
              }
              if (result.address.lastName) {
                orderInfo.cusLastName = result.address.lastName || ''
              }
              if (result.address.city) {
                orderInfo.cusCity = result.address.city || ''
              }
              if (result.address.state) {
                orderInfo.cusState = result.address.state || ''
              }
              if (result.address.country) {
                orderInfo.cusCountry = result.address.country || ''
              }
              if (result.address.zipCode) {
                orderInfo.cusZip = result.address.zipCode || ''
              }
            }
            setOrderInfo(orderInfo)
  
            // utils.fireMainOrderToGTMConversionV2();
  
            if (upsellRedirectUrl.lastIndexOf('/') >= 0) {
              // for upsell link
              // const redirectUrl = upsellRedirectUrl.substring(
              //   upsellRedirectUrl.lastIndexOf('/') + 1,
              //   upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
              // )
              const splitLink = upsellRedirectUrl.split('?')
              let redirectUrl = ''
              if (splitLink.length <= 1) {
                redirectUrl = upsellRedirectUrl.substring(
                  upsellRedirectUrl.lastIndexOf('/') + 1,
                  upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
                )
              } else {
                redirectUrl = splitLink[0].substring(splitLink[0].lastIndexOf('/') + 1, splitLink[0].length)
              }
  
              location.href = updateCurrentBLTrackingParam(redirectUrl + window.ctrwowUtils.link.getCustomPathName())
            } else {
              // redirect to confirm page
              location.href = updateCurrentBLTrackingParam(upsellRedirectUrl + window.ctrwowUtils.link.getCustomPathName())
            }
          } else {
            // remove gtm upsell purchas event
            localStorage.removeItem('fireUpsellForGTMPurchase')
  
            localStorage.setItem('userPaymentType', 'paypal')
            if (localStorage.getItem('paypal_isMainOrder') === 'upsell') {
              location.href = updateCurrentBLTrackingParam(siteSetting.successUrl + window.ctrwowUtils.link.getCustomPathName())
            } else {
              // write log to firebase
              // try {
              //     const d = new Date();
              //     const loggingInfo = JSON.parse(utils.localStorage().get('loggingInfo'));
              //     const opts = {
              //         method: 'POST',
              //         headers: {
              //             'Content-Type': 'application/json'
              //         },
              //         body: JSON.stringify({
              //             time: d.toISOString() + ' - ' + d.toTimeString(),
              //             url: location.href,
              //             response: result,
              //             device: navigator.userAgent,
              //             orderNumber: loggingInfo.orderNumber,
              //             trackingNumber: loggingInfo.trackingNumber,
              //             callBackUrl: loggingInfo.callBackUrl
              //         })
              //     }
              //     fetch('https://log-success-page.firebaseio.com/ajaxfail.json', opts);
              // } catch(err) {
              //     console.log('error logging');
              // }
  
              // redirect to decline page
              location.href = updateCurrentBLTrackingParam(siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName())
            }
          }
        }

        window.ctrwowUtils.events.emit("triggerAfterApprovePayment")
        if (window.triggerAfterApprovePayment !== undefined) {
          const isFinished = setInterval(() => {
            if (window.loaderDone === true) {
              clearInterval(isFinished);
              afterApprovePayment(result)
            }
          }, 100); 
        } else {
          afterApprovePayment(result)
        }
      })
    }
  }

  const creditcard_fiserv = {
    checkFiservApprove: () => {
      const { __ctrPageConfiguration } = window
      const orderInfo = ctrwowUtils.localStorage().get('orderInfo') !== null ? JSON.parse(ctrwowUtils.localStorage().get('orderInfo')) : null
      const isTest = !!(orderInfo && orderInfo.orderParams && orderInfo.orderParams.toLowerCase().indexOf('iscardtest') > -1)
      // url return https://publish.ctrwow.com/5fa22d1e835ba6183c5abb39/success.html?iscardtest=1?ipgtransaction_id=84595454084&code=00&message=Function%20performed%20error-free&approval_code=Y:129279:4595454084:PPX%20:211110690686&transaction_status=APPROVED&exception=&transaction_id=379728b9-ec8d-45e6-9f89-9694e2610bce
      // check orderInfo.orderParams => append param to url
      const orderParams = orderInfo && orderInfo.orderParams
      const newUrl = window.location.href + '&' + orderParams
      window.history.pushState({ path: newUrl }, '', newUrl)

      const __transactionStatus = ctrwowUtils.link.getParameterByName('transaction_status').toLowerCase()
      if (__transactionStatus === 'approved') {
        const __transactionId = ctrwowUtils.link.getParameterByName('transaction_id')
        const __apiFiservApprove = `${__ctrPageConfiguration.crmEndpoint}/threeds/${__transactionId}/69${isTest === true ? '?isTest=true' : ''}`
        const headers = {
          'content-type': 'application/json'
        }
        if (__ctrPageConfiguration && __ctrPageConfiguration.cid) {
          headers.X_CID = __ctrPageConfiguration.cid
        }
        ctrwowUtils
          .callAjax(__apiFiservApprove, {
            method: 'PUT',
            headers: headers
          })
          .then((result) => {
            window.ctrwowUtils.events.emit("triggerAfterApprovePayment");
            // data not return data
            // redirect to upsell page
            if (upsellRedirectUrl.lastIndexOf('/') >= 0) {
              // for upsell link
              // const redirectUrl = upsellRedirectUrl.substring(
              //   upsellRedirectUrl.lastIndexOf('/') + 1,
              //   upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
              // )
              const splitLink = upsellRedirectUrl.split('?')
              let redirectUrl = ''
              if (splitLink.length <= 1) {
                redirectUrl = upsellRedirectUrl.substring(
                  upsellRedirectUrl.lastIndexOf('/') + 1,
                  upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
                )
              } else {
                redirectUrl = splitLink[0].substring(splitLink[0].lastIndexOf('/') + 1, splitLink[0].length)
              }
              location.href = updateCurrentBLTrackingParam(redirectUrl + window.ctrwowUtils.link.getCustomPathName())
            } else {
              // redirect to confirm page
              location.href = updateCurrentBLTrackingParam(upsellRedirectUrl + window.ctrwowUtils.link.getCustomPathName())
            }
          })
          .catch(() => {
            if (localStorage.getItem('orderInfo') !== null) {
              // redirect to confirm page
              location.href = updateCurrentBLTrackingParam(siteSetting.successUrl + window.ctrwowUtils.link.getCustomPathName())
            } else {
              // redirect to decline page
              location.href = updateCurrentBLTrackingParam(siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName())
            }
          })
      }
    }
  }

  const klarna = {
    checkKlarnaApprove: () => {
      const { __ctrPageConfiguration } = window
      const ctr_checkout_url = ctrwowUtils.localStorage().get('ctr_checkout_url') && new URL(ctrwowUtils.localStorage().get('ctr_checkout_url'))
      const orderInfo = ctrwowUtils.localStorage().get('orderInfo') !== null ? JSON.parse(ctrwowUtils.localStorage().get('orderInfo')) : null
      const isTest = !!(orderInfo && orderInfo.orderParams && orderInfo.orderParams.toLowerCase().indexOf('iscardtest') > -1)
      const paymentContinueResult =
        sessionStorage.getItem('paymentContinueResult') !== null ? JSON.parse(sessionStorage.getItem('paymentContinueResult')) : null

      function redirectToError() {
        console.log('redirectToError======================')
        window.ctrwowUtils.localStorage().remove('orderOnePageFlag')
        window.sessionStorage.removeItem('paymentContinueResult')
        location.href = updateCurrentBLTrackingParam(siteSetting.declineUrl + window.ctrwowUtils.link.getCustomPathName())
      }

      const __transactionStatus = ctrwowUtils.link.getParameterByName('redirect_status').toLowerCase()
      switch (__transactionStatus) {
        case 'succeeded': {
          window.ctrwowUtils.localStorage().remove('orderOnePageFlag')
          window.sessionStorage.removeItem('paymentContinueResult')
          // call API Approve Klarna
          const __trackingNumber = ctrwowUtils.localStorage().get('trackingNumber')
          const __webKey = ctrwowUtils.localStorage().get('webkey_for_success_page')
          const __apiKlarnaApprove = `${__ctrPageConfiguration.crmEndpoint}/orders/${__webKey}?trackingNumber=${__trackingNumber}${
            isTest === true ? '&isTest=true' : ''
          }`
          const headers = {
            'content-type': 'application/json'
          }
          if (__ctrPageConfiguration && __ctrPageConfiguration.cid) {
            headers.X_CID = __ctrPageConfiguration.cid
          }
          ctrwowUtils
            .callAjax(__apiKlarnaApprove, {
              method: 'PUT',
              headers: headers
            })
            .then((result) => {
              window.ctrwowUtils.events.emit("triggerAfterApprovePayment");
              if (result.success === true) {
                if (upsellRedirectUrl.lastIndexOf('/') >= 0) {
                  // for upsell link
                  // const redirectUrl = upsellRedirectUrl.substring(
                  //   upsellRedirectUrl.lastIndexOf('/') + 1,
                  //   upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
                  // )
                  const splitLink = upsellRedirectUrl.split('?')
                  let redirectUrl = ''
                  if (splitLink.length <= 1) {
                    redirectUrl = upsellRedirectUrl.substring(
                      upsellRedirectUrl.lastIndexOf('/') + 1,
                      upsellRedirectUrl.indexOf('?') >= 0 ? upsellRedirectUrl.indexOf('?') : upsellRedirectUrl.length
                    )
                  } else {
                    redirectUrl = splitLink[0].substring(splitLink[0].lastIndexOf('/') + 1, splitLink[0].length)
                  }
                  location.href = updateCurrentBLTrackingParam(redirectUrl + window.ctrwowUtils.link.getCustomPathName())
                } else {
                  // redirect to confirm page
                  location.href = updateCurrentBLTrackingParam(upsellRedirectUrl + window.ctrwowUtils.link.getCustomPathName())
                }
              } else {
                redirectToError()
              }
            })
            .catch(() => {
              redirectToError()
            })
          break
        }
        case 'canceled': {
          //  redirect to checkout page => The FE can retry it along with the other options.
          if (paymentContinueResult === null || paymentContinueResult.length === 0) {
            redirectToError()
          } else {
            window.ctrwowUtils.localStorage().set('orderOnePageFlag', true)
            location.href = updateCurrentBLTrackingParam(
              ctr_checkout_url.origin + ctr_checkout_url.pathname + window.location.search + window.ctrwowUtils.link.getCustomPathName()
            )
          }
          break
        }
        case 'failed': {
          // redirect to checkout page => the FE should present the other options, like Buy now, pay later, Monthly financing, and discard current option
          if (paymentContinueResult === null || paymentContinueResult.length === 0) {
            redirectToError()
          } else {
            window.ctrwowUtils.localStorage().set('orderOnePageFlag', true)
            location.href = updateCurrentBLTrackingParam(
              ctr_checkout_url.origin + ctr_checkout_url.pathname + window.location.search + window.ctrwowUtils.link.getCustomPathName()
            )
          }
          break
        }
      }
    }
  }

  switch (userPaymentType) {
    case 'stripe':
    case 'ideal':
    case 'sofort': {
      // case ideal - sofort
      payment.checkPaypalApprove()
      break
    }
    case 'creditcard': {
      // case creditcard fiserv
      creditcard_fiserv.checkFiservApprove()
      break
    }
    case 'klarna': {
      // case klarna
      klarna.checkKlarnaApprove()
      break
    }
    default: {
      // case paypal
      paypal.checkPaypalApprove()
      break
    }
  }
  // if (userPaymentType === 'stripe' || userPaymentType === 'ideal' || userPaymentType === 'sofort') {
  //   payment.checkPaypalApprove()
  // } else if( userPaymentType === 'creditcard') {
  //   creditcard_fiserv.checkFiservApprove()
  // } else {
  //   paypal.checkPaypalApprove()
  // }
}
