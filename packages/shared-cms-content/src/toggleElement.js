export const hideElm = (elm, isShowed) => {
  if (!elm || !elm.style) {
    return
  }
  if (isShowed) {
    elm.style.display = getDisplayBkVal()
    setTimeout(() => {
      if (window.getComputedStyle(elm).getPropertyValue('display') === 'none') {
        elm.style.display = 'block'
      }
    }, 10)
  } else {
    setDisplayBkVal()
    elm.style.display = 'none'
  }

  function setDisplayBkVal() {
    elm.setAttribute('data-bk-ctr-display', elm.style.display || '')
  }

  function getDisplayBkVal() {
    return elm.getAttribute('data-bk-ctr-display') || ''
  }
}
