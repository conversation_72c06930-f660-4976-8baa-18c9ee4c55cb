export const getDetailsEndpoint = () => window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.contentDetailsEndpoint
export const getContentUrl = (details) => `${details.schemaName}/${details.data.slug}.html`

export const parseContentData = (details) => ({
  // publishedDate: new Date().toDateString(),
  // authorName: 'CTRwow',
  ...details,
  contentUrl: getContentUrl(details || {}),
  ...details.data,
  publishedDate: new Date(details.publishedOn || details.updatedOn).toDateString(),
  updatedOn: new Date(details.publishedOn || details.updatedOn).toDateString()
})

export const hasField = (details, fieldName) => typeof details === 'object' && fieldName in details

export const getValue = (details, fieldName) => (typeof details === 'object' ? details[fieldName] : undefined)

export const saveDetailToLocalStore = (details) => {
  try {
    window[`ctrCmsContentDetails_${getDetailsEndpoint()}`] = parseContentData(details)
  } catch (e) {
    console.log(e)
  }
}

export const getDetailFromLocalStore = () => {
  try {
    return window[`ctrCmsContentDetails_${getDetailsEndpoint()}`]
  } catch (e) {
    console.log(e)
  }
}
