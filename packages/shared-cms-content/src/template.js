// import {getTemplateGroupName, setTemplateGroupName} from 'packages/widget-cms-content-details/src/prepareTemplateData'

const MAIN_TEMPLATE_NAME_ATTR = 'data-main-template-name'
export const SUB_TEMPLATE_NAME_ATTR__LIST = 'data-template-name-list'

export const getTemplateGroupName = (element) => element.getAttribute(MAIN_TEMPLATE_NAME_ATTR)
export const setTemplateGroupName = (element) => element.setAttribute(MAIN_TEMPLATE_NAME_ATTR, `templateGroupName__${element.id || Date.now()}`)

export const addTemplateContainer = (templateName, template, mainElement) => {
  const templateGroupName = getTemplateGroupName(mainElement)
  let container = document.querySelector(`.${templateGroupName}`)

  if (!container) {
    container = document.createElement('div')
    container.style.position = 'fixed'
    container.style.bottom = '200vh'
    container.classList.add(templateGroupName)
    document.body.appendChild(container)
  }

  container.insertAdjacentHTML('beforeend', `<div ${templateName || ''} >${template.replaceAll(' id=', ' data-node-identify=')}</div>`)
}

export const addCssRule = (node) => {
  const elms = node.querySelectorAll('[id]')
  const ids = []
  node.id && ids.push(node.id)
  elms.forEach((elm) => ids.push(elm.id) && elm.setAttribute('data-node-identify', elm.id))

  if (!ids.length) {
    return
  }

  const styleEl = document.createElement('style')
  styleEl.setAttribute('name', [node.id, node.className].join('-'))
  document.head.appendChild(styleEl)
  const styleSheetForTemplateNode = styleEl.sheet

  const styleSheetsList = [...document.styleSheets]
  styleSheetsList.forEach((styleSheet) => {
    try {
      const rules = [...styleSheet.cssRules]
      const replaceIdsWithNodeIdentify = (cssText) => ids.reduce((acc, id) => acc.replaceAll(`#${id}`, `[data-node-identify=${id}]`), cssText)
      let index = 0
      rules.forEach((rule) => {
        const cssText = rule.cssText
        const replaceId = ids.find((id) => cssText.includes(`#${id}`))
        replaceId && styleSheetForTemplateNode.insertRule(replaceIdsWithNodeIdentify(cssText), index++)
      })
    } catch (e) {
      console.log('Access to stylesheet %s is denied. Ignoring...', styleSheet.href)
    }
  })
}

export const prepareListTemplateItem = (element, isAddCssRule, mainElement, setAttributeToNode) => {
  const templateListItems = element.querySelectorAll('.js-list-template-item')

  templateListItems.forEach((node, index) => {
    isAddCssRule && addCssRule(node)

    let nodeHtml = node.outerHTML
    let placeholderByFieldName = ''

    nodeHtml = nodeHtml.replaceAll(/{list::[a-z0-9]+}/g, (matched) => {
      placeholderByFieldName = matched.replace('list::', '')
      return placeholderByFieldName
    })

    if (placeholderByFieldName.length < 2) {
      return
    }

    const templateName = `${placeholderByFieldName}__${index}`

    addTemplateContainer(`${SUB_TEMPLATE_NAME_ATTR__LIST}=${templateName}`, nodeHtml, mainElement || element)
    setAttributeToNode ? setAttributeToNode(node, templateName) : node.setAttribute(SUB_TEMPLATE_NAME_ATTR__LIST, templateName)
  })
}
