import { getValue, hasField, parseContentData } from './contentDetails.domainData'
import { getTemplateGroupName, SUB_TEMPLATE_NAME_ATTR__LIST } from './template'
import { replaceTemplateItemByKeys } from 'shared-formatter/src/replaceTemplateItemByKeys'
import { updateTemplateSourceValue } from 'shared-formatter/src/imageSourceFormatters'
import { populateDataToNodeContent } from 'shared-formatter/src/populatePlaceholderTemplate'

export const populateContentDetailsToElm = (mainElement, itemDetails) => {
  // 1.replace src of img-element
  itemDetails = parseContentData(itemDetails)

  // populate image src
  mainElement.querySelectorAll('img[src-template]').forEach((imgElm) => {
    imgElm.outerHTML = replaceTemplateItemByKeys(itemDetails)(imgElm.outerHTML)
  })
  updateTemplateSourceValue(mainElement)

  populateDataToNodeContent({
    fnGetNewTextContent: (textContent) => {
      let newContent = textContent
      const keys = Object.keys(itemDetails) || []
      keys.forEach((key) => {
        try {
          const newContentSplit = newContent.replace('{', '').replace('}', '').split('__')
          if (newContentSplit.length === 3 && newContentSplit[0] === key) {
            const key1 = newContentSplit[0]
            const index = parseInt(newContentSplit[1])
            const key2 = newContentSplit[2]
            const value = itemDetails[key1]?.[index]?.[key2] || ''
            newContent = newContent.replaceAll(newContent, value || '')
            // newContent = newContent.replaceAll(`${encodeURIComponent('{')}${key}${encodeURIComponent('}')}`, value || '')
          } else {
            const value = itemDetails[key]
            newContent = newContent.replaceAll(`{${key}}`, value || '')
            newContent = newContent.replaceAll(`${encodeURIComponent('{')}${key}${encodeURIComponent('}')}`, value || '')
          }
        } catch (error) {
          console.log('parse error:' + newContent)
        }
      })
      return newContent
    },
    attributes: ['title', 'href'],
    parentWrapper: mainElement
  })

  // 2. replace template [js-list-item] - element which has class [js-list*]
  const templateGroupNode = document.querySelector(`.${getTemplateGroupName(mainElement)}`)
  // populate placeholder with list patter: {list::{fieldName}}
  templateGroupNode &&
    templateGroupNode.querySelectorAll &&
    templateGroupNode.querySelectorAll(`[${SUB_TEMPLATE_NAME_ATTR__LIST}]`).forEach((item) => {
      const placeholderByFieldName = item.getAttribute(SUB_TEMPLATE_NAME_ATTR__LIST)
      const elementInMainContent = mainElement.querySelector(`[${SUB_TEMPLATE_NAME_ATTR__LIST}='${placeholderByFieldName}']`)

      if (!placeholderByFieldName || placeholderByFieldName.length < 2 || !elementInMainContent) {
        return
      }

      // generate node-list based on data
      const itemNodeTemplate = item.innerHTML
      const fieldNamePlaceholder = placeholderByFieldName.split('__')[0]
      const fieldName = fieldNamePlaceholder.substring(1, fieldNamePlaceholder.length - 1)
      if (hasField(itemDetails, fieldName)) {
        const fieldValues = getValue(itemDetails, fieldName) || []
        const renderingString = fieldValues.map((fieldValue) => itemNodeTemplate.replaceAll(fieldNamePlaceholder, fieldValue))
        // result = result.replaceAll(getPlaceholderNameForArrayList(placeholderByFieldName), renderingString.join(''))
        elementInMainContent.insertAdjacentHTML('beforebegin', renderingString.join(''))
      }
      elementInMainContent.style.display = 'none'
    })

  // 3. replace other values
  populateDataToNodeContent({
    fnGetNewTextContent: (textContent) => {
      console.log(textContent)
      return replaceTemplateItemByKeys(itemDetails)(textContent)
    },
    parentWrapper: mainElement
  })
}
