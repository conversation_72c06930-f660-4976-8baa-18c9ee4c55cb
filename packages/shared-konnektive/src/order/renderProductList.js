import { populateDataToNodeContent } from '../../../shared-formatter/src/populatePlaceholderTemplate'
import { populateData } from './populateData'
import { getDescendantProp, getShippingFee } from '../helper/'

export function renderProductList(data, list, fieldObj) {
  for (let i = 0, il = list.items.length; i < il; i++) {
    const productId = Number(list.items[i].dataset.id)
    const product = data.find((item) => item.productId === productId)

    Object.keys(fieldObj).forEach((key) => {
      const value = fieldObj[key]
      const itemEl = list.items[i]
      const fields = itemEl.querySelectorAll(value)
      Array.prototype.slice.call(fields).forEach((field) => {
        if (product) {
          field.classList.remove('loading')
          let textContent = ''
          textContent = getDescendantProp(product, key)
          if (key === 'couponText') {
            if (textContent) {
              field.parentNode.classList.remove('hidden')
            }
            if (!textContent) {
              textContent = field.textContent
            }
          }
          if (key === 'shippingFee') {
            textContent = getShippingFee(product)
          }

          field.textContent = textContent
          populateDataToNodeContent({
            fnGetNewTextContent: (textContent) => populateData(textContent, product),
            parentWrapper: itemEl
          })
        } else {
          if (key !== 'couponText') {
            field.textContent = ''
          }
          field.classList.add('loading')
        }
      })
    })
  }
}
