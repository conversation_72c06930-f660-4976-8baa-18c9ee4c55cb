import { getVisibleElms } from 'shared-common/src/getVisibleElms'

export function getCurrentIndexPidInPackage(list, className) {
  const getVisibleElmBaseOnClassName = getVisibleElms(list.element.querySelectorAll(className))
  const currentSelectedPid = Number(getVisibleElmBaseOnClassName[0].dataset.id)
  const currentPackage = list.element
    .querySelector('.sale-type-item.active')
    .getAttribute('package')
    .split(',')
    .map((pid) => Number(pid))
  return currentPackage.indexOf(currentSelectedPid)
}
