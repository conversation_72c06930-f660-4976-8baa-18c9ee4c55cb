export function getSelectedProduct() {
  return window.upsells[window.upsell_productindex || 0]
}
export function getUpsellData(orderInfo) {
  const product = getSelectedProduct()
  const data = {
    chosenProducts: [
      {
        type: 'upsell',
        campaignId: product.campaignId,
        productId: product.productId,
        sku: product.sku,
        price: product.productPrices.DiscountedPrice.Value,
        currency: product.productPrices.DiscountedPrice.GlobalCurrencyCode,
        quantity: product && product.quantity,
        is_upsell: true
      }
    ]
  }
  return data
}
