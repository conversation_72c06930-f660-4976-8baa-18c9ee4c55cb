import { postAjax } from '../helper/ajax'
import { WEB_SERVICE_URL } from 'shared-checkout-flow/src/configurable.constants'

export function detectRedirectConfirmPage() {
  console.log('Detect Redirect Confirm Page!')
}
export function confirmOrder() {
  const orderInfo = JSON.parse(window.localStorage.getItem('orderInfo') || '{}')
  const url = `${WEB_SERVICE_URL}/konnektive/order/confirm?orderId=${orderInfo.orderNumber}`
  postAjax(url)
    .then((result) => {
      window.localStorage.removeItem('ctr__funnel_savedinfo')
    })
    .catch((e) => console.log(e))
}
