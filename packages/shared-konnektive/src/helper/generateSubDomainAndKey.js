export function generateSubDomainAndKey(Builder, model) {
  try {
    const config = Builder.getConfig().data.settings
    console.log('----------config: ', config);
    model.addAttributes({
      konnektiveUserName: config.konnektive.loginId || 'apiwow',
      konnektivePassword: config.konnektive.password || 'ctrwow'
      // konnektiveUserName: config.konnektiveUserName || 'apiwow',
      // konnektivePassword: config.konnektivePassword || 'ctrwow'
    })
  } catch (e) {
    alert('Please Setup Sub Domain, konnektive API Key!')
    console.log(e)
  }
}
