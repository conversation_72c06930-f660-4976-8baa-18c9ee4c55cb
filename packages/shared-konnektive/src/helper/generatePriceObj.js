import { generateCurrencyNumber } from './generateCurrencyNumber'

export function generatePriceObj(price, fe_quantity) {
  const discountedPrice = Number(price.price)
  const fullPrice = Number(price.msrp)
  const savePrice = Number((fullPrice - discountedPrice).toFixed(2))
  const unitPrice = Number((discountedPrice / fe_quantity).toFixed(2))

  return {
    productPrices: {
      DiscountedPrice: {
        FormattedValue: generateCurrencyNumber(discountedPrice),
        Value: discountedPrice,
        GlobalCurrencyCode: window.currencyCode
      },
      SavePrice: {
        FormattedValue: generateCurrencyNumber(savePrice),
        Value: savePrice,
        GlobalCurrencyCode: window.currencyCode
      },
      FullRetailPrice: {
        FormattedValue: generateCurrencyNumber(fullPrice),
        Value: fullPrice,
        GlobalCurrencyCode: window.currencyCode
      },
      UnitDiscountRate: {
        FormattedValue: generateCurrencyNumber(unitPrice),
        Value: unitPrice,
        GlobalCurrencyCode: window.currencyCode
      }
    }
  }
}
