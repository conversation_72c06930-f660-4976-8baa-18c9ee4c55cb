import { removeParameter } from './param'

export function canGoNextUpsellUrl(isSuccess) {
  const orderInfo = window.localStorage.getItem('orderInfo') ? JSON.parse(window.localStorage.getItem('orderInfo')) : ''
  if (!orderInfo) return false

  const upsellIndex = orderInfo.upsellIndex

  // If No Upsell
  if (!orderInfo.upsells) return false
  // If fail at Order page
  if (!isSuccess && upsellIndex === 0) return false
  // If last upsell
  if (upsellIndex >= orderInfo.upsells.length) return false

  if (orderInfo.upsells.length > upsellIndex) return true
}

export function redirectNextPage(isSuccess, upsellName) {
  const orderInfo = window.localStorage.getItem('orderInfo') ? JSON.parse(window.localStorage.getItem('orderInfo')) : ''
  if (!orderInfo) {
    console.log('No data to handle redirect page !')
    return false
  }

  let upParam = ''
  if (upsellName) {
    upParam = '?up_' + upsellName

    if (isSuccess) {
      upParam += '=1'
    } else {
      upParam += '=0'
    }
  }

  const newUrl = removeParameter([
    'errorFound',
    'responseCode',
    'customerId',
    'orderId',
    'orderTotal',
    'orderSalesTaxPercent',
    'orderSalesTaxAmount',
    'test',
    'gatewayId',
    'prepaid_match',
    'gatewayCustomerService',
    'gatewayDescriptor',
    'ACS',
    'alt_pay_method',
    'subscription_id',
    'line_items'
  ])
  const currentParams = newUrl.split('?')[1] || ''
  const confirmOrDeclineUrl = isSuccess ? orderInfo.confirmUrl : orderInfo.declineUrl
  const isCanNext = canGoNextUpsellUrl(isSuccess)
  const upsellIndex = orderInfo.upsellIndex

  let nextUrl = ''
  if (isCanNext) {
    nextUrl = orderInfo.upsells[upsellIndex].upsellUrl.split('?')[0]
  } else {
    nextUrl = confirmOrDeclineUrl
  }

  let params = upParam
  if (params) {
    if (currentParams) {
      params += '&' + currentParams
    }
  } else if (currentParams) {
    params = '?' + currentParams
  }

  window.location.href = nextUrl + params
}
