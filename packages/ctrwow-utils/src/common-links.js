import { isPreviewMode } from 'shared-external-libs-loader/src/viewMode'

function convertHrefToUrlObject(href) {
  let url = ''

  try {
    url = new URL(href)
    // Fix for IE 11
    if (url && url.origin === '//') {
      url = ''
    }
  } catch (error) {
    url = ''
    // console.log('the href is invalid', href)
  }
  return url
}

function extractQueryString(url) {
  const trackingParam = ''
  if (!url) {
    url = location
  }
  if (typeof url === 'string') {
    url = convertHrefToUrlObject(url)
  }

  if (!url.search) return trackingParam

  const params = new URLSearchParams(getCustomPathName())
  var isSpecialCharacter = getCustomPathName().indexOf('#') > -1

  if (!params.toString()) return trackingParam

  const queryString = []

  for (const param of params) {
    const paramName = param[0]
    const paramValue = isSpecialCharacter ? decodeURIComponent(param[1]) : encodeURIComponent(param[1])
    queryString.push([paramName, paramValue].join('='))
  }

  if (!queryString.length) return trackingParam
  if (!trackingParam) return queryString.join('&')
  return queryString.join('&') + '&' + trackingParam
}

function removeDuplicateParams(href) {
  const url = convertHrefToUrlObject(href)
  if (!url) return href
  const params = url.search
  const queryString = params.substr(1)
  const arrayParams = queryString.split('&')
  if (arrayParams.length === 0) return url
  const keys = []
  const newParams = []
  // override the previous params if they existen
  arrayParams.reverse().forEach(function (param) {
    const key = param.split('=')[0]
    if (!keys.includes(key)) {
      keys.push(key)
      newParams.push(param)
    }
  })
  return url.origin + url.pathname + '?' + newParams.reverse().join('&')
}

function removeDuplicateParamsInHref(href) {
  const strParams = href.split('?').length > 1 ? href.split("?")[1] : ''
  if (strParams !== '') {
    const arrayParams = strParams.split('&')
    if (arrayParams.length <= 1) return href
    const keys = []
    const newParams = []
    arrayParams.reverse().forEach(function (param) {
      const key = param.split('=')[0]
      if (!keys.includes(key)) {
        keys.push(key)
        newParams.push(param)
      }
    })
    return href.split('?')[0] + '?' + newParams.reverse().join('&')
  }
  return href
}

function getJoinString(href) {
  const url = convertHrefToUrlObject(href)
  if (!url) return '?'
  // Check exist query string from url
  const joinString = url.search === '' ? '?' : '&'
  return joinString
}

function getFinalLink(href, queryString) {
  if (!queryString) return href
  // const joinString = getJoinString(href)

  let joinString = getJoinString(href)

  if (href.indexOf('?') > -1) {
    joinString = '&'
  }

  const result = removeDuplicateParams(href + joinString + queryString)
  return result
}

function updateURLPath(link, path) {
  var url = new URL(link)
  try {
    const url2 = new URL(path)
    url.hostname = url2.hostname
    url.pathname = url2.pathname
    return url.href
  } catch (error) {
    const patt = /[^\s/][\w-]+\.html/g // Validate .html file
    if (path.match(patt)) {
      url.pathname = url.pathname.replace(patt, path)
    }
    return url.href
  }
}

export function mergeQueryStringParams(href, queryString) {
  if (!queryString) return href
  let joinString = getJoinString(href)

  if (href.indexOf('?') > -1) {
    joinString = '&'
  }

  const result = removeDuplicateParams(href + joinString + queryString)
  return result
}

export function mergeWithCurrentQueryString(link) {
  const currentQueryString = extractQueryString()
  if (!link || !currentQueryString || link.startsWith('#') || link.startsWith('mailto') || link.startsWith('tel')) return link

  try {
    link = mergeQueryStringParams(link, currentQueryString)
  } catch (err) {
    console.log('Format link error: ', err)
  }

  return link
}

export function getParameterByName(name, url) {
  if (!url) url = isPreviewMode() ? window.parent.location.href : location.href
  url = url.toLowerCase()
  name = name.toLowerCase()
  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&]*)|&|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

export function queryURLParameter(name, url) {
  if (!url) url = isPreviewMode() ? window.parent.location.href : location.href
  // name = name.toLowerCase()
  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&]*)|&|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

export function queryURLParameterWithUpperCaseName(name, url) {
  if (!url) url = isPreviewMode() ? window.parent.location.href : location.href
  let result = null
  const locationSearch = url.split('?')
  if (locationSearch.length < 2) return null

  const params = locationSearch[1].split(/\?|\&/)
  params.forEach(function (it) {
    if (it) {
      const param = it.split('=')
      if (param[0].toLowerCase() === name.toLowerCase()) {
        result = param[1]
        // eslint-disable-next-line no-useless-return
        return
      }
    }
  })

  return result
}

export function populateParamToLinks() {
  const currentQueryString = extractQueryString()
  const $ = jQuery
  try {
    $('a').each(function () {
      const currentHref = $(this).attr('href')
      if (
        !currentHref ||
        !currentQueryString ||
        currentHref.startsWith('#') ||
        currentHref.startsWith('mailto') ||
        currentHref.startsWith('tel') ||
        currentHref.includes('javascript')
      ) {
        return
      }
      let newHref = getFinalLink(currentHref, currentQueryString)

      // CSB-9692: remove duplicate params
      newHref = removeDuplicateParamsInHref(newHref)
      $(this).attr('href', newHref)
    })

    const elmCtrwowBody = document.querySelector(".ctr_wow_body")
    elmCtrwowBody && elmCtrwowBody.classList.remove("disable-cta")
  } catch (err) {
    console.log('Format link error: ', err)
  }
}

export function populateTrackingParamToCtaLinks() {
  try {
    if (window.__CTR_FP_TRACKING_SETTINGS && window.__CTR_FP_TRACKING_SETTINGS.FP_TRACKING_CUSTOM_DATA) {
      const $ = jQuery
      const $a = $('a[call-to-action]')
      $a.each(function () {
        let currentHref = $(this).attr('href') || this.href || ''
        const linkType = $(this).attr('ctr-type-link')
        if (linkType === 'external') {
          const fpCustomData = JSON.parse(window.__CTR_FP_TRACKING_SETTINGS.FP_TRACKING_CUSTOM_DATA)
          const { siteId, pageId } = fpCustomData
          // const trackingConversionUrl = encodeURIComponent(location.href)
          const trackingConversionUrl = encodeURIComponent(`${location.origin}${location.pathname}`)
          const trackingQueryStr = `ctr_tracking__site_id=${siteId}&ctr_tracking__page_id=${pageId}&ctr_tracking__conversion_url=${trackingConversionUrl}`
          currentHref = mergeQueryStringParams(currentHref, trackingQueryStr)
          $(this).attr('href', currentHref)
        }
      })
    }
  } catch (err) {
    console.log('Format link error: ', err)
  }
}

export function populateNewURLPathToCtaLinks() {
  try {
    const PRESALE = 1
    const INTERSTITIAL = 5
    let name = ''
    if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.PAGE_TYPE === PRESALE) {
      name = 'pcta'
    }
    if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.PAGE_TYPE === INTERSTITIAL) {
      name = 'icta'
    }
    const path = getParameterByName(name)
    const $a = $('a[call-to-action]')
    $a.each(function () {
      const currentHref = $(this).prop('href') // Use prop to get full href instead of only relative path
      if (!path) return
      const newHref = updateURLPath(currentHref, path)
      $(this).attr('href', newHref)
    })
  } catch (error) {
    console.log('populateNewURLPathToCtaLinks error: ', error)
  }
}

function getTrackingParamValue(queryString) {
  const trackingParams = ['ctr_tracking__click_id', 'ctr_tracking__original_click_id', 'ctr_cssid', 'ctr_ppid', 'ctr_psid', 'ctr_ppu', 'ctr_io']
  try {
    const trackingValue = {}
    const params = new URLSearchParams(queryString)
    for (const param of params) {
      const paramName = param[0]
      const paramValue = param[1]
      if (trackingParams.includes(paramName)) {
        if (paramName === 'ctr_ppu') {
          trackingValue[paramName] = encodeURIComponent(decodeURIComponent(paramValue))
        } else {
          trackingValue[paramName] = paramValue
        }
      }
    }
    return trackingValue
  } catch (e) {}
  return {}
}

function populateTrackingParams(url, trackingValues) {
  try {
    let updateUrl = url
    Object.keys(trackingValues).forEach((trackingParamName) => {
      updateUrl = updateURLParameter(updateUrl, trackingParamName, trackingValues[trackingParamName])
    })
    return updateUrl
  } catch (e) {}
  return url
}

// Handle function redirect page
function redirectPage(page, target, isReturn) {
  const backupTrackingValues = getTrackingParamValue(page) || {}

  const currentQueryString = location.search.length > 0 ? window.ctrwowUtils.link.getCustomPathName().substr(1) : ''
  if (page.indexOf('?') > 0) {
    page += currentQueryString !== '' ? '&' + currentQueryString : ''
  } else {
    page += currentQueryString !== '' ? '?' + currentQueryString : ''
  }

  // eslint-disable-next-line eqeqeq
  const convertHref = new URL(page, window.location.href)
  let newUrl = mergeWithCurrentQueryString(convertHref.href)
  newUrl = populateTrackingParams(newUrl, backupTrackingValues)
  if (isReturn) {
    return newUrl
  }
  if (typeof target !== 'undefined' && target === '_blank') {
    window.open(newUrl)
  } else {
    location.href = newUrl
  }
  return false
}

function updateURLParameter(url, param, paramVal) {
  let newAdditionalURL = ''
  let tempArray = url.split('?')
  const baseURL = tempArray[0]
  const additionalURL = tempArray[1]
  let temp = ''
  if (additionalURL) {
    tempArray = additionalURL.split('&')
    for (let i = 0; i < tempArray.length; i++) {
      // eslint-disable-next-line eqeqeq
      if (tempArray[i].split('=')[0] != param) {
        newAdditionalURL += temp + tempArray[i]
        temp = '&'
      }
    }
  }

  const rows_txt = temp + '' + param + '=' + paramVal
  return baseURL + '?' + newAdditionalURL + rows_txt
}

function removeParamFromUrl(key, paramsStr) {
  let param
  let params_arr = []
  if (paramsStr !== '') {
    params_arr = paramsStr.split('&')
    for (let i = params_arr.length - 1; i >= 0; i--) {
      param = params_arr[i].split('=')[0]
      if (param === key) {
        params_arr.splice(i, 1)
      }
    }
  }

  return params_arr.join('&')
}

function getCustomPathName() {
  const regex = new RegExp('[?&]')
  const results = regex.exec(window.location.href)
  const newlocationSearch = results ? window.location.href.substring(results.index) : ''

  return newlocationSearch
}

export const queryParamToScrollElement = function () {
  const paramSrcollTo = queryURLParameter('scrollto')
  const selectorId = document.getElementById(paramSrcollTo)

  selectorId && selectorId.scrollIntoView()
}

export const commonActions = {
  getParameterByName,
  getQueryParameter: getParameterByName,
  queryURLParameter,
  mergeWithCurrentQueryString,
  mergeQueryStringParams,
  redirectPage,
  updateURLParameter,
  updateURLPath,
  removeParamFromUrl,
  getCustomPathName
}
