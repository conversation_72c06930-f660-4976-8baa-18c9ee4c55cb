import {
  commonActions,
  populateParamToLinks,
  populateTrackingParamToCtaLinks,
  populateNewURLPathToCtaLinks,
  queryParamToScrollElement
} from './common-links'
import { isPreviewMode, inIframe } from 'shared-external-libs-loader/src/viewMode'
import getDeviceType from 'shared-trackings/src/getDeviceType'
import CONFIGURABLE_CONSTANTS from './configurable.constants'

function validateTracking() {
  if ((window.__CTR_ENV && window.__CTR_ENV === 'CTR_APP') || isPreviewMode() || inIframe()) {
    throw new Error('Cannot track in ctr_wow app')
  }
  return true
}

// eslint-disable-next-line no-unused-vars
function create_UUID() {
  var dt = new Date().getTime()
  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (dt + Math.random() * 16) % 16 | 0
    dt = Math.floor(dt / 16)
    // eslint-disable-next-line eqeqeq
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
  return uuid
}

function funnelRouter() {
  try {
    validateTracking()
    const updateCtaLink = (link) => jQuery('a[call-to-action]').attr('href', link)

    const baseUrl = CONFIGURABLE_CONSTANTS.CTRWOW_FUNNEL_API_BASE_URL
    const params = window.ctrwowUtils.link
      .getCustomPathName()
      .substr(1)
      .toLowerCase()
      .split('&')
      .reduce(function (acc, item) {
        // returns first occurence and stops
        const parts = item.split('=')
        acc[parts[0]] = parts[1]
        return acc
      }, {})

    if (!params.ctr_fid || !params.ctr_fnid) {
      return
    }

    updateCtaLink(
      baseUrl + '/funnelPageAction' + window.ctrwowUtils.link.getCustomPathName() + '&ctr_action=1' + `&ctr_device_type=${getDeviceType()}`
    )
  } catch (e) {
    console.warn('funnelRouter error', e)
  }
}

export function populateAffIdParamToCtaLinks() {
  try {
    if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.AFFILIATE_ID) {
      const $ = jQuery
      const $a = $('a[call-to-action]')
      $a.each(function () {
        let currentHref = $(this).attr('href')
        currentHref = commonActions.updateURLParameter(currentHref, 'ctr_tracking__refid', window.__CTRWOW_CONFIG.AFFILIATE_ID)
        $(this).attr('href', currentHref)
      })
    }
  } catch (err) {
    console.log('populateAffIdParamToCtaLinks error: ', err)
  }
}

export function populateClickIdParamToCtaLinks() {
  try {
    const $ = jQuery
    const $a = $('a[call-to-action]')
    $a.each(function () {
      let currentHref = $(this).attr('href')
      const clickId = create_UUID()
      const originalClickId = commonActions.getParameterByName('ctr_tracking__original_click_id') || clickId
      currentHref = commonActions.updateURLParameter(currentHref, 'ctr_tracking__click_id', clickId)
      currentHref = commonActions.updateURLParameter(currentHref, 'ctr_tracking__original_click_id', originalClickId)
      $(this).attr('href', currentHref)
      $(this).attr('click-id', clickId)
    })
  } catch (err) {
    console.log('populateClickIdParamToCtaLinks error: ', err)
  }
}

export function populateClickIdParamToCtaButton() {
  try {
    const $ = jQuery
    const $ctaButtons = $('button.ctr_cta_button')
    $ctaButtons.each(function () {
      const clickId = create_UUID()
      const originalClickId = commonActions.getParameterByName('ctr_tracking__original_click_id') || clickId
      $(this).attr('ctr-cta-id', $(this).attr('id') || create_UUID())
      $(this).attr('ctr-tracking-original-click-id', originalClickId)
      $(this).attr('ctr-tracking-click-id', clickId)
    })
  } catch (err) {
    console.log('populateClickIdParamToCtaLinks error: ', err)
  }
}

export const addCommonLinkHandler = () => {
  if (!window.ctrwowUtils.isLiveMode()) {
    // do not call api in builder
    return
  }
  console.log('[addCommonLinkHandler]')
  populateParamToLinks()
  populateTrackingParamToCtaLinks()
  funnelRouter()
  populateAffIdParamToCtaLinks()
  populateClickIdParamToCtaLinks()
  populateNewURLPathToCtaLinks()
  populateClickIdParamToCtaButton()
  queryParamToScrollElement()
}
