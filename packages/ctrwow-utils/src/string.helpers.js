// eslint-disable-next-line no-extend-native
const replaceAll_bk = String.prototype.replaceAll

// eslint-disable-next-line no-extend-native
String.prototype.replaceAll = function (search, replacement) {
  const target = this
  if (typeof search === 'string' && typeof replacement === 'string') {
    return target.split(search).join(replacement)
  }
  return replaceAll_bk.call(target, search, replacement)
}
