import { callAjax } from './../callAjax'

const _updateThrottled = (throttled, affParam, siteDomain) => {
  try {
    return callAjax('https://yz3or1urua.execute-api.us-east-1.amazonaws.com/prod/updateThrottled', {
      method: 'POST',
      body: JSON.stringify({
        affId: affParam,
        siteDomain: siteDomain,
        throttled: parseInt(throttled) + 1
      })
    }).then(() => {
      console.log('updated sc successfully!')
      return false
    })
  } catch (e) {
    return Promise.reject(false)
  }
}

export default function checkAff(affParam, siteDomainUrl, { countryCode = '' }) {
  try {
    if (siteDomainUrl && affParam) {
      const siteDomain = siteDomainUrl.replace(/(www|test)\./, '')
      return callAjax('https://yz3or1urua.execute-api.us-east-1.amazonaws.com/prod/so', {
        method: 'POST',
        body: JSON.stringify({
          affId: affParam,
          siteDomain: siteDomain,
          countryCode: countryCode
        })
      }).then((result) => {
        if (result && result.status) {
          window.localStorage.setItem('checkedAff', 'true')

          // resolve
          const aff = result.data
          const shouldThrottled = (aff.percent / 100) * aff.totalOrders
          if (shouldThrottled - aff.throttled >= 1) {
            // no fire but update throttled
            return _updateThrottled(aff.throttled, affParam, siteDomain)
          } else {
            return true
          }
        } else {
          throw new Error('Check Status Failed')
        }
      })
    } else {
      return Promise.reject(false)
    }
  } catch (err) {
    console.log(err)
    return Promise.reject(false)
  }
}
