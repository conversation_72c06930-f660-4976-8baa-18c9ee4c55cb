export default function pushGtmTrackingEvent(eventName, data) {
  try {
    if (!eventName) {
      throw new Error('Event Name not found')
    }
    window.__CTR_FP_TRACKING.getFingerPrintId().then(function (fingerPrintID) {
      window.dataLayer = window.dataLayer || []
      let eventData = { event: eventName }
      if (fingerPrintID) {
        eventData.fpid = fingerPrintID
      }
      if (typeof data === 'object') {
        eventData = Object.assign(eventData, data)
      }
      window.dataLayer.push(eventData)
    })
  } catch (e) {
    console.warn('GTM tracking error: ' + e)
    return false
  }
  return true
}
