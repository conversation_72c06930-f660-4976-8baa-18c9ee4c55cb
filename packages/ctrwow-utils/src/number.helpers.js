/***
 *
 * @param number
 * @param formattedValue
 * @returns {string|*}
 */
export function formaterNumberByFormattedValue(number, formattedValue) {
  const unformatCountry = ['JPY', 'VND']
  try {
    const VALUE_PLACEHOLDER = 'XXXX'
    let separator = '.'
    let regex = /(\d+.*,*)(.|,)(\d{2})/
    let decimalLength = 2
    if (window.localStorage.getItem('decimalLength')) {
      decimalLength = parseInt(window.localStorage.getItem('decimalLength'))
      regex = decimalLength === 1 ? /(\d+.*,*)(.|,)(\d{1})/ : decimalLength === 0 ? /(\d+.*,*)(.|,)(\d{0})/ : /(\d+.*,*)(.|,)(\d{2})/
    }
    let formatNumber = number.toFixed(decimalLength).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    if (unformatCountry.indexOf(window.localStorage.getItem('currencyCode')) > -1) {
      number = Math.round(number)
      formatNumber = number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    }
    const pattern = formattedValue.replace(regex, (match, p1, p2) => {
      separator = p2
      return VALUE_PLACEHOLDER
    })

    if (separator === ',') {
      formatNumber = number
        .toFixed(decimalLength)
        .replace('.', ',')
        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.')

      if (unformatCountry.indexOf(window.localStorage.getItem('currencyCode')) > -1) {
        formatNumber = number
          .toString()
          .replace('.', ',')
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.')
      }
    }

    return pattern.replace(VALUE_PLACEHOLDER, formatNumber)
  } catch (e) {
    return number
  }
}

export function convertNumberToCurrency(num) {
  const lang = document.querySelector('html').getAttribute('lang').toLowerCase()
  const formatNum = new Intl.NumberFormat(`${lang}-${lang.toUpperCase()}`, { style: 'currency', currency: window.currencyCode }).format(Number(num))
  return formatNum
}
