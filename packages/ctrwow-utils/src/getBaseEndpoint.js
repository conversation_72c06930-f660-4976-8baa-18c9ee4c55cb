import CONFIGURABLE_CONSTANTS from './configurable.constants'
import getPageSettings from 'shared-checkout-flow/src/getPageSettings'

const getCrmBaseUrl = () => CONFIGURABLE_CONSTANTS.CRM_BASE_URL || 'https://sales-prod.tryemanagecrm.com/api'
// const getNewPriceCRMBaseUrl = () => CONFIGURABLE_CONSTANTS.CRM_NEW_PRICE_API_URL || 'https://prices.tryemanagecrm.com/api'
const getNewPriceCRMBaseUrl = (instance = null) => {
  // instance = EmanageCRMJS.Order / EmanageCRMJS.Campaign
  const pageSettings = getPageSettings()
  if (instance && pageSettings.sourceConfig.source === 'SHOPIFY') {
    return instance.baseAPIEndpoint
  }

  if (process.env.ENV === 'dev') {
    return 'https://ecrm-prices-dev.azurewebsites.net/api'
  }

  return CONFIGURABLE_CONSTANTS.CRM_NEW_PRICE_API_URL || 'https://prices.tryemanagecrm.com/api'
}

// const getSalesSupportCRMBaseUrl = () => CONFIGURABLE_CONSTANTS.CRM_SALESSUPPORT_API_URL || 'https://salessupport.tryemanagecrm.com/api'
const getSalesSupportCRMBaseUrl = (instance = null) => {
  // instance = EmanageCRMJS.Order / EmanageCRMJS.Campaign
  const pageSettings = getPageSettings()

  if (instance && pageSettings.sourceConfig.source === 'SHOPIFY') {
    return instance.baseAPIEndpoint
  }

  if (process.env.ENV === 'dev') {
    return 'https://ecrm-salessupport-dev.azurewebsites.net/api'
  }

  return CONFIGURABLE_CONSTANTS.CRM_SALESSUPPORT_API_URL || 'https://salessupport.tryemanagecrm.com/api'
}

// const getSalesPciCRMBaseUrl = () => CONFIGURABLE_CONSTANTS.CRM_SALESPCI_API_URL || 'https://sales-pci.tryemanagecrm.com/api'
const getSalesPciCRMBaseUrl = (instance = null) => {
  // instance = EmanageCRMJS.Order / EmanageCRMJS.Campaign
  const pageSettings = getPageSettings()

  if (instance && pageSettings.sourceConfig.source === 'SHOPIFY') {
    return instance.baseAPIEndpoint
  }

  if (process.env.ENV === 'dev') {
    return window.__devctrwowDebug_salePCI || 'https://ca54-125-235-239-35.ngrok-free.app/api'
    // return 'https://emanage-dev-websales-api.azurewebsites.net/api'
  }

  // if (window.isTestAPI && window.testAPI.endpoint) {
  //   return window.testAPI.endpoint.Sales_API
  // }
  if (window.__CTRWOW_CONFIG.X_CID === 'e63fdf5b-b7c1-48e4-92ae-bca237debdbc') {
    return 'https://ecrm-websales-prod-alternative-one.azurewebsites.net/api'
  }
  return CONFIGURABLE_CONSTANTS.CRM_SALESPCI_API_URL || 'https://sales-pci.tryemanagecrm.com/api'
}

export { getCrmBaseUrl, getNewPriceCRMBaseUrl, getSalesSupportCRMBaseUrl, getSalesPciCRMBaseUrl }
