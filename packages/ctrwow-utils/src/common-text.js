function isInViewport(elem) {
  const { top, bottom, width, height } = elem.getBoundingClientRect()
  const vHeight = window.innerHeight || document.documentElement.clientHeight
  return (top > 0 || bottom > 0) && top < vHeight && width > 0 && height > 0
}

function handleScroll() {
  const matchedElements = document.querySelectorAll(`[highlight]`)
  matchedElements.length &&
    matchedElements.forEach((el) => {
      if (!el.classList.contains('active') && isInViewport(el)) {
        el.classList.add('active')
      }
    })
}

export function addCommonTextHandler() {
  // Fix window.innerHeight return 0 at initialize
  setTimeout(function () {
    handleScroll()
    window.addEventListener('scroll', handleScroll)
  }, 500)
}
