/** Handle window locaStorage of html5 */
export default function localStorage() {
  return {
    get: (key) => {
      try {
        if (typeof Storage !== 'undefined') {
          return window.localStorage.getItem(key)
        } else {
          console.log('Sorry! No Web Storage support ....')
          return null
        }
      } catch (e) {
        return null
      }
    },
    set: (key, value) => {
      try {
        if (typeof Storage !== 'undefined') {
          window.localStorage.setItem(key, value)
        } else {
          console.log('Sorry! No Web Storage support ...')
        }
      } catch (e) {
        // do nothing
      }
    },
    remove: (item) => {
      try {
        if (typeof Storage !== 'undefined') {
          window.localStorage.removeItem(item)
        } else {
          console.log('Sorry! No Web Storage support ...')
        }
      } catch (e) {
        // do nothing
      }
    }
  }
}
