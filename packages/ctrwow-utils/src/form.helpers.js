export function populateFormData(form, data) {
  try {
    if (typeof form === 'string') {
      form = document.querySelector(form)
    }
    if (!form) return
    Object.keys(data).forEach((key) => {
      const value = data[key]
      const inputEl = form.querySelector(`[name="${key}"]`)
      if (inputEl) {
        if (inputEl.type === 'radio' || inputEl.type === 'checkbox') {
          inputEl.checked = value
        } else {
          inputEl.value = value
        }
        if (inputEl.tagName === 'SELECT') {
          inputEl.dataset.value = value
        }
        inputEl.dispatchEvent(new CustomEvent('change'))
      }
    })
  } catch (error) {
    console.warn('populateFormData Error: ', error)
  }
}

export function getFormData(form) {
  try {
    if (typeof form === 'string') {
      form = document.querySelector(form)
    }
    if (!form) return {}
    const inputs = form.querySelectorAll('input, select, textarea')
    if (!inputs.length) return {}
    const data = {}
    Array.prototype.forEach.call(inputs, (input) => {
      if (input.type === 'radio' || input.type === 'checkbox') {
        data[input.name] = input.checked
      } else {
        data[input.name] = input.value
      }
    })
    return data
  } catch (error) {
    console.warn('getFormData Error: ', error)
    return {}
  }
}

function validationRecaptcha(validateCaptchaId) {
  const renderCaptchaElm = document.querySelectorAll(`${validateCaptchaId ? `${validateCaptchaId} ` : ''}iframe[title="reCAPTCHA"]`)
  const responseFormat = {
    isValid: false,
    message: 'Please prove that you are a not a robot by selecting the checkbox!!'
  }
  try {
    if (renderCaptchaElm.length < 1) {
      responseFormat.isValid = true
      responseFormat.message = ''
      return responseFormat
    }
    for (const item of renderCaptchaElm) {
      const captchaId = item.closest('[siteKey]').getAttribute('render-captchaid') || 0

      if (window.grecaptcha && window.grecaptcha.getResponse(Number(captchaId)) !== '') {
        responseFormat.isValid = true
        responseFormat.message = ''
      } else {
        responseFormat.isValid = false
        responseFormat.message = 'Please prove that you are a not a robot by selecting the checkbox!!'
        break
      }
    }

    return responseFormat
  } catch (error) {
    console.warn('getReacptcha Error: ', error)
    return responseFormat
  }
}

export function asyncValidateForm(validateCaptchaId = undefined) {
  return validationRecaptcha(validateCaptchaId)
}
