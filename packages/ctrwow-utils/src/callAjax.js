export function callAjax(url, settings) {
  let settingsClone = {}
  if (!url) {
    throw 'API URL is missing!'
  }
  const headersOptions = {
    'content-type': 'application/json'
  }
  if (settings && typeof settings === 'object') {
    settingsClone = Object.assign(
      {
        method: 'GET',
        headers: settings.headers ? Object.assign(headersOptions, settings.headers) : headersOptions
      },
      settings
    )
  }

  return window.fetch(url, settingsClone).then(function (response) {
    if (response.status === 204 || response.status === 205) {
      return null
    }

    if (response.status >= 200 && response.status < 300) {
      // return response.json();
      return response.text().then(function (text) {
        try {
          return text ? JSON.parse(text) : {}
        } catch (e) {
          return {}
        }
      })
    }

    throw {
      status: response.status,
      statusText: response.statusText,
      response: response
    }
  })
}
