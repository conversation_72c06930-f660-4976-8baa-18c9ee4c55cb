export function getUserAnalyticsInfo() {
  function getUserMachineAnalyticsInfo() {
    // eslint-disable-next-line no-undef
    var client = new ClientJS()
    var bs64 = ''
    var ua = client.getBrowserData().ua
    var canvasPrint = client.getCanvasPrint()
    var customFP = '&CustomFingerprint=' + client.getCustomFingerprint(ua, canvasPrint).toString()
    var str = ''
    var map = [
      {
        name: 'SoftwareVersion',
        fn: 'getSoftwareVersion'
      },
      {
        name: 'Fingerprint',
        fn: 'getFingerprint'
      },
      {
        name: 'UserAgentLowerCase',
        fn: 'getUserAgentLowerCase'
      },
      {
        name: '<PERSON>rowser',
        fn: 'getBrowser'
      },
      {
        name: 'BrowserVersion',
        fn: 'getBrowserVersion'
      },
      {
        name: 'Engine',
        fn: 'getEngine'
      },
      {
        name: 'OS',
        fn: 'getOS'
      },
      {
        name: 'OSVersion',
        fn: 'getOSVersion'
      },
      {
        name: 'Device',
        fn: 'getDevice'
      },
      {
        name: 'DeviceType',
        fn: 'getDeviceType'
      },
      {
        name: 'DeviceVendor',
        fn: 'getDeviceVendor'
      },
      {
        name: 'CPU',
        fn: 'getCPU'
      },
      {
        name: 'isMobile',
        fn: 'isMobile'
      },
      {
        name: 'isMobileMajor',
        fn: 'isMobileMajor'
      },
      {
        name: 'isMobileAndroid',
        fn: 'isMobileAndroid'
      },
      {
        name: 'isMobileOpera',
        fn: 'isMobileOpera'
      },
      {
        name: 'isMobileWindows',
        fn: 'isMobileWindows'
      },
      {
        name: 'isMobileBlackBerry',
        fn: 'isMobileBlackBerry'
      },
      {
        name: 'isMobileIOS',
        fn: 'isMobileIOS'
      },
      {
        name: 'isIphone',
        fn: 'isIphone'
      },
      {
        name: 'isIpad',
        fn: 'isIpad'
      },
      {
        name: 'isIpod',
        fn: 'isIpod'
      },
      {
        name: 'ScreenPrint',
        fn: 'getScreenPrint'
      },
      {
        name: 'ColorDepth',
        fn: 'getColorDepth'
      },
      {
        name: 'CurrentResolution',
        fn: 'getCurrentResolution'
      },
      {
        name: 'AvailableResolution',
        fn: 'getAvailableResolution'
      },
      {
        name: 'DeviceXDPI',
        fn: 'getDeviceXDPI'
      },
      {
        name: 'DeviceYDPI',
        fn: 'getDeviceYDPI'
      },
      {
        name: 'Plugins',
        fn: 'getPlugins'
      },
      {
        name: 'isJava',
        fn: 'isJava'
      },
      {
        name: 'JavaVersion',
        fn: 'getJavaVersion'
      },
      {
        name: 'isFlash',
        fn: 'isFlash'
      },
      {
        name: 'FlashVersion',
        fn: 'getFlashVersion'
      },
      {
        name: 'isSilverlight',
        fn: 'isSilverlight'
      },
      {
        name: 'SilverlightVersion',
        fn: 'getSilverlightVersion'
      },
      {
        name: 'isMimeTypes',
        fn: 'isMimeTypes'
      },
      {
        name: 'MimeTypes',
        fn: 'getMimeTypes'
      },
      {
        name: 'Fonts',
        fn: 'getFonts'
      },
      {
        name: 'isLocalStorage',
        fn: 'isLocalStorage'
      },
      {
        name: 'isSessionStorage',
        fn: 'isSessionStorage'
      },
      {
        name: 'isCookie',
        fn: 'isCookie'
      },
      {
        name: 'TimeZone',
        fn: 'getTimeZone'
      },
      {
        name: 'Language',
        fn: 'getLanguage'
      },
      {
        name: 'SystemLanguage',
        fn: 'getSystemLanguage'
      },
      {
        name: 'isCanvas',
        fn: 'isCanvas'
      }
      // {
      //     name: "CanvasPrint",
      //     fn: "getCanvasPrint",
      // }
    ]
    for (var i = 0; i <= map.length - 1; i++) {
      var val = client[map[i].fn]()
      var name = map[i].name
      if (val) {
        val = val.toString()
      } else {
        val = 'n/a'
      }
      str = str + '&' + name + '=' + encodeURIComponent(val)
    }
    str = str + customFP
    str = str.substring(1)
    try {
      bs64 = window.btoa(str)
    } catch (er) {
      console.log('bs64 errr')
    }
    return {
      referringUrl: document.referrer,
      landingUrl: window.location.href,
      userStringData64: bs64
    }
    // return analytics
  }
  return window.ctrwowUtils.getDependencies(['https://ctrwowprodcdn.blob.core.windows.net/ctrwow/assets/js/client.min.js']).then(function () {
    return getUserMachineAnalyticsInfo()
  })
}
