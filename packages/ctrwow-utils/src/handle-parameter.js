export function clearParameter(...params) {
  for (const param of params) {
    let url = document.location.href
    const urlparts = url.split('?')

    if (urlparts.length >= 2) {
      const urlBase = urlparts.shift()
      const queryString = urlparts.join('?')

      const prefix = encodeURIComponent(param) + '='
      const pars = queryString.split(/[&;]/g)
      for (let i = pars.length - 1; i >= 0; i--) {
        if (pars[i].lastIndexOf(prefix, 0) !== -1) {
          pars.splice(i, 1)
        }
      }
      url = urlBase
      if (pars.length > 0) {
        url += '?' + pars.join('&')
      }
      window.history.pushState('', document.title, url)
    }
  }
}

export function addParamIntoUrl(param, value) {
  clearParameter(param)
  const currentUrl = window.location.href
  const newparam = currentUrl.indexOf('?') > -1 ? `&${param}=${value}` : `?${param}=${value}`
  const newurl = currentUrl + newparam

  window.history.pushState({ path: newurl }, '', newurl)
}

export function getQueryParameter(param) {
  let href = ''
  if (location.href.indexOf('?')) {
    href = location.href.substr(location.href.indexOf('?'))
  }

  const value = href.match(new RegExp('[?&]' + param + '=([^&]*)(&?)', 'i'))
  return value ? value[1] : null
}