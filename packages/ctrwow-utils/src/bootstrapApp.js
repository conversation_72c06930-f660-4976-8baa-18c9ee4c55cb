/**
 *
 * @param urls
 * @param callback
 * @Desc: Dynamic load external libs
 */
import getDependencies from 'shared-external-libs-loader/src/getDependencies'

export function injectDependencies(urls = [], callback = () => true) {
  getDependencies(urls).then(() => {
    callback && callback()
  })
}

/**
 *
 * @param func
 * @param deps
 * @returns {boolean}
 * @Desc: bootstrap app with add external lib first
 */
export function bootstrap(func = () => true, deps = []) {
  if (deps && deps.length) {
    injectDependencies(deps, func)
    return true
  }
  func()
}
