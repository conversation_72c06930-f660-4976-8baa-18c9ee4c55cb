export default function simplePubSub() {
  const events = {}

  function on(eventName, fn) {
    events[eventName] = events[eventName] || []
    events[eventName].push(fn)
  }

  function off(eventName, fn) {
    if (events[eventName]) {
      for (let i = 0; i < events[eventName].length; i++) {
        if (events[eventName][i] === fn) {
          events[eventName].splice(i, 1)
          break
        }
      }
    }
  }

  function emit(eventName, data) {
    let count = 0
    const timer = setInterval(() => {
      count++
      if (count >= 50) {
        // 20 * 200 = 10 seconds
        clearInterval(timer)
      }
      if (events[eventName]) {
        clearInterval(timer)
        Array.prototype.slice.call(events[eventName]).forEach(function (fn) {
          fn(data)
        })
      }
    }, 200)
    // if (events[eventName]) {
    //     Array.prototype.slice.call(events[eventName]).forEach(function (fn) {
    //         fn(data);
    //     });
    // }
  }

  return {
    on: on,
    off: off,
    emit: emit
  }
}
