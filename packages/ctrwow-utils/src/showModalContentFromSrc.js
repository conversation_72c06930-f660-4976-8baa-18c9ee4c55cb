function createModalEle() {
  if ($('.ctr-modal_load_content_from_src').length) {
    return
  }
  const modalWrapper = $('<div class="ctr-modal_load_content_from_src"><div').css({
    display: 'none' /* Hidden by default */,
    position: 'fixed' /* Stay in place */,
    'z-index': 1 /* Sit on top */,
    left: 0,
    top: 0,
    width: '100%' /* Full width */,
    height: '100%' /* Full height */,
    overflow: 'auto' /* Enable scroll if needed */,
    'background-color': 'rgba(0,0,0,0.4)' /* Black w/ opacity */
  })
  const modalBody = $('<div class="ctr-modal_load_content_from_src-body"></div>').css({
    'background-color': '#fefefe',
    margin: '2% auto' /* 15% from the top and centered */,
    padding: '20px',
    border: '1px solid #888',
    width: '80%' /* Could be more or less, depending on screen size */
  })

  const closeButton = $('<span class="close">&times;</span>')
    .on('click', function () {
      $('.ctr-modal_load_content_from_src').css({ display: 'none' })
    })
    .css({
      color: '#aaa',
      float: 'right',
      'font-size': '28px',
      'font-weight': 'bold'
    })
  const modalFooter = $('<div class="ctr-modal_load_content_from_src-footer"></div>').css({
    padding: '8px',
    'text-align': 'right'
  })
  const okButton = $('<button> OK </button>')
    .css({
      padding: '8px 16px'
    })
    .on('click', function () {
      $('.ctr-modal_load_content_from_src').css({ display: 'none' })
    })
  modalFooter.append(okButton)
  modalBody.append(closeButton)
  modalBody.append('<div class="ctr-modal_load_content_from_src-content"></div>')
  modalBody.append(modalFooter)

  modalWrapper.append(modalBody)
  // modalWrapper.append(modalFooter);
  $('body').append(modalWrapper)
}

export const showModalContentFromSrc = (contentSrc) => {
  createModalEle()
  const modalContent = $(`<iframe id="iframe" frameBorder="0" width="100%" height="500px" src="${contentSrc}"></iframe>`)
  $('.ctr-modal_load_content_from_src-content').find('#iframe').remove()

  $('.ctr-modal_load_content_from_src-content').append(modalContent)
  $('.ctr-modal_load_content_from_src').css({
    display: 'block'
  })
}
