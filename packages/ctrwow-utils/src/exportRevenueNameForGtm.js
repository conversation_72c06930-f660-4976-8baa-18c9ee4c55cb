;(function (global, document) {
  function exportRevenueNameForGtm() {
    try {
      let orderInfo = window.localStorage.getItem('orderInfo')
      if (!orderInfo) {
        return
      }
      orderInfo = JSON.parse(orderInfo)
      const revenueNames = [
        'initialPurchaseRevenue',
        'firstUpsellRevenue',
        'secondUpsellRevenue',
        'thirdUpsellRevenue',
        'fourthUpsellRevenue',
        'fifthUpsellRevenue',
        'sixthUpsellRevenue'
      ]
      let nameIndex = 0
      let revenueName = revenueNames[0]
      let price = orderInfo.orderTotalFull
      const orderedUpsellArr = orderInfo.upsellUrls || []

      if (orderedUpsellArr.length > 0) {
        nameIndex = orderedUpsellArr[orderedUpsellArr.length - 1].index
        price = orderedUpsellArr[orderedUpsellArr.length - 1].price
        revenueName = revenueNames[nameIndex]
      }

      global[revenueName] = price
    } catch (e) {
      console.log(e)
    }
  }
  exportRevenueNameForGtm()
})(window, document)
