;(function (global, document) {
  global._q = (selector) => {
    return document.querySelector(selector)
  }

  global._qAll = (selector) => {
    return document.querySelectorAll(selector)
  }

  global._qById = (id) => {
    return document.getElementById(id)
  }

  global._createElem = (elem) => {
    return document.createElement(elem)
  }

  global._getClosest = (elem, selector) => {
    if (!Element.prototype.matches) {
      Element.prototype.matches =
        Element.prototype.matchesSelector ||
        Element.prototype.mozMatchesSelector ||
        Element.prototype.msMatchesSelector ||
        Element.prototype.oMatchesSelector ||
        Element.prototype.webkitMatchesSelector ||
        function (s) {
          const matches = (this.document || this.ownerDocument).querySelectorAll(s)
          let i = matches.length
          while (--i >= 0 && matches.item(i) !== this) {}
          return i > -1
        }
    }

    // Get the closest matching element
    for (; elem && elem !== document; elem = elem.parentNode) {
      if (elem.matches(selector)) {
        return elem
      }
    }
    return null
  }

  global.q = (selector) => {
    var qSelector = document.querySelectorAll(selector)

    return {
      addClass: function (className) {
        for (const elm of qSelector) {
          elm.classList.add(className)
        }
      },
      removeClass: function (className) {
        for (const elm of qSelector) {
          elm.classList.remove(className)
        }
      },
      display: function (pro) {
        for (const elm of qSelector) {
          elm.style.display = pro
        }
      },
      hide: function () {
        for (const elm of qSelector) {
          elm.style.display = 'none'
        }
      }
    }
  }
})(window, document)
