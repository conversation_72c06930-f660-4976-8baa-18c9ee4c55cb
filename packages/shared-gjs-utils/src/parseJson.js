import _isString from 'lodash/fp/isString'
import _isNil from 'lodash/fp/isNil'

export function convertFromStringToObj(string, defaultValue = {}, isThrowError = false) {
  try {
    return _isNil(string) ? defaultValue : _isString(string) ? JSON.parse(string) : string
  } catch (e) {
    if (!isThrowError) {
      return defaultValue
    } else {
      throw e
    }
  }
}

export function convertFromObjToString(obj, defaultValue = undefined, isThrowError = false) {
  try {
    return JSON.stringify(obj)
  } catch (e) {
    if (!isThrowError) {
      return defaultValue
    } else {
      throw e
    }
  }
}
