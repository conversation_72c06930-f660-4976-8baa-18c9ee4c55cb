import _pipe from 'lodash/fp/pipe'
import _filter from 'lodash/fp/filter'
import _map from 'lodash/fp/map'
import _get from 'lodash/fp/get'

export function addCommonInfoSimple(controllerFn, { traits = [], packageName } = {}) {
  const bindingPropsData = _pipe([
    _filter(_get('changeProp')),
    _map(({ name, type }) => `'${name}' : '{[ ${[type === 'textarea' ? '_' : '', name].join('')} ]}'`)
  ])(traits)

  // mapping trait value to props
  const bindingString = `try { var props= { ${bindingPropsData.join(',')} }}catch(e){console.log(e)}`

  const contents = [
    bindingString,
    `if (window.ctrDebug && window.ctrDebug.run(this, '${packageName}', props)) return true`,
    `(${controllerFn.toString()})(this, props)`,
    `console.log('run controller')`
  ].join(';')

  return `(function controller() { ${contents} }).apply(this)`
}

export default function addCommonInfo(controllerMod, { traits = [], packageName } = {}) {
  const bindingPropsData = _pipe([
    _filter(_get('changeProp')),
    _map(({ name, type }) => `'${name}' : '{[ ${[type === 'textarea' ? '_' : '', name].join('')} ]}'`)
  ])(traits)

  // mapping trait value to props
  const bindingString = `var props; try { props= { ${bindingPropsData.join(',')} }}catch(e){console.log(e)}`
  const controllerFnStr = typeof controllerMod === 'string' ? controllerMod : controllerMod.default || ''

  const contents = [
    bindingString,
    `if (window.ctrDebug && window.ctrDebug.run(this, '${packageName}', props)) return true`,
    controllerFnStr + '(this, props)',
    `console.log('run controller')`
  ].join(';')

  return `try { ${contents} } catch(e){console.log(e)}`
}
