/* eslint-disable */
export function widgetControllerWrapper(controllerMod, { packageName } = {}) {
  try {
    const controllerFnStr = controllerMod && controllerMod.default ? controllerMod.default : controllerMod
    const main_body = [
      'console.log(scriptProps)',
      `if (window.ctrDebug && window.ctrDebug.run(this, '${packageName}', scriptProps)) return true`,
      controllerFnStr + '(this, scriptProps)',
    ].join(';')

    const exception_handler = [
      `console.log("[${packageName}] controller - exception handler")`,
      `console.log(e)`
    ].join(';')

    const body = `try { ${main_body} }catch(e){ ${exception_handler} };console.log("[${packageName}]run controller");`

    return new Function(`return function ctrWidgetController(scriptProps){ ${body} }`)()
  } catch (e) {
    alert(`[CTRWOW] [EXTERNAL WIDGET] - cannot add ${packageName}. Please try again`)
    console.log(e)
  }
}
