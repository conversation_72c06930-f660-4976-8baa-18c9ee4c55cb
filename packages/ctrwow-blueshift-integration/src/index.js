/* eslint-disable */
const EVENT_API_KEY = window.__CTR_BS_API_KEY || process.env.API_KEY

function initScript(rs) {
  window._blueshiftid = EVENT_API_KEY
  window.blueshift = window.blueshift || []
  if (blueshift.constructor === Array) {
    blueshift.load = function() {
      var d = function(a) {
        return function() {
          blueshift.push([a].concat(Array.prototype.slice.call(arguments, 0)))
        }
      }, e = ['identify', 'track', 'click', 'pageload', 'capture', 'retarget']
      for (var f = 0; f < e.length; f++) blueshift[e[f]] = d(e[f])
    }
  }
  blueshift.load()
  if (blueshift.constructor === Array) {
    (function() {
      var b = document.createElement('script')
      b.type = 'text/javascript', b.async = !0, b.src = ('https:' === document.location.protocol ? 'https:' : 'http:') + '//cdn.getblueshift.com/blueshift.js'
      b.id = '_ctr_bs_script'
      var c = document.getElementsByTagName('script')[0]
      c.parentNode.insertBefore(b, c)
      b.onload = function() {
        rs(true)
      }
    })()
  }
}

// --- helpers
export function loadBlueshiftDependency() {
  return new Promise((rs, rj) => {
    try {
      if (!EVENT_API_KEY) {
        throw 'Missing BS API_KEY'
      }
      if (!document.getElementById('_ctr_bs_script')) {
        initScript(rs)
      } else {
        const limitTime = 4000 //4s
        let counter = 0
        const timer = 100
        const intevalId = setInterval(() => {
          if (blueshift.constructor !== Array) {
            clearInterval(intevalId)
            rs(true)
          }
          if (counter > limitTime) {
            clearInterval(intevalId)
            rj(false)
          }
          counter = counter + timer

        }, timer)
      }
    } catch (e) {
      console.warn('trackPageLoad error: ' + e)
      rj(false)
    }
  })
}

// exporter

export function trackPageLoad() {
  loadBlueshiftDependency().then(rs => {
    window.blueshift.pageload()
  })
}

export function track(eventName, data = {}) {
  if (!eventName) {
    return
  }
  loadBlueshiftDependency().then(rs => {
    window.blueshift.track(eventName, data)
  })
}

export function identify(data = {}) {
  if (!data || typeof data !== 'object') {
    return
  }
  loadBlueshiftDependency().then(rs => {
    window.blueshift.identify(data)
  })
}

// ---
// function init() {
//   trackPageLoad()
// }
//
// window.addEventListener('load', function() {
//   init()
// })
