export function loadCountries() {
  // https://api.eflow.team/v1/meta/countries
  $.ajax({
    url: `https://api.eflow.team/v1/meta/countries`,
    method: 'GET',
    success: function (response) {
      if (response.countries && response.countries.length > 0) {
        document.getElementById('countriesAjax').innerHTML = ''
        response.countries.map((item) => {
          document.getElementById('countriesAjax').innerHTML += `<option value="${item.country_code}">${item.country_name}</option>`
        })
      }
    },
    error: function (response) {
      console.log(response)
    }
  })
}
