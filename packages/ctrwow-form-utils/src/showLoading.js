export function showAjaxLoading() {
  const preloadingElem = document.querySelector('.loading-wrapper')
  const preloadingNumber = window.ctrwowUtils.link.getQueryParameter('preloading') ? window.ctrwowUtils.link.getQueryParameter('preloading') : 1
  const preloading = document.getElementById('preloading' + preloadingNumber)
  if (preloading) {
    preloading.style.display = 'block'
    preloading.style.opacity = '1'
  } else if (preloadingElem) {
    preloadingElem.style.display = 'block'
    preloadingElem.style.opacity = '1'
  }
}
