export function hanldleChangeState(countryCode) {
  const code = countryCode.toLowerCase()
  if (code) {
    // fetch(`https://cdn-sgn.dfowebsys-h01.com/states/${code}.json`)
    //     .then(res => res.json())
    //     .then(data => console.log(data))
    //     .catch(err => console.log(err));
    $.ajax({
      url: `https://cdn-sgn.dfowebsys-h01.com/states/${code}.json`,
      method: 'GET',
      success: function (response) {
        if (response && response.length > 0) {
          document.getElementById('stateAjax').innerHTML = ''
          response.map((item) => {
            document.getElementById('stateAjax').innerHTML += `<option value="${item.CountryCode}">${item.StateName}</option>`
          })
        }
      },
      error: function (error) {
        console.log(error)
        document.getElementById('stateAjax').innerHTML = ''
        document.getElementById('stateAjax').innerHTML += `<option value="null">null</option>`
      }
    })
  }
}
