import { addInputError } from './addInputError'
import { removeInputError } from './removeInputError'
import { isValidURL } from './validateURL'

export function validateInput(input) {
  if (input.attributes.required !== undefined && input.value.trim() === '') {
    addInputError(input)
  } else if (input.attributes.email !== undefined) {
    // validate email
    if (window.ctrwowUtils.isEmail(input.value)) {
      removeInputError(input)
    } else {
      addInputError(input)
    }
  } else if (input.attributes.url !== undefined) {
    // validate Url
    if (isValidURL(input.value)) {
      removeInputError(input)
    } else {
      addInputError(input)
    }
  } else if (input.attributes.phonebr !== undefined) {
    // validate Brazil phone number
    if (window.ctrwowUtils.validatePhoneBr(input)) {
      removeInputError(input)
    } else {
      addInputError(input)
    }
  } else if ((input.id.indexOf('_postal') > 0 || input.id.indexOf('_cep') > 0) && typeof input.pattern !== 'undefined') {
    if (new RegExp(input.pattern.toLowerCase()).test(input.value.toLowerCase())) {
      removeInputError(input)
    } else {
      addInputError(input)
    }
  } else {
    removeInputError(input)
  }
}
