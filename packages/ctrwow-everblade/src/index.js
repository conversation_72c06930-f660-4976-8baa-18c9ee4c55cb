/* eslint-disable*/
let surchargeShipping, insuranceShipping
const miniUpsellArr = []
const orderSummaryList = document.querySelector('.js-order-summary .order-summary__list')

function displayInfo(type, className, data) {
  if (!data) return
  const offshoreShipping = window.offshoreShipping ? window.offshoreShipping : 'Offshore shipping - applies to outside if the continental United States'
  switch (type) {
    case 'add':
      const wrapperElm = document.createElement('div')
      let template = ''
      if (data.productId === 34) {
        template = `<span>${data.productName}</span><span>${data.productPrices.DiscountedPrice.FormattedValue}</span>`
      } else if (data.productId === 100) {
        template = `<span>${data.productName} <br><i>*${offshoreShipping}</i></span><span>${data.productPrices.DiscountedPrice.FormattedValue}</span>`
      }

      const currentItem = document.querySelector('.' + className)

      if (!currentItem) {
        pushToMiniUpsells(data, true)
        // Append HTML
        wrapperElm.classList.add('order-summary__item', className)
        wrapperElm.innerHTML = template
        orderSummaryList.appendChild(wrapperElm)
      }

      break
    case 'remove':
      const itemRemove = document.querySelector('.' + className)
      const dataIndex = miniUpsellArr.findIndex((elm) => elm.productId === data.productId)

      if (itemRemove) {
        // Remove MiniUpsell Data
        miniUpsellArr.splice(dataIndex, 1)
        window.ctrwowCheckout.checkoutData.setMiniUpsell(miniUpsellArr.length > 0 ? miniUpsellArr : '')
        // Remove HTML
        orderSummaryList.removeChild(itemRemove)
      }
      break
  }
}

const countryCodeElm = document.querySelector('form[name="shippingAddress"] .country-select')
function detectCountryCode() {
  countryCodeElm.addEventListener('change', function () {
    const _self = this

    if (_self.value === 'US') {
      displayInfo('remove', 'order-summary__sur-shipping', surchargeShipping)
    } else {
      displayInfo('add', 'order-summary__sur-shipping', surchargeShipping)
    }
  })
}

function validateCountry() {
  if (window.ctrwowUtils.localStorage().get('ctr__countryCode') !== 'US' && countryCodeElm.value !== 'US' && countryCodeElm.value !== '') {
    displayInfo('add', 'order-summary__sur-shipping', surchargeShipping)
  }
}

function addProtectionShipping() {
  const protectionShippingBox = document.querySelector('.protect-shipping')
  const inputCheckBox = protectionShippingBox && protectionShippingBox.querySelector('input')

  inputCheckBox && inputCheckBox.addEventListener('change', function () {
    const _self = this

    if (_self.checked) {
      displayInfo('add', 'order-summary__ins-shipping', insuranceShipping)
    } else {
      displayInfo('remove', 'order-summary__ins-shipping', insuranceShipping)
    }
  })
}

/* custom checkout flow */

// Global variables
const elm = document.querySelector('.extra-popup').parentElement
window.downsellPop = null
window.downsellUpgradePop = null
window.checkoutData = null
const hasLoader = location.href.toUpperCase().indexOf('LOADER=1') > -1

let orderInfo

// get attributes' info from diggy popup
const webkey1 = window._q('.extra-popup').parentElement.getAttribute('webkey1')
const webkey2 = window._q('.extra-popup').parentElement.getAttribute('webkey2')
const webkeyDS1 = window._q('.extra-popup').parentElement.getAttribute('webkeyds1')
const webkeyDS2 = window._q('.extra-popup').parentElement.getAttribute('webkeyds2')
const miniPID = window._q('.extra-popup').parentElement.getAttribute('mini-pid').split(',')


function resetParam() {
  let url = location.href
    url = removeUrlParameter(url, 'upgrade')
    url = removeUrlParameter(url, 'dpop')
    window.history.replaceState({ url: url }, null, url)
}
function removeUrlParameter(url, paramKey) {
  const r = new URL(url)
  r.searchParams.delete(paramKey)
  return r.href
}
function appendParam(param, value) {
  const newHref = window.ctrwowUtils.link.updateURLParameter(location.href, param, value)
  if (window.ctrwowUtils.isLiveMode()) {
    window.history.pushState({ path: newHref }, '', newHref)
  }
}

function handlePopupEvents() {
  const diggyAddBtns = elm.querySelectorAll('.extra-popup .btn-add')
  const diggyCancelBtns = elm.querySelectorAll('.extra-popup .secondPopup .btn-close, .extra-popup .secondPopup .section-button .btn-cancel')
  const addUpsellBtns = window._qAll('.btnUpsellOrder')
  const cancelUpsellBtns = window._qAll('.btnDisplayUpgradePopup')
  const addUpgradeDownsellBtns = window._qAll('.upsell-popup .btn-add')
  const cancelUpgradeDownsellBtns = window._qAll('.upsell-popup .btn-cancel, .upsell-popup .btn-close')

  Array.prototype.forEach.call(addUpsellBtns, (btn) => {
    btn.addEventListener('click', () => {
      // window.noDownSell && (window.productUpgrade = getProductDownsellUpgrade(window.upgrade_products))
      window.productUpgrade = getProductDownsellUpgrade(window.upgrade_products)

      window.isFirstDiggy === undefined && switchFn(handleCases)(2)
      window.isFirstDiggy === true && switchFn(handleCases)(4)
      if(window.noDownSell) {
        window.isFirstDiggy === false && switchFn(handleCases)(2)
      } else {
        window.isFirstDiggy === false && switchFn(handleCases)(6)
      }
      appendParam('upgrade', 1)
      window.ctrwowUtils.showGlobalLoading()
    })
  })

  Array.prototype.forEach.call(cancelUpsellBtns, (btn) => {
    btn.addEventListener('click', () => {
      !window.noDownSell && getDownsellUpgrade()
      window.ctrwowUtils.showGlobalLoading()
      if(window.noDownSell) {
        if(window.isFirstDiggy === undefined || window.isFirstDiggy === false) {
          switchFn(handleCases)(1)
        }
        window.isFirstDiggy === true && switchFn(handleCases)(3)
      } else {
        window.ctrwowUtils.events.on('beforeHandleShowingDownsell', () => {
          renderDownsellData()
          window._q('.upsell-popup').classList.add('show')
        })
      }
      appendParam('upgrade', 0)
    })
  })

  Array.prototype.forEach.call(addUpgradeDownsellBtns, (btn) => {
    btn.addEventListener('click', () => {
      window._q('.upsell-popup').classList.remove('show')
      window.isFirstDiggy === undefined && switchFn(handleCases)(7)
      window.isFirstDiggy === true && switchFn(handleCases)(8)
      window.isFirstDiggy === false && switchFn(handleCases)(9)
      appendParam('dpop', 1)
    })
  })

  Array.prototype.forEach.call(cancelUpgradeDownsellBtns, (btn) => {
    btn.addEventListener('click', () => {
      window._q('.upsell-popup').classList.remove('show')
      window.isFirstDiggy === undefined && switchFn(handleCases)(null)
      window.isFirstDiggy === true && switchFn(handleCases)(3)
      window.isFirstDiggy === false && switchFn(handleCases)(5)
      appendParam('dpop', 0)
    })
  })

  Array.prototype.forEach.call(diggyCancelBtns, (btn) => {
    btn.addEventListener('click', () => {
      showUpsellInfo()
    })
  })

  Array.prototype.forEach.call(diggyAddBtns, (btn) => {
    btn.addEventListener('click', () => {
      window.ctrwowUtils.events.on('beforeHandleShowingUpsell', () => {
        showUpsellInfo()
      })
    })
  })
}

const contains = (element, text) => RegExp(text).test(element.textContent)

function replaceStringBrackets(orderData) {
  const cusFirstName = orderData ? orderData.customer.firstName : ''
  const cusLastName = orderData ? orderData.customer.lastName : ''
  const allChildNodes = window._qAll('.upsell-section > *')
  Array.prototype.slice.call(allChildNodes).forEach((node) => wrapSpan(node))
  function wrapSpan(node) {
    if ((node.nodeName === '#text' && contains(node, '{firstname}')) || (node.nodeName === '#text' && contains(node, '{lastname}'))) {
      const s = document.createElement('span')
      s.innerHTML = node.textContent.split('{firstname}').join(cusFirstName).split('{lastname}').join(cusLastName)
      node.parentElement.insertBefore(s, node.parentElement.childNodes[0])
      node.remove()
    } else {
      const length = node.childNodes.length
      for (let i = 0; i < length; i++) wrapSpan(node.childNodes[i], i)
    }
  }
}

function handleShowingUpsell() {
  window.ctrwowUtils.events.emit('onShowingUpsellInfo')
  Array.prototype.forEach.call(window._qAll('.checkout-section, .idtmm1w_global, .extra-popup, .paymentProccessing'), (el) => {
    el.style.display = 'none'
  })
  Array.prototype.forEach.call(window._qAll('.upsell-section'), (el) => {
    el.style.display = 'block'
  })
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

function showUpsellInfo() {
  handleShowingUpsell()

  orderInfo = window.paymentInfo ? window.paymentInfo : null
  // orderInfo && replaceStringBrackets(orderInfo.customer.firstName, orderInfo.customer.lastName)
  replaceStringBrackets(orderInfo)

  const productPopupPrice = window.productPop ? window.productPop.productPrices.DiscountedPrice.Value : 0
  const productPopupUpgradePrice = window.productUpgradePop ? window.productUpgradePop.productPrices.DiscountedPrice.Value : 0

  const upsellUpgradePrice =
    window.productUpgrade.productPrices.DiscountedPrice.Value -
    window.productMain.productPrices.DiscountedPrice.Value +
    (productPopupUpgradePrice - productPopupPrice)
  const upsellUpgradeFullPrice = window.productUpgrade.productPrices.FullRetailPrice.Value + productPopupUpgradePrice

  Array.prototype.forEach.call(window._qAll('.spanFullPrice'), (el) => {
    el.textContent = window.ctrwowUtils.number.formaterNumberByFormattedValue(
      upsellUpgradeFullPrice,
      window.productUpgrade.productPrices.FullRetailPrice.FormattedValue
    )
  })
  Array.prototype.forEach.call(window._qAll('.spanUpsellPrice'), (el) => {
    el.textContent = window.ctrwowUtils.number.formaterNumberByFormattedValue(
      upsellUpgradePrice,
      window.productUpgrade.productPrices.DiscountedPrice.FormattedValue
    )
  })
}

function renderDownsellData() {
  const upsellPopup = window._q('.upsell-popup')
  let downsellDiscopuntedPrice, downsellFullRetailPrice, downsellSavePrice, downsellDiscountedFormat, downsellllFullRetailFormat

  window.downsellPop = window.isFirstDiggy === undefined ? null : window.isFirstDiggy ? window.dataDiggyDs1 : window.dataDiggyDs2
  window.downsellUpgradePop = window.isFirstDiggy === undefined ? null : window.isFirstDiggy ? window.dataDiggyDsUp1 : window.dataDiggyDsUp2

  const productPopupPrice = window.downsellPop ? window.downsellPop.productPrices.DiscountedPrice.Value : 0
  const productPopupUpgradePrice = window.downsellUpgradePop ? window.downsellUpgradePop.productPrices.DiscountedPrice.Value : 0

  if (!window.isFirstDiggy) {
    downsellDiscopuntedPrice = window.downsellUpgradeProduct2.productPrices.DiscountedPrice.Value + productPopupUpgradePrice
    downsellSavePrice =
      window.downsellUpgradeProduct2.productPrices.DiscountedPrice.Value -
      window.downsellMainProduct2.productPrices.DiscountedPrice.Value +
      (productPopupUpgradePrice - productPopupPrice)
    downsellFullRetailPrice = window.downsellUpgradeProduct2.productPrices.FullRetailPrice.Value
    downsellDiscountedFormat = window.downsellUpgradeProduct2.productPrices.DiscountedPrice.FormattedValue
    downsellllFullRetailFormat = window.downsellUpgradeProduct2.productPrices.FullRetailPrice.FormattedValue
  } else {
    downsellDiscopuntedPrice = window.downsellUpgradeProduct1.productPrices.DiscountedPrice.Value + productPopupUpgradePrice
    downsellSavePrice =
      window.downsellUpgradeProduct1.productPrices.DiscountedPrice.Value -
      window.downsellMainProduct1.productPrices.DiscountedPrice.Value +
      (productPopupUpgradePrice - productPopupPrice)
    downsellFullRetailPrice = window.downsellUpgradeProduct1.productPrices.FullRetailPrice.Value
    downsellDiscountedFormat = window.downsellUpgradeProduct1.productPrices.DiscountedPrice.FormattedValue
    downsellllFullRetailFormat = window.downsellUpgradeProduct1.productPrices.FullRetailPrice.FormattedValue
  }

  const unitPrice = upsellPopup.querySelector('.unitPrice')
  const unitFullPrice = upsellPopup.querySelector('.unitFullPrice')
  const savePrice = upsellPopup.querySelector('.savePrice')

  if (unitPrice) {
    unitPrice.textContent = unitPrice.textContent.replace(
      '{price}',
      window.ctrwowUtils.number.formaterNumberByFormattedValue(downsellDiscopuntedPrice, downsellDiscountedFormat)
    )
  }
  if (unitFullPrice) {
    unitFullPrice.textContent = unitFullPrice.textContent.replace(
      '{fullprice}',
      window.ctrwowUtils.number.formaterNumberByFormattedValue(downsellFullRetailPrice, downsellllFullRetailFormat)
    )
  }
  if (savePrice) {
    savePrice.textContent = window.ctrwowUtils.number.formaterNumberByFormattedValue(downsellSavePrice, downsellDiscountedFormat)
  }
}

function getMainDownsellProduct(arr) {
  const selectedProduct = window.ctrwowCheckout.checkoutData.getProduct()
  const productMain = arr.find((item) => item.productId === selectedProduct.productId)
  return productMain
}

function getListProductDownsellUpgrade(productDsArr) {
  const downsellUpgradePro = []
  productDsArr.forEach((item) => {
    if (window.main_product_id.indexOf(item.productId) === -1) {
      downsellUpgradePro.push(item)
    }
  })
  return getProductDownsellUpgrade(downsellUpgradePro)
}

function getProductDownsellUpgrade(arr) {
  const productSelectQuantity = window.ctrwowCheckout.checkoutData.getProduct().quantity
  let productUpgradeItem = null
  arr.forEach((item) => {
    if (item.quantity === productSelectQuantity) {
      productUpgradeItem = item
    }
  })
  return productUpgradeItem
}

function getDownsellUpgrade() {
  const headers = {
    'content-type': 'application/json',
    X_CID: window.__ctrPageConfiguration.cid
  }
  const endpoint = window.ctrwowUtils.getNewPriceCRMBaseUrl()
  const endpointDS1 = webkeyDS1 ? `${endpoint}/${webkeyDS1}/products/prices` : null
  const endpointDS2 = webkeyDS2 ? `${endpoint}/${webkeyDS2}/products/prices` : null
  let urls = []
  urls = [endpointDS1, endpointDS2]
  urls = urls.filter((url) => url !== null)
  // const urls = [`${endpoint}/${webkeyDS1}/products/prices`, `${endpoint}/${webkeyDS2}/products/prices`]
  Promise.all(
    urls.map((url) => {
      return fetch(url, {
        method: 'GET',
        headers: headers
      }).then((res) => res.json())
    })
  )
    .then((results) => {
      if (results) {
        window.downsellUpgradeProduct1 = results[0] && getListProductDownsellUpgrade(results[0].prices)
        window.downsellMainProduct1 = results[0] && getMainDownsellProduct(results[0].prices)
        window.downsellUpgradeProduct2 = results[1] && getListProductDownsellUpgrade(results[1].prices)
        window.downsellMainProduct2 = results[1] && getMainDownsellProduct(results[1].prices)
        window.campaignDs1 = results[0] && results[0].campaignName
        window.campaignDs2 = results[1] && results[1].campaignName
      }
    })
    .catch((error) => console.warn(error))
    .finally(() => {
      document.querySelector('.paymentProccessing').style.display = 'none'
      window.ctrwowUtils.events.emit('beforeHandleShowingDownsell')
    })
}

function getProductMiniUpsellOfDownsell(array) {
  return array.find((item) => item.productId === Number(miniPID[0]))
}

function getProductMiniUpsellUpgradeOfDownsell(array) {
  return array.find((item) => item.productId === Number(miniPID[1]))
}

function getDiggyDownsellProducts() {
  !hasLoader && window.ctrwowUtils.showGlobalLoading()
  const headers = {
    'content-type': 'application/json',
    X_CID: window.__ctrPageConfiguration.cid
  }
  const endpoint = `${window.ctrwowUtils.getNewPriceCRMBaseUrl()}/campaigns`
  const endpoint1st = webkeyDS1 ? `${endpoint}/${webkeyDS1}/products/prices/miniupsells` : null
  const endpoint2nd = webkeyDS2 ? `${endpoint}/${webkeyDS2}/products/prices/miniupsells` : null
  const endpoint3rd = webkey1 ? `${endpoint}/${webkey1}/products/prices/miniupsells` : null
  let urls = []
  urls = [endpoint1st, endpoint2nd, endpoint3rd]
  urls = urls.filter((url) => url !== null)
  // const urls = [
  //   `${endpoint}/${webkeyDS1}/products/prices/miniupsells`,
  //   `${endpoint}/${webkeyDS2}/products/prices/miniupsells`,
  //   `${endpoint}/${webkey1}/products/prices/miniupsells`
  // ]
  Promise.all(
    urls.map((url) => {
      return fetch(url, {
        method: 'GET',
        headers: headers
      }).then((res) => res.json())
    })
  )
    .then((results) => {
      if (results) {
        window.dataDiggyDs1 = results[0] && getProductMiniUpsellOfDownsell(results[0])
        window.dataDiggyDs2 = results[1] && getProductMiniUpsellOfDownsell(results[1])
        window.dataDiggyDsUp1 = results[0] && getProductMiniUpsellUpgradeOfDownsell(results[0])
        window.dataDiggyDsUp2 = results[1] && getProductMiniUpsellUpgradeOfDownsell(results[1])
        window.miniUpsellData = results[2] && results[2]
        surchargeShipping =  window.miniUpsellData && results[2].find((elm) => elm.productId === 100)
        insuranceShipping = window.miniUpsellData && results[2].find((elm) => elm.productId === 34)
        detectCountryCode()
        addProtectionShipping()
        validateCountry()
      }
    }).finally(() => {
      window.ctrwowUtils.hideGlobalLoading()
    })
    .catch((error) => console.warn(error))
}

// !Handle all cases

const switchFn = (lookupObject, defaultCase = 'default') => (expression) => (lookupObject[expression] || lookupObject[defaultCase])()
const handleCases = {
  1: () => submitOrderData(),
  2: () => submitUpgradeProduct(),
  3: () => submitFirstDiggyWithoutUpgrade(),
  4: () => submitFirstDiggyWithUpgrade(),
  5: () => submitSecondDiggyWithoutUpgrade(),
  6: () => submitSecondDiggyWithUpgrade(),
  7: () => submitDownsellUpgradeProduct(),
  8: () => submitFirstDiggyWithDownsellUpgrade(),
  9: () => submitSecondDiggyWithDownsellUpgrade(),
  default: () => submitOrderData()
}

// Case 2 (webkey1)
function submitUpgradeProduct() {
  window.ctrwowCheckout.checkoutData.setProduct(window.productUpgrade)
  submitOrderData()
}

// Case 3 (webkey1)
function submitFirstDiggyWithoutUpgrade() {
  pushToMiniUpsells(window.productPop, false)
  submitOrderData()
}

// Case 4 (webkey1)
function submitFirstDiggyWithUpgrade() {
  window.ctrwowCheckout.checkoutData.setProduct(window.productUpgrade)
  pushToMiniUpsells(window.productUpgradePop, false)
  submitOrderData()
}

// Case 5 (webkey2)
function submitSecondDiggyWithoutUpgrade() {
  window.ctrwowCheckout.getCheckoutConfig().webKey = webkey2
  window.ctrwowCheckout.getCheckoutConfig().offerName = window.campaign2
  pushToMiniUpsells(window.productPop, false)
  submitOrderData()
}

// Case 6 (webkey2)
function submitSecondDiggyWithUpgrade() {
  window.ctrwowCheckout.getCheckoutConfig().webKey = webkey2
  window.ctrwowCheckout.getCheckoutConfig().offerName = window.campaign2
  window.ctrwowCheckout.checkoutData.setProduct(window.productUpgrade)
  pushToMiniUpsells(window.productUpgradePop, false)
  submitOrderData()
}

// Case 7 (webkeyDS1)
function submitDownsellUpgradeProduct() {
  window.ctrwowCheckout.getCheckoutConfig().webKey = webkeyDS1
  window.ctrwowCheckout.getCheckoutConfig().offerName = window.campaignDs1
  window.ctrwowCheckout.checkoutData.setProduct(window.downsellUpgradeProduct1)
  submitOrderData()
}

// Case 8 (webkeyDS1)
function submitFirstDiggyWithDownsellUpgrade() {
  window.ctrwowCheckout.getCheckoutConfig().webKey = webkeyDS1
  window.ctrwowCheckout.getCheckoutConfig().offerName = window.campaignDs1
  window.ctrwowCheckout.checkoutData.setProduct(window.downsellUpgradeProduct1)
  pushToMiniUpsells(window.dataDiggyDsUp1, false)
  submitOrderData()
}

// Case 9 (webkeyDS2)
function submitSecondDiggyWithDownsellUpgrade() {
  window.ctrwowCheckout.getCheckoutConfig().webKey = webkeyDS2
  window.ctrwowCheckout.getCheckoutConfig().offerName = window.campaignDs2
  window.ctrwowCheckout.checkoutData.setProduct(window.downsellUpgradeProduct2)
  pushToMiniUpsells(window.dataDiggyDsUp2, false)
  submitOrderData()
}

function pushToMiniUpsells(productInput, addedToSum) {
  miniUpsellArr.push({
    name: productInput.productDisplayName,
    productId: productInput.productId,
    price: productInput.productPrices.DiscountedPrice.Value,
    formatPrice: productInput.productPrices.DiscountedPrice.FormattedValue,
    addToSummary: addedToSum,
    type: 'mini',
    shippingMethodId: productInput.shippings.length > 0 ? productInput.shippings[0].shippingMethodId : null
  })

  window.ctrwowCheckout.checkoutData.setMiniUpsell(miniUpsellArr)
}

function submitOrderData() {
  if (window.orderType === 'PP') {
    window.ctrwowCheckout.payment
      .checkoutWithPaypal(window.paymentProcessorId)
      .catch(() => (elm.querySelector('.paymentProccessing').style.display = 'none'))
  } else if (window.orderType === 'GG' || window.orderType === 'AP') {
    window.gap.handleAppleGoogleClick()
  } else {
    window.ctrwowCheckout.payment
      .checkoutWithCreditCard(window.paymentInfo)
      .catch(() => (document.querySelector('.paymentProccessing').style.display = 'none'))
  }
}

function handleSubmitBtns() {
  const creditSubmitBtns = document.querySelectorAll('button[name="checkoutWithCreditCardV1"]')
  const paypalSubmitBtns = document.querySelectorAll('.checkoutWithPaypal')
  const googleSubmitBtn = document.getElementById('btn-google-pay') || document.querySelector('.google-button')
  const appleSubmitBtn = document.getElementById('btn-apple-pay') || document.querySelector('.apple-button')

  Array.prototype.forEach.call(creditSubmitBtns, (btn) => {
    btn.addEventListener('click', () => {
      window.orderType = 'CC'
    })
  })
  Array.prototype.forEach.call(paypalSubmitBtns, (btn) => {
    btn.addEventListener('click', () => {
      window.orderType = 'PP'
    })
  })

  googleSubmitBtn && googleSubmitBtn.addEventListener('click', () => {
    window.orderType = 'GG'
  })

  appleSubmitBtn && appleSubmitBtn.addEventListener('click', () => {
    window.orderType = 'AP'
  })
}

window.addEventListener('DOMContentLoaded', () => {
  handlePopupEvents()
  handleSubmitBtns()
  getDiggyDownsellProducts()
})

window.addEventListener('load', () => {
  resetParam()
  window.noDownSell && window.ctrwowUtils.events.on('beforeHandleShowingUpsell', () => {
    showUpsellInfo()
  })
})
