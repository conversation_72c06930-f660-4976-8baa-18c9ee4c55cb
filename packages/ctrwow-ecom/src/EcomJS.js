/* eslint-disable */
const store = {};

const mockApi = {
  Cart: {
    updateProductToCart: function(productId, quantity, sessionId, callbackWrapper) {
      console.log('API updateProductToCart: ', productId, quantity, sessionId, callbackWrapper);
      callbackWrapper(null, 'ok');
    },
    initCart(product, callbackWrapper) {
      console.log('API initCart: ', product);
      callbackWrapper(null, 'API initCart ok');
    },
    addProductToCart(product, sessionId, callbackWrapper) {
      console.log('API addProductToCart: ', product, sessionId);
      callbackWrapper(null, 'API addProductToCart ok');
    },
    removeProductFromCart(productId, sessionId, callbackWrapper) {
      console.log('API addProductToCart: ', productId, sessionId);
      callbackWrapper(null, 'API removeProductFromCart ok');
    }
  },
  Checkout: {
    CreditCardPayment: {
      placeOrder(sessionId, orderData, callbackWrapper) {
        console.log('API placeOrder: ', sessionId, orderData);
        callbackWrapper(null, 'API placeOrder ok');
      }
    },
    applyCoupon(sessionId, couponCode, callbackWrapper) {
      console.log('API applyCoupon: ', sessionId, couponCode);
      callbackWrapper(null, 'API applyCoupon ok');
    },
    checkPaypalApprove(callbackWrapper) {
      console.log('API checkPaypalApprove: ');
      callbackWrapper(null, 'API checkPaypalApprove ok');
    }
  }
}

export default function getEcomJS() {
  if (!window.__CTR_ECOM_CONFIG) throw new Error('Missing __ctrPageConfiguration')
  if (!window.__CTR_ECOM_CONFIG.campaignWebKey) throw new Error('Missing webKey')
  if (!window.__CTR_ECOM_CONFIG.cid) throw new Error('Missing cid')
  if (!window.ctrwowUtils) throw new Error('Missing ctrwowUtils')
  
  const isTest = window.ctrwowUtils.link.getQueryParameter('isCardTest') === '1'

  const ecomJS = new window.EcommJS({
    webkey: window.__CTR_ECOM_CONFIG.campaignWebKey,
    cid: window.__CTR_ECOM_CONFIG.cid,
    // getAPIEndpoint: process.env.CRM_WEBSALE_GET_API_ENDPOINT_URL + '/api',
    // postAPIEndpoint: process.env.CRM_WEBSALE_POST_API_ENDPOINT_URL + '/api',
    getAPIEndpoint: 'https://salessupport.tryemanagecrm.com/api',
    postAPIEndpoint: 'https://sales-pci.tryemanagecrm.com/api',
    isTest
  })
  ecomJS.Cart.postAPIEndpoint = 'https://sales-pci.tryemanagecrm.com/api'
  ecomJS.Checkout.postAPIEndpoint = 'https://sales-pci.tryemanagecrm.com/api'
  ecomJS.Campaign.getAPIEndpoint = 'https://salessupport.tryemanagecrm.com/api'
  return ecomJS
}
