/* eslint-disable */
import { CACHE_KEYS } from './constants'
import { utils as EcomUtils } from './utils'

function getRequestHeader() {
  const headers = {
    'content-type': 'application/json'
  }
  if (window.__CTR_ECOM_CONFIG && window.__CTR_ECOM_CONFIG.cid) {
    headers.X_CID = window.__CTR_ECOM_CONFIG.cid
  }
  return headers
}

function getBaseUrl() {
  return EcomUtils.getBaseUrl()
}

function getLoginUrl() {
  return `${getBaseUrl()}/login.html`
}

function getAccountUrl() {
  return `${getBaseUrl()}/account.html`
}

export const responseHandler = (response) => {
  EcomUtils.hideLoading()
  if (response.status === 401) {
    logout()
    return
  }
  if (response.status >= 200 && response.status < 300) {
    // return response.json();
    return response.text().then((text) => {
      try {
        return text ? JSON.parse(text) : {}
      } catch (e) {
        return {}
      }
    })
  }
  return Promise.reject(false)
}

export function privateApiRequestSdk(path, options = { method: 'GET', body: {}, headers: {} }) {
  let Authorization = ''
  try {
    if (options.headers && options.headers.Authorization) {
      Authorization = options.headers.Authorization
    } else {
      Authorization = `Bearer ${window.localStorage.getItem(CACHE_KEYS.AUTH_TOKEN)}`
    }
  } catch (e) {
    console.warn(e)
  }
  const newOptions = {
    method: options.method || 'GET',
    headers: {
      ...getRequestHeader(),
      Authorization
    }
  }
  if (options.method === 'POST' && options.body) {
    newOptions.body = JSON.stringify(options.body || {})
  }
  EcomUtils.showLoading()
  return window.fetch(`${process.env.CRM_PRIVATE_API_ENDPOINT_URL}/${path}`, newOptions).then(responseHandler)
}

/**
 * login
 * @param email
 * @param password
 * @response PROMISE<{}>
 * {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RRvYl1NYos7qeRL912p1KUMOXrEKXyy_pS6ofFoqEXc",
    "expiresIn": 10800,
    "orderNumber": null,
    "email": "<EMAIL>"}
 */

function login({ email, password }) {
  if (!email || !password) {
    throw 'Missing email or password'
  }
  const headers = { Authorization: `Basic ${btoa(`${email}:${password}`)}` }
  const isOrderNumber = password.search('C') > 0
  const loginApi = isOrderNumber
    ? privateApiRequestSdk('customer/auth/escn', { headers, method: 'POST' })
    : privateApiRequestSdk('customer/auth', { headers, method: 'POST' })
  return loginApi.then(function (rs) {
    window.localStorage.setItem(CACHE_KEYS.AUTH_TOKEN, rs.token)
    return {
      redirectUrl: getAccountUrl()
    }
  })
}

/**
 *   {
    "orderId": 7952524,
    "orderNumber": "********",
    "campaignId": "3041",
    "campaignName": "SuperBoost Wifi EN VSL - View Page Split (ST)",
    "productName": "Wifi Booster",
    "productId": 764,
    "quantity": null,
    "orderStatus": "Paid",
    "saleType": "Hard Sale",
    "sku": "30082",
    "isRegularOrder": false,
    "shoppingCartId": null,
    "createDate": "2019-12-24T07:18:08.077",
    "createDateOffset": "2019-12-24T07:18:08.076669+00:00",
    "lastUpdateDate": "2019-12-24T07:18:12.22"
  }
 */
function getOrderList() {
  const orderListApi = privateApiRequestSdk('/order/list')
  return orderListApi.then(function (rs) {
    // TODO: transform to view
    return rs
  })
}

/**
 * https://emanage-prod-csm-api.azurewebsites.net/order/********
 * {"id":********,"orderNumber":"*********","orderStatusId":2,"workflowStatus":"Active","languageCode":"EN","cardId":"6a113558-c77f-461d-a762-2de1ef892b40","currencyCode":"USD","currencySign":"US$","orderPrice":63.18,"orderPriceUSD":63.18,"campaignProductSaleType":"Hard Sale","billingAddressId":6855602,"addressId":6855602,"recurringOrderId":null,"comment":"","campaignName":"YehYeh EN Golden OP (Diggy Popup)","campaignLandingUrl":"https://www.yehyehapp.com/en/order.html","lastUpdateBy":"WebSales","orderDate":"2020-10-23T09:38:25.407","lastUpdateDate":"2020-10-23T09:38:28.317","customerId":5059088,"customerCountryCode":"VN","customerEmail":"<EMAIL>","orderDateOffset":"2020-10-23T09:38:25.4066341+00:00","upsellOrderId":null,"miniUpsellOrderId":null,"orderType":"Regular Order","ip":"**************","orderBehavior":"Test Order","isPotentialFraud":false,"shoppingCartId":null,"product":{"id":3599,"productName":"YehYeh Programmable NFC Sticker (Round) - 8 Units","sku":"30491_8","category":null,"quantity":8},"address":{"id":6855602,"firstName":"123123123","middleName":null,"lastName":"123123123123123","address1":"Pirineos 123123B, Portales Norte, Mexico City, CDMX, Mexico","address2":"Pirineos 123123B, Portales Norte, Mexico City, CDMX, Mexico","city":"México D.F.","state":"01","countryCode":"VN","countryName":"Vietnam","zipCode":"1231231234","phoneNumber":"123123123"},"billingAddress":{"id":6855602,"firstName":"123123123","middleName":null,"lastName":"123123123123123","address1":"Pirineos 123123B, Portales Norte, Mexico City, CDMX, Mexico","address2":"Pirineos 123123B, Portales Norte, Mexico City, CDMX, Mexico","city":"México D.F.","state":"01","countryCode":"VN","countryName":"Vietnam","zipCode":"1231231234","phoneNumber":"123123123"},"coupon":null,"workflow":[{"taskId":15423836,"description":"Charge shipping + discounted package price","taskType":"Charge","placeOrder":true,"status":"Completed","chargeAmount":"$63.18","schedule":"2020-10-23T09:38:00","executionTime":"2020-10-23T09:38:00","resultMessage":"See payments receipts for details."},{"taskId":15423837,"description":"Order Confirmation","taskType":"Email","placeOrder":false,"status":"Completed","chargeAmount":null,"schedule":"2020-10-23T09:43:00","executionTime":"2020-10-23T10:02:00","resultMessage":"Order Confirmation email <NAME_EMAIL> from <EMAIL>"}],"receipts":[{"id":14377757,"transactionId":"6a112a6b-89a0-4bb6-82c9-ce5c2302ceb9","paymentStatus":"Paid","paymentProcessorId":28,"paymentDescription":"Product","prnCode":"eManagePay","paymentNumber":"*********","currencyCode":"USD","amount":63.18,"formattedAmount":"$63.18","receiptDate":"2020-10-23T09:38:28.113"}],"notes":[],"events":[],"relatedOrders":[],"shipments":[{"id":10650438,"status":"Shipping","shippingMethod":"[All Categories - $6.95] - 8 Units","createDate":"2020-10-23T09:38:25.8633333","isReshipment":false,"trackings":[]}],"statusHistory":[{"statusName":"New","lastUpdateBy":"WebSales","lastUpdateDate":"2020-10-23T09:38:25.5633333"},{"statusName":"Paid","lastUpdateBy":"WebSales","lastUpdateDate":"2020-10-23T09:38:28.36"}],"digitalProducts":[]}
 */
function getOrderDetail(orderId) {
  const orderDetailApi = privateApiRequestSdk(`/order/${orderId}`)
  return orderDetailApi.then(function (rs) {
    // TODO: transform to view
    return rs
  })
}

/**
 * addNewTicket
 * @param orderId
 * @param subject
 * @param body
 */
function addNewTicket({ orderId, subject, body }) {
  const newTicketApi = privateApiRequestSdk(`/ticket/${orderId}/new`, {
    method: 'POST',
    body: { subject, body }
  })
  return newTicketApi.then(function (rs) {
    // TODO: transform to view
    return rs
  })
}

/**
 * getTicketsByOrderId
 * @param orderId
 * @response PROMISE<array({})>
 * [{
    "id": 32093,
    "customerId": 5059088,
    "orderId": ********,
    "ticketId": 2187823,
    "productMasterAccountId": null,
    "quantity": null,
    "creationDate": "2020-10-30T00:00:00",
    "ticket": {
      "id": 2187823,
      "customerId": 5059088,
      "ticketTypeId": 3,
      "ticketStatusId": 8,
      "ticketPriorityId": 1,
      "ticketDepartmentId": 1,
      "ticketSourceId": 3,
      "branchId": 1,
      "languageCode": "EN",
      "ticketNumber": "TKT002187823",
      "email": "<EMAIL>",
      "ticketSubject": "Leavenoprint Customer Service ********",
      "owner": "Mirna Mohamed",
      "unreadMessages": false,
      "numberOfMessages": 2,
      "mailboxName": null,
      "createDate": "2020-10-30T08:41:20.1",
      "lastUpdateDate": "2020-10-30T09:57:09.98",
      "lastUpdateBy": null,
      "messages": [
        {
          "id": 6957058,
          "sender": "<EMAIL>",
          "subject": "Re: Leavenoprint Customer Service ********",
          "messageUri": "https://emanageprodstorage.blob.core.windows.net/mailboxes/<EMAIL>/ebdd41b8-e650-4f1c-8cd0-1843b3d38532_Mail.txt?sv=2017-04-17&sr=c&sig=GVBGcUiDHw%2FLnvMLBGDycP9aA5Qoer5arEC3bksprNY%3D&se=2020-11-02T08%3A12%3A38Z&sp=r",
          "hasAttachments": false,
          "inReplyTo": 6953970,
          "repliedBy": "Mirna Mohamed",
          "isCustomerMessage": false,
          "receivedDate": "2020-10-30T09:57:09.713",
          "isRead": true
        },
        {
          "id": 6953970,
          "sender": "<EMAIL>",
          "subject": "Leavenoprint Customer Service ********",
          "messageUri": "https://emanageprodstorage.blob.core.windows.net/mailboxes/<EMAIL>/c26ed106-72e8-4bdf-a89f-82004c151cee_Mail.txt?sv=2017-04-17&sr=c&sig=GVBGcUiDHw%2FLnvMLBGDycP9aA5Qoer5arEC3bksprNY%3D&se=2020-11-02T08%3A12%3A38Z&sp=r",
          "hasAttachments": false,
          "inReplyTo": null,
          "repliedBy": null,
          "isCustomerMessage": true,
          "receivedDate": "2020-10-30T08:41:20.1",
          "isRead": true
        }
      ]
    }
  }]
 */
function getTicketsByOrderId(orderId) {
  const getTicketsByOrderIdApi = privateApiRequestSdk(`/ticket/${orderId}/list`)
  return getTicketsByOrderIdApi.then(function (rs) {
    // TODO: transform to view
    return rs
  })
}

function logout() {
  window.localStorage.removeItem(CACHE_KEYS.AUTH_TOKEN)
  window.location.href = getLoginUrl()
}

function isLogin() {
  const autoKen = window.localStorage.getItem(CACHE_KEYS.AUTH_TOKEN)
  if (autoKen) return Promise.resolve(true)
  return Promise.reject(false)
}

export const privateHelpers = {
  login,
  logout,
  isLogin,
  getOrderList,
  getOrderDetail,
  addNewTicket,
  getTicketsByOrderId
}
