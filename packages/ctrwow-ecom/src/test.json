[{"id": 32060, "customerId": 5059088, "orderId": ********, "ticketId": 2183767, "productMasterAccountId": null, "quantity": null, "creationDate": "2020-10-28T00:00:00", "ticket": {"id": 2183767, "customerId": 5059088, "ticketTypeId": 3, "ticketStatusId": 4, "ticketPriorityId": 1, "ticketDepartmentId": 1, "ticketSourceId": 3, "branchId": 1, "languageCode": "EN", "ticketNumber": "TKT002183767", "email": "<EMAIL>", "ticketSubject": "Leavenoprint Customer Service ********", "owner": "<PERSON>", "unreadMessages": false, "numberOfMessages": 1, "mailboxName": null, "createDate": "2020-10-28T08:17:20.777", "lastUpdateDate": "2020-10-28T08:25:04.003", "lastUpdateBy": null, "messages": [{"id": 6937495, "sender": "<EMAIL>", "subject": "Leavenoprint Customer Service ********", "messageUri": "https://emanageprodstorage.blob.core.windows.net/mailboxes/<EMAIL>/0fd558e8-4d25-4ec3-9a01-c0371bd9c7be_Mail.txt?sv=2017-04-17&sr=c&sig=EWxbsNSZEspUuJxYjFBnCEUSxinfxy%2FGo5FomGOoB7Y%3D&se=2020-11-02T08%3A12%3A37Z&sp=r", "hasAttachments": false, "inReplyTo": null, "repliedBy": null, "isCustomerMessage": true, "receivedDate": "2020-10-28T08:17:20.777", "isRead": true}]}}, {"id": 32073, "customerId": 5059088, "orderId": ********, "ticketId": 2187042, "productMasterAccountId": null, "quantity": null, "creationDate": "2020-10-29T00:00:00", "ticket": {"id": 2187042, "customerId": 5059088, "ticketTypeId": 3, "ticketStatusId": 1, "ticketPriorityId": 1, "ticketDepartmentId": 1, "ticketSourceId": 3, "branchId": 1, "languageCode": "EN", "ticketNumber": "TKT002187042", "email": "<EMAIL>", "ticketSubject": "Leavenoprint Customer Service ********", "owner": "<PERSON>", "unreadMessages": false, "numberOfMessages": 1, "mailboxName": null, "createDate": "2020-10-29T08:49:42.533", "lastUpdateDate": "2020-10-29T08:53:04.657", "lastUpdateBy": null, "messages": [{"id": 6949936, "sender": "<EMAIL>", "subject": "Leavenoprint Customer Service ********", "messageUri": "https://emanageprodstorage.blob.core.windows.net/mailboxes/<EMAIL>/4fb043db-566d-4c68-afaf-803fef3a8106_Mail.txt?sv=2017-04-17&sr=c&sig=GVBGcUiDHw%2FLnvMLBGDycP9aA5Qoer5arEC3bksprNY%3D&se=2020-11-02T08%3A12%3A38Z&sp=r", "hasAttachments": false, "inReplyTo": null, "repliedBy": null, "isCustomerMessage": true, "receivedDate": "2020-10-29T08:49:42.533", "isRead": true}]}}, {"id": 32093, "customerId": 5059088, "orderId": ********, "ticketId": 2187823, "productMasterAccountId": null, "quantity": null, "creationDate": "2020-10-30T00:00:00", "ticket": {"id": 2187823, "customerId": 5059088, "ticketTypeId": 3, "ticketStatusId": 8, "ticketPriorityId": 1, "ticketDepartmentId": 1, "ticketSourceId": 3, "branchId": 1, "languageCode": "EN", "ticketNumber": "TKT002187823", "email": "<EMAIL>", "ticketSubject": "Leavenoprint Customer Service ********", "owner": "<PERSON><PERSON>", "unreadMessages": false, "numberOfMessages": 2, "mailboxName": null, "createDate": "2020-10-30T08:41:20.1", "lastUpdateDate": "2020-10-30T09:57:09.98", "lastUpdateBy": null, "messages": [{"id": 6957058, "sender": "<EMAIL>", "subject": "Re: Leavenoprint Customer Service ********", "messageUri": "https://emanageprodstorage.blob.core.windows.net/mailboxes/<EMAIL>/ebdd41b8-e650-4f1c-8cd0-1843b3d38532_Mail.txt?sv=2017-04-17&sr=c&sig=GVBGcUiDHw%2FLnvMLBGDycP9aA5Qoer5arEC3bksprNY%3D&se=2020-11-02T08%3A12%3A38Z&sp=r", "hasAttachments": false, "inReplyTo": 6953970, "repliedBy": "<PERSON><PERSON>", "isCustomerMessage": false, "receivedDate": "2020-10-30T09:57:09.713", "isRead": true}, {"id": 6953970, "sender": "<EMAIL>", "subject": "Leavenoprint Customer Service ********", "messageUri": "https://emanageprodstorage.blob.core.windows.net/mailboxes/<EMAIL>/c26ed106-72e8-4bdf-a89f-82004c151cee_Mail.txt?sv=2017-04-17&sr=c&sig=GVBGcUiDHw%2FLnvMLBGDycP9aA5Qoer5arEC3bksprNY%3D&se=2020-11-02T08%3A12%3A38Z&sp=r", "hasAttachments": false, "inReplyTo": null, "repliedBy": null, "isCustomerMessage": true, "receivedDate": "2020-10-30T08:41:20.1", "isRead": true}]}}]