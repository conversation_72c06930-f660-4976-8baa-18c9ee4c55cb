function showLoading() {
  const $ecomLoading = $('.ctr_ecom_loading_wrap')
  if ($ecomLoading && $ecomLoading.length) {
    $ecomLoading.addClass('active')
  } else {
    window.ctrwowUtils.showGlobalLoading()
  }
}

function hideLoading() {
  const $ecomLoading = $('.ctr_ecom_loading_wrap')
  if ($ecomLoading && $ecomLoading.length) {
    $ecomLoading.removeClass('active')
  } else {
    window.ctrwowUtils.hideGlobalLoading()
  }
}

function getBaseUrl() {
  // const { origin, pathname } = window.location
  // const siteId = pathname.split('/')[1]
  // return `${origin}/${siteId}`

  const { origin, pathname } = window.location
  if (origin.indexOf('https://publish.ctrwow.com') > -1) {
    const siteId = pathname.split('/')[1]
    return `${origin}/${siteId}`
  }
  return `${origin}`
}

export const utils = {
  showLoading,
  hideLoading,
  getBaseUrl
}
