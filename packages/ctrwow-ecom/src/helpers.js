/* eslint-disable */
import { CACHE_KEYS, TYPES_OF_NEW_API } from './constants'
import EcomJ<PERSON> from './EcomJS'
import { events as EcomEvent } from './events'
import { utils as EcomUtils } from './utils'
import { appendCtaClickIdTrackingParam } from 'shared-trackings'
import mergeCurrentParamsWithCtaClickIdTrackingParam from 'shared-trackings/src/mergeCurrentParamsWithCtaClickIdTrackingParam'

function getRequestHeader() {
  const headers = {
    'content-type': 'application/json'
  }
  if (window.__CTR_ECOM_CONFIG && window.__CTR_ECOM_CONFIG.cid) {
    headers.X_CID = window.__CTR_ECOM_CONFIG.cid
  }
  return headers
}

function formatCurrencyNumber(number, countryCode, currencyCode) {
  return new Intl.NumberFormat(countryCode, { style: 'currency', currency: currencyCode }).format(number)
}

export function webSaleApiRequest(path, { method = 'GET', body = {}, endpointType }) {
  let apiEndpint = ''
  const options = {
    method: method,
    headers: {
      ...getRequestHeader()
    }
  }

  if (endpointType !== undefined) {
    switch (endpointType) {
      case TYPES_OF_NEW_API.SALESSUPPORT:
        apiEndpint = 'https://salessupport.tryemanagecrm.com/api'
        break
      case TYPES_OF_NEW_API.SALESPCI:
        apiEndpint = 'https://sales-pci.tryemanagecrm.com/api'
        break
      case TYPES_OF_NEW_API.PRICES:
        apiEndpint = 'https://prices.tryemanagecrm.com/api'
        break
    }
  } else {
    if (method.toLowerCase() == 'get') {
      apiEndpint = 'https://salessupport.tryemanagecrm.com/api'
    } else {
      apiEndpint = 'https://sales-pci.tryemanagecrm.com/api'
      options.body = JSON.stringify(body)
    }
  }

  return window.fetch(`${apiEndpint}${path}`, options).then((rs) => rs.json())
  // return Promise.resolve(mockdata)
}

const webKey = window.__CTR_ECOM_CONFIG ? window.__CTR_ECOM_CONFIG.campaignWebKey : ''

function redirectPage(url) {
  if (url) {
    window.location.href = url
  }
}

function getProductPageUrl(productId) {
  let modelData = {}
  try {
    if (!productId) {
      throw 'Missing Product Id'
    }

    modelData = JSON.parse(window.atob(window.__CTR_ECOM_CONFIG.ecomCategoriesSetting))
    const mCategories = modelData.categories || {}
    const mPageUrls = modelData.pageUrls || {}

    if (mCategories.categories && mCategories.categories.length) {
      let productViews = []
      const getProductByCategory = (categories) => {
        categories.forEach((category) => {
          if (category.products && category.products.length) {
            productViews = productViews.concat(category.products)
          }
          if (category.categories && category.categories.length) {
            getProductByCategory(category.categories)
          }
        })
      }
      getProductByCategory(mCategories.categories)
      const productView = productViews.find((pv) => pv.productId.toString() === productId.toString())
      return mPageUrls[productView.viewId].url
    }
  } catch (e) {}
  return ''
}

export const getCampaignProductPrices = () => {
  if (!window[CACHE_KEYS.CAMPAIGNS]) {
    EcomUtils.showLoading()
    return webSaleApiRequest(`/campaigns/${webKey}/products/prices`, { method: 'GET', type: TYPES_OF_NEW_API.PRICES })
      .then((rs) => {
        if (!rs) {
          throw 'Error'
        }
        window.localStorage.setItem(CACHE_KEYS.CAMPAIGNS, JSON.stringify(rs))
        if (rs.location) {
          window.localStorage.setItem(CACHE_KEYS.COUNTRY_CODE, rs.location.countryCode)
          window.localStorage.setItem("customerIP",rs.location.ip)
        }
        window[CACHE_KEYS.CAMPAIGNS] = rs
        return rs
      })
      .finally(EcomUtils.hideLoading)
  }
  return Promise.resolve(window[CACHE_KEYS.CAMPAIGNS])
}

function storeCampaignsInfo() {
  return getCampaignProductPrices().then((rs) => {
    return window.localStorage.setItem(CACHE_KEYS.CAMPAIGNS, JSON.stringify(rs))
  })
}

function getProductPrices() {
  try {
    return getCampaignProductPrices().then((rs) => {
      return rs ? rs.prices : []
    })
  } catch (e) {
    console.warn(e)
  }
  return Promise.reject(false)
}

function getProductPriceById(productId) {
  try {
    return getProductPrices().then((products = []) => {
      return products.find((p) => p.productId.toString() === productId.toString())
    })
  } catch (e) {
    console.warn(e)
  }
  return Promise.reject(false)
}

export function getCartData() {
  const cartDataStr = window.localStorage.getItem(CACHE_KEYS.DOTCMS_CART) ? window.localStorage.getItem(CACHE_KEYS.DOTCMS_CART) : window.localStorage.getItem(CACHE_KEYS.CART)
  return cartDataStr ? JSON.parse(cartDataStr) : null
}

export const updateCartItemQuantity = (productId, quantity = 0, callback) => {
  let newQuantity = quantity
  if (!productId) {
    throw new Error('Missing Product Id')
  }
  const cartData = getCartData()
  cartData.items = cartData.items.map(function (item) {
    if (item.productId.toString() === productId.toString()) {
      item.quantity = Number(quantity)
      const priceQuantityFormatted = getPriceQuantity({ value: item.price, quantity, countryCode: item.countryCode, currencyCode: item.currencyCode })
      item.priceQuantityFormatted = priceQuantityFormatted
    }
    return item
  })
  const callbackWrapper = function (error, result) {
    EcomUtils.hideLoading()
    if (error) {
      callback && callback(error, result)
      EcomEvent.updateCartItemQuantity.dispatch({ error: true, data: { cartData } })
      return
    }
    if (!error) {
      window.localStorage.setItem(CACHE_KEYS.CART, JSON.stringify(cartData))
      window.localStorage.setItem(CACHE_KEYS.DOTCMS_CART, JSON.stringify(cartData))
      callback && callback(false, cartData.items)
      EcomEvent.updateCartItemQuantity.dispatch({ error: false, data: { cartData } })
    }
  }

  EcomJS().Cart.updateProductToCart(productId, newQuantity, cartData.sessionId, callbackWrapper)
}

export function initCart(product, callback) {
  if (!product) {
    throw Error('product are required')
  }
  const callbackWrapper = function (error, sessionId) {
    if (!error) {
      const items = []
      items.push(product)
      const data = {
        sessionId: sessionId,
        items: items
      }
      window.localStorage.setItem(CACHE_KEYS.CART, JSON.stringify(data))
      window.localStorage.setItem(CACHE_KEYS.DOTCMS_CART, JSON.stringify(data))
    }
    callback && callback(error, sessionId)
  }
  return EcomJS().Cart.initCart(product, callbackWrapper)
}

function addNewProductToCart(product, callback) {
  if (!product) {
    throw Error('product are required')
  }
  if (!product.productId) throw Error('productId is required')
  // ? Discount = 0, charged via shipping Fee
  // if (!product.price) throw Error('price is required')
  if (!product.quantity) throw Error('quantity is required')
  if (!product.shippingMethodId) throw Error('shippingMethodId is required')

  const cartData = getCartData()
  const callbackWrapper = function (error, result) {
    if (!error) {
      cartData.items.push(product)
      window.localStorage.setItem(CACHE_KEYS.CART, JSON.stringify(cartData))
      window.localStorage.setItem(CACHE_KEYS.DOTCMS_CART, JSON.stringify(cartData))
    }
    typeof callback === 'function' && callback(error, result)
  }
  EcomJS().Cart.addProductToCart(product, cartData.sessionId, callbackWrapper)
}

function getPriceQuantity({ value, quantity, countryCode, currencyCode }) {
  const priceQuantity = Number(value) * quantity
  const priceQuantityFormatted = formatCurrencyNumber(priceQuantity, countryCode, currencyCode)
  return priceQuantityFormatted
}

function getProductCartItemPayload(productId, quantity) {
  return getProductPriceById(productId).then((productData) => {
    const location = window[CACHE_KEYS.CAMPAIGNS].location
    const priceQuantityFormatted = getPriceQuantity({
      value: productData.productPrices.DiscountedPrice.Value,
      quantity,
      countryCode: location.countryCode,
      currencyCode: location.currencyCode
    })
    return {
      productId: productData.productId.toString(),
      productName: productData.productName,
      productDisplayName: productData.productDisplayName,
      price: productData.productPrices.DiscountedPrice ? productData.productPrices.DiscountedPrice.Value : '',
      fullPrice: productData.productPrices.FullRetailPrice ? productData.productPrices.FullRetailPrice.FormattedValue : '',
      fullPriceValue: productData.productPrices.FullRetailPrice ? productData.productPrices.FullRetailPrice.Value : '',
      discountedPrice: productData.productPrices.DiscountedPrice ? productData.productPrices.DiscountedPrice.Value : '',
      appliedCouponPrice: productData.productPrices.DiscountedPrice ? productData.productPrices.DiscountedPrice.Value : '',
      priceFormatted: productData.productPrices.DiscountedPrice ? productData.productPrices.DiscountedPrice.FormattedValue : '',
      formattedPrice: productData.productPrices.DiscountedPrice ? productData.productPrices.DiscountedPrice.FormattedValue : '',
      priceQuantityFormatted,
      quantity: quantity,
      countryCode: location.countryCode,
      currencyCode: location.currencyCode,
      shippingMethodId: productData.shippings && productData.shippings.length > 0 ? productData.shippings[0].shippingMethodId : null,
      shippingPrice: productData.shippings && productData.shippings.length > 0 ? productData.shippings[0].formattedPrice : null,
      shippingPriceValue: productData.shippings && productData.shippings.length > 0 ? productData.shippings[0].price : null
    }
  })
}

function getCurrencySymbol({ priceValue = 0, priceFormatted = '' }) {
  return priceFormatted.replace(priceValue, '')
}

export function addCartItem({ productId, quantity, imageUrl, productName, productSubTitle, productIds = '' }, callback) {
  try {
    if (!productId) {
      throw Error('Missing productId')
    }
    if (!quantity) {
      throw Error('Missing quantity')
    }
    EcomUtils.showLoading()
    getProductCartItemPayload(productId, quantity).then((productAddToCartInfo) => {
      const cartData = getCartData()
      productAddToCartInfo.listProduct = productIds
      productAddToCartInfo.productImageUrl = imageUrl
      productAddToCartInfo.productName = productName ? productName : productAddToCartInfo.productName
      productAddToCartInfo.productSubTitle = productSubTitle || ''
      productAddToCartInfo.titleProductName = productSubTitle || ''
      productAddToCartInfo.productUrl = window.ctrwowEcom.helpers.getProductPageUrl(productAddToCartInfo.productId)
      productAddToCartInfo.currencySymbol = getCurrencySymbol({
        priceFormatted: productAddToCartInfo.priceFormatted,
        priceValue: productAddToCartInfo.price
      })

      const cbWithFireEvent = (...agrs) => {
        EcomUtils.hideLoading()
        callback(...agrs)
        EcomEvent.addCartItem.dispatch({ error: false, data: { cartData } })
      }
      if (!cartData) {
        initCart(productAddToCartInfo, cbWithFireEvent)
      } else {
        const productExisted =
          cartData &&
          cartData.items.find(function (cp) {
            return cp.productId == productAddToCartInfo.productId
          })
        if (productExisted) {
          const newQuantity = (productExisted.quantity || 1) + Number(quantity)
          updateCartItemQuantity(productExisted.productId, newQuantity, callback)
        } else {
          addNewProductToCart(productAddToCartInfo, cbWithFireEvent)
        }
      }
    })
  } catch (e) {
    console.error('addCartItem error: ', e)
    EcomEvent.addCartItem.dispatch({
      error: e,
      data: { product }
    })
  }
}


export function addProductVariantToCartItem({ productId, quantity, imageUrl, productName, productSubTitle, productIds}, callback) {
  try {
    if (!productId) {
      throw Error('Missing productId')
    }
    if (!quantity) {
      throw Error('Missing quantity')
    }

    if (!productIds) {
      throw Error('Missing productIds')
    }

    EcomUtils.showLoading()
    const cartItem = getCartData() || {};
    const productVariantId = productIds.split(',')[quantity-1];

    const productVariantAddedCartItem= (cartItem.items || []).find(function (product) {
      return product.productName === productName
    })

    if(productVariantAddedCartItem) {
      window.ctrwowEcom.helpers.removeCartItem(productVariantAddedCartItem.productId, (error, results) => {
        if (error) {
          console.log(error, results)
          callback && callback(error, result)
        }
        addCartItem(
          {
            productId: Number(productVariantId),
            quantity: 1,
            imageUrl,
            productName,
            productSubTitle,
            productIds
          },
          function (result) {
            callback && callback(result)
          }
        )
      })
    } else if(productVariantId) {
      addCartItem(
        {
          productId: Number(productVariantId),
          quantity: 1,
          imageUrl,
          productName,
          productSubTitle,
          productIds
        },
        function (result) {
          console.log('addMultipleProductToCartItem ok', result)
          callback && callback(result)
        }
      )
    }
  } catch (e) {
    console.error('addMultipleProductToCartItem error: ', e)
  }
}

export function removeCartItem(productId, next) {
  try {
    if (!productId) {
      throw Error('Missing ProductId')
    }
    EcomUtils.showLoading()
    const cartData = getCartData()
    const callbackWrapper = function (error, result) {
      EcomUtils.hideLoading()
      if (!error) {
        cartData.items = cartData.items.filter(function (item) {
          return item.productId.toString() !== productId.toString()
        })
        window.localStorage.setItem(CACHE_KEYS.CART, JSON.stringify(cartData))
        window.localStorage.setItem(CACHE_KEYS.DOTCMS_CART, JSON.stringify(cartData))
      }
      EcomEvent.removeCartItem.dispatch({
        error: false,
        data: { cartData }
      })
      typeof next === 'function' && next(error, result)
    }
    EcomJS().Cart.removeProductFromCart(productId, cartData.sessionId, callbackWrapper)
  } catch (e) {
    console.error('removeCartItem error: ', e)
    EcomEvent.removeCartItem.dispatch({
      error: e,
      data: { productId }
    })
  }
}

function getPaypalRedirectUrl(results) {
  if (results.callBackUrl) {
    return results.callBackUrl
  } else if (results.paymentContinueResult && results.paymentContinueResult.actionUrl !== '') {
    return results.paymentContinueResult.actionUrl
  } else {
    return window.__ctrPageConfiguration.confirmUrl
  }
}

// {"success":false,"message":"Processing payment.","shoppingCartNumber":"709972C","callBackUrl":null,"trackingNumber":null,"descriptor":"Shop Perro Pal","isPrePaidCreditCard":false,"paymentContinueResult":null,"customerVerification":{"emailVerified":null,"shippingAddressVerified":null,"billingAddressVerified":null},"campaignTrackings":[],"upsells":[]}
function checkoutByPaypal(orderCheckoutData, next) {
  try {
    const cartData = getCartData()
    if (!orderCheckoutData) {
      throw Error('Missing OrderData')
    }

    if (!cartData) {
      throw Error('Missing sessionId')
    }
    const $checkoutButton = $('button.checkoutWithPaypal')
    saveCheckoutPageUrlToLocalStorage()

    const callbackWrapper = function (error, result) {
      if (!error) {
        console.log('CHECK_OUT_PAYPAL SUCCESS')
        EcomUtils.hideLoading()
        const orderData = getOrderData(orderCheckoutData)
        saveOrderInfoSuccess({
          result,
          cartData,
          orderData
        })

        if (window.location.href.indexOf('?') > 0) {
          window.localStorage.setItem(CACHE_KEYS.PARAMS_CONFIRM_PAGE, location.href.split('?')[1])
        }

        const redirectUrl = getPaypalRedirectUrl(result)
        redirectPage(appendCtaClickIdTrackingParam(redirectUrl + location.search, $checkoutButton))
      } else {
        console.log(false, 'checkout failed')
        // goto decline
        const declineUrl =
          window.__ctrPageConfiguration && window.__ctrPageConfiguration.declineUrl ? window.__ctrPageConfiguration.declineUrl + location.search : ''
        redirectPage(appendCtaClickIdTrackingParam(declineUrl, $checkoutButton))
      }
      typeof next === 'function' && next(error, result)
    }
    EcomUtils.showLoading()
    const orderData = {
      ...orderCheckoutData
    }
    if (!orderData.payment) {
      orderData.payment = {}
    }
    orderData.payment.callBackParam = mergeCurrentParamsWithCtaClickIdTrackingParam($checkoutButton)
    EcomJS().Checkout.PaypalPayment.placeOrder(cartData.sessionId, orderData, callbackWrapper)
  } catch (e) {
    console.error('checkoutByPaypal error: ', e)
  }
}

function getOrderData(formCheckoutData) {
  if (!formCheckoutData) {
    return
  }
  const { customer = {}, payment = {}, shippingAddress = {}, billingAddress = {} } = formCheckoutData
  const orderData = {}

  orderData.useShippingAddressForBilling = !Object.keys(billingAddress).length
  orderData.billingAddress = null
  if (!orderData.useShippingAddressForBilling) {
    orderData.billingAddress = {
      address1: billingAddress.address1,
      address2: billingAddress.address2,
      city: billingAddress.city,
      zipCode: billingAddress.zipCode,
      state: billingAddress.state,
      countryCode: billingAddress.countryCode,
      firstName: customer.firstName,
      lastName: customer.lastName,
      phoneNumber: customer.phoneNumber
    }
  }
  orderData.shippingAddress = {
    address1: shippingAddress.address1,
    address2: shippingAddress.address2,
    city: shippingAddress.city,
    zipCode: shippingAddress.zipCode,
    state: shippingAddress.state,
    countryCode: shippingAddress.countryCode,
    firstName: customer.firstName,
    lastName: customer.lastName,
    phoneNumber: customer.phoneNumber
  }
  orderData.payment = {
    name: `${customer.firstName} ${customer.lastName}`,
    creditcard: payment.creditcard,
    creditCardBrand: 'Visa',
    expiration: payment.expiration,
    cvv: payment.cvv
  }
  orderData.email = customer.email
  return orderData
}

function saveOrderInfoSuccess({ result, cartData, orderData }) {
  const address1 = orderData.shippingAddress.address1
  const city = orderData.shippingAddress.city
  const $selectStateBilling = document.querySelector('form[name="billingAddress"] select[name="state"] option:checked')
  const $selectCountryBilling = document.querySelector('form[name="billingAddress"] select[name="countryCode"] option:checked')

  const $selectCountryShipping = document.querySelector('form[name="shippingAddress"] select[name="countryCode"] option:checked')
  const $selectStateShipping = document.querySelector('form[name="shippingAddress"] select[name="state"] option:checked')

  const billingStateName = $selectStateBilling ? $selectStateBilling.text : ''
  const billingCountryName = $selectCountryBilling ? $selectCountryBilling.text : ''

  const shippingStateName = $selectCountryShipping ? $selectCountryShipping.text : ''
  const shippingCountryName = $selectStateShipping ? $selectStateShipping.text : ''

  let priceType = 'price'
  if (cartData.couponCode && cartData.couponCode !== '') {
    priceType = 'appliedCouponPrice'
  }
  const orderInfoSuccess = {
    products: cartData.items,
    cartNumber: result.shoppingCartNumber,
    customerAddress: `${address1}, ${city}, ${shippingCountryName}`,
    customerCountryName: `${shippingCountryName}`,
    customerStateName: `${shippingStateName}`,
    billingStateName: `${billingStateName}`,
    billingCountryName: `${billingCountryName}`,
    customerId: result.customerResult ? result.customerResult.customerId : '',

    paymentMethod: result.descriptor,
    couponCode: cartData.couponCode ? cartData.couponCode : '',
    shippingAddress: orderData.shippingAddress,
    billingAddress: orderData.billingAddress,
    email: orderData.email,
    orderTotalFull: getTotalPrice(cartData.items, priceType)
  }
  return window.localStorage.setItem(CACHE_KEYS.ORDER_INFO, JSON.stringify(orderInfoSuccess))
}

function updatePaypalOrderInforSuccessLocalstorage(paypalApproveResult) {
  try {
    const result = { ...paypalApproveResult }
    const orderCheckoutSuccessInfo = JSON.parse(window.localStorage.getItem(CACHE_KEYS.ORDER_INFO))
    const shippingAddress = {
      address1: result.address1 ? result.address1 : '',
      address2: result.address2 ? result.address2 : '',
      city: result.city ? result.city : '',
      countryCode: '',
      customerCountryName: result.country ? result.country : '',
      firstName: result.firstName ? result.firstName : '',
      lastName: result.lastName ? result.lastName : '',
      phoneNumber: '',
      state: '',
      stateName: result.state ? result.state : '',
      zipCode: result.zipCode ? result.zipCode : ''
    }

    const orderInfoSuccess = {
      ...orderCheckoutSuccessInfo,
      customerAddress: `${shippingAddress.address1}, ${shippingAddress.city}, ${shippingAddress.customerCountryName}`,
      customerCountryName: `${shippingAddress.customerCountryName}`,
      billingCountryName: `${shippingAddress.customerCountryName}`,
      customerStateName: `${shippingAddress.state}`,
      billingStateName: `${shippingAddress.state}`,
      shippingAddress: shippingAddress,
      billingAddress: shippingAddress,
      email: result.email ? result.email : ''
    }
    return window.localStorage.setItem(CACHE_KEYS.ORDER_INFO, JSON.stringify(orderInfoSuccess))
  } catch (e) {
    console.warn('updatePaypalOrderInforSuccessLocalstorage error', e)
  }
}

function checkoutByCreditCard(formCheckoutData, next, isNotAutoRedirect) {
  try {
    const $checkoutWithCreditCardButton = $('button[name="checkoutWithCreditCard"]')
    const cartData = getCartData()
    if (!formCheckoutData) {
      throw Error('Missing OrderData')
    }

    if (!cartData) {
      throw Error('Missing sessionId')
    }
    const orderData = getOrderData(formCheckoutData)
    saveCheckoutPageUrlToLocalStorage()
    const callbackWrapper = function (error, result) {
      try {
        window.localStorage.setItem('ctr_ecom__checkout_logs', JSON.stringify({ error, result, formCheckoutData }))
      } catch (e) {}

      // EcomUtils.hideLoading()
      if (!error && result.success) {
        console.log(false, 'checkout ok')
        saveOrderInfoSuccess({ result, orderData, cartData })
        clearCartDataStorage()
        const confirmUrl =
          window.__ctrPageConfiguration && window.__ctrPageConfiguration.confirmUrl ? window.__ctrPageConfiguration.confirmUrl + location.search : ''
        const confirmUrlWithParams = appendCtaClickIdTrackingParam(confirmUrl, $checkoutWithCreditCardButton)
        if(isNotAutoRedirect) {
          next(false, {redirectUrl: confirmUrlWithParams})
        } else {
          redirectPage(confirmUrlWithParams)
        }
      } else {
        console.log(false, 'checkout faile')
        // goto decline
        const declineUrl =
          window.__ctrPageConfiguration && window.__ctrPageConfiguration.declineUrl ? window.__ctrPageConfiguration.declineUrl + location.search : ''
        const declineUrlWithParams = appendCtaClickIdTrackingParam(declineUrl, $checkoutWithCreditCardButton)
        if(isNotAutoRedirect) {
          next(true, {redirectUrl: declineUrlWithParams})
        } else {
          redirectPage(declineUrlWithParams)
        }
      }
      // typeof next === 'function' && next(error, result)
    }
    EcomUtils.showLoading()
    return EcomJS().Checkout.CreditCardPayment.placeOrder(cartData.sessionId, orderData, callbackWrapper)
  } catch (e) {
    console.error('checkoutByCreditCard error: ', e)
    next && next(true, e)
  }
}

function couponApi(couponCode) {
  const cartData = getCartData()
  const path = `shoppingcart/${webKey}/applycoupon?sessionId=${cartData.sessionId}&couponCode=${couponCode}`
  const couponApi = webSaleApiRequest(path, {
    method: 'POST'
  })
  return couponApi
}

function applyCoupon(couponCode, next) {
  function handleError(errorMessage) {
    EcomEvent.applyCoupon.dispatch({ error: true, data: { errorMessage } })
    typeof next === 'function' && next(true, errorMessage)
  }

  try {
    if (!couponCode) {
      throw new Error('Missing couponCode')
    }
    const cartData = getCartData()
    if (!cartData) {
      throw new Error('Missing SessionId')
    }
    EcomUtils.showLoading()
    const defaultErrorMessage = {
      message: 'Sorry, that discount code is either invalid or expired.',
      messageDetail: 'Sorry, that discount code is either invalid or expired.'
    }
    couponApi(couponCode)
      .then(function (result) {
        console.log('APPLY_COUPON DONE', { result })
        if (result && result.couponCode) {
          console.log('APPLY_COUPON SUCCESS')
          const cartData = getCartData()
          cartData.couponCode = result.couponCode
          cartData.couponDescription = result.couponDescription
          if (cartData.items && Array.isArray(cartData.items)) {
            cartData.items = cartData.items.map(function (pt) {
              const productAppliedCoupon = result.products.find(function (pac) {
                return Number(pac.productId) === Number(pt.productId)
              })
              pt.appliedCouponPrice = productAppliedCoupon ? Number(productAppliedCoupon.price) : 0
              pt.originalProductPrice = productAppliedCoupon ? Number(productAppliedCoupon.originalProductPrice) : 0
              pt.formatedAppliedCouponPrice = pt.priceFormatted.replace(pt.discountedPrice, pt.appliedCouponPrice)
              pt.discountedAppliedAmount = Number(pt.discountedPrice) - Number(pt.appliedCouponPrice)
              pt.formatedDiscountedAppliedAmount = pt.priceFormatted.replace(pt.discountedPrice, pt.discountedAppliedAmount)
              return pt
            })
          }
          window.localStorage.setItem(CACHE_KEYS.CART, JSON.stringify(cartData))
          window.localStorage.setItem(CACHE_KEYS.DOTCMS_CART, JSON.stringify(cartData))
          EcomEvent.applyCoupon.dispatch({ error: false, data: { cartData } })
          typeof next === 'function' && next(false, result)
        } else {
          handleError({
            message: result.message || defaultErrorMessage.message,
            messageDetail: result.messageDetail || defaultErrorMessage.messageDetail
          })
        }
      })
      .catch(function (rs) {
        console.log('applyCoupon error', { rs })
        handleError(defaultErrorMessage)
      })
      .finally(function () {
        EcomUtils.hideLoading()
      })
  } catch (e) {
    console.error('applyCoupon error: ', e)
    EcomUtils.hideLoading()
  }
}

function jsonStringToObj(str) {
  try {
    return JSON.parse(str)
  } catch (e) {}
  return {}
}

export function checkPaypalApprove(next) {
  try {
    const $checkoutButton = $('button.checkoutWithPaypal')
    const callbackWrapper = function (error, result) {
      EcomUtils.hideLoading()
      const __ctrPageConfiguration = jsonStringToObj(window.localStorage.getItem(CACHE_KEYS.CTR_PAGE_CONFIGURE))

      if (!error) {
        console.log('CHECK_OUT_PAYPAL_APPROVE SUCCESS', { result })
        window.localStorage.setItem('ctr_ecom_checkout_paypal_log', JSON.stringify({ result }))
        updatePaypalOrderInforSuccessLocalstorage(result)
        let confirmUrl = __ctrPageConfiguration.confirmUrl
        const paramsForConfirmPage = window.localStorage.getItem(CACHE_KEYS.PARAMS_CONFIRM_PAGE)
        if (paramsForConfirmPage) {
          confirmUrl += confirmUrl.indexOf('?') > 0 ? '&' + paramsForConfirmPage : '?' + paramsForConfirmPage
        }
        redirectPage(appendCtaClickIdTrackingParam(confirmUrl, $checkoutButton))
      } else {
        redirectPage(appendCtaClickIdTrackingParam(__ctrPageConfiguration.declineUrl, $checkoutButton))
      }
      typeof next === 'function' && next(error, result)
    }
    EcomUtils.showLoading()
    EcomJS().Checkout.checkPaypalApprove(callbackWrapper)
  } catch (e) {
    console.error('checkPaypalApprove error: ', e)
  }
}

export function clearAllStorage() {
  window.localStorage.removeItem(CACHE_KEYS.CART)
  window.localStorage.removeItem(CACHE_KEYS.DOTCMS_CART)
  window.localStorage.removeItem(CACHE_KEYS.ORDER_INFO)
}

export function clearCartDataStorage() {
  window.localStorage.removeItem(CACHE_KEYS.CART)
  window.localStorage.removeItem(CACHE_KEYS.DOTCMS_CART)
}

export function clearOrderCheckoutDataStorage() {
  window.localStorage.removeItem(CACHE_KEYS.ORDER_INFO)
}

function getTotalPrice(cartItems, priceType = 'price') {
  let total = 0
  try {
    cartItems.forEach((item) => {
      const constItemPriceType = item[priceType] ? item[priceType] : item['discountedPrice']
      total += Number(constItemPriceType) * Number(item.quantity)
    })
  } catch (e) {}
  return total
}
function getTotalQuantity(cartItems) {
  try {
    return cartItems.reduce((accumulator, item) => {
      return accumulator + Number(item.quantity)
    }, 0)
  } catch (e) {}
  return 0
}

function getOrderSummary() {
  const rs = {}
  try {
    let orderCheckoutSuccessInfo = null
    if (window.localStorage.getItem(CACHE_KEYS.ORDER_INFO)) {
      orderCheckoutSuccessInfo = JSON.parse(window.localStorage.getItem(CACHE_KEYS.ORDER_INFO))
    } else {
      const cartData = getCartData()
      orderCheckoutSuccessInfo = {
        products: cartData.items,
        couponCode: cartData.couponCode ? cartData.couponCode : '',
        orderTotalFull: getTotalPrice(cartData.items, cartData.couponCode ? 'appliedCouponPrice' : 'price')
      }
    }

    let priceType = 'price'
    let shippingPrice = 0
    const tax = 0
    let currencyCode = ''
    let countryCode = ''
    // TODO: lam
    const shippingMethod = 'FREE'
    const { shippingAddress = {}, billingAddress = {}, products = [], cartNumber = '', paymentMethod = '' } = orderCheckoutSuccessInfo

    if (orderCheckoutSuccessInfo.couponCode && orderCheckoutSuccessInfo.couponCode !== '') {
      priceType = 'appliedCouponPrice'
    }

    let subTotal = 0
    let applyCouponTotal = 0

    rs.items = products.map((item) => {
      // total += item[priceType] * Number(item.quantity)
      const itemPrice = item[priceType] || item['discountedPrice']
      const totalPrice = itemPrice * (item.isSwitchPID ? 1 : Number(item.quantity))

      applyCouponTotal = applyCouponTotal + totalPrice
      subTotal = subTotal + Number(item.discountedPrice) * (item.isSwitchPID ? 1 : Number(item.quantity))

      currencyCode = item.currencyCode || ctrwowUtils.localStorage().get('currencyCode')
      countryCode = item.countryCode || ctrwowUtils.localStorage().get('ctr__countryCode')

      if (item.hasOwnProperty('shippingPriceValue')) {
        shippingPrice += item.shippingPriceValue
      }

      return {
        ...item,
        productName: item.productName,
        quantity: item.quantity,
        // price: `${formatCurrencyNumber(item[priceType], countryCode, currencyCode)}`,
        price: `${formatCurrencyNumber(itemPrice, countryCode, currencyCode)}`,
        totalPrice: `${formatCurrencyNumber(totalPrice, countryCode, currencyCode)}`,
        thumbnailUrl: item.productImageUrl,
        shippingPrice: item.shippingPriceValue === 0 ? 'FREE' : item.shippingPrice
      }
    })
    const shippingPriceFormated = formatCurrencyNumber(shippingPrice, countryCode, currencyCode)
    const o_date = new Intl.DateTimeFormat()
    const f_date = (m_ca, m_it) => Object({ ...m_ca, [m_it.type]: m_it.value })
    const m_date = o_date.formatToParts().reduce(f_date, {})
    const applyCouponDiscountTotal = subTotal - applyCouponTotal
    rs.cart = {
      orderNumber: cartNumber,
      orderDate: m_date.day + '-' + m_date.month + '-' + m_date.year,
      orderDateYyyyMmDd: m_date.year + '-' + m_date.month + '-' + m_date.day,
      orderTotal: `${formatCurrencyNumber(subTotal + tax + shippingPrice - applyCouponDiscountTotal, countryCode, currencyCode)}`,
      customerInfo: {
        shippingInfo: {
          customerName: `${shippingAddress.firstName} ${shippingAddress.lastName}`,
          address1: shippingAddress.address1,
          city: shippingAddress.city,
          zip: shippingAddress.zipCode,
          state: orderCheckoutSuccessInfo.customerStateName,
          country: orderCheckoutSuccessInfo.customerCountryName,
          method: shippingMethod
        },
        paymentMethod,
        customerEmail: orderCheckoutSuccessInfo.email
      },
      products: rs.items,
      subTotal: `${formatCurrencyNumber(subTotal + tax - applyCouponDiscountTotal, countryCode, currencyCode)}`,
      shipping: `${formatCurrencyNumber(shippingPrice, countryCode, currencyCode)}`,
      taxes: `${formatCurrencyNumber(tax, countryCode, currencyCode)}`,
      total: `${formatCurrencyNumber(subTotal + tax + shippingPrice - applyCouponDiscountTotal, countryCode, currencyCode)}`,
      applyCouponDiscountTotal: `${formatCurrencyNumber(applyCouponDiscountTotal, countryCode, currencyCode)}`,
      isApplyCouponPrice: priceType === 'appliedCouponPrice',
      customerEmail: orderCheckoutSuccessInfo.email,
      shippingPrice: shippingPrice === 0 ? 'FREE': shippingPriceFormated,
      paymentMethod: orderCheckoutSuccessInfo.paymentMethod || ''
    }

    if (billingAddress) {
      rs.cart.customerInfo.billingInfo = {
        customerName: `${billingAddress.firstName} ${billingAddress.lastName}`,
        address1: billingAddress.address1,
        city: billingAddress.city,
        zip: billingAddress.zipCode,
        state: orderCheckoutSuccessInfo.billingStateName,
        country: orderCheckoutSuccessInfo.billingCountryName
      }
    } else {
      rs.cart.customerInfo.billingInfo = rs.cart.customerInfo.shippingInfo
    }
  } catch (e) {}
  return rs
}

function getProductPriceFormatted(productInfo) {
  const price = productInfo.productPrices && productInfo.productPrices.DiscountedPrice ? productInfo.productPrices.DiscountedPrice.FormattedValue : ''
  return price
}

function getProductPriceValue(productInfo) {
  const price = productInfo.productPrices && productInfo.productPrices.DiscountedPrice ? productInfo.productPrices.DiscountedPrice.Value : ''
  return price
}

function getFullPriceFormatted(productInfo) {
  const price = productInfo.productPrices && productInfo.productPrices.FullRetailPrice ? productInfo.productPrices.FullRetailPrice.FormattedValue : ''
  return price
}

function getFullPriceValue(productInfo) {
  const price = productInfo.productPrices && productInfo.productPrices.FullRetailPrice ? productInfo.productPrices.FullRetailPrice.Value : ''
  return price
}

function signUp(data, callback) {
  if (!data) {
    throw 'Missing Data'
  }

  const crud = {
    createCRMCustomerInfo: function (webkey, payload) {
      const headers = {
        'content-type': 'application/json'
      }
      if (window.__CTR_ECOM_CONFIG && window.__CTR_ECOM_CONFIG.cid) {
        headers.X_CID = window.__CTR_ECOM_CONFIG.cid
      }
      const endpoint = window.ctrwowUtils.getCrmBaseUrl() + '/leadgens/' + webkey
      return global.ctrwowUtils
        .callAjax(endpoint, {
          method: 'POST',
          body: JSON.stringify(payload),
          headers: headers
        })
        .catch((e) => {
          console.warn(e)
        })
    }
  }

  EcomUtils.showLoading()
  window.ctrwowUtils.getUserAnalyticsInfo().then(function (analyticsV2Data) {
    console.log(analyticsV2Data)
    const payload = {
      isFromEmailWidget: true,
      ...data,
      email: data.email,
      analyticsV2: analyticsV2Data
    }
    crud
      .createCRMCustomerInfo(webKey, payload)
      .then(function () {
        // window.localStorage.setItem('_CTR_LEADGEN__EMAIL_COLLECTION_EMAIL', emailValue);
        callback(true)
      })
      .catch(function (e) {
        callback(false)
      })
      .finally(EcomUtils.hideLoading)
  })
}

function isCheckoutPage() {
  return window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.PAGE_TYPE === 2
}

function saveCtrPageConfig() {
  window.localStorage.setItem(CACHE_KEYS.CTR_PAGE_CONFIGURE, JSON.stringify(__ctrPageConfiguration))
}

function saveCheckoutPageUrlToLocalStorage(url) {
  saveCtrPageConfig()
  window.localStorage.setItem(CACHE_KEYS.CHECKOUT_PAGE_URL, url || window.location.origin + window.location.pathname)
}

function getShippingFee(cartItems, priceType = 'shippingPriceValue') {
  if (!cartItems) { return Number('0.00') }

  let total = 0
  try {
    cartItems.forEach((item) => {
      if (item.hasOwnProperty(priceType)) {
        total += item[priceType]
      }
    })
  } catch (e) {}
  return total
}

function register(data, callback) {
  if (!data) {
    throw 'Missing Data'
  }
  // TODO
  console.log('register', data)
  callback && callback(data)
}

function getEcomJS() {
  return EcomJS()
}

function setElementFromUrl(element, messageUri) {
  try {
    fetch(messageUri)
      .then((response) => response.text())
      .then((string) => {
        element.innerHTML = string.trim()
      })
  } catch (ex) {
    element.innerHTML = ''
  }
}

function isOverMaxOfQuantity(productId, quantity, maxQuantity) {
  const cartData = getCartData()

  if (Number(quantity) > Number(maxQuantity)) {
    return true
  }

  if (!cartData || !cartData.items || !cartData.items.length || !maxQuantity) {
    return false
  }
  const product = cartData.items.find((item) => item.productId.toString() === productId.toString())
  return product && Number(product.quantity) + Number(quantity) > Number(maxQuantity)
}

function checkoutByOtherPayment(formCheckoutData, next, isNotAutoRedirect) {
  try {
    const $checkoutWithCreditCardButton = $('button[name="checkoutWithCreditCard"]')
    const cartData = getCartData()
    if (!formCheckoutData) {
      throw Error('Missing OrderData')
    }

    if (!cartData) {
      throw Error('Missing sessionId')
    }
    // const orderData = getOrderData(formCheckoutData)
    const orderData = formCheckoutData
    saveCheckoutPageUrlToLocalStorage()
    const callbackWrapper = function (error, result) {
      try {
        window.localStorage.setItem('ctr_ecom__checkout_logs', JSON.stringify({ error, result, formCheckoutData }))
      } catch (e) {}

      // EcomUtils.hideLoading()
      if (!error && result.success) {
        console.log(false, 'checkout ok')
        saveOrderInfoSuccess({ result, orderData, cartData })
        clearCartDataStorage()
        const confirmUrl =
          window.__ctrPageConfiguration && window.__ctrPageConfiguration.confirmUrl ? window.__ctrPageConfiguration.confirmUrl + location.search : ''
        const confirmUrlWithParams = appendCtaClickIdTrackingParam(confirmUrl, $checkoutWithCreditCardButton)
        if(isNotAutoRedirect) {
          next(false, {redirectUrl: confirmUrlWithParams})
        } else {
          redirectPage(confirmUrlWithParams)
        }
      } else {
        console.log(false, 'checkout fail')
        // goto decline
        const declineUrl =
          window.__ctrPageConfiguration && window.__ctrPageConfiguration.declineUrl ? window.__ctrPageConfiguration.declineUrl + location.search : ''
        const declineUrlWithParams = appendCtaClickIdTrackingParam(declineUrl, $checkoutWithCreditCardButton)
        if(isNotAutoRedirect) {
          next(true, {redirectUrl: declineUrlWithParams})
        } else {
          redirectPage(declineUrlWithParams)
        }
      }
      // typeof next === 'function' && next(error, result)
    }
    EcomUtils.showLoading()
    return EcomJS().Checkout.CreditCardPayment.placeOrder(cartData.sessionId, orderData, callbackWrapper)
  } catch (e) {
    console.error('checkoutByOther error: ', e)
    next && next(true, e)
  }
}
export const helpers = {
  addCartItem,
  removeCartItem,
  updateCartItemQuantity,
  checkPaypalApprove,
  applyCoupon,
  checkoutByCreditCard,
  getCartData,
  checkoutByPaypal,
  getProductPrices,
  getProductPriceById,
  getCampaignProductPrices,
  getOrderSummary,
  getProductPriceFormatted,
  getProductPriceValue,
  getFullPriceFormatted,
  getFullPriceValue,
  signUp,
  clearAllStorage,
  clearCartDataStorage,
  clearOrderCheckoutDataStorage,
  isCheckoutPage,
  getTotalPrice,
  getTotalQuantity,
  saveCheckoutPageUrlToLocalStorage,
  getShippingFee,
  register,
  getEcomJS,
  formatCurrencyNumber,
  setElementFromUrl,
  saveCtrPageConfig,
  isOverMaxOfQuantity,
  getProductPageUrl,
  addProductVariantToCartItem,
  checkoutByOtherPayment,
  saveOrderInfoSuccess,
  getOrderData,
  getPaypalRedirectUrl
}
