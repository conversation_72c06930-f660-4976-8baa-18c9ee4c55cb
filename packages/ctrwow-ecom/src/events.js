export const ECOM_EVENT = {
  addCartItem: 'addCartItem',
  removeCartItem: 'removeCartItem',
  updateCartItemQuantity: 'updateCartItemQuantity',
  applyCoupon: 'applyCoupon',
  openCart: 'openCart'
}

function subscribe(topic, callback) {
  // const token = window.PubSub.subscribe(topic, callback)
  console.log('event subscribe', { topic })
  // window.PubSub.unsubscribe(token)
  // window.PubSub.subscribe(topic, callback)
  window.ctrwowUtils.events.off(topic)
  window.ctrwowUtils.events.on(topic, callback)
}

function publish(topic, data) {
  console.log('event publish', { topic, data })
  // window.PubSub.publishSync(topic, data)
  window.ctrwowUtils.events.emit(topic, data)
}

function createEvent(eventName) {
  return {
    onComplete: (callback) => {
      subscribe(`Ecom_${eventName}`, callback)
    },
    dispatch: (data) => {
      publish(`Ecom_${eventName}`, data)
    }
  }
}

export const addCartItem = createEvent(ECOM_EVENT.addCartItem)
export const removeCartItem = createEvent(ECOM_EVENT.removeCartItem)
export const updateCartItemQuantity = createEvent(ECOM_EVENT.updateCartItemQuantity)
export const applyCoupon = createEvent(ECOM_EVENT.applyCoupon)
export const openCart = createEvent(ECOM_EVENT.openCart)

export const events = {
  addCartItem,
  removeCartItem,
  updateCartItemQuantity,
  applyCoupon,
  openCart
}
