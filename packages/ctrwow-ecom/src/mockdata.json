{"campaignName": "DFY Daily EN eCommerce", "prices": [{"productId": 312, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TV Radius <PERSON>", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30005", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 522, "shippingName": "[All Categories] $14.99 Shipping - 1 Unit", "price": 14.99, "formattedPrice": "$14.99"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 35.74, "FormattedValue": "$35.74", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 54.99, "FormattedValue": "$54.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 35.74, "FormattedValue": "$35.74", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 314, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Amplifier", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30006", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 34.99, "FormattedValue": "$34.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 69.98, "FormattedValue": "$69.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 34.99, "FormattedValue": "$34.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 302, "externalProductId": 0, "externalProductVariantId": 0, "productName": "CommandoBeam", "productTypeName": "Electronics", "productDisplayName": null, "sku": "21515", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 522, "shippingName": "[All Categories] $14.99 Shipping - 1 Unit", "price": 14.99, "formattedPrice": "$14.99"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 67.49, "FormattedValue": "$67.49", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 67.49, "FormattedValue": "$67.49", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 432, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Xenatorch", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30014", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 34.5, "FormattedValue": "$34.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 46.0, "FormattedValue": "$46.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 34.5, "FormattedValue": "$34.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 528, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Uberfix MD", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30027", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 74.74, "FormattedValue": "$74.74", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 35.0, "FormattedValue": "$35.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 114.99, "FormattedValue": "$114.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 74.74, "FormattedValue": "$74.74", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 533, "externalProductId": 0, "externalProductVariantId": 0, "productName": "<PERSON><PERSON>", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30028", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 79.98, "FormattedValue": "$79.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 50.0, "FormattedValue": "$50.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 659, "externalProductId": 0, "externalProductVariantId": 0, "productName": "EmergBreak", "productTypeName": "Tech", "productDisplayName": null, "sku": "30031", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 14.99, "FormattedValue": "$14.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 29.98, "FormattedValue": "$29.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 14.99, "FormattedValue": "$14.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 125, "externalProductId": 0, "externalProductVariantId": 0, "productName": "HDZoom360", "productTypeName": "Mobile", "productDisplayName": null, "sku": "30015", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 79.99, "FormattedValue": "$79.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 620, "externalProductId": 0, "externalProductVariantId": 0, "productName": "MirrorCast Media", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30048", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 67.5, "FormattedValue": "$67.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 90.0, "FormattedValue": "$90.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 67.5, "FormattedValue": "$67.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 427, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Tigress <PERSON>", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30013", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 25.99, "FormattedValue": "$25.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 51.98, "FormattedValue": "$51.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 25.99, "FormattedValue": "$25.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 653, "externalProductId": 0, "externalProductVariantId": 0, "productName": "NuWiper", "productTypeName": "Tech", "productDisplayName": null, "sku": "30049", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 14.99, "FormattedValue": "$14.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 24.99, "FormattedValue": "$24.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 14.99, "FormattedValue": "$14.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 642, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Exposure Optics", "productTypeName": "Tech", "productDisplayName": null, "sku": "30039", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 47.99, "FormattedValue": "$47.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 79.99, "FormattedValue": "$79.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 47.99, "FormattedValue": "$47.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 648, "externalProductId": 0, "externalProductVariantId": 0, "productName": "<PERSON>", "productTypeName": "Tech", "productDisplayName": null, "sku": "30043", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 24.99, "FormattedValue": "$24.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 49.98, "FormattedValue": "$49.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 24.99, "FormattedValue": "$24.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 388, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Raptor VR", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30011", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 66.75, "FormattedValue": "$66.75", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 89.0, "FormattedValue": "$89.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 66.75, "FormattedValue": "$66.75", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 393, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Raptor VR Controller", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30012", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 29.7, "FormattedValue": "$29.70", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 33.0, "FormattedValue": "$33.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 29.7, "FormattedValue": "$29.70", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 691, "externalProductId": 0, "externalProductVariantId": 0, "productName": "CircaCharge", "productTypeName": "Car Accessories", "productDisplayName": null, "sku": "30059", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 99.99, "FormattedValue": "$99.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 235, "externalProductId": 0, "externalProductVariantId": 0, "productName": "SecureWatch365", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30001", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 79.49, "FormattedValue": "$79.49", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 105.99, "FormattedValue": "$105.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 79.49, "FormattedValue": "$79.49", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 692, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TapNCharge", "productTypeName": "Mobile", "productDisplayName": null, "sku": "30058", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 59.97, "FormattedValue": "$59.97", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 673, "externalProductId": 0, "externalProductVariantId": 0, "productName": "BriteGrip", "productTypeName": "Tech", "productDisplayName": null, "sku": "30057", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Treiss Pro", "productTypeName": "Mobile", "productDisplayName": null, "sku": "30018", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.0, "FormattedValue": "$39.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 60.0, "FormattedValue": "$60.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.0, "FormattedValue": "$39.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 375, "externalProductId": 0, "externalProductVariantId": 0, "productName": "LightStrike", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30002", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 56.24, "FormattedValue": "$56.24", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 56.24, "FormattedValue": "$56.24", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 224.99, "FormattedValue": "$224.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 251, "externalProductId": 0, "externalProductVariantId": 0, "productName": "ArcStrike", "productTypeName": "Electronics", "productDisplayName": null, "sku": "21543", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 142.99, "FormattedValue": "$142.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 313, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TVRoxx", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30022", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 152.99, "FormattedValue": "$152.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 444, "externalProductId": 0, "externalProductVariantId": 0, "productName": "EZ StreamPad", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30023", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 99.98, "FormattedValue": "$99.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 564, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TrustyCharge", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30021", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.39, "FormattedValue": "$49.39", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 35.0, "FormattedValue": "$35.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 75.99, "FormattedValue": "$75.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.39, "FormattedValue": "$49.39", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 760, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TrendyTechMedia", "productTypeName": "Home ", "productDisplayName": null, "sku": "30078", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 41.99, "FormattedValue": "$41.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 41.99, "FormattedValue": "$41.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 571, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Car Ozonator", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30036", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 50.0, "FormattedValue": "$50.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 59.98, "FormattedValue": "$59.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 757, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TrendyTechMedia", "productTypeName": "Tech", "productDisplayName": null, "sku": "30088", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 33.99, "FormattedValue": "$33.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 56.65, "FormattedValue": "$56.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 33.99, "FormattedValue": "$33.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 759, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TrendyTechMedia", "productTypeName": "Home ", "productDisplayName": null, "sku": "30077", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 47.99, "FormattedValue": "$47.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 79.99, "FormattedValue": "$79.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 47.99, "FormattedValue": "$47.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 630, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Portable Speaker Disc", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30045", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 25.0, "FormattedValue": "$25.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 50.0, "FormattedValue": "$50.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 50.0, "FormattedValue": "$50.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 25.0, "FormattedValue": "$25.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 898, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Door Defense", "productTypeName": "Home ", "productDisplayName": null, "sku": "30125", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 42.99, "FormattedValue": "$42.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 42.99, "FormattedValue": "$42.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 71.65, "FormattedValue": "$71.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 804, "externalProductId": 0, "externalProductVariantId": 0, "productName": "BuzzBGone", "productTypeName": "Home ", "productDisplayName": null, "sku": "30107", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 66.65, "FormattedValue": "$66.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 758, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TrendyTechMedia", "productTypeName": "Car Accessories", "productDisplayName": null, "sku": "30034", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 59.95, "FormattedValue": "$59.95", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 92.23, "FormattedValue": "$92.23", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 59.95, "FormattedValue": "$59.95", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 123, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TruLight", "productTypeName": "Electronics", "productDisplayName": null, "sku": "21565", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 85.5, "FormattedValue": "$85.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 95.0, "FormattedValue": "$95.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 85.5, "FormattedValue": "$85.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 814, "externalProductId": 0, "externalProductVariantId": 0, "productName": "CircaCharge", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30073", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 9.99, "FormattedValue": "$9.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 19.98, "FormattedValue": "$19.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 9.99, "FormattedValue": "$9.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 834, "externalProductId": 0, "externalProductVariantId": 0, "productName": "CircaCharge", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30075", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 9.99, "FormattedValue": "$9.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 9.99, "FormattedValue": "$9.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 19.98, "FormattedValue": "$19.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 816, "externalProductId": 0, "externalProductVariantId": 0, "productName": "CircaCharge", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30074", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 9.99, "FormattedValue": "$9.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 19.98, "FormattedValue": "$19.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 9.99, "FormattedValue": "$9.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 888, "externalProductId": 0, "externalProductVariantId": 0, "productName": "LiveWave", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30108", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.95, "FormattedValue": "$39.95", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 61.46, "FormattedValue": "$61.46", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.95, "FormattedValue": "$39.95", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 401, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Micro SD Card (32GB)", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30003", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 40.5, "FormattedValue": "$40.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 40.5, "FormattedValue": "$40.50", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 45.0, "FormattedValue": "$45.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 849, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Germ Fix", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30113", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 6.99, "FormattedValue": "$6.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 58.32, "FormattedValue": "$58.32", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 6.99, "FormattedValue": "$6.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 879, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Fever Patrol", "productTypeName": "Health", "productDisplayName": null, "sku": "30117", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 66.65, "FormattedValue": "$66.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 948, "externalProductId": 0, "externalProductVariantId": 0, "productName": "LiveWave", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30109", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 34.99, "FormattedValue": "$34.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 69.98, "FormattedValue": "$69.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 34.99, "FormattedValue": "$34.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 664, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Aqua Pure Straw", "productTypeName": "Health", "productDisplayName": null, "sku": "30053", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 29.99, "FormattedValue": "$29.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1011, "externalProductId": 0, "externalProductVariantId": 0, "productName": "InfinitiKloud", "productTypeName": "Tech", "productDisplayName": null, "sku": "30171", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 149.98, "FormattedValue": "$149.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1086, "externalProductId": 0, "externalProductVariantId": 0, "productName": "InfinitiKloud", "productTypeName": "Tech", "productDisplayName": null, "sku": "30173", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 83.32, "FormattedValue": "$83.32", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1116, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Wifi Booster + AU & UK Plugs", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30082_30161_30162_1", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 3, "salesWorkflowId": 11, "parentProductId": null, "shippings": [{"shippingMethodId": 519, "shippingName": "[All Categories] Free Shipping  - 3 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 83.32, "FormattedValue": "$83.32", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 764, "externalProductId": 0, "externalProductVariantId": 0, "productName": "SuperBoost", "productTypeName": "Home ", "productDisplayName": null, "sku": "30082", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 83.32, "FormattedValue": "$83.32", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 986, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Wifi Booster AU Plug", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30161", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 987, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Wifi Booster UK Plug", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30162", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 5.0, "FormattedValue": "$5.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1184, "externalProductId": 0, "externalProductVariantId": 0, "productName": "MaiCharging", "productTypeName": "LightTrack", "productDisplayName": null, "sku": "30216", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 69.99, "FormattedValue": "$69.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 116.65, "FormattedValue": "$116.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 69.99, "FormattedValue": "$69.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1272, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Fotialamp", "productTypeName": "LightTrack", "productDisplayName": "Fotialamp", "sku": "30226", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 64.99, "FormattedValue": "$64.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 108.32, "FormattedValue": "$108.32", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 64.99, "FormattedValue": "$64.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1173, "externalProductId": 0, "externalProductVariantId": 0, "productName": "QuietBuds", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30209", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 9, "shippingName": "Free Shipping - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 66.65, "FormattedValue": "$66.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1174, "externalProductId": 0, "externalProductVariantId": 0, "productName": "QuietBuds (Rose Gold)", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30210", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 9, "shippingName": "Free Shipping - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 99.98, "FormattedValue": "$99.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 59.99, "FormattedValue": "$59.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1322, "externalProductId": 0, "externalProductVariantId": 0, "productName": "RangeXTD Router", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30251", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 9, "shippingName": "Free Shipping - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.95, "FormattedValue": "$49.95", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 99.9, "FormattedValue": "$99.90", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.95, "FormattedValue": "$49.95", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 2267, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Playbeatz 2.0", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30357", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 9, "shippingName": "Free Shipping - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 83.32, "FormattedValue": "$83.32", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 2457, "externalProductId": 0, "externalProductVariantId": 0, "productName": "OshenWatch", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30389", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 40334.0, "FormattedValue": "$40,334.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 76.91, "FormattedValue": "$76.91", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 35.0, "FormattedValue": "$35.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": true, "IsGlobalCampaignPrice": null}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 1186, "externalProductId": 0, "externalProductVariantId": 0, "productName": "<PERSON><PERSON><PERSON><PERSON>", "productTypeName": "LightTrack", "productDisplayName": null, "sku": "30218", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 66.65, "FormattedValue": "$66.65", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 743, "externalProductId": 0, "externalProductVariantId": 0, "productName": "NatureSpa Shower Head", "productTypeName": "Home ", "productDisplayName": null, "sku": "30084", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 79.98, "FormattedValue": "$79.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 39.99, "FormattedValue": "$39.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3180, "externalProductId": 0, "externalProductVariantId": 0, "productName": "LaidBack Feet Set", "productTypeName": "Health", "productDisplayName": null, "sku": "30431", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 69.99, "FormattedValue": "$69.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 107.68, "FormattedValue": "$107.68", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 69.99, "FormattedValue": "$69.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3277, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Williston Force Desktop AC F11", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30447", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 138.45, "FormattedValue": "$138.45", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3275, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Williston Force Portable AC F25", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30445", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 138.45, "FormattedValue": "$138.45", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3142, "externalProductId": 0, "externalProductVariantId": 0, "productName": "RenuBack Relief", "productTypeName": "LightTrack", "productDisplayName": null, "sku": "30436", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.98, "FormattedValue": "$49.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 76.89, "FormattedValue": "$76.89", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.98, "FormattedValue": "$49.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 2172, "externalProductId": 0, "externalProductVariantId": 0, "productName": "Neck Relax", "productTypeName": "Home ", "productDisplayName": null, "sku": "30340", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 78.99, "FormattedValue": "$78.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "@DiscountPercentage": {"Type": "@DiscountPercentage", "Value": 50.0, "FormattedValue": "$50.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 157.98, "FormattedValue": "$157.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 78.99, "FormattedValue": "$78.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 2990, "externalProductId": 0, "externalProductVariantId": 0, "productName": "TVFix", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30373", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 76.91, "FormattedValue": "$76.91", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 49.99, "FormattedValue": "$49.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3432, "externalProductId": 0, "externalProductVariantId": 0, "productName": "PeacePlay Wearable Speakers", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30459", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 69.99, "FormattedValue": "$69.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 107.68, "FormattedValue": "$107.68", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 69.99, "FormattedValue": "$69.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3178, "externalProductId": 0, "externalProductVariantId": 0, "productName": "InHeat -  Mug Warmer and Wireless Charger", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30420", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 179.98, "FormattedValue": "$179.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 89.99, "FormattedValue": "$89.99", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}, {"productId": 3096, "externalProductId": 0, "externalProductVariantId": 0, "productName": "SoloForce", "productTypeName": "Electronics", "productDisplayName": null, "sku": "30412", "message": null, "warrantyTypeId": null, "warrantyPeriod": null, "quantity": 1, "salesWorkflowId": 4, "parentProductId": null, "shippings": [{"shippingMethodId": 517, "shippingName": "[All Categories] Free Shipping  - 1 Unit", "price": 0.0, "formattedPrice": "$0.00"}], "productPrices": {"DiscountedPrice": {"Type": "DiscountedPrice", "Value": 89.98, "FormattedValue": "$89.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "FullRetailPrice": {"Type": "FullRetailPrice", "Value": 138.43, "FormattedValue": "$138.43", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "UnitDiscountRate": {"Type": "UnitDiscountRate", "Value": 89.98, "FormattedValue": "$89.98", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}, "Surcharge": {"Type": "Surcharge", "Value": 0.0, "FormattedValue": "$0.00", "CountryCode": null, "GlobalCurrencyCode": "USD", "IsGlobalProductPrice": false, "IsGlobalCampaignPrice": true}}, "customFields": [], "progressiveDiscounts": null}], "location": {"countryCode": "VN", "countryName": "Vietnam", "regionCode": "SG", "regionName": "<PERSON>", "city": "Ho Chi Minh City", "zipCode": "700000", "currencyCode": "USD", "ip": "**************"}}