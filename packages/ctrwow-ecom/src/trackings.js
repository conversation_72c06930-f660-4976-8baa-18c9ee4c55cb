/* eslint-disable */
import { CACHE_KEYS } from './constants'
import { fireGtmConversion, fireFunnelConversion, fireEverFlow, fireFpConversion, fireConversionsWithCheckAff } from 'shared-trackings'

function getOrderInfo() {
  try {
    return JSON.parse(window.localStorage.getItem(CACHE_KEYS.ORDER_INFO))
  } catch (e) {}
  return null
}

function getCampainInfo() {
  try {
    return JSON.parse(window.localStorage.getItem(CACHE_KEYS.CAMPAIGNS))
  } catch (e) {}
  return null
}

function handleFireEverFlowConversion() {
  const trackingConversionData = getTrackingConversionData()
  try {
    const isEverFlowFired = window.localStorage.getItem(CACHE_KEYS.EVEFRLOW_CONVERSION)
    if (!isEverFlowFired) {
      fireEverFlow(trackingConversionData)
      window.localStorage.setItem(CACHE_KEYS.EVEFRLOW_CONVERSION, 'true')
    }
  } catch (err) {
    console.log('error: ', err)
  }
}

function handleFireFunnelConversion() {
  try {
    const isFireFunnelConversion = window.localStorage.getItem(CACHE_KEYS.CTR_FUNNEL_CONVERSION)
    if (!isFireFunnelConversion) {
      fireFunnelConversion(
        function () {
          window.localStorage.setItem(CACHE_KEYS.CTR_FUNNEL_CONVERSION, 'true')
        },
        function () {
          console.log('fireFunnelConversion error')
        },
        function (error) {
          console.warn('fireFunnelConversion error' + error)
        }
      )
    }
  } catch (e) {
    console.warn('fireFunnelConversion error', e)
  }
}

function getTrackingConversionData() {
  const caimpainInfo = getCampainInfo()
  const orderCheckoutSuccessInfo = getOrderInfo()
  let trackingData = {}
  if (caimpainInfo && orderCheckoutSuccessInfo) {
    trackingData = {
      campaignName: caimpainInfo.campaignName,
      campaignWebKey: window.__CTR_ECOM_CONFIG ? window.__CTR_ECOM_CONFIG.campaignWebKey : '',
      currencyCode: caimpainInfo.location.currencyCode,
      customerId: orderCheckoutSuccessInfo.customerId,
      customeremail: orderCheckoutSuccessInfo.email,
      cusEmail: orderCheckoutSuccessInfo.email,
      firstName: orderCheckoutSuccessInfo.shippingAddress.firstName,
      cusFirstName: orderCheckoutSuccessInfo.shippingAddress.firstName,
      ip: caimpainInfo.location.ip,
      lastName: orderCheckoutSuccessInfo.shippingAddress.lastName,
      cusLastName: orderCheckoutSuccessInfo.shippingAddress.lastName,
      orderNumber: orderCheckoutSuccessInfo.cartNumber,
      orderTotalFull: orderCheckoutSuccessInfo.orderTotalFull,
      orderPrice: orderCheckoutSuccessInfo.orderTotalFull
    }
  }

  return trackingData
}

function handleFireFpConversion() {
  try {
    const orderCheckoutSuccessInfo = getOrderInfo()
    const caimpainInfo = getCampainInfo()
    if (!window.localStorage.getItem(CACHE_KEYS.CTR_FP_CONVERSION) && orderCheckoutSuccessInfo && caimpainInfo) {
      const conversionUrl = window.localStorage.getItem(CACHE_KEYS.CHECKOUT_PAGE_URL)
      fireFpConversion(getTrackingConversionData(), conversionUrl)
      window.localStorage.setItem(CACHE_KEYS.CTR_FP_CONVERSION, 'true')
    }
  } catch (e) {
    console.warn('fireFpConversion e', e)
  }
}

function trackGtm(eventName, data) {
  window.ctrwowUtils.tracking.pushGtmTrackingEvent(eventName, data)
}

function addToCart() {
  try {
    trackGtm('AddToCart')
  } catch (e) {
    console.warn('addToCart error: ', { e })
  }
}

function handleFireGtmConversion() {
  try {
    if (!window.localStorage.getItem(CACHE_KEYS.GTM_CONVERSION)) {
      const trackData = getTrackingConversionData()
      fireGtmConversion({
        orderNumber: trackData.orderNumber,
        orderTotalFull: trackData.orderPrice,
        currencyCode: trackData.currencyCode
      })
      window.localStorage.setItem(CACHE_KEYS.GTM_CONVERSION, 'true')
    }
  } catch (err) {
    console.log('error: ', err)
  }
}

function handleTrackConversions() {
  handleFireFpConversion()
  handleFireGtmConversion()
  handleFireEverFlowConversion()
  handleFireFunnelConversion()
  window.localStorage.setItem(CACHE_KEYS.ECOM_CONVERSION, 'true')
}

function conversions() {
  try {
    if (!window.localStorage.getItem(CACHE_KEYS.ECOM_CONVERSION)) {
      const campainInfo = getCampainInfo()
      const countryCode = campainInfo && campainInfo.location ? campainInfo.location.countryCode : null
      fireConversionsWithCheckAff({
        handleTrackConversions,
        countryCode
      })
    }
  } catch (e) {
    return false
  }
  return true
}

function clearTrackingDataStorage() {
  window.localStorage.removeItem(CACHE_KEYS.ECOM_CONVERSION)
  window.localStorage.removeItem(CACHE_KEYS.EVEFRLOW_CONVERSION)
  window.localStorage.removeItem(CACHE_KEYS.CTR_FP_CONVERSION)
  window.localStorage.removeItem(CACHE_KEYS.CTR_FUNNEL_CONVERSION)
  window.localStorage.removeItem(CACHE_KEYS.GTM_CONVERSION)
}

export const trackings = {
  conversions,
  addToCart,
  trackGtm,
  clearTrackingDataStorage
}
