// COMMON CONFIG
// eslint-disable-next-line no-undef
const isProd = ENV__IS_PRODUCTION_MODE
// const isProd = false

/* SHOPIFY INTEGRATION - api endpoint */
const SHOPIFY_BASE_URL = isProd
  ? window.isShopifyNewAPI
    ? 'https://ctrwow-shopify-api.azurewebsites.net/api/v2'
    : 'https://ctrwow-shopify-api.azurewebsites.net/api'
  : window.isShopifyNewAPI
  ? 'https://ctrwow-shopify-api-dev.azurewebsites.net/api/v2'
  : 'https://ctrwow-shopify-api-dev.azurewebsites.net/api'

export const SOURCE_CONFIG = {
  SHOPIFY: {
    basePostAPIEndpoint: SHOPIFY_BASE_URL,
    baseGetAPIEndpoint: SHOPIFY_BASE_URL
  }
}

export const WEB_SERVICE_URL = 'https://ctrwow-dev-ecommerceplatformintegrationpublic-microservice.azurewebsites.net/api'
