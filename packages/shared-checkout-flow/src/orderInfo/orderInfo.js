export const setOrderInfo = (orderInfo) => localStorage.setItem('orderInfo', JSON.stringify(orderInfo))
export const getOrderInfo = () => {
  try {
    return JSON.parse(localStorage.getItem('orderInfo'))
  } catch (e) {
    return {}
  }
}

// DATA IN LOCAL STORAGE
export const setUserFirstName = (firstName) => localStorage.setItem('user_firstname', firstName)
export const getUserFirstName = () => localStorage.getItem('user_firstname')

export const setUserLastName = (lastName) => localStorage.setItem('user_lastname', lastName)
export const getUserLastName = () => localStorage.getItem('user_lastname')
