function isElement(element) {
  return element instanceof Element || element instanceof HTMLDocument
}

const readForm = (form) => {
  try {
    const inputs = (form && form.elements) || []

    const formData = []
    for (let i = 0; i < inputs.length; i++) {
      const value = inputs[i].value
      const key = inputs[i].name
      key && value && formData.push({ key, value })
    }

    return formData.length ? [form.name, formData] : []
  } catch (e) {
    console.log('[addAddedInfoToOrderPayload]')
    console.log(e)
    return []
  }
}

const getData = (addedData) => {
  if (isElement(addedData)) {
    return readForm(addedData)
  } else {
    return addedData
  }
}

const getFormsByName = (formName) => {
  const forms = window.__ctrOrderPayloadAddedInfo || []

  if (!formName) {
    return forms
  }

  const re = []
  forms.forEach((form) => {
    if (form.name === formName) {
      re.push(form)
    }
  })

  return re
}

export default function addAddedInfoToOrderPayload(orderPayload) {
  const forms = window.__ctrOrderPayloadAddedInfo || []
  forms.forEach((form) => {
    const [name, formData] = getData(form)
    try {
      if (!name) {
        return
      }
      if (!orderPayload[name]) {
        orderPayload[name] = formData
      } else if (typeof orderPayload[name] === 'object' && typeof formData === 'object') {
        orderPayload[name] = { ...orderPayload[name], ...formData }
      }
    } catch (error) {
      console.log(error)
    }
  })
}

function addAddedInfoByKey(key, values) {
  window.__ctrOrderPayloadAddedInfo = window.__ctrOrderPayloadAddedInfo || []
  window.__ctrOrderPayloadAddedInfo.push([key, values])
}

export function addInstallmentPayment(installmentValue) {
  addAddedInfoByKey('payment', { Instalments: installmentValue })
}

/**
 * Set value to additional-form by key
 * @param fieldName[string] field name
 * @param fieldValue[string] field value
 * @param formName[string]  form name - search all form and update the first-item in case this field is empty
 * * @returns {boolean}     true if update success otherwise return false
 */
export function setDataByKey(fieldName, fieldValue, formName) {
  const forms = getFormsByName(formName)

  return !!forms.find((form) => {
    const input = form.querySelector && form.querySelector(`[name=${fieldName}]`)

    if (isElement(input)) {
      input.value = fieldValue
      return true
    }

    return false
  })
}

/**
 * Set value to additional-form by key
 * @param fieldName[string] field name
 * @param formName[string]  form name - search all form and update the first-item in case this field is empty
 * * @returns {boolean}     true if update success otherwise return false
 */
export function getDataByKey(fieldName, formName) {
  const forms = getFormsByName(formName)
  let fieldValue = null

  forms.find((form) => {
    const input = form.querySelector && form.querySelector(`[name=${fieldName}]`)

    if (isElement(input)) {
      fieldValue = input.value
      return true
    }

    return false
  })

  return fieldValue
}

export const getMaropostId = () => getDataByKey('MaropostSettingsId')
export const setMaropostId = (value) => setDataByKey('MaropostSettingsId', value)
