class MainProductList {
  constructor() {
    console.log('init MainProductList')
    this.data = {
      productList: null
    }
    this.productListDataHandler = {}
  }

  get productList() {
    return this.data.productList
  }

  set productList(newVal) {
    try {
      const oldVal = this.data.productList
      this.data.productList = newVal
      this.executeHandler('productList', oldVal, newVal)
    } catch (e) {
      console.log(e)
    }
  }

  addHandler(key, handler) {
    if (!this.productListDataHandler[key]) {
      this.productListDataHandler[key] = []
    }

    const handlers = this.productListDataHandler[key]
    handlers.push(handler)

    return () => {
      const index = handlers.indexOf(handler)
      handlers.splice(index, 1)
    }
  }

  executeHandler(key, oldVal, newVal) {
    const handlers = (this.productListDataHandler && this.productListDataHandler[key]) || []
    handlers.forEach((fn) => {
      try {
        fn(newVal, oldVal)
      } catch (e) {
        console.log(e)
      }
    })
  }
}

// only support one instance per page
let productListData
if (window.__productListData) {
  productListData = window.__productListData
} else {
  productListData = window.__productListData = new MainProductList()
}

export const initData = () => {
  console.log('MainProductList::initData')
  productListData = new MainProductList()
}

const getData = (dataKey) => dataKey && productListData[dataKey]
const setData = (dataKey, dataValue) => {
  productListData[dataKey] = dataValue
}

export const setProductList = (val) => setData('productList', val)
export const getProductList = () => getData('productList')
export const onProductListChange = (handler) => {
  productListData.addHandler('productList', handler)
}

export const getProductFromCache = (webKey) => {
  try {
    let campProducts = window.localStorage.getItem('campproducts')
    if (!campProducts) return false

    if (campProducts && campProducts.length > 0) {
      campProducts = JSON.parse(campProducts)
      const isCamp = hasCampaign(campProducts, webKey)
      if (isCamp) {
        return filterCampWithKey(campProducts, webKey)
      }
    }
    return false
  } catch (e) {
    return false
  }
}

const isExpired = (camps, webKey) => {
  const beforeDate = new Date(camps[0][webKey].timestamp)
  const newDate = new Date()
  const res = Math.abs(newDate - beforeDate) / 1000
  const minutes = Math.floor(res / 60)
  if (minutes > 20) return false
  return true
}

const hasCampaign = (campProducts, webKey) => {
  if (!campProducts) return false
  try {
    const camps = campProducts.camps.filter((item) => item[webKey])
    return isExpired(camps, webKey)
  } catch (error) {
    return false
  }
}

const filterCampWithKey = (campProducts, webKey) => {
  if (!campProducts) return false

  if (campProducts && campProducts.camps.length > 0) {
    campProducts = campProducts.camps.filter((item) => item[webKey])
    return (campProducts.length > 0 && campProducts[0][webKey]) || false
  }
}

export const setProductListToCache = (res, webKey) => {
  try {
    let campProducts = window.localStorage.getItem('campproducts')
    if (campProducts) {
      campProducts = JSON.parse(campProducts)
    } else {
      campProducts = {
        camps: []
      }
    }
    if (typeof res.prices !== 'undefined') {
      res.timestamp = new Date().toISOString()
      const camps = campProducts.camps.filter((item) => item[webKey])

      let camp = {}
      if (camps.length > 0) {
        camp = camps[0]
        camp[webKey] = res
      } else {
        camp[webKey] = res
        campProducts.camps.push(camp)
      }
    }
    window.localStorage.setItem('campproducts', JSON.stringify(campProducts))
  } catch (e) {
    // do nothing
  }
}
