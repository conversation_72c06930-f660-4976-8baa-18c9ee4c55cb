// export default function canReplaceContentWithPlaceholder(elem) {
//   return elem ? elem.children.length === 0 || ['span', 'p'].indexOf(elem.tagName.toLowerCase()) >= 0 : false
// }

const isTextNode = (node) => node && node.nodeType === 3

const replaceNodeContentWithNewContent = (node, fnGetNewTextContent) => {
  const textContent = node.textContent
  const newTextContent = textContent ? fnGetNewTextContent(textContent) : null
  return newTextContent !== textContent ? newTextContent : null
}

const updateHtml = (node, newHtmlContent) => node && newHtmlContent && (node.innerHTML = newHtmlContent)

export function updateElementContentWithPlaceholder(elm, fnGetNewTextContent) {
  const childNodes = elm.childNodes
  const numChildNodes = elm.childNodes.length

  if (numChildNodes === 1 && isTextNode(childNodes[0])) {
    const newContent = replaceNodeContentWithNewContent(elm, fnGetNewTextContent)
    updateHtml(elm, newContent)
    return
  }

  const isDivNode = elm && elm.tagName && elm.tagName.toLowerCase() === 'div'

  for (let i = 0; i < numChildNodes; i++) {
    // only handle with textNode
    const childNode = childNodes[i]
    if (isTextNode(childNode)) {
      const newContent = replaceNodeContentWithNewContent(childNode, fnGetNewTextContent)

      if (newContent) {
        const newNode = document.createElement(isDivNode ? 'div' : 'span')
        updateHtml(newNode, newContent)
        elm.replaceChild(newNode, childNode)
      }
    }
  }
}

export function populateDataToNodeContent(fnGetNewTextContent) {
  const { _qAll } = window
  const allElements = _qAll('body *')
  for (const elem of allElements) {
    updateElementContentWithPlaceholder(elem, fnGetNewTextContent)
  }
}
