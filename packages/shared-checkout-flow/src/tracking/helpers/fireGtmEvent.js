import { fireGtmConversion } from 'shared-trackings'

export function fireGtmEventForMainOrder(orderInfo) {
  const utils = window.ctrwowUtils
  try {
    let isMainOrderToGTMConversionFired = false
    if (utils.localStorage().get('isMainOrderToGTMConversionFired')) {
      isMainOrderToGTMConversionFired = true
    }

    if (orderInfo && orderInfo.orderNumber && !isMainOrderToGTMConversionFired) {
      const currencyCode = window.localStorage.getItem('currencyCode')
      fireGtmConversion({
        // orderId: orderInfo.orderNumber,
        // price: orderInfo.orderTotalFull ? orderInfo.orderTotalFull : '',
        ...orderInfo,
        ...(currencyCode && { currencyCode })
      })
      utils.localStorage().set('isMainOrderToGTMConversionFired', true)
      console.log('fireMainOrderToGTMConversion fire event Conversion')
    }
  } catch (err) {
    console.log('error: ', err)
  }
}

export function fireGtmEventForUpsell() {
  try {
    const utils = window.ctrwowUtils
    const fireUpsellForGTMPurchase = utils.localStorage().get('fireUpsellForGTMPurchase')
    
    let objDataPushGtmTrackingEvent
    try {
      const orderInfo = JSON.parse(utils.localStorage().get('orderInfo'))
      const totalMiniUpsellPrice = orderInfo.totalPriceMiniUpsell
      const purchaseMiniUpsell = orderInfo.upsellUrls.filter((item) => item.index === 0).length > 0 ? orderInfo.upsellUrls.filter((item) => item.index === 0)[0].price : 0
      const totalFullPrice = Number(orderInfo.orderTotalFull) - purchaseMiniUpsell + totalMiniUpsellPrice
      const upsellPrice = (orderInfo.upsellUrls.length > 0 && orderInfo.upsellUrls[orderInfo.upsellUrls.length - 1].index !== 0) ? orderInfo.upsellUrls[orderInfo.upsellUrls.length - 1].price : 0
      const totalPrice  = orderInfo.upsellUrls.reduce((totalFullPrice, item) => totalFullPrice + item.price, totalFullPrice) 
      objDataPushGtmTrackingEvent = { upsellPrice, totalPrice}
    } catch(e) {
      objDataPushGtmTrackingEvent = null
    }
    

    if (fireUpsellForGTMPurchase && fireUpsellForGTMPurchase !== '') {
      objDataPushGtmTrackingEvent !== null 
            ? utils.tracking.pushGtmTrackingEvent(`Upsell "${fireUpsellForGTMPurchase}"`, objDataPushGtmTrackingEvent) 
            : utils.tracking.pushGtmTrackingEvent(`Upsell "${fireUpsellForGTMPurchase}"`)
      utils.localStorage().remove('fireUpsellForGTMPurchase')
      console.log('fireUpsellForGTMPurchase fire with event: Upsell ', fireUpsellForGTMPurchase)
    }
  } catch (err) {
    console.log(err)
  }
}

// export default function fireGtmEvent(orderInfo) {
//   fireGtmEventForMainOrder(orderInfo)
//   fireGtmEventForUpsell(orderInfo)
// }
