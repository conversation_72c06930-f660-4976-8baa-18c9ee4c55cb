import {
  fireFpConversion as fireSiteConversion,
  fireFunnelConversion
} from 'shared-trackings'

function getOrderNumber(isMainOrder, orderInfo = {}) {
  if (isMainOrder) {
    return orderInfo.orderNumber
  }

  const currentUpsellIndex = orderInfo.upsellIndex - 1
  if (currentUpsellIndex > -1) {
    const upsellInfo = orderInfo.upsellUrls[currentUpsellIndex] || {}

    return upsellInfo.orderNumber
  } else {
    return false
  }
}

function getConversionData(isMainOrder, orderInfo) {
  const localStorage = window.ctrwowUtils.localStorage()
  const { trackingUrl, ...rest } = isMainOrder ? getMainOrderData() : getUpsellData()

  return {
    trackingUrl,
    customerEmail: orderInfo.cusEmail,
    firstName: localStorage.get('user_firstname'),
    lastName: localStorage.get('user_lastname'),
    ...rest
  }

  function getUpsellData() {
    const currentUpsellIndex = orderInfo.upsellIndex - 1
    const { orderedProducts: [orderedProducts = {}] = [], orderNumber, ...rest } = orderInfo.upsellUrls[currentUpsellIndex] || {}

    return {
      trackingUrl: rest.url,
      currencyCode: localStorage.get('upsell_currencyCode') || '',
      ip: localStorage.get('upsell_ip') || '',
      customerId: rest.customerId,
      campaignName: rest.campaignName,
      campaignWebKey: rest.campaignWebKey,
      orderNumber: orderNumber,
      orderPrice: orderInfo.upsellPriceToUpgrade,
      productId: orderedProducts.pid,
      productName: orderedProducts.name,
      sku: orderedProducts.sku,
      quantity: orderedProducts.quantity,
      isUpsell: true
    }
  }

  function getMainOrderData() {
    const orderedProducts = (orderInfo.orderedProducts || [])[0] || {}
    return {
      trackingUrl: localStorage.get('ctr_checkout_url'),
      trackingPageId: localStorage.get('ctr_checkout_page_id'),
      trackingPage_publishVersion: localStorage.get('ctr_checkout_page_publishVersion'),
      currencyCode: localStorage.get('currencyCode') || '',
      ip: localStorage.get('ip') || '',
      customerId: orderInfo.customerId,
      campaignName: orderInfo.campaignName,
      campaignWebKey: orderInfo.campaignWebKey,
      orderNumber: orderInfo.orderNumber,
      orderPrice: orderInfo.orderTotalFull,
      productId: orderedProducts.pid,
      productName: orderedProducts.name,
      sku: orderedProducts.sku,
      quantity: orderInfo.quantity,
    }
  }
}

const triggerConversionEvent = (isMainOrder) => {
  try {
    const localStorage = window.ctrwowUtils.localStorage()

    const orderInfo = JSON.parse(localStorage.get('orderInfo')) || {}
    const orderNumber = getOrderNumber(isMainOrder, orderInfo)

    const { trackingUrl, ...conversionData } = getConversionData(isMainOrder, orderInfo) || {}

    // conversion for page
    const trackingKey = orderNumber ? `isFireConversion__${orderNumber}` : undefined
    if (trackingKey && !localStorage.get(trackingKey)) {
      const onSuccess = () => localStorage.set(trackingKey, true)
      fireSiteConversion(conversionData, trackingUrl, onSuccess)
    }

    // conversion for funnel
    const trackingKey_funnel = orderNumber ? `isFireConversionFunnel__${orderNumber}` : undefined
    if (trackingKey_funnel && !localStorage.get(trackingKey_funnel)) {
      const onSuccess = () => localStorage.set(trackingKey_funnel, true)
      fireFunnelConversion(onSuccess, undefined, conversionData)
    }
    
  } catch (e) {
    console.log(e)
  }
}

export default function fireCtrwowConversion() {
  triggerConversionEvent(true)
  triggerConversionEvent(false)
}
