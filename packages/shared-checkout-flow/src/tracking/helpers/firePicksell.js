// [source : ctrwowlib > fireEverFlow]
export default function fireEverFlow() {
  // Fire picksell
  const $picksell = window.$picksell
  if (!$picksell) {
    return
  }

  const isPicksellFired = localStorage.getItem('isPicksellFired')
  if (!isPicksellFired) {
    const conventionTrackingPrice = window.localStorage.getItem('conventionTrackingPrice')
    $picksell.trackingConversion(Number(conventionTrackingPrice))
    localStorage.setItem('isPicksellFired', true)
  }
}
