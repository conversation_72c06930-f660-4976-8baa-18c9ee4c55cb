import { fireEverFlow as handleFireEverFlow } from 'shared-trackings'
import { initDFOLib } from 'shared-trackings/src/fireEverFlow'
// [source : ctrwowlib > fireEverFlow]
export default function fireEverFlow(orderInfo) {
  try {
    if (!localStorage.getItem('isEverFlowFired')) {
      const url_checkOut = localStorage.getItem('ctr_checkout_url')
      if (
        url_checkOut &&
        (url_checkOut.toLocaleLowerCase().indexOf('www.getuvbrite.com') > -1 ||
          url_checkOut.toLocaleLowerCase().indexOf('www.zenfluffsleep.com') > -1 ||
          url_checkOut.toLocaleLowerCase().indexOf('www.getchargecard.com') > -1)
      ) {
        // Alt affid was update to payload and submit to CRM
        // update  everflow flow for altaffid
        initDFOLib(true, false)
      } else {
        // normal fire
        handleFireEverFlow(orderInfo)
      }

      // handleFireEverFlow(orderInfo)
      // localStorage.setItem('isEverFlowFired', 'true')
    }
  } catch (err) {
    console.log('error: ', err)
  }
}

// // [source : ctrwowlib > fireEverFlow]
// export default function fireEverFlow(orderInfo) {
//   const {
//     ctrwowUtils: { link }
//   } = window
//
//   const everFlowUrl =
//     'https://#DOMAIN/?nid=#NETWORK_ID&oid=#OFFER_ID&transaction_id=#TRANSACTION_ID&adv1=#ADV1&coupon_code=#CC&sub1=#S1&sub2=#S2&sub3=#S3&sub4=#S4&sub5=#S5'
//   try {
//     // [Tracking - EF] Do not fire conversion to EF if param ERS=Y exists on URL
//     // ref: https://dfoglobal.atlassian.net/browse/CTR-1201
//     const ersParam = link.getParameterByName('ers') || ''
//     if (ersParam && ersParam.toLowerCase() === 'y') {
//       return
//     }
//
//     let isEverFlowFired = false
//     if (localStorage.getItem('isEverFlowFired')) {
//       isEverFlowFired = true
//     }
//
//     const domain = link.getParameterByName(TRACKING_EVENTS.everflowDomainParams)
//
//     if (orderInfo && orderInfo.orderNumber && !isEverFlowFired && domain) {
//       const offer_id = link.getParameterByName('S4') || ''
//       const transaction_id = link.getParameterByName('S5') || ''
//       const network_id = link.getParameterByName('NETWORK_ID') || ''
//       const coupon_code = link.getParameterByName('CC') || ''
//       const sub1 = link.getParameterByName('S1') || ''
//       const sub2 = link.getParameterByName('S2') || ''
//       const sub3 = link.getParameterByName('S3') || ''
//       const sub4 = link.getParameterByName('S4') || ''
//       const sub5 = link.getParameterByName('S5') || ''
//
//       let url = everFlowUrl.replace('#ADV1', orderInfo.orderNumber)
//       url = url.replace('#NETWORK_ID', network_id)
//       url = url.replace('#OFFER_ID', offer_id)
//       url = url.replace('#TRANSACTION_ID', transaction_id)
//       url = url.replace('#DOMAIN', domain)
//       url = url.replace('#CC', coupon_code)
//       url = url.replace('#S1', sub1)
//       url = url.replace('#S2', sub2)
//       url = url.replace('#S3', sub3)
//       url = url.replace('#S4', sub4)
//       url = url.replace('#S5', sub5)
//
//       const iframe = document.createElement('iframe')
//       iframe.src = url
//       iframe.id = 'everflow'
//       iframe.setAttribute('frameborder', 0)
//       iframe.setAttribute('scrolling', 'no')
//       iframe.style = 'width: 1px; height: 1px'
//       document.body.appendChild(iframe)
//
//       localStorage.setItem('isEverFlowFired', true)
//     }
//   } catch (err) {
//     console.log('error: ', err)
//   }
// }
