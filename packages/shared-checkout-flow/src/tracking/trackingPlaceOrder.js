import { fireGtmEventForMainOrder, fireGtmEventForUpsell } from './helpers/fireGtmEvent'
import fireEverFlow from './helpers/fireEverFlow'
import fireAnyTracking from './helpers/fireAnyTracking'
import fireFbPixel from './helpers/fireFbPixel'
// import firePicksell from './helpers/firePicksell'
import fireCtrwowConversion from './helpers/fireCtrwowConversion'
import { fireConversionsWithCheckAff } from 'shared-trackings'

import { getOrderInfo } from './../orderInfo/orderInfo'

const fireTracking = (orderInfo) => {
  // ctrwow finger-print tracking
  fireCtrwowConversion()

  fireEverFlow(orderInfo)
  fireFbPixel(orderInfo)
  fireAnyTracking(orderInfo)
  // firePicksell() // remove picksell tracking
  fireGtmEventForMainOrder(orderInfo)
}

export default function trackingPlaceOrder() {
  // track gtm for upsell
  fireGtmEventForUpsell()

  const orderInfo = getOrderInfo()

  if (!orderInfo) {
    return
  }

  try {
    const { ctrwowUtils } = window
    const isMainOrderToGTMConversionFired = ctrwowUtils.localStorage().get('isMainOrderToGTMConversionFired')
    if (isMainOrderToGTMConversionFired) {
      fireTracking(orderInfo)
    } else if (!window.localStorage.getItem('checkedAff')) {
      const mainOrderLink = ctrwowUtils.localStorage().get('mainOrderLink')
      fireConversionsWithCheckAff({
        handleTrackConversions: () => fireTracking(orderInfo),
        trackingLink: `${location.host}${mainOrderLink}`,
        countryCode: ctrwowUtils.localStorage().get('ctr__countryCode')
      })
    }
  } catch (e) {
    console.warn('Tracking error: ' + e)
    fireTracking(orderInfo)
  }
}

// export default function trackingPlaceOrder() {
//   const orderInfo = getOrderInfo()
//   try {
//     const { ctrwowUtils } = window
//     // const mainOrderLink = localStorage().get('mainOrderLink')
//     const mainOrderLink = ctrwowUtils.localStorage().get('mainOrderLink')
//
//     orderInfo &&
//       ctrwowUtils.tracking
//         .checkAff(ctrwowUtils.link.getQueryParameter('Affid'), `${location.host}${mainOrderLink}`, {
//           countryCode: ctrwowUtils.localStorage().get('ctr__countryCode')
//         })
//         .then((re) => re && fireTracking(orderInfo))
//         .catch(() => fireTracking(orderInfo))
//   } catch (e) {
//     console.warn('Tracking error: ' + e)
//     fireTracking(orderInfo)
//   }
// }
