// import { getOrderInfo, isPurchaseByUpsell, removePurchaseByUpsell } from './../orderInfo'
// import fireCtrFpUpSellConversionEvent from './helpers/fireCtrFpUpSellConversionEvent'
//
// export default function sendGtmTrackingUpSellPurchaseEvent() {
//   if (isPurchaseByUpsell()) {
//     const orderInfo = getOrderInfo()
//
//     const upsellIdx = orderInfo.upsellIndex - 1
//     const upsellOfferName = orderInfo.upsells && orderInfo.upsells.length > 0 ? orderInfo.upsells[upsellIdx].linkedCampaignName : ''
//
//     window.ctrwowUtils.tracking.pushGtmTrackingEvent('Upsell ' + upsellOfferName)
//     fireCtrFpUpSellConversionEvent(orderInfo)
//     removePurchaseByUpsell()
//   }
// }
