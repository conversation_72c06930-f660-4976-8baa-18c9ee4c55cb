// import { getOrderInfo } from './../orderInfo'
// import { TRACKING_EVENTS } from './constants'
// import fireFpConversion from './helpers/fireFpConversion'
//
// export default function fireGtmUpSellConversionEvent() {
//   const isFiredGtmUpSellConversionEvent = localStorage.getItem('isFiredGtmUpSellConversionEvent')
//   if (!isFiredGtmUpSellConversionEvent) {
//     const orderInfoData = getOrderInfo()
//
//     const conventionTrackingPrice = window.localStorage.getItem('conventionTrackingPrice')
//     const currencyCode = window.localStorage.getItem('currencyCode')
//
//     const eventData = {}
//     if (orderInfoData && conventionTrackingPrice) {
//       eventData.orderId = orderInfoData.orderNumber
//       eventData.price = Number(conventionTrackingPrice)
//       eventData.currencyCode = currencyCode // TODO - dont know how to get it
//     }
//
//     window.ctrwowUtils.tracking.pushGtmTrackingEvent(TRACKING_EVENTS.conversionEventName, eventData)
//     localStorage.setItem('isFiredGtmUpSellConversionEvent', true)
//   }
//
//   fireFpConversion()
// }
