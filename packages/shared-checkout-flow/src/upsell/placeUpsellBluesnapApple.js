import { _placeUpsellOrder, _placeUpsellUpgrade } from './helpers'
import { getTokenBluesnap, initBluesnapButton, getProductNameAndPrice } from './../payment/bluesnap.helper'

export const upsellBluesnapApple = {
  __appendDivBluesnapApple: () => {
    if (document.querySelector('div[data-bluesnap="walletButton"]') === null) {
      var str__BluesnapApple = `<div data-gjs-name="Block Render Bluesnap Button" data-bluesnap="walletButton"></div>`
      var elm__BluesnapApple = document.createElement('div')
      elm__BluesnapApple.innerHTML = str__BluesnapApple

      var elm__BluesnapAppleSubmitButton = document.querySelector('.js-btn-place-upsell-order')
      if (elm__BluesnapAppleSubmitButton) {
        var elm__Parent = elm__BluesnapAppleSubmitButton.parentNode
        elm__Parent && elm__Parent.insertBefore(elm__BluesnapApple, elm__BluesnapAppleSubmitButton)
      }
    }
  },
  __handleInitButton: ({ isUpsellUpgrade }) => {
    const __upsellProductNameAndPrice = getProductNameAndPrice('upsell')

    // strTotalPrice update in share-checkout-flow
    const objInitBluesnapAppleButton = {
      strProductName: __upsellProductNameAndPrice.productName,
      strTotalPrice: __upsellProductNameAndPrice.totalPrice,
      fnPlaceOrder: () => {
        if (isUpsellUpgrade) {
          const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
          _placeUpsellUpgrade(product, null)
        } else {
          _placeUpsellOrder()
        }
      }
    }
    initBluesnapButton(objInitBluesnapAppleButton)
  },
  init: ({ ctaButtons, isUpsellUpgrade }) => {
    upsellBluesnapApple.__appendDivBluesnapApple()
    /**
     * use param useBluesnapCDN=true => use lib from ctrwow cdn
     */
    const urlBluesnap = window.ctrwowUtils.link.getQueryParameter('isCardTest')
      ? window.ctrwowUtils.link.getQueryParameter('useBluesnapCDN')
        ? 'https://cdn.wowsuite.ai/ctrwow/public-assets/ctr-widgets/public-assets/bluesnap.js'
        : 'https://sandpay.bluesnap.com/web-sdk/5/bluesnap.js'
      : 'https://pay.bluesnap.com/web-sdk/5/bluesnap.js'

    window.ctrwowUtils.getDependencies([urlBluesnap], { delayUntilInteract: false }).then(() => {
      getTokenBluesnap()
        .then((data) => {
          if (data !== undefined) {
            // register bluesnap apple
            window.bluesnapToken = data.token
            upsellBluesnapApple.__handleInitButton({
              isUpsellUpgrade
            })

            // handle click button upsell place-order
            Array.prototype.slice.call(ctaButtons).forEach((ele) => {
              ele.addEventListener('click', function () {
                window.ctrwowUtils.showGlobalLoading() // recommit code

                // settimeout to dismiss loading if user click cancel button
                window.loadingTimeout = null
                window.loadingTimeout = setTimeout(() => {
                  window.ctrwowUtils.hideGlobalLoading()
                }, 5000)

                const elm__ApplePayButton = document.querySelector('#apple-pay-button')
                elm__ApplePayButton && elm__ApplePayButton.click()
              })
            })
          } else {
            console.log('[BLUESNAP] Cannot get token.')
          }
        })
        .catch((error) => {
          console.log('[BLUESNAP] Error: ', error)
        })
    })
  }
}
