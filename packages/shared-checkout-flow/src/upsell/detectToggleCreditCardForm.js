export function detectToggleCreditCardForm(products) {
  try {
    let giftCard = window.localStorage.getItem('giftCard')
    let orderInfo = window.localStorage.getItem('orderInfo')
    if (giftCard && window.localStorage.getItem('userPaymentType') === 'creditcard') {
      giftCard = JSON.parse(giftCard)
      orderInfo = JSON.parse(orderInfo)
      // window.q('[name="payment"]').display('block')

      if (giftCard.CanPayAll) {
        if (orderInfo.cardId === '00000000-0000-0000-0000-000000000000') {
          window.q('[name="payment"]').display('block')
        } else {
          window.q('[name="payment"]').hide()
        }
      } else {
        window.q('[name="payment"]').hide()
      }
    } else {
      window.q('[name="payment"]').hide()
    }
  } catch (e) {
    console.log(e)
  }
}
