import { _placeUpsellUpgrade, _placeUpsell<PERSON>rder, _getUpParam } from './helpers'

const __ctrPageConfiguration = window.__ctrPageConfiguration
let eCRM
window.ctrwowUtils.getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js']).then(() => {
  eCRM = new window.EmanageCRMJS({
    webkey: __ctrPageConfiguration.webKey,
    cid: __ctrPageConfiguration.cid,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })
})

export function _placeUpsellBlueSnapGoogle(ctaButtons, isUpgradeUpsell = false) {
  window.ctrwowUtils.getDependencies(['https://pay.google.com/gp/p/js/pay.js']).then(() => {
    eCRM.Order.getMidAndPrn((data) => {
      try {
        // window.token = data.token
        window.objMidBlueSnapGoogle = data
        initBlueSnapGoogle(ctaButtons, isUpgradeUpsell)
      } catch (err) {
        console.log('Bluesnap Goole get midId error: ', err)
      }
    }, 58)
  })
}
export function initBlueSnapGoogle(ctaButtons, isUpgradeUpsell) {
  const orderInfo = window.ctrwowUtils.localStorage().get('orderInfo') ? JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo')) : null
  const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
  const amount = product.productPrices.DiscountedPrice.Value - orderInfo.orderTotal

  const baseRequest = {
    apiVersion: 2,
    apiVersionMinor: 0
  }
  const allowedCardNetworks = ['AMEX', 'MASTERCARD', 'VISA']
  const allowedCardAuthMethods = ['PAN_ONLY', 'CRYPTOGRAM_3DS']
  const tokenizationSpecification = {
    type: 'PAYMENT_GATEWAY',
    parameters: {
      gateway: 'bluesnap',
      gatewayMerchantId: window.objMidBlueSnapGoogle.token.toString()
    }
  }
  const baseCardPaymentMethod = {
    type: 'CARD',
    parameters: {
      allowedAuthMethods: allowedCardAuthMethods,
      allowedCardNetworks: allowedCardNetworks
    }
  }
  const cardPaymentMethod = Object.assign({}, baseCardPaymentMethod, {
    tokenizationSpecification: tokenizationSpecification
  })
  let paymentsClient = null
  const getGoogleIsReadyToPayRequest = () => {
    return Object.assign({}, baseRequest, {
      allowedPaymentMethods: [baseCardPaymentMethod]
    })
  }

  const getGooglePaymentDataRequest = () => {
    const paymentDataRequest = Object.assign({}, baseRequest)
    paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod]
    paymentDataRequest.transactionInfo = getGoogleTransactionInfo()
    paymentDataRequest.merchantInfo = {
      // @todo a merchant ID is available for a production environment after approval by Google
      // See {@link https://developers.google.com/pay/api/web/guides/test-and-deploy/integration-checklist|Integration checklist}
      merchantId: window.objMidBlueSnapGoogle.token.toString(), // '12345678901234567890',
      merchantName: window.objMidBlueSnapGoogle.entity
    }
    paymentDataRequest.shippingAddressParameters = getGoogleShippingAddressParameters()
    paymentDataRequest.emailRequired = true
    return paymentDataRequest
  }

  function getGoogleShippingAddressParameters() {
    return {
      phoneNumberRequired: true
    }
  }

  const getGooglePaymentsClient = () => {
    let environment = 'TEST'
    if (!eCRM.isTest) {
      environment = 'PRODUCTION'
    }
    if (paymentsClient === null) {
      paymentsClient = new window.google.payments.api.PaymentsClient({ environment })
    }
    return paymentsClient
  }

  const onGooglePayLoaded = () => {
    const paymentsClient = getGooglePaymentsClient()
    paymentsClient
      .isReadyToPay(getGoogleIsReadyToPayRequest())
      .then(function (response) {
        if (response.result) {
          addGooglePayButton()
          // @todo prefetch payment data to improve performance after confirming site functionality
          // prefetchGooglePaymentData();
        }
      })
      .catch(function (err) {
        // show error in developer console for debugging
        console.error(err)
      })
  }
  onGooglePayLoaded()

  const addGooglePayButton = () => {
    Array.prototype.slice.call(ctaButtons).forEach((item) => {
      item.addEventListener('click', function () {
        onGooglePaymentButtonClicked()
      })
    })
  }

  const getGoogleTransactionInfo = () => {
    // const tax = parseFloat(window.taxPercent) || 0
    // const product = window.ctrwowCheckout.checkoutData.getProduct()
    // const productPrice = product.productPrices.DiscountedPrice.Value
    // const shippingPrice = product.shippings.length > 0 ? product.shippings[0].price : 0
    // let totalPrice = productPrice + shippingPrice
    // if (window.diggy__HasUpgrade === true && window.miniUpsell !== undefined && window.miniUpsell.length > 0) {
    //   const diggy__productMiniUpsell = window.diggy__productMiniUpsell
    //   const diggy__ProductPrice = diggy__productMiniUpsell.productPrices.DiscountedPrice.Value
    //   const diggy__ShippingPrice = diggy__productMiniUpsell.shippings.length > 0 && diggy__productMiniUpsell.shippings[0].price
    //   totalPrice += diggy__ProductPrice + diggy__ShippingPrice
    // }

    // totalPrice = totalPrice * (1 + tax)
    // dynamic data
    return {
      countryCode: window.ctrwowUtils.localStorage().get('ctr__countryCode').toUpperCase(),
      currencyCode: window.ctrwowUtils.localStorage().get('currencyCode').toUpperCase(),
      totalPriceStatus: 'FINAL',
      totalPriceLabel: 'Total',
      totalPrice: amount.toFixed(2).toString()
    }
  }

  const onGooglePaymentButtonClicked = (hasDiggy = false) => {
    const paymentDataRequest = getGooglePaymentDataRequest()
    paymentDataRequest.transactionInfo = getGoogleTransactionInfo(hasDiggy)
    paymentDataRequest.shippingAddressRequired = true // fill shipping address on the popup

    const paymentsClient = getGooglePaymentsClient()
    paymentsClient
      .loadPaymentData(paymentDataRequest)
      .then(function (paymentData) {
        // handle the response
        processPayment(paymentData)
      })
      .catch(function (err) {
        console.log('Cancelled Bluesnap Google. Msg: ', err)
        window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl + '?' + _getUpParam() + '=0')
      })
  }
  window.__paymentWithBlueSnapGoogle = onGooglePaymentButtonClicked

  const processPayment = (paymentData) => {
    // show returned data in developer console for debugging
    console.log(paymentData)
    const paymentToken = b642utf8(JSON.stringify(paymentData))
    // @todo pass payment token to your gateway to process payment
    console.log(paymentToken)

    // placeUpsellOrder
    if (isUpgradeUpsell) {
      const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
      _placeUpsellUpgrade(product, paymentToken)
    } else {
      _placeUpsellOrder(paymentToken)
    }
  }

  const b642utf8 = (str) => {
    return btoa(
      str.replace(/[\u00A0-\u2666]/g, function (c) {
        return '&#' + c.charCodeAt(0) + ';'
      })
    )
  }
}
