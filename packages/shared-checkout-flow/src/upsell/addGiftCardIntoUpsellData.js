import { isVisibleElm } from 'shared-common/src/isVisibleElm'

export function detectAddCreditCardIntoUpsellData(upsellData) {
  try {
    let orderInfo = window.localStorage.getItem('orderInfo')
    if (!orderInfo) return upsellData
    orderInfo = JSON.parse(orderInfo)

    if (isVisibleElm(window._q('[name="payment"]'))) {
      delete upsellData.GiftCardNumber
      upsellData.payment = {
        name: `${orderInfo.cusFirstName} ${orderInfo.cusLastName}`,
        creditCardBrand: window._q('[name="creditCardBrand"]').value,
        creditcard: window._q('[name="creditcard"]').value.replace(/-/g, ''),
        expiration: window._q('[name="expiration"]').value,
        cvv: window._q('[name="cvv"]').value
      }
    }
  } catch (e) {
    console.log(e)
  }
}
export function addGiftCardIntoUpsellData(upsellData) {
  try {
    const giftCard = window.giftCard
    let orderInfo = window.localStorage.getItem('orderInfo')
    if (!giftCard || !orderInfo) return upsellData

    orderInfo = JSON.parse(orderInfo)

    if (!isVisibleElm(window._q('[name="payment"]'))) {
      upsellData.GiftCardNumber = giftCard.GiftCardNumber
    }

    if (giftCard.CanPayAll) {
      upsellData.payment = {
        paymentProcessorId: 62
      }
      upsellData.shippingAddress = {
        ...upsellData.shippingAddress,
        firstName: orderInfo.cusFirstName || '',
        lastName: orderInfo.cusLastName || '',
        phoneNumber: orderInfo.cusPhone || '',
        city: orderInfo.cusCity || '',
        countryCode: orderInfo.cusCountry || '',
        state: orderInfo.cusState || '',
        zipCode: orderInfo.cusZip || ''
      }
    }

    detectAddCreditCardIntoUpsellData(upsellData)

    window.localStorage.setItem('giftCard', JSON.stringify(giftCard))
  } catch (e) {
    console.log(e)
  }
}
