import { getPaymentName, PAYMENT_TYPE } from '../payment/paymentType.constants'
// import { addGiftCardIntoUpsellData } from './addGiftCardIntoUpsellData'
import { detectAddCreditCardIntoUpsellData } from './addGiftCardIntoUpsellData'
import { appendCtaClickIdTrackingParam } from 'shared-trackings'
import { adjustShippingOrder } from 'shared-common/src/adjustShippingOrder'
import { ctrwow__Stripe3Ds, loading3Ds } from '../payment/stripe.helpers'

console.log('ver: 1.0.1')
const __ctrPageConfiguration = window.__ctrPageConfiguration
let eCRM
window.ctrwowUtils
  .getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js', window.ctrwowUtils.getCtrLibLink('ctrwowUpsell', '1.2.0')])
  .then(() => {
    eCRM = new window.EmanageCRMJS({
      webkey: __ctrPageConfiguration.webKey,
      cid: __ctrPageConfiguration.cid,
      lang: '',
      isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
    })
  })

const upsell = {
  orderInfo: JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo')),
  products: [],
  upsellCampaignName: '',
  mainWebKey: __ctrPageConfiguration.webKey,
  upsellWebKey: window.upsellWebKey,
  CID: __ctrPageConfiguration.cid
}
// ********************************************************
export async function _getUpsellData(source) {
  const checkPaymentProcess = (paymentProcessorId) => {
    if (paymentProcessorId === 5 || paymentProcessorId === 31) {
      return true
    }
    return false
  }
  // creditcard => get card id
  let pay = {
    cardId: upsell.orderInfo.cardId
  }

  // !creditcard => get paymentprocessid
  var isPaymentPaypal = (upsell.orderInfo.useCreditCard && checkPaymentProcess(parseInt(upsell.orderInfo.paymentProcessorId))) || false
  if (window.localStorage.getItem('userPaymentType') === 'google_apple_pay') {
    upsell.orderInfo.paymentProcessorId = 54
  }
  if (!upsell.orderInfo.useCreditCard && upsell.orderInfo.paymentProcessorId) {
    pay = {
      paymentProcessorId: Number(upsell.orderInfo.paymentProcessorId)
    }
  } else {
    // add installment
    if (!!upsell.orderInfo.installmentValue && upsell.orderInfo.installmentValue !== '') {
      pay.Instalments = upsell.orderInfo.installmentValue
    }
  }

  // add callback param to server to keep track
  const replacedParam = window.ctrwowUtils.link
    .getCustomPathName()
    .replace(/\?|\&*paymentId=[^&]*/g, '')
    .replace(/\?|\&*token=[^&]*/g, '')
    .replace(/\?|\&*(transactionCode|errorCode|cancel)=[^&]*/g, '') // remove param of ale pay
    .replace(
      // eslint-disable-next-line no-useless-escape
      /\?|\&*PayerID=[^&]*/g,
      ''
    )
  pay.callBackParam = replacedParam !== '' ? '?' + replacedParam + '&' + _getUpParam() : '?' + _getUpParam()
  // add antiFraud
  let antiFraud
  try {
    antiFraud = JSON.parse(window.ctrwowUtils.localStorage().get('antiFraud'))
  } catch (ex) {
    console.log(ex)
    antiFraud = null
  }

  let radarSession
  try {
    radarSession = JSON.parse(window.ctrwowUtils.localStorage().get('stripeRadar')).radarSession
  } catch (ex) {
    console.log(ex)
    radarSession = null
  }

  const upsellData = {
    campaignUpsell: {
      webKey: upsell.mainWebKey,
      relatedOrderNumber: upsell.orderInfo.orderNumber
    },
    shippingMethodId:
      upsell.products[window.upsell_productindex].shippings.length > 0
        ? upsell.products[window.upsell_productindex].shippings[0].shippingMethodId
        : null,
    comment: '',
    useShippingAddressForBilling: true,
    productId: upsell.products[window.upsell_productindex].productId,
    customer: { email: !isPaymentPaypal ? upsell.orderInfo.cusEmail : null },
    payment: pay,
    shippingAddress: upsell.orderInfo.addressId != null ? { id: upsell.orderInfo.addressId } : null,
    funnelBoxId: 0,
    antiFraud: {
      sessionId: antiFraud ? antiFraud.sessionId : '',
      StripeRadarSessionId: radarSession ? radarSession.id : null
    }
  }
  const paymentType =
    !upsell.orderInfo.useCreditCard && upsellData.payment.paymentProcessorId ? getPaymentName(upsellData.payment.paymentProcessorId) : ''
  switch (paymentType) {
    case PAYMENT_TYPE.GAP: {
      upsellData.mid = {
        midId: eCRM.isTest ? window.midId.toString().substr(0, 3) : window.midId
      }
      break
    }
    case PAYMENT_TYPE.BRAINTREE: {
      upsellData.payment.nonce = source.nonce
      break
    }
    case PAYMENT_TYPE.SEPA: {
      upsellData.payment.iban = window.ctrwowUtils.localStorage().get('ctr__iBanNumber')
      upsellData.payment.name = window.ctrwowUtils.localStorage().get('user_firstname') + ' ' + window.ctrwowUtils.localStorage().get('user_lastname')
      break
    }
    case PAYMENT_TYPE.BLUESNAPGOOGLE: {
      // submit walletPaymentToken, name, midId
      upsellData.payment.walletPaymentToken = source
      upsellData.payment.name = ''
      upsellData.mid = {
        midId: eCRM.isTest ? window.objMidBlueSnapGoogle.midId.toString().substr(0, 3) : window.objMidBlueSnapGoogle.midId
      }
      break
    }
    case PAYMENT_TYPE.FASTLANE: {
      const paymentToken = await window.fastlane.getPaymentToken()
      upsellData.payment = {
        ...upsellData.payment,
        ...(paymentToken && { PaymentFieldToken: paymentToken })
      }
      break
    }
    case PAYMENT_TYPE.BLUESNAP_APPLE: {
      upsellData.payment.paymentFieldToken = window.bluesnapToken
      break
    }
    default:
      break
  }

  if (window.maroPostId) {
    upsellData.additionalInfo = [
      {
        key: 'MaropostSettingsId',
        value: window.maroPostId
      }
    ]
  }

  return upsellData
}

/* export function _adjustShippingOrder(data) {
  if (!data.shippings || (data.shippings && data.shippings.length <= 1)) {
    window.shippingIndex = 0
    return data
  }

  // ? Sort Shipping Fee. from free -> fee: a-b
  data.shippings.sort((a, b) => {
    return a.price - b.price
  })

  // ! Check rm param => update shippingIndex
  const rmParam = window.ctrwowUtils.link.queryURLParameter('rm')
  // ? If rm=1 => Free ship
  // ? else => Have Shipping Fee
  if (rmParam === '1' || window.isFreeShip) {
    window.shippingIndex = 0
  } else {
    window.shippingIndex = data.shippings.length - 1
  }
  return data
} */

export async function _getUpsellUpgradeData(product, source) {
  console.log('get upsell upgrade data: 1.0.0')
  // upsell.product = product
  upsell.product = adjustShippingOrder(product)
  // creditcard => get card id
  let pay = {
    cardId: upsell.orderInfo.cardId
  }

  // !creditcard => get paymentprocessid
  if (window.localStorage.getItem('userPaymentType') === 'google_apple_pay') {
    upsell.orderInfo.paymentProcessorId = 54
  }
  if (!upsell.orderInfo.useCreditCard && upsell.orderInfo.paymentProcessorId) {
    pay = {
      paymentProcessorId: Number(upsell.orderInfo.paymentProcessorId)
    }
  } else {
    // add installment
    if (!!upsell.orderInfo.installmentValue && upsell.orderInfo.installmentValue !== '') {
      pay.Instalments = upsell.orderInfo.installmentValue
    }
  }

  // add callback param to server to keep track
  const replacedParam = window.ctrwowUtils.link
    .getCustomPathName()
    .replace(/\?|\&*paymentId=[^&]*/g, '')
    .replace(/\?|\&*token=[^&]*/g, '')
    .replace(
      // eslint-disable-next-line no-useless-escape
      /\?|\&*PayerID=[^&]*/g,
      ''
    )
  pay.callBackParam = replacedParam !== '' ? '?' + replacedParam + '&' + _getUpParam() : '?' + _getUpParam()
  // add antiFraud
  let antiFraud
  try {
    antiFraud = JSON.parse(window.ctrwowUtils.localStorage().get('antiFraud'))
  } catch (ex) {
    console.log(ex)
    antiFraud = null
  }

  let radarSession
  try {
    radarSession = JSON.parse(window.ctrwowUtils.localStorage().get('stripeRadar')).radarSession
  } catch (ex) {
    console.log(ex)
    radarSession = null
  }

  // upgrade product upsell
  let pIDRegularUpgrade = upsell.orderInfo.orderedProducts[0].pid
  if (window.upgrade_upsell_product && window.pidRegularUpgrade) {
    pIDRegularUpgrade = window.pidRegularUpgrade
  }
  // end upgrade product upsell

  const upsellData = {
    productId: pIDRegularUpgrade,
    shippingMethodId: upsell.product.shippings.length > 0 ? upsell.product.shippings[window.shippingIndex || 0].shippingMethodId : null,
    comment: '',
    useShippingAddressForBilling: true,
    customer: {
      email: upsell.orderInfo.cusEmail
    },
    payment: pay,
    funnelBoxId: 0,
    shippingAddress: upsell.orderInfo.addressId != null ? { id: upsell.orderInfo.addressId } : null,
    billingAddress: null,
    antiFraud: {
      sessionId: antiFraud ? antiFraud.sessionId : '',
      StripeRadarSessionId: radarSession ? radarSession.id : null
    }
  }
  const paymentType =
    !upsell.orderInfo.useCreditCard && upsellData.payment.paymentProcessorId ? getPaymentName(upsellData.payment.paymentProcessorId) : ''
  switch (paymentType) {
    case PAYMENT_TYPE.GAP: {
      upsellData.mid = {
        midId: eCRM.isTest ? window.midId.toString().substr(0, 3) : window.midId
      }
      break
    }
    case PAYMENT_TYPE.BRAINTREE: {
      // submit nonce, deviceData, midId
      upsellData.payment.nonce = source && source.nonce ? source.nonce : null
      upsellData.payment.deviceData = source && source.deviceData ? source.deviceData : null
      upsellData.mid = {
        midId: eCRM.isTest ? window.objMidBraintreePaypal.midId.toString().substr(0, 3) : window.objMidBraintreePaypal.midId
      }
      break
    }
    case PAYMENT_TYPE.BLUESNAPGOOGLE: {
      // submit walletPaymentToken, name, midId
      upsellData.payment.walletPaymentToken = source
      upsellData.payment.name = ''
      upsellData.mid = {
        midId: eCRM.isTest ? window.objMidBlueSnapGoogle.midId.toString().substr(0, 3) : window.objMidBlueSnapGoogle.midId
      }
      break
    }
    case PAYMENT_TYPE.AMAZON_PAY: {
      upsellData.payment.region = source.region
      upsellData.payment.merchantId = source.merchantId
      upsellData.payment.providerOrderReferenceId = source.providerOrderReferenceId
      upsellData.mid = {
        midId: eCRM.isTest ? window.objMidAmazonPay.midId.toString().substr(0, 3) : window.objMidAmazonPay.midId
      }
      break
    }
    case PAYMENT_TYPE.SEPA: {
      // submit iban, name, midId
      upsellData.payment.iban = window.ctrwowUtils.localStorage().get('ctr__iBanNumber')
      upsellData.payment.name = window.ctrwowUtils.localStorage().get('user_firstname') + ' ' + window.ctrwowUtils.localStorage().get('user_lastname')
      break
    }
    case PAYMENT_TYPE.BLUESNAP_APPLE: {
      upsellData.payment.paymentFieldToken = window.bluesnapToken
      break
    }
    case PAYMENT_TYPE.FASTLANE: {
      const paymentToken = await window.fastlane.getPaymentToken()
      upsellData.payment = {
        ...upsellData.payment,
        ...(paymentToken && { PaymentFieldToken: paymentToken })
      }
      break
    }
    default:
      break
  }

  if (window.maroPostId) {
    upsellData.additionalInfo = [
      {
        key: 'MaropostSettingsId',
        value: window.maroPostId
      }
    ]
  }

  return upsellData
}

export function _saveLocalUpsellData(responseData) {
  const processing = new Promise((resolve, reject) => {
    if (responseData != null && responseData.success) {
      resolve(responseData, upsell)
    } else {
      reject(responseData, upsell)
    }
  })

  return processing.then((responseData, upsell) => {
    // window.ctrwowUtils.localStorage().set('fireUpsellForGTMPurchase', getUpParam() + '=1')
    window.ctrwowUtils.localStorage().set('paypal_isMainOrder', 'upsell')
    window.ctrwowUtils.localStorage().set('upsellOrderNumber', responseData.orderNumber)
    // success page will use this trackingNumber to call confirm payment api
    if (responseData.trackingNumber) {
      window.ctrwowUtils.localStorage().set('trackingNumber', responseData.trackingNumber)
    }

    const { upsellUrls = [] } = upsell.orderInfo
    const selectedProduct = upsell.products[window.upsell_productindex]
    upsellUrls.push({
      index: upsell.orderInfo.upsellIndex,
      price: selectedProduct.productPrices.DiscountedPrice.Value,
      campaignWebKey: upsell.upsellWebKey,
      campaignName: upsell.upsellCampaignName,
      orderNumber: responseData.orderNumber,
      customerId: responseData.customerResult.customerId,
      url: location.href,
      orderedProducts: [
        {
          sku: selectedProduct.sku,
          pid: selectedProduct.productId,
          name: selectedProduct.productName
        }
      ]
    })
    const savedOfUpsell = selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value
    upsell.orderInfo.upsellIndex += window.upsellStep || 1
    upsell.orderInfo.savedTotal += savedOfUpsell
    upsell.orderInfo.upsellPriceToUpgrade = selectedProduct.productPrices.DiscountedPrice.Value
    upsell.orderInfo.upsellUrls = upsellUrls

    window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(upsell.orderInfo))
    window.ctrwowUtils.localStorage().set('webkey_to_check_paypal', upsell.upsellWebKey)
  })
}

export function _getUpsellNameFromUrl() {
  let parts = location.href.split('special-offer-')
  if (parts.length > 1) {
    parts = parts[1].split('.html')[0].split('-')
    // remove last word which length = 24 (could be - guid of page)
    if (parts[parts.length - 1].length === 24) {
      parts.pop()
    }
    return parts.join('-')
  }
  return null
}

export function _handleLastUpsellOrError() {
  let upParam = ''
  const upsellName = _getUpsellNameFromUrl()
  if (upsellName) {
    upParam = '?up_' + upsellName
    if (upsell.orderInfo.isUpsellOrdered === 1) {
      upParam += '=1'
    } else {
      upParam += '=0'
    }
  }

  let redirectUrl = __ctrPageConfiguration.successUrl + upParam
  redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
  window.ctrwowUtils.link.redirectPage(redirectUrl)
}

export function _getUpParam() {
  let upParam = ''
  if (location.href.split('special-offer-', 2).length > 1) {
    upParam = 'up_' + location.href.split('special-offer-', 2)[1].split('.html', 1) + '=1'
  }
  return upParam
}
// ********************************************************

export async function _placeUpsellOrder(source = null) {
  window.ctrwowUtils.showGlobalLoading()
  console.log('_placeUpsellOrder debugger')
  const upsellData = await _getUpsellData(source)
  // addGiftCardIntoUpsellData(upsellData)
  detectAddCreditCardIntoUpsellData(upsellData)

  window.ctrwowUtils.events.emit('onBeforePlaceUpsellOrder')

  eCRM.Order.placeUpsellOrder(upsellData, __ctrPageConfiguration.webKey, function (dataResponse) {
    const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
    if (paymentType === 'google_apple_pay') {
      const midId = eCRM.isTest ? window.midId.toString().substr(0, 3) : window.midId
      eCRM.Order.confirmGoogleApplePay(dataResponse.trackingNumber, source.source.id, midId, (dataResponse) => {
        console.log(dataResponse)
      })
    }
    setTimeout(() => {
      if (!window.isPauseProcess) {
        _saveLocalUpsellData(dataResponse, window.upsell_productindex).then((dataResponse) => {
          if (dataResponse.callBackUrl) {
            // document.location = dataResponse.callBackUrl
            document.location = appendCtaClickIdTrackingParam(dataResponse.callBackUrl, $('.ctr_cta_button'))
          } else if (dataResponse.paymentContinueResult && dataResponse.paymentContinueResult.actionUrl !== '') {
            // document.location = dataResponse.paymentContinueResult.actionUrl
            document.location = appendCtaClickIdTrackingParam(dataResponse.paymentContinueResult.actionUrl, $('.ctr_cta_button'))
          } else if (upsell.orderInfo.upsellIndex < upsell.orderInfo.upsells.length) {
            const upsellUrl = upsell.orderInfo.upsells[upsell.orderInfo.upsellIndex].upsellUrl
            // let redirectUrl = upsellUrl.substring(
            //   upsellUrl.lastIndexOf('/') + 1,
            //   upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length
            // )
            let redirectUrl
            const splitLink = upsellUrl.split('?')
            if (splitLink.length <= 1) {
              redirectUrl = upsellUrl.substring(
                upsellUrl.lastIndexOf('/') + 1,
                upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length
              )
            } else {
              redirectUrl = splitLink[0].substr(splitLink[0].lastIndexOf('/') + 1)
            }
            redirectUrl = redirectUrl + '?' + _getUpParam() + '=1'
            try {
              redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
            } catch (e) {}

            window.ctrwowUtils.link.redirectPage(redirectUrl)
            // window.ctrwowUtils.link.redirectPage(redirectUrl + '?' + _getUpParam() + '=1')
          } else {
            _handleLastUpsellOrError()
          }
        })
      } else {
        window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
        window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
          _saveLocalUpsellData(dataResponse, window.upsell_productindex).then((dataResponse) => {
            if (dataResponse.callBackUrl) {
              // document.location = dataResponse.callBackUrl
              document.location = appendCtaClickIdTrackingParam(dataResponse.callBackUrl, $('.ctr_cta_button'))
            } else if (dataResponse.paymentContinueResult && dataResponse.paymentContinueResult.actionUrl !== '') {
              // document.location = dataResponse.paymentContinueResult.actionUrl
              document.location = appendCtaClickIdTrackingParam(dataResponse.paymentContinueResult.actionUrl, $('.ctr_cta_button'))
            } else if (upsell.orderInfo.upsellIndex < upsell.orderInfo.upsells.length) {
              const upsellUrl = upsell.orderInfo.upsells[upsell.orderInfo.upsellIndex].upsellUrl
              let redirectUrl = upsellUrl.substring(
                upsellUrl.lastIndexOf('/') + 1,
                upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length
              )
              redirectUrl = redirectUrl + '?' + _getUpParam() + '=1'
              try {
                redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
              } catch (e) {}
              window.ctrwowUtils.link.redirectPage(redirectUrl)
              // window.ctrwowUtils.link.redirectPage(redirectUrl + '?' + _getUpParam() + '=1')
            } else {
              _handleLastUpsellOrError()
            }
          })
        })
      }
    }, 3000)
  })
}

// export function _placeUpsellListicle(upsellData, upsell, upsell_productindex) {
//   eCRM.Order.placeUpsellOrder(upsellData, upsellWebKey, function(dataResponse){
//     const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
//     if (paymentType === 'google_apple_pay') {
//       let midId = eCRM.isTest ? window.midId.toString().substr(0, 3) : window.midId
//       eCRM.Order.confirmGoogleApplePay(result.trackingNumber, source.source.id, midId, (dataResponse) => {
//         console.log(dataResponse)
//       })
//     }
//     setTimeout(() => {
//       _saveLocalUpsellData().then((responseData, upsell, upsell_productindex) => {
//         console.log('abc')
//       })
//     }, 3000);
//   })
// }

async function __afterUpgradeUpsell({ result, eCRM, source }) {
  window.__loading3Ds = null
  loading3Ds && (window.__loading3Ds = loading3Ds(false))
  if (result != null && result.success) {
    // === start check 3Ds
    let __TIMEOUT = 0
    const __continueResult = result.paymentContinueResult?.items.reduce((objResult, item) => {
      objResult[item.key] = item.value
      return objResult
    }, {})

    if (__continueResult && __continueResult.requires_action === 'true') {
      try {
        window.__loading3Ds.waiting()
        // window.timeout4SuccessPopup defined in creditcardSB widget
        __TIMEOUT = window.timeout4SuccessPopup
        const __ctrwow__Stripe3Ds = await ctrwow__Stripe3Ds()
        const { requires_action_publishkey, requires_action_client_secret } = __continueResult
        const rsVerify3Ds = await __ctrwow__Stripe3Ds.init({
          publicKey: requires_action_publishkey,
          clientSecrect: requires_action_client_secret
        })
        if (rsVerify3Ds && rsVerify3Ds.success) {
          const __confirm3Ds = await __ctrwow__Stripe3Ds.confirmOrder(result)
          if (__confirm3Ds.success) {
            // window.isUseSuccessAndFailPopup defined in creditcardSB widget
            window.isUseSuccessAndFailPopup && window.__loading3Ds.success()
          } else {
            // window.isUseSuccessAndFailPopup defined in creditcardSB widget
            window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
            setTimeout(() => {
              window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
            }, __TIMEOUT)
          }
        } else {
          // window.isUseSuccessAndFailPopup defined in creditcardSB widget
          window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
          setTimeout(() => {
            window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
          }, __TIMEOUT)
        }
      } catch (error) {
        window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
      }
    }
    // === end check 3Ds

    setTimeout(() => {
      const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
      if (paymentType === 'google_apple_pay') {
        // const midId = window.ctrwowUtils.link.getQueryParameter('isCardTest') ? window.midId.toString().substr(0, 3) : window.midId

        const midId = window.ctrwowUtils.link.getQueryParameter('isCardTest') ? 2 : window.midId
        eCRM.Order.confirmGoogleApplePay(result.trackingNumber, source.source.id, midId, (dataResponse) => {
          console.log(dataResponse)
          window.ctrwowUtils.localStorage().set('fireUpsellForGTMPurchase', _getUpParam().split('=')[0])
          window.ctrwowUtils.localStorage().set('paypal_isMainOrder', 'upsell')
          window.ctrwowUtils.localStorage().set('isMainOrder', 'upsell')
          window.ctrwowUtils.localStorage().set('upsellOrderNumber', result.orderNumber)
          window.ctrwowUtils.localStorage().set('webkey_to_check_paypal', window.__ctrPageConfiguration.webKey)
          if (dataResponse && dataResponse.success === true) {
            const orderInfoData = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
            orderInfoData.upsellIndex += 1
            orderInfoData.orderTotal = upsell.product.productPrices.DiscountedPrice.Value
            orderInfoData.savedTotal = upsell.product.productPrices.FullRetailPrice.Value - upsell.orderInfo.orderTotal
            orderInfoData.orderedProducts.sku = upsell.product.sku
            window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(orderInfoData))

            if (result.upsells.length < 1 && orderInfoData.upsells.length > 0) {
              result.upsells = orderInfoData.upsells
            }
            if (result.callBackUrl) {
              document.location = result.callBackUrl
            } else if (result.paymentContinueResult && result.paymentContinueResult.actionUrl !== '') {
              document.location = result.paymentContinueResult.actionUrl
            } else if (orderInfoData.upsellIndex < orderInfoData.upsells.length) {
              const upsellUrl = orderInfoData.upsells[orderInfoData.upsellIndex].upsellUrl
              const redirectUrl = upsellUrl.substring(
                upsellUrl.lastIndexOf('/') + 1,
                upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length
              )
              window.ctrwowUtils.link.redirectPage(redirectUrl + '?' + _getUpParam())
            } else {
              window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
            }
          } else {
            window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
          }
        })
      } else {
        window.ctrwowUtils.localStorage().set('fireUpsellForGTMPurchase', _getUpParam().split('=')[0])
        window.ctrwowUtils.localStorage().set('paypal_isMainOrder', 'upsell')
        window.ctrwowUtils.localStorage().set('isMainOrder', 'upsell')
        window.ctrwowUtils.localStorage().set('upsellOrderNumber', result.orderNumber)
        window.ctrwowUtils.localStorage().set('webkey_to_check_paypal', window.__ctrPageConfiguration.webKey)

        const orderInfoData = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
        orderInfoData.upsellIndex += window.upsellStep || 1
        orderInfoData.orderTotal = upsell.product.productPrices.DiscountedPrice.Value
        orderInfoData.savedTotal = upsell.product.productPrices.FullRetailPrice.Value - upsell.orderInfo.orderTotal
        orderInfoData.orderedProducts.sku = upsell.product.sku
        window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(orderInfoData))

        if (result.upsells.length < 1 && orderInfoData.upsells.length > 0) {
          result.upsells = orderInfoData.upsells
        }
        if (result.callBackUrl) {
          document.location = result.callBackUrl
        } else if (result.paymentContinueResult && result.paymentContinueResult.actionUrl !== '') {
          document.location = result.paymentContinueResult.actionUrl
        } else if (orderInfoData.upsellIndex < orderInfoData.upsells.length) {
          const upsellUrl = orderInfoData.upsells[orderInfoData.upsellIndex].upsellUrl
          let redirectUrl = upsellUrl.substring(
            upsellUrl.lastIndexOf('/') + 1,
            upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length
          )
          redirectUrl = redirectUrl + '?' + _getUpParam()
          try {
            redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
          } catch (e) {}
          window.ctrwowUtils.link.redirectPage(redirectUrl)

          // window.ctrwowUtils.link.redirectPage(redirectUrl + '?' + _getUpParam())
        } else {
          // window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
          window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
        }
      }
    }, __TIMEOUT)
  } else {
    window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
    // window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
  }
}

export async function _placeUpsellUpgrade(product, source = null) {
  const paymentType = window.localStorage.getItem('userPaymentType').toLocaleLowerCase() || ''

  switch (paymentType) {
    case 'paypal':
    case 'fastlane': {
      const elmLoading = document.querySelector('.paymentProccessing.paymentProccessing_paypal')
      if (elmLoading) {
        elmLoading && elmLoading.classList.remove('js-hidden')
        window.isNotShowLoading = true
      }
      break
    }
    default: {
      const elmLoading = document.querySelector('.paymentProccessing.paymentProccessing_creditcard')
      if (elmLoading) {
        elmLoading && elmLoading.classList.remove('js-hidden')
        window.isNotShowLoading = true
      }
      break
    }
  }

  if (!window.isNotShowLoading) {
    window.ctrwowUtils.showGlobalLoading()
  }

  const upsellData = await _getUpsellUpgradeData(product, source)
  // addGiftCardIntoUpsellData(upsellData)
  detectAddCreditCardIntoUpsellData(upsellData)
  window.ctrwowUtils.events.emit('onBeforePlaceUpsellOrder')
  // const urlPostAPI = `${window.__ctrPageConfiguration.crmEndpoint}/orders/${upsell.orderInfo.orderNumber}/${upsell.product.productId}`
  // upgrade product upsell
  let orderUpgrade = upsell.orderInfo.orderNumber
  if (window.upgrade_upsell_product && window.orderNumberUpgrade) {
    orderUpgrade = window.orderNumberUpgrade
  }
  // end upgrade product upsell
  const urlPostAPI = `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/orders/upgrade/${orderUpgrade}/${upsell.product.productId}`

  window.ctrwowUtils
    .callAjax(urlPostAPI, {
      method: 'POST',
      headers: {
        X_CID: __ctrPageConfiguration.cid,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(upsellData)
    })
    .then((result) => {
      if (!window.isPauseProcess) {
        __afterUpgradeUpsell({ result: result, eCRM: eCRM, source: source })
      } else {
        window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
        window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
          __afterUpgradeUpsell({ result: result, eCRM: eCRM, source: source })
        })
      }
    })
    .catch((err) => {
      console.log(err)
      // window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
      window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
    })
}
