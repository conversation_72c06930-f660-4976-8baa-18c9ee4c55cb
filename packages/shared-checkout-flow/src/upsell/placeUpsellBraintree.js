import { _placeUpsellUpgrade, _placeUpsell<PERSON>rder, _getUpParam } from './helpers'

const __ctrPageConfiguration = window.__ctrPageConfiguration
let eCRM
window.ctrwowUtils.getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js']).then(() => {
  eCRM = new window.EmanageCRMJS({
    webkey: __ctrPageConfiguration.webKey,
    cid: __ctrPageConfiguration.cid,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })
})

export function _placeUpsellBraintree(ctaButtons, isUpgradeUpsell = false) {
  window.ctrwowUtils.getDependencies(['https://js.braintreegateway.com/js/braintree-2.32.1.min.js']).then(() => {
    eCRM.Order.getMidAndPrn((data) => {
      try {
        window.token = data.token
        window.objMidBraintreePaypal = data

        Array.prototype.slice.call(ctaButtons).forEach((ele) => {
          ele.addEventListener('click', function () {
            initBraintree(isUpgradeUpsell)
            const checkoutUpsell = setInterval(() => {
              if (window.checkout) {
                clearInterval(checkoutUpsell)
                window.checkout.paypal.initAuthFlow()
              }
            }, 200)
          })
        })
      } catch (err) {
        console.log('Braintree get midId error: ', err)
      }
    }, 52)
  })
}
export function initBraintree(isUpgradeUpsell) {
  const orderInfo = window.ctrwowUtils.localStorage().get('orderInfo') ? JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo')) : null
  const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
  const amount = product.productPrices.DiscountedPrice.Value - orderInfo.orderTotal
  // if (spanUpsellPriceElm) {
  //   amount = Number(spanUpsellPriceElm.innerText.replace(/[^0-9]/g, ''))
  // }
  window.braintree.setup(window.token, 'custom', {
    onReady: function (integration) {
      window.checkout = integration
    },
    onPaymentMethodReceived: function (payload) {
      // receive payload with nonce
      console.log(payload)
      const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
      if (payload && payload.nonce) {
        if (isUpgradeUpsell) {
          _placeUpsellUpgrade(product, payload)
        } else {
          _placeUpsellOrder(payload)
        }
      } else {
        console.log('Cannot find nonce!')
      }
    },
    paypal: {
      singleUse: true,
      amount,
      currency: window.ctrwowUtils.localStorage().get('currencyCode'),
      enableShippingAddress: 'true',
      headless: true,
      onAuthorizationDismissed: function (err) {
        console.log('Cancelled Braintree Paypal. Msg: ', err)
        window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl + '?' + _getUpParam() + '=0')
      }
    }
  })
}
