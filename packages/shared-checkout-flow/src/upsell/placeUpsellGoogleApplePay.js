import { _placeUpsellUpgrade, _placeUpsellOrder, _getUpParam } from './helpers'

const __ctrPageConfiguration = window.__ctrPageConfiguration
let eCRM
window.ctrwowUtils.getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js']).then(() => {
  eCRM = new window.EmanageCRMJS({
    webkey: __ctrPageConfiguration.webKey,
    cid: __ctrPageConfiguration.cid,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })
})

export function _placeUpsellOrderByGoogleOrApple(ctaButtons, isUpsellUpgrade = false) {
  window.ctrwowUtils.getDependencies(['https://js.stripe.com/v3/']).then(() => {
    initStripe(ctaButtons, isUpsellUpgrade)
  })
}

export function initStripe(ctaButtons, isUpsellUpgrade) {
  eCRM.Order.getMidAndPrn((data) => {
    if (!window.Stripe) {
      console.log('no stripe loaded')
    }

    try {
      window.midId = data.midId
      const stripe = window.Stripe(data.prnCode.split(';')[0], { stripeAccount: data.prnCode.split(';')[1] })

      // init Stripe instance with default value
      let countryCode = window.ctrwowUtils.localStorage().get('countryCode')
      let currencyCode = window.ctrwowUtils.localStorage().get('currencyCode')

      // override for testing at the unsupported countries
      const isTestGAP = window.ctrwowUtils.link.getQueryParameter('isTestGAP')
      if (isTestGAP && isTestGAP === 'true') {
        countryCode = 'US'
        currencyCode = 'usd'
      }

      // init instance with default value
      const paymentRequest = stripe.paymentRequest({
        country: countryCode,
        currency: currencyCode.toLowerCase(),
        total: {
          label: 'Sample Product',
          amount: 0
        },
        requestPayerName: true,
        requestPayerEmail: true,
        requestPayerPhone: true
      })

      // Check the availability of the Payment Request API first.
      paymentRequest.canMakePayment().then(function (result) {
        if (result) {
          console.log(result)
          Array.prototype.slice.call(ctaButtons).forEach((ele) => {
            ele.addEventListener('click', function (e) {
              e.preventDefault()
              const spanUpsellPriceElm = document.querySelector('.spanUpsellPrice')
              let amount = 0
              if (spanUpsellPriceElm) {
                amount = Number(spanUpsellPriceElm.innerText.replace(/[^0-9]/g, ''))
              }
              window.taxPercent = window.taxPercent || 0
              amount = Number((amount * (1 + window.taxPercent)).toFixed(0))

              // get product name from className js-name or crm
              const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
              const elm__MainProductName = document.querySelector('.js-name')
              const productName = elm__MainProductName ? elm__MainProductName.innerText : product.productName

              paymentRequest.update({
                currency: window.ctrwowUtils.localStorage().get('currencyCode').toLowerCase(),
                total: {
                  label: productName,
                  amount
                }
              })
              paymentRequest.show()
            })
          })
        } else {
          console.log('not support')
        }
      })

      paymentRequest.on('source', function (event) {
        console.log(event)
        event.complete('success')
        window.ctrwowUtils.localStorage().set('google_apple_upsell_email', event.payerEmail)
        if (isUpsellUpgrade) {
          const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
          _placeUpsellUpgrade(product, event)
        } else {
          _placeUpsellOrder(event)
        }
      })
      paymentRequest.on('cancel', function (event) {
        console.log(event)
        document.querySelector('.loading-wrapper').style.display = 'block'
        window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl + '?' + _getUpParam() + '=0')
      })
    } catch (err) {
      console.log(err)
    }
  }, 54)
}
