import { _placeUpsellUpgrade, _placeUpsellOrder } from './helpers'
import { __showAmazonButton, __initAmazonPay, __getUpsellPayLoad } from './../payment/amazonPay.helpers'

const __ctrPageConfiguration = window.__ctrPageConfiguration
let eCRM
window.ctrwowUtils.getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js']).then(() => {
  eCRM = new window.EmanageCRMJS({
    webkey: __ctrPageConfiguration.webKey,
    cid: __ctrPageConfiguration.cid,
    lang: '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })
})

export function _placeUpsellAmazonPay(ctaButtons, isUpgradeUpsell = false) {
  // append HTML Amazon into upsell page
  var appendAmazonHTML = function appendAmazonHTML() {
    console.log('append Amazon HTML')

    window.isRenderAmazonHTML = true
    var str__AmazonHTML = `<div class="amazon-wrapper" style="max-width: 300px; margin: 0 auto;">
      <div id="amazonButton"></div>
      <div id="amazonShippingAddress" style="display: none; height: 200px; margin-bottom: 15px;"></div>
      <div id="amazonWallet" style="display: none; height: 200px;"></div>
    </div>`
    var elm__AmazonHTML = document.createElement('div')
    elm__AmazonHTML.innerHTML = str__AmazonHTML

    if (ctaButtons) {
      var elm__Parent = ctaButtons[0].parentNode
      elm__Parent && elm__Parent.insertBefore(elm__AmazonHTML, ctaButtons[0])
    }
  }

  const elm__AmazonWrapper = document.querySelector('.amazon-wrapper')
  console.log(elm__AmazonWrapper)

  if (elm__AmazonWrapper !== null) {
    appendAmazonHTML()

    eCRM.Order.getMidAndPrn((data) => {
      try {
        data.midId = eCRM.isTest ? parseInt(data.midId.toString().substring(0, 3)) : data.midId
        window.objMidAmazonPay = data

        window.sellerId = window.objMidAmazonPay.merchantId
        window.clientId = window.objMidAmazonPay.clientId
        var elm__AmazonPay = {
          idAmazonButton: 'amazonButton',
          idAmazonShipping: 'amazonShippingAddress',
          idAmazonWallet: 'amazonWallet',
          classAmazonSubmitButton: 'js-btn-place-upsell-order'
        }

        ctaButtons.length > 0 &&
          Array.prototype.slice.call(ctaButtons).forEach((item) => {
            item.style.opacity = '0.7'
            item.style.pointerEvents = 'none'
          })

        __showAmazonButton({ id__AmazonButton: elm__AmazonPay.idAmazonButton })
        __initAmazonPay({
          elm: document,
          id__AmazonButton: elm__AmazonPay.idAmazonButton,
          id__AmazonShipping: elm__AmazonPay.idAmazonShipping,
          id__AmazonWallet: elm__AmazonPay.idAmazonWallet,
          class__AmazonSubmitButton: elm__AmazonPay.classAmazonSubmitButton
        })
      } catch (err) {
        console.log('Amazon Pay Error: ', err)
      }
    }, 61)
  }

  var addEventListenerToAmazonButton = function addEventListenerToAmazonButton(ctaButtons) {
    Array.prototype.slice.call(ctaButtons).forEach(function (ele) {
      ele.addEventListener('click', function (e) {
        e.preventDefault()
        var postData = __getUpsellPayLoad()
        const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
        if (isUpgradeUpsell) {
          _placeUpsellUpgrade(product, postData)
        } else {
          _placeUpsellOrder(postData)
        }
      })
    })
  }

  window.ctrwowUtils.events.on('emit__AmazonSaveClientBehavior', function () {
    addEventListenerToAmazonButton(ctaButtons)
  })
}
