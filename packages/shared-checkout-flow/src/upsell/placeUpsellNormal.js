import { _placeUpsellOrder, _placeUpsellUpgrade } from './helpers'

export function _placeUpsellNormal(ctaButtons, isUpsellUpgrade = false) {
  Array.prototype.slice.call(ctaButtons).forEach((ele) => {
    ele.addEventListener('click', function (e) {
      e.preventDefault()
      const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
      if (isUpsellUpgrade) {
        _placeUpsellUpgrade(product, null)
      } else {
        _placeUpsellOrder()
      }
    })
  })
}
