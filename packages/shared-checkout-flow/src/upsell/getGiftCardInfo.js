export function detectToggleCreditCardForm(products) {
  try {
    window.giftCard = undefined
    let giftCard = window.localStorage.getItem('giftCard')
    if (giftCard) {
      giftCard = JSON.parse(giftCard)
      // window.q('[name="payment"]').display('block')

      const currentProduct = products.prices[window.upsell_productindex]
      const discountedPrice = currentProduct.productPrices.DiscountedPrice.Value
      const shippingPrice = currentProduct.shippings[0].price
      const totalPrice = (discountedPrice + shippingPrice) * (1 + (window.taxPercent || 0))

      let isJustUseGiftCardAtMainOrder = false
      if (giftCard.CanPayAll) {
        isJustUseGiftCardAtMainOrder = true
        if (giftCard.Balance >= totalPrice) {
          window.q('[name="payment"]').hide()
        } else {
          window.q('[name="payment"]').display('block')
        }
      } else {
        // Use gift card and payment information at main order
        window.q('[name="payment"]').hide()
      }

      let giftCardPrice = 0
      giftCard.CanPayAll = false
      giftCard.FreeShip = false

      if (shippingPrice === 0) giftCard.FreeShip = true

      if (giftCard.Balance >= totalPrice) {
        giftCardPrice = totalPrice
        if (giftCard.FreeShip || giftCard.CanPayShipping) {
          giftCard.CanPayAll = true
        } else if (isJustUseGiftCardAtMainOrder) {
          window.q('[name="payment"]').display('block')
        }
      } else {
        giftCardPrice = giftCard.Balance
      }

      giftCard.NewBalance = giftCard.Balance - giftCardPrice
      window.giftCard = giftCard
      // window.localStorage.setItem('giftCard', JSON.stringify(giftCard))
    } else {
      window.q('[name="payment"]').hide()
    }
  } catch (e) {
    console.log(e)
  }
}
export function getGiftCardInfo(products) {
  let giftCard = window.localStorage.getItem('giftCard')
  if (!giftCard) return
  if (window.isCallingGiftCard) {
    return
  }

  giftCard = JSON.parse(giftCard)
  const url = `//sales-api.tryemanagecrm.com/api/giftcards/balance/${giftCard.GiftCardNumber}`
  const settings = {
    headers: { X_CID: window.__CTRWOW_CONFIG.X_CID }
  }
  window.isCallingGiftCard = true
  window.q('[name="payment"]').hide()
  window.ctrwowUtils
    .callAjax(url, settings)
    .then((result) => {
      if (result && result.balance > 0) {
        giftCard.Balance = result.balance
        window.localStorage.setItem('giftCard', JSON.stringify(giftCard))
      } else {
        window.localStorage.removeItem('giftCard')
      }
      detectToggleCreditCardForm(products)
      window.isCallingGiftCard = false
    })
    .catch((e) => {
      window.isCallingGiftCard = true
      window.localStorage.removeItem('giftCard')
      detectToggleCreditCardForm(products)
      console.log(e)
    })
}
