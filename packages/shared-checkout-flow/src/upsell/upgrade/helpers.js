import { PAYMENT_TYPE } from '../../payment/paymentType.constants'
import { adjustShippingOrder } from 'shared-common/src/adjustShippingOrder'
import { detectAddCreditCardIntoUpsellData } from '../addGiftCardIntoUpsellData'
import { appendCtaClickIdTrackingParam } from 'shared-trackings'
import { ctrwow__Stripe3Ds, loading3Ds } from '../../payment/stripe.helpers'
import getPageSettings from '../../getPageSettings'

const upsellHelpers = () => {
  const { __ctrPageConfiguration, ctrwowUtils } = window
  let eCRM
  ctrwowUtils.getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js']).then(() => {
    eCRM = new window.EmanageCRMJS({
      webkey: __ctrPageConfiguration.webKey,
      cid: __ctrPageConfiguration.cid,
      lang: '',
      isTest: !!ctrwowUtils.link.getParameterByName('isCardTest')
    })
  })

  const upsell = {
    orderInfo: JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo')),
    products: [],
    upsellCampaignName: '',
    mainWebKey: __ctrPageConfiguration.webKey,
    upsellWebKey: window.upsellWebKey,
    CID: __ctrPageConfiguration.cid
  }

  const getMid = (paymentProcessorId) => {
    return Promise((resolve, reject) => {
      eCRM.Order.getMidAndPrn(paymentProcessorId)
        .then((data) => resolve(data))
        .catch((error) => reject(error))
    })
  }

  // # Get upsell param
  const __getUpParam = () => {
    let upParam = ''
    if (location.href.split('special-offer-', 2).length > 1) {
      upParam = 'up_' + location.href.split('special-offer-', 2)[1].split('.html', 1) + '=1'
    }
    return upParam
  }

  // #1 Get upsell data
  const getUpsellUpgradeData = async (product, additionalPaymentInfo) => {
    // default case credit card
    const __upsellData = {
      payment: {
        cardId: upsell.orderInfo.cardId
      },
      mid: ''
    }
    upsell.product = adjustShippingOrder(product)

    const paymentType = ctrwowUtils.localStorage().get('userPaymentType')
    switch (paymentType) {
      case PAYMENT_TYPE.GAP: {
        __upsellData.mid = {
          midId: eCRM.isTest ? window.midId.toString().substr(0, 3) : window.midId
        }
        break
      }
      case PAYMENT_TYPE.BRAINTREE: {
        __upsellData.payment = {
          nonce: additionalPaymentInfo.nonce || null,
          deviceData: additionalPaymentInfo.deviceData || null
        }
        __upsellData.mid = {
          midId: eCRM.isTest ? window.objMidBraintreePaypal.midId.toString().substr(0, 3) : window.objMidBraintreePaypal.midId
        }
        break
      }
      case PAYMENT_TYPE.BLUESNAPGOOGLE: {
        __upsellData.payment = {
          walletPaymentToken: additionalPaymentInfo,
          name: ''
        }
        __upsellData.mid = {
          midId: eCRM.isTest ? window.objMidBlueSnapGoogle.midId.toString().substr(0, 3) : window.objMidBlueSnapGoogle.midId
        }
        break
      }
      case PAYMENT_TYPE.AMAZON_PAY: {
        __upsellData.payment = {
          region: additionalPaymentInfo.region || null,
          merchantId: additionalPaymentInfo.merchantId || null,
          providerOrderReferenceId: additionalPaymentInfo.providerOrderReferenceId
        }
        __upsellData.mid = {
          midId: eCRM.isTest ? window.objMidAmazonPay.midId.toString().substr(0, 3) : window.objMidAmazonPay.midId
        }
        break
      }
      case PAYMENT_TYPE.SEPA: {
        __upsellData.payment = {
          iban: ctrwowUtils.localStorage().get('ctr__iBanNumber'),
          name: ctrwowUtils.localStorage().get('user_firstname') + ' ' + ctrwowUtils.localStorage().get('user_lastname')
        }
        break
      }
      case PAYMENT_TYPE.BLUESNAP_APPLE: {
        __upsellData.payment = {
          paymentFieldToken: window.bluesnapToken
        }
        break
      }
      case PAYMENT_TYPE.FASTLANE: {
        const paymentToken = await window.fastlane.getPaymentToken()
        __upsellData.payment = {
          ...(paymentToken && { PaymentFieldToken: paymentToken })
        }
        break
      }
      default: {
        break
      }
    }

    const replacedParam = window.ctrwowUtils.link
      .getCustomPathName()
      .replace(/\?|\&*paymentId=[^&]*/g, '')
      .replace(/\?|\&*token=[^&]*/g, '')
      .replace(
        // eslint-disable-next-line no-useless-escape
        /\?|\&*PayerID=[^&]*/g,
        ''
      )
    const antiFraud = JSON.parse(window.ctrwowUtils.localStorage().get('antiFraud')) || null
    const radarSession = JSON.parse(window.ctrwowUtils.localStorage().get('stripeRadar')).radarSession || null

    // upgrade product upsell
    let pIDRegularUpgrade = upsell.orderInfo.orderedProducts[0].pid
    if (window.upgrade_upsell_product && window.pidRegularUpgrade) {
      pIDRegularUpgrade = window.pidRegularUpgrade
    }
    // end upgrade product upsell

    const upsellData = {
      ...__upsellData,
      productId: pIDRegularUpgrade,
      shippingMethodId: upsell.product.shippings.length > 0 ? upsell.product.shippings[window.shippingIndex || 0].shippingMethodId : null,
      comment: '',
      useShippingAddressForBilling: true,
      customer: {
        email: upsell.orderInfo.cusEmail
      },
      payment: {
        ...__upsellData.payment,
        callBackParam: replacedParam !== '' ? '?' + replacedParam + '&' + __getUpParam() : '?' + __getUpParam()
      },
      funnelBoxId: 0,
      shippingAddress: upsell.orderInfo.addressId != null ? { id: upsell.orderInfo.addressId } : null,
      billingAddress: null,
      antiFraud: {
        sessionId: antiFraud ? antiFraud.sessionId : '',
        StripeRadarSessionId: radarSession ? radarSession.id : null
      },
      ...(window.maroPostId && {
        additionalInfo: [
          {
            key: 'MaropostSettingsId',
            value: window.maroPostId
          }
        ]
      })
    }

    return upsellData
  }

  // #3.1 confirm upsell order (optional)
  const __confirmUpgradeUpsell = async (paymentType, { result, additionalPaymentInfo }) => {
    switch (paymentType) {
      case PAYMENT_TYPE.GAP: {
        const midId = window.ctrwowUtils.link.getQueryParameter('isCardTest') ? 2 : window.midId
        eCRM.Order.confirmGoogleApplePay(result.trackingNumber, additionalPaymentInfo.source.id, midId, (dataResponse) => {
          __continueFlow(result)
        })
        break
      }
      case PAYMENT_TYPE.STRIPE_ELEMENTS: {
        __continueFlow(result)
        break
      }
      case PAYMENT_TYPE.PAYPAL_GOOGLE_PAY: {
        if (!result?.trackingNumber) {
          window.ctrwowUtils.link.redirectPage(getPageSettings().confirmUrl)
        } else {
          const { status } = await window.paypal.Googlepay().confirmOrder({
            orderId: result.trackingNumber,
            paymentMethodData: additionalPaymentInfo.paymentMethodData
          })

          if (status === 'APPROVED') {
            const URL = `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/orders/${window.__ctrPageConfiguration.webKey}?trackingNumber=${
              result.trackingNumber
            }`
            window.ctrwowUtils
              .callAjax(URL, {
                method: 'PUT',
                headers: {
                  X_CID: window.__ctrPageConfiguration.cid,
                  'Content-Type': 'application/json'
                }
              })
              .then((rs) => {
                if (!window.isPauseProcess) {
                  __continueFlow(result)
                } else {
                  window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder')
                  window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
                    __continueFlow(result)
                  })
                }
              })
              .catch((error) => {
                console.error('Error updating order status:', error)
                window.ctrwowUtils.link.redirectPage(getPageSettings().declineUrl)
              })
          } else {
            window.ctrwowUtils.link.redirectPage(getPageSettings().confirmUrl)
          }
        }
        break
      }
    }
  }

  // #3.2 continue flow
  const __continueFlow = (result, isRedirectPage = false) => {
    window.ctrwowUtils.localStorage().set('fireUpsellForGTMPurchase', __getUpParam().split('=')[0])
    window.ctrwowUtils.localStorage().set('paypal_isMainOrder', 'upsell')
    window.ctrwowUtils.localStorage().set('isMainOrder', 'upsell')
    window.ctrwowUtils.localStorage().set('upsellOrderNumber', result.orderNumber)
    window.ctrwowUtils.localStorage().set('webkey_to_check_paypal', window.__ctrPageConfiguration.webKey)

    const orderInfoData = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
    orderInfoData.upsellIndex += window.upsellStep || 1
    orderInfoData.orderTotal = upsell.product.productPrices.DiscountedPrice.Value
    orderInfoData.savedTotal = upsell.product.productPrices.FullRetailPrice.Value - upsell.orderInfo.orderTotal
    orderInfoData.orderedProducts.sku = upsell.product.sku
    window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(orderInfoData))

    if (isRedirectPage) return

    if (result.upsells.length < 1 && orderInfoData.upsells.length > 0) {
      result.upsells = orderInfoData.upsells
    }
    if (result.callBackUrl) {
      document.location = result.callBackUrl
    } else if (result.paymentContinueResult && result.paymentContinueResult.actionUrl !== '') {
      document.location = result.paymentContinueResult.actionUrl
    } else if (orderInfoData.upsellIndex < orderInfoData.upsells.length) {
      const upsellUrl = orderInfoData.upsells[orderInfoData.upsellIndex].upsellUrl
      let redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
      redirectUrl = redirectUrl + '?' + __getUpParam()
      try {
        redirectUrl = appendCtaClickIdTrackingParam(redirectUrl, $('.ctr_cta_button'))
      } catch (e) {}
      window.ctrwowUtils.link.redirectPage(redirectUrl)
    } else {
      window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
    }
  }

  // #2 place upsell order
  const placeUpgradeUpsell = async (product, additionalPaymentInfo) => {
    if (!window.isNotShowLoading) {
      ctrwowUtils.showGlobalLoading()
    }

    const upsellData = await getUpsellUpgradeData(product, additionalPaymentInfo)
    detectAddCreditCardIntoUpsellData(upsellData)
    window.ctrwowUtils.events.emit('onBeforePlaceUpsellOrder')

    // upgrade product upsell
    let orderUpgrade = upsell.orderInfo.orderNumber
    if (window.upgrade_upsell_product && window.orderNumberUpgrade) {
      orderUpgrade = window.orderNumberUpgrade
    }

    const url__UpgradeUpsell = `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/orders/upgrade/${orderUpgrade}/${upsell.product.productId}`
    window.ctrwowUtils
      .callAjax(url__UpgradeUpsell, {
        method: 'POST',
        headers: {
          X_CID: __ctrPageConfiguration.cid,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(upsellData)
      })
      .then((result) => {
        if (!window.isPauseProcess) {
          afterPlaceUpsell({ result, eCRM, additionalPaymentInfo })
        } else {
          window.ctrwowUtils.events.emit('onAfterPlaceUpsellOrder', result)
          window.ctrwowUtils.events.on('continuePlaceUpsellOrderProcess', () => {
            afterPlaceUpsell({ result, eCRM, additionalPaymentInfo })
          })
        }
      })
      .catch((err) => {
        console.log(err)
        // window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
        window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
      })
  }

  // #3 after place upsell order
  const afterPlaceUpsell = async ({ result, eCRM, additionalPaymentInfo }) => {
    window.__loading3Ds = null
    loading3Ds && (window.__loading3Ds = loading3Ds(false))

    if (result != null && result.success) {
      const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
      switch (paymentType) {
        case PAYMENT_TYPE.GAP:
        case PAYMENT_TYPE.STRIPE_ELEMENTS:
        case PAYMENT_TYPE.PAYPAL_GOOGLE_PAY: {
          __confirmUpgradeUpsell(paymentType, { result, additionalPaymentInfo })
          break
        }
        default: {
          // ===== case credit-card =====
          // === start check stripe 3Ds
          let __TIMEOUT = 0
          window.__loading3Ds.waiting()
          // window.timeout4SuccessPopup defined in creditcardSB widget
          __TIMEOUT = window.timeout4SuccessPopup
          const __ctrwow__Stripe3Ds = await ctrwow__Stripe3Ds()
          __ctrwow__Stripe3Ds.confirmUpsell({ orderNumber: result.orderNumber }).then(async (__confirmUpsell) => {
            const __continueResult = __confirmUpsell[0].items.reduce((objResult, item) => {
              objResult[item.key] = item.value
              return objResult
            }, {})

            if (__continueResult && __continueResult.requires_action === 'true') {
              window.__loading3Ds.waiting()
              const { requires_action_publishkey, requires_action_client_secret } = __continueResult
              const rsVerify3Ds = await __ctrwow__Stripe3Ds.init({
                publicKey: requires_action_publishkey,
                clientSecrect: requires_action_client_secret
              })
              if (rsVerify3Ds && rsVerify3Ds.success) {
                const __confirm3Ds = await __ctrwow__Stripe3Ds.confirmOrder(result)
                if (__confirm3Ds.success) {
                  // window.isUseSuccessAndFailPopup defined in creditcardSB widget
                  window.isUseSuccessAndFailPopup && window.__loading3Ds.success()
                } else {
                  // window.isUseSuccessAndFailPopup defined in creditcardSB widget
                  window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
                  setTimeout(() => {
                    window.ctrwowUtils.link.redirectPage(
                      appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button'))
                    )
                  }, __TIMEOUT)
                }
              } else {
                window.isUseSuccessAndFailPopup && window.__loading3Ds.fail()
                setTimeout(() => {
                  window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
                }, __TIMEOUT)
              }
              setTimeout(() => {
                __continueFlow(result)
              }, __TIMEOUT)
            }
          })
          // === end check stripe 3Ds
          break
        }
      }
    } else {
      window.ctrwowUtils.link.redirectPage(appendCtaClickIdTrackingParam(window.__ctrPageConfiguration.confirmUrl, $('.ctr_cta_button')))
    }
  }

  return {
    placeUpgradeUpsell,
    getMid
  }
}

export default upsellHelpers
