import upsellHelpers from './helpers'

const __placeUpsellUpgrade__StripeElements = (ctaButtons) => {
  const __upsellHelpers = upsellHelpers()
  const __placeUpsellUpgrade = () => {
    const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
    __upsellHelpers.placeUpgradeUpsell(product)
  }

  Array.prototype.slice.call(ctaButtons).forEach((ele) => {
    ele.addEventListener('click', function (e) {
      e.preventDefault()
      __placeUpsellUpgrade()
    })
  })
}

export { __placeUpsellUpgrade__StripeElements }
