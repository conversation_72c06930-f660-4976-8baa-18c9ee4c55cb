import upsellHelpers from './helpers'
import { ctrPayPal } from '../../payment/paypal.helper'

const __placeUpsellUpgrade__PayPalGooglePay = (ctaButtons) => {
  const __upsellHelpers = upsellHelpers()
  const __placeUpsellUpgrade = (paymentData) => {
    const product = window.ctrwowUpsell.productListData.getProductList().prices[window.upsell_productindex]
    __upsellHelpers.placeUpgradeUpsell(product)
  }
  __upsellHelpers
    .getMid(91)
    .then(async (objMid) => {
      if (!objMid) throw new Error('Cannot get mid')
      const __paypal = ctrPayPal()
      await __paypal.loadSDK(['GOOGLE'], objMid)
      const { allowedPaymentMethods, merchantInfo } = await window.paypal.Googlepay().config()
      __paypal
        .Google(
          {
            objMid,
            usePurchasePopup: false,
            allowedPaymentMethods,
            merchantInfo
          },
          __placeUpsellUpgrade
        )
        .init()

      Array.prototype.slice.call(ctaButtons).forEach((ele) => {
        ele.addEventListener('click', function (e) {
          e.preventDefault()
          window.__onProcessPayPalGooglePay()
        })
      })
    })
    .catch((error) => {
      throw new Error(error.message)
    })
}

export default __placeUpsellUpgrade__PayPalGooglePay
