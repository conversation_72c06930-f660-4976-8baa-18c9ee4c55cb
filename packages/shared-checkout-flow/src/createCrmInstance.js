import * as productList from './orderInfo/mainProductList'
import { SOURCE_CONFIG } from './configurable.constants'

export default function createCrmInstance(pageSettings) {
  const { sourceConfig: { source } = {} } = pageSettings

  const newEmanageCRM = new window.EmanageCRMJS({
    ...(SOURCE_CONFIG[source] || {}),
    webkey: pageSettings.webKey || '',
    cid: pageSettings.cid || '',
    // DEV-NOTE: use default value from lib
    // baseEndpoint is different for GET/POST request
    // baseAPIEndpoint: pageSettings.crmEndpoint,
    lang: pageSettings.lang || '',
    declineUrl: pageSettings.declineUrl || '',
    successUrl: pageSettings.successUrl || '',
    isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
  })

  // START overwrite getProduct list in EmanageCRM
  const old_getProducts = newEmanageCRM.Campaign.getProducts
  newEmanageCRM.Campaign.getProducts = function (callback) {
    return old_getProducts.call(newEmanageCRM.Campaign, (response) => {
      try {
        productList.setProductListToCache(response, newEmanageCRM.Campaign.webkey)
        productList.setProductList(response)
        callback(response)
      } catch (e) {
        console.log(e)
      }
    })
  }

  return newEmanageCRM
}
