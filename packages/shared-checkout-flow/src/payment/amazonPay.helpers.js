const __ctrPageConfiguration = window.__ctrPageConfiguration

export function __showAmazonButton({ id__AmazonButton }) {
  window.onAmazonLoginReady = function () {
    window.amazon.Login.setClientId(window.clientId)
  }

  window.onAmazonPaymentsReady = function () {
    let authRequest
    window.OffAmazonPayments.Button(id__AmazonButton, window.sellerId, {
      type: 'PwA', // LwA=> login with amazon, PwA =>  Amazon Pay
      color: 'Gold',
      size: 'x-large',

      authorization: function () {
        const loginOptions = {
          scope: 'profile payments:widget payments:shipping_address',
          popup: 'true',
          language: 'en-US'
        }

        // redirect to current url
        authRequest = window.amazon.Login.authorize(loginOptions, location.href)

        console.log(authRequest)

        authRequest.onComplete = function (e) {
          console('onComplete: ', e)
        }
      },
      onError: function (error) {
        console.log('Show Amazon Button Error', error)
      },
      onReady: function () {
        console.log('Show Amazon Button Ready.')
      }
    })
  }
}

export function __initAmazonPay({ elm, id__AmazonButton, id__AmazonShipping, id__AmazonWallet, class__AmazonSubmitButton }) {
  const urlLibAmazon =
    window.ctrwowUtils.link.getQueryParameter('iscardtest') === '1'
      ? `https://static-na.payments-amazon.com/OffAmazonPayments/us/sandbox/js/Widgets.js?sellerId=${window.sellerId}`
      : `https://static-na.payments-amazon.com/OffAmazonPayments/us/js/Widgets.js?sellerId=${window.sellerId}`
  window.ctrwowUtils.getDependencies([urlLibAmazon]).then(() => {
    elm = elm === undefined ? document : elm
    const loadAddressBook = function () {
      new window.OffAmazonPayments.Widgets.AddressBook({
        sellerId: window.sellerId,
        // agreementType: 'BillingAgreement',
        onReady: function (orderReference) {
          // Enter code here that you want to be executed
          // when the address widget has been rendered.
          // console.log(orderReference);
        },
        onAddressSelect: function (orderReference) {
          // Replace the following code with the action that you want to perform
          // after the address is selected.
          // The amazonBillingAgreementId can be used to retrieve
          // the address details by calling the GetBillingAgreementDetails operation.
          // If rendering the AddressBook and Wallet widgets on the same page, you
          // should wait for this event before you render the Wallet widget for
          // the first time.
          // The Wallet widget re-renders itself on all subsequent
          // onAddressSelect events without any action from you. We don't
          // recommend that you explicitly refresh it.
          console.log('[Amazon] onSelectAddress')
          // showWallet()
          getAddress(window.amazon__OrderReferenceId)
          // showConsent();
        },
        onOrderReferenceCreate: function (orderReference) {
          window.amazon__OrderReferenceId = orderReference.getAmazonOrderReferenceId()

          const elm__AmazonShippingAddess = elm.querySelector(`#${id__AmazonShipping}`)
          const orderInfo = !!window.ctrwowUtils.localStorage().get('orderInfo')
          if (orderInfo) {
            elm__AmazonShippingAddess && (elm__AmazonShippingAddess.style.display = 'none')
          } else {
            elm__AmazonShippingAddess && (elm__AmazonShippingAddess.style.display = 'block')
          }
          console.log('AmazonOrderReferenceId: ', window.amazon__OrderReferenceId)
        },
        design: {
          designMode: 'responsive'
        },
        onError: function (error) {
          console.log(error)
        }
      }).bind(id__AmazonShipping)
    }

    const showWallet = function () {
      new window.OffAmazonPayments.Widgets.Wallet({
        sellerId: window.sellerId,
        onPaymentSelect: function (orderReference) {
          // Replace this code with the action that you want to perform
          // after the payment method is chosen.

          // Ideally this would enable the next action for the buyer
          // including either a "Continue" or "Place Order" button.
          // console.log('onPaymentSelect is ready, please show the Place Order button');
          console.log(orderReference)
        },
        design: {
          designMode: 'responsive'
        },
        onError: function (error) {
          console.log(error)
          // Your error handling code.
          // During development you can use the following
          // code to view error messages:
          // error.getErrorCode() + ': ' + error.getErrorMessage());
          // See "Handling Errors" for more information.
        }
      }).bind(id__AmazonWallet)
    }

    const getAddress = function (amazonOrderReferenceId) {
      // const endpoint = window.__ctrPageConfiguration ? window.__ctrPageConfiguration.crmEndpoint : 'https://sales-prod.tryemanagecrm.com/api'
      const endpoint = window.ctrwowUtils.getSalesSupportCRMBaseUrl()
      let url = `${endpoint}/customers/amazonpay/address?merchantId=${window.sellerId}&region=US&accessToken=${window.access_token}&orderReferenceId=${amazonOrderReferenceId}`
      if (window.ctrwowUtils.link.getQueryParameter('iscardtest') !== null) {
        url += '&istest=true'
      }
      // url = `https://sales-prod.tryemanagecrm.com/api/customers/amazonpay/address?merchantId=${window.sellerId}&region=US&accessToken=${window.access_token}&orderReferenceId=${amazonOrderReferenceId}&isTest=true`
      fetch(url, {
        headers: {
          X_CID: __ctrPageConfiguration.cid
        }
      })
        .then((res) => res.json())
        .then((data) => {
          // show amazon wallet
          const elm__AmazonWallet = elm.querySelector(`#${id__AmazonWallet}`)
          const orderInfo = !!window.ctrwowUtils.localStorage().get('orderInfo')
          if (orderInfo) {
            elm__AmazonWallet && (elm__AmazonWallet.style.display = 'none')

            const btnPlaceOrder = document.querySelectorAll(`.${class__AmazonSubmitButton}`)
            btnPlaceOrder.length > 0 &&
              Array.prototype.slice.call(btnPlaceOrder).forEach((item) => {
                item.style.opacity = '1'
                item.style.pointerEvents = 'inherit'
              })
          } else {
            elm__AmazonWallet && (elm__AmazonWallet.style.display = 'block')

            const btnPlaceOrder = elm.querySelector(`.${class__AmazonSubmitButton}`)
            if (btnPlaceOrder) {
              btnPlaceOrder.classList.remove('hidden')
              btnPlaceOrder.querySelector('button').removeAttribute('disabled')
            }
          }

          window.amazon__AddressInfo = data
          console.log('Amazon Address: ', window.amazon__AddressInfo)
        })
        .catch((err) => console.log(err))
    }

    const initial = () => {
      const urlParams = new URLSearchParams(window.location.search)
      const access_token = urlParams.get('access_token')

      const elm__AmazonButton = document.querySelector(`#${id__AmazonButton}`)
      if (access_token) {
        elm__AmazonButton && (elm__AmazonButton.style.display = 'none')
        window.access_token = access_token
        loadAddressBook()
        showWallet()
      } else {
        elm__AmazonButton && (elm__AmazonButton.style.display = 'block')
      }
      window.ctrwowUtils.events.emit('emit__AmazonSaveClientBehavior')
    }
    initial()
  })
}

export function __getOrderPayLoad() {
  const postData = {
    customer: {
      email: window.amazon__AddressInfo.email,
      firstName: window.amazon__AddressInfo.name.split(' ')[0],
      lastName: window.amazon__AddressInfo.name.split(' ')[1],
      phoneNumber: window.amazon__AddressInfo.phone
    },
    payment: {
      paymentProcessorId: 61,
      region: window.amazon__AddressInfo.countryCode,
      merchantId: window.sellerId,
      providerOrderReferenceId: window.amazon__OrderReferenceId
    },
    mid: {
      midId: window.objMidAmazonPay.midId
    },
    shippingAddress: {
      firstName: window.amazon__AddressInfo.name.split(' ')[0],
      lastName: window.amazon__AddressInfo.name.split(' ')[1],
      phoneNumber: window.amazon__AddressInfo.phone,
      address1: window.amazon__AddressInfo.addressLine1,
      city: window.amazon__AddressInfo.city,
      countryCode: window.amazon__AddressInfo.countryCode,
      state: window.amazon__AddressInfo.stateOrRegion,
      zipCode: window.amazon__AddressInfo.postalCode
    },
    billingAddress: null
  }
  return postData
}

export function __getUpsellPayLoad() {
  return {
    region: window.amazon__AddressInfo.countryCode,
    merchantId: window.sellerId,
    providerOrderReferenceId: window.amazon__OrderReferenceId
  }
}
