const get__MID = (paymentProccessId, params) => {
  return new Promise((resolve, reject) => {
    const __isTest = new URL(window.location.href.toLocaleLowerCase()).searchParams.get('iscardtest') === '1' && true
    const __params = new URLSearchParams({
      amount: '0.50',
      ...params,
      ...(__isTest && { isTest: true })
    })

    const __url = `${window.ctrwowUtils.getSalesSupportCRMBaseUrl()}/campaigns/${
      window.__ctrPageConfiguration.webKey
    }/${paymentProccessId}/mid?${__params.toString()}`
    window.ctrwowUtils
      .callAjax(__url, {
        method: 'GET',
        headers: {
          X_CID: window.__ctrPageConfiguration.cid,
          'Content-Type': 'application/json'
        }
      })
      .then((result) => {
        resolve(result)
      })
      .catch((error) => {
        reject(error.message)
      })
  })
}

const get__FullPrice4Transaction = () => {
  const tax = parseFloat(window.taxPercent) || 0
  let totalPrice = 0
  const product = window.ctrwowCheckout?.checkoutData?.getProduct() || null
  if (product) {
    const productPrice = product.productPrices.DiscountedPrice.Value
    const shippingPrice = product.shippings.length > 0 ? product.shippings[window.shippingIndex || 0].price : 0
    totalPrice = productPrice + shippingPrice
    // if (window.diggy__HasUpgrade === true && window.miniUpsells !== undefined && window.miniUpsells.length > 0) {
    //   const diggy__productMiniUpsell = window.diggy__productMiniUpsell
    //   const diggy__ProductPrice = diggy__productMiniUpsell.productPrices.DiscountedPrice.Value
    //   const diggy__ShippingPrice = diggy__productMiniUpsell.shippings.length > 0 && diggy__productMiniUpsell.shippings[0].price
    //   totalPrice += diggy__ProductPrice + diggy__ShippingPrice
    // }
    const miniPro = window.ctrwowCheckout.checkoutData.getMiniUpsell() || []
    miniPro.forEach((mini) => {
      totalPrice += mini.price || 0
      totalPrice += mini.shippingPrice || 0
    })

    if (window.multiMainProducts && window.multiMainProducts.length > 0) {
      window.multiMainProducts.forEach((pro) => {
        totalPrice += pro.price || 0
        totalPrice += pro.shippingPrice || 0
      })
    }

    totalPrice = totalPrice * (1 + tax)
  } else {
    const upsellProduct = window.ctrwowUpsell?.productListData?.getProductList().prices[window.upsell_productindex]
    const upsellProductPrice = upsellProduct && upsellProduct.productPrices.DiscountedPrice.Value
    const upsellShippingPrice = upsellProduct && upsellProduct.shippings.length > 0 && upsellProduct.shippings[0].price
    totalPrice = (upsellProductPrice + upsellShippingPrice) * (1 + tax)

    if (window.multiMainProducts && window.multiMainProducts.length > 0) {
      window.multiMainProducts.forEach((pro) => {
        totalPrice += pro.price || 0
        totalPrice += pro.shippingPrice || 0
      })
    }

    if (window.multipleMiniUpsells && window.multipleMiniUpsells.length > 0) {
      window.multipleMiniUpsells.forEach((pro) => {
        totalPrice += pro.price || 0
        totalPrice += pro.shippingPrice || 0
      })
    }
    totalPrice = totalPrice * (1 + tax)
  }

  // dynamic data
  return {
    countryCode: window.ctrwowUtils.localStorage().get('ctr__countryCode').toUpperCase(),
    currencyCode: window.ctrwowUtils.localStorage().get('currencyCode').toUpperCase(),
    totalPriceStatus: 'FINAL',
    totalPriceLabel: 'Total',
    totalPrice: totalPrice.toFixed(2).toString()
  }
}

const get__Link = (pageURL, trackingNumber) => {
  const baseURL = window.location.href
  if (pageURL.indexOf('https://') > -1 || pageURL.indexOf('http://') > -1) {
    if (baseURL.indexOf('?') > -1) {
      return `${pageURL}${baseURL.substring(baseURL.indexOf('?'))}&token=${trackingNumber}`
    }
    return `${pageURL}?token=${trackingNumber}`
  }
  return `${baseURL.substring(0, baseURL.lastIndexOf('/'))}${pageURL.substring(1)}${baseURL.substring(baseURL.indexOf('?'))}&token=${trackingNumber}`
}

export { get__MID, get__FullPrice4Transaction, get__Link }
