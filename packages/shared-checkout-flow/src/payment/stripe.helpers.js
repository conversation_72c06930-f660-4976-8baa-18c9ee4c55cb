const STRIPE_SDK = 'https://js.stripe.com/v3/'
let sdkLoaded = false
const loadStripeSdk = async () => {
  try {
    if (sdkLoaded) return true

    const URL_SDK = new URL(STRIPE_SDK)
    const PARAMS_SDK = new URLSearchParams()
    URL_SDK.search = PARAMS_SDK.toString()
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = URL_SDK.toString()
    const loadPromise = new Promise((resolve, reject) => {
      script.onload = () => {
        sdkLoaded = true
        resolve(true)
      }
      script.onerror = () => reject(new Error('Failed to load Stripe SDK'))
    })

    document.head.appendChild(script)
    return await loadPromise
  } catch (error) {
    console.error('Stripe SDK loading error:', error)
    return false
  }
}

export const ctrwow__Stripe3Ds = () => {
  let __stripe = null
  let __clientSecret = null
  const init = async ({ publicKey, clientSecrect }) => {
    const sdkLoaded = await loadStripeSdk()
    if (!sdkLoaded) {
      throw new Error('Failed to load Stripe Sdk')
    }
    __clientSecret = clientSecrect
    const { Stripe } = window
    __stripe = Stripe(publicKey, { locale: window.ctrwowUtils.localStorage().get('ctr__countryCode') || 'us' })
    return confirmCardPayment({ status: null })
  }

  const confirmCardPayment = async ({ status }) => {
    if (status === null || status === 'requires_action' || status === 'requires_confirmation') {
      const __result3Ds = await __stripe.confirmCardPayment(__clientSecret)
      return new Promise((resolve, reject) => {
        if (__result3Ds && __result3Ds.paymentIntent) {
          __result3Ds.paymentIntent.status === 'succeeded'
            ? resolve({ success: true, status: __result3Ds.paymentIntent?.status })
            : resolve({ success: false, status: __result3Ds.paymentIntent?.status })
        } else {
          reject({ success: false, type: 'STRIPE_3DS_FAIL', status: null })
        }
      })
    }
  }

  const confirmOrder = ({ orderNumber }) => {
    return new Promise((resolve, reject) => {
      const URL = `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/orders/${orderNumber}/confirm`
      window.ctrwowUtils
        .callAjax(URL, {
          method: 'PUT',
          headers: {
            X_CID: window.__ctrPageConfiguration.cid,
            'Content-Type': 'application/json'
          }
        })
        .then((rs) => {
          resolve(rs)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const confirmUpsell = ({ orderNumber }) => {
    return new Promise((resolve, reject) => {
      const URL = `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/orders/upsell/${orderNumber}`
      window.ctrwowUtils
        .callAjax(URL, {
          method: 'POST',
          headers: {
            X_CID: window.__ctrPageConfiguration.cid,
            'Content-Type': 'application/json'
          }
        })
        .then((rs) => {
          resolve(rs)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  return {
    init,
    confirmCardPayment,
    confirmOrder,
    confirmUpsell
  }
}

export const loading3Ds = (isCheckout = true) => {
  const rootElement = isCheckout
    ? document.querySelector('button[name=checkoutWithCreditCardV1')?.parentElement
    : document.querySelector('.paymentProccessing.paymentProccessing_creditcard') || document.querySelector('.loading-wrapper')
  if (!rootElement) return
  const elms = {
    loading: isCheckout ? rootElement.querySelector('.paymentProccessing') : rootElement,
    textDefault: rootElement.querySelector('.paymentProccessing__loader__content'),
    textWaiting: rootElement.querySelector('.paymentProccessing__loader__content__challenge'),
    loadingDefault: rootElement.querySelector('.paymentProccessing__loader'),
    loadingSuccess: rootElement.querySelector('.paymentProccessing__loader_success'),
    loadingFail: rootElement.querySelector('.paymentProccessing__loader_fail')
  }
  const __hidePopup = () => {
    const elms__PurchasePopup = document.querySelectorAll('.popup_widget.extra-popup')
    elms__PurchasePopup.forEach((elm) => {
      // elm.classList.remove('popup_widget--opened')
      elm.style.display = 'none'
    })
  }

  const waiting = () => {
    console.log('waiting !!!')
    elms.textDefault && elms.textDefault.classList.add('js-hidden')
    elms.textWaiting && elms.textWaiting.classList.remove('js-hidden')
    elms.loadingSuccess && elms.loadingSuccess.classList.add('js-hidden')
    elms.loadingFail && elms.loadingFail.classList.add('js-hidden')
    elms.loadingDefault && elms.loadingDefault.classList.remove('js-hidden')
    elms.loading && (elms.loading.style.display = 'block')
  }

  const success = () => {
    console.log('success !!!')
    elms.loadingSuccess && elms.loadingSuccess.classList.remove('js-hidden')
    elms.loadingFail && elms.loadingFail.classList.add('js-hidden')
    elms.loadingDefault && elms.loadingDefault.classList.add('js-hidden')
    elms.loading && (elms.loading.style.display = 'block')
  }

  const fail = () => {
    console.log('fail !!!')
    __hidePopup()
    elms.loadingSuccess && elms.loadingSuccess.classList.add('js-hidden')
    elms.loadingFail && elms.loadingFail.classList.remove('js-hidden')
    elms.loadingDefault && elms.loadingDefault.classList.add('js-hidden')
    elms.loading && (elms.loading.style.display = 'block')
    window.ctrwowUtils.events.emit('stripe__CustomLoading3Ds')
  }

  const checkFailForm = () => {
    if (elms.loadingFail) return true
    return false
  }

  return {
    waiting,
    success,
    fail,
    checkFailForm
  }
}

export const ctrwow__StripeDropIn = (stripeConfig) => {
  const { __ctrPageConfiguration } = window
  const { objMid, countryCode } = stripeConfig
  const { clientSecret, token: publicKey, clientId: stripeAccount, useConnectAccount } = objMid
  let stripe, stripe__Elements

  const __loadSDK = async () => {
    try {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://js.stripe.com/v3/'

      return await new Promise((resolve, reject) => {
        script.onload = () => resolve(true)
        script.onerror = () => reject(new Error(`[Stripe SDK] Failed to load: ${script.src}`))
        document.head.appendChild(script)
      })
    } catch (error) {
      console.error(`[Stripe SDK] Loading error:`, error)
      return false
    }
  }

  const init = async () => {
    console.log('init Stripe')
    if (!window.Stripe) {
      const sdkLoaded = await __loadSDK()
      if (!sdkLoaded) {
        throw new Error('Failed to load Stripe SDK')
      }
    }

    const { Stripe } = window
    stripe = Stripe(publicKey, { ...(typeof useConnectAccount !== 'undefined' && useConnectAccount && { stripeAccount }) })

    __loadStripePaymentMethods()
  }

  const __loadStripePaymentMethods = () => {
    if (window.mainQuantity) {
      const orderSave = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
      orderSave.quantity = window.mainQuantity
      window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(orderSave))
    }

    stripe__Elements = stripe.elements({
      appearance: { theme: 'stripe' },
      locale: countryCode,
      clientSecret
    })
    const __paymentElement = stripe__Elements.create('payment', {
      layout: 'accordion',
      fields: {
        billingDetails: 'never'
      },
      terms: {
        card: 'never'
      }
    })
    document.querySelector('#payment-elements') && __paymentElement.mount('#payment-elements')
  }

  const __getLink = (pageURL, trackingNumber) => {
    const baseURL = window.location.href
    if (pageURL.indexOf('https://') > -1 || pageURL.indexOf('http://') > -1) {
      return `${pageURL}${baseURL.substring(baseURL.indexOf('?'))}&token=${trackingNumber}`
    }
    return `${baseURL.substring(0, baseURL.lastIndexOf('/'))}${pageURL.substring(1)}${baseURL.substring(
      baseURL.indexOf('?')
    )}&token=${trackingNumber}`
  }

  const onProcessStripeElements = async (result) => {
    const { customer, shippingAddress } = JSON.parse(window.paymentInfo) || {}
    const { error } = await stripe.confirmPayment({
      elements: stripe__Elements,
      confirmParams: {
        return_url: __getLink(__ctrPageConfiguration.successUrl, result.trackingNumber),
        payment_method_data: {
          billing_details: {
            name: `${customer?.firstName} ${customer?.lastName}` || '',
            email: customer?.email || '',
            phone: customer?.phoneNumber || '',
            address: {
              line1: shippingAddress?.address1 || '',
              line2: shippingAddress?.address2 || '',
              city: shippingAddress?.city || '',
              state: shippingAddress?.state || '',
              postal_code: shippingAddress?.zipCode || '',
              country: shippingAddress?.countryCode || ''
            }
          }
        }
      }
    })

    if (error) {
      console.log(`[ERROR]: ${error.message}`)
      window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.declineUrl)
    } else {
      console.log(`[SUCCESS`)
    }
  }

  const onSubmitStripeElements = async () => {
    const { error: submitError } = await stripe__Elements.submit()
    if (submitError) {
      return false
    }
    return true
  }

  return {
    init,
    onProcessStripeElements,
    onSubmitStripeElements
  }
}
