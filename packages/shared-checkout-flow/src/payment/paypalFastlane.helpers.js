const PAYPAL_SDK_URL = 'https://www.paypal.com/sdk/js'
let sdkLoaded = false
const { ctrwowUtils, __ctrPageConfiguration } = window
const objCUSTOMER_FORM = {
  name: {
    firstName: document.querySelector('input[name=firstName]'),
    lastName: document.querySelector('input[name=lastName]')
  },
  phoneNumber: {
    nationalNumber: document.querySelector('input[name=phoneNumber]')
  }
}
const objSHIPPING_FORM = {
  addressLine1: document.querySelector('input[name=address1]'),
  addressLine2: document.querySelector('input[name=address2]'),
  adminArea2: document.querySelector('input[name=city]'),
  countryCode: document.querySelector('select[name=countryCode]'),
  adminArea1: document.querySelector('select[name=state]'),
  postalCode: document.querySelector('input[name=zipCode]')
}
const TIME_DELAY = 4000

export const loadPayPalSdk = async (clientId, clientToken) => {
  try {
    if (sdkLoaded) return true

    const URL_SDK = new URL(PAYPAL_SDK_URL)
    const PARAMS_SDK = new URLSearchParams({
      'client-id': clientId,
      components: 'buttons,fastlane',
      'enable-funding': 'venmo'
    })
    URL_SDK.search = PARAMS_SDK.toString()
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.setAttribute('data-sdk-client-token', clientToken)
    script.setAttribute('data-client-metadata-id', clientToken)
    script.src = URL_SDK.toString()
    const loadPromise = new Promise((resolve, reject) => {
      script.onload = () => {
        sdkLoaded = true
        resolve(true)
      }
      script.onerror = () => reject(new Error('Failed to load PayPal SDK'))
    })

    document.head.appendChild(script)
    return await loadPromise
  } catch (error) {
    console.error('PayPal SDK loading error:', error)
    return false
  }
}

function FastLanePopup() {
  this.elm__FastlanePopup = document.querySelector('.fastlane-popup')

  this.hasPopup = function () {
    return !ctrwowUtils.localStorage().get('fastlane-popup-opened') && !!this.elm__FastlanePopup
  }

  this.open = function () {
    const exitpopupActivate = document.querySelectorAll('.popup-activate')
    if (this.elm__FastlanePopup) {
      // ctrwowUtils.hideGlobalLoading()
      this.elm__FastlanePopup.style.display = 'flex'
      this.elm__FastlanePopup.classList.remove('hidden')
      exitpopupActivate.forEach((item) => {
        item.style.display = 'none'
        item.classList.add('js-hidden')
      })
    }
  }

  this.close = function () {
    if (this.elm__FastlanePopup) {
      this.elm__FastlanePopup.classList.add('hidden')
    }
  }

  this.handleClosePopup = function () {
    const arrElmClosePopup = document.querySelectorAll('.fastlane-popup .close-button')
    Array.prototype.slice.call(arrElmClosePopup).forEach((elmClosePopup) => {
      elmClosePopup.addEventListener('click', () => {
        // add param to check if user denied fastlane
        window.ctrwowUtils.handleParam.addParamIntoUrl('flane', 0)
        this.close()
      })
    })
  }

  this.handleClickAgreeButton = function (fnCallback) {
    const elm__FastlaneAgree = document.querySelector('.agree-button')
    if (elm__FastlaneAgree) {
      elm__FastlaneAgree.addEventListener('click', () => {
        document.querySelector('axo-auth') && (document.querySelector('axo-auth').style.display = 'block')
        ctrwowUtils.localStorage().set('fastlane-popup-opened', true)
        // add param to check if user denied fastlane
        window.ctrwowUtils.handleParam.addParamIntoUrl('flane', 1)
        this.close()
        fnCallback && fnCallback()
      })
    }
  }
}

export const ctrwow_FastLane = async () => {
  const { paypal } = window
  if (!paypal?.Fastlane) return

  const FASTLANE_TYPE = ctrwowUtils.link.getParameterByName('fastlane_type') || 'PAY_NOW' // PAY_NOW or PAY_CONTINUE
  const { identity, profile, FastlaneWatermarkComponent, FastlanePaymentComponent } = await paypal.Fastlane()
  const PAYMENT_COMPONENT = await FastlanePaymentComponent()
  let __shippingAddress, __memberAuthenticatedSuccessfully, __paymentToken

  const __isEmail = (emailValue) => {
    if (emailValue.trim().length === 0) return false
    const filter = /^([\w-\.\+]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,8}|[0-9]{1,3})(\]?)$/
    if (filter.test(emailValue)) {
      return true
    } else {
      return false
    }
  }

  const __updateCssLoadingWrapper = async () => {
    const __style = document.createElement('style')
    __style.textContent = `
      axo-auth {
        display: none;
        font-family: 'Roboto';
      }
      .loading-wrapper {
        z-index: 999
      }
      button.btn-disabled {
        pointer-events: none;
        // background-color: #cacaca !important;
        // box-shadow: 0px 4px 0px 0px #7c7c7c !important;
      }
      button.btn-fastlane {
        background-color: #0059FF !important;
        color: #fff !important;
        box-shadow: 0px 4px 0px 0px #00318C !important;
        padding: 15px;
      }
    `
    document.head.appendChild(__style)
  }

  const __renderWatermark = async (isShow = false) => {
    /**
     * Render watermark component below email input
     */
    if (!isShow) {
      document.querySelector('#watermark-container') && document.querySelector('#watermark-container').remove()
    } else {
      if (FastlaneWatermarkComponent) {
        const elm__Email = document.querySelector('input[name=email]')
        if (!elm__Email) {
          return
        }

        if (!document.querySelector('#watermark-container')) {
          const elm__Watermark = `<div id="watermark-container" style="margin-top: 10px;"></div>`
          elm__Email.insertAdjacentHTML('afterend', elm__Watermark)
          const watermark = await FastlaneWatermarkComponent({ includeAdditionalInfo: true })
          watermark.render('#watermark-container')
        }
      } else {
        console.warn('FastlaneWatermarkComponent is not exists.')
      }
    }
  }

  const __displayFastLaneElement = (isShow = false) => {
    const elm__FastlaneButton = document.querySelector('.stripe-button.fastlane-submit-button')
    const elm__PaymentForm = document.querySelector('form[name=payment]').parentElement
    const elm__CreditcardSB = document.querySelector('button[name=checkoutWithCreditCardV1]')
    const elm__FastlanePaymentInfo = document.querySelector('#fastlane-payment-info')
    const elm__ChangeShippingAddress = document.querySelector('.fastlane-change-shipping-address')
    const elm__CheckoutButton = document.querySelector('.stripe-button.creditcard-button button')
    if (isShow) {
      __renderWatermark(true)
      elm__PaymentForm && elm__PaymentForm.classList.add('hidden')
      elm__CreditcardSB && elm__CreditcardSB.classList.add('hidden')
      elm__FastlaneButton && elm__FastlaneButton.classList.remove('hidden')
      elm__FastlanePaymentInfo && elm__FastlanePaymentInfo.classList.remove('hidden')
      elm__ChangeShippingAddress && elm__ChangeShippingAddress.classList.remove('hidden')
      elm__CheckoutButton && elm__CheckoutButton.classList.remove('hidden')
    } else {
      __renderWatermark(false)
      elm__PaymentForm && elm__PaymentForm.classList.remove('hidden')
      elm__CreditcardSB && elm__CreditcardSB.classList.remove('hidden')
      elm__FastlaneButton && elm__FastlaneButton.classList.add('hidden')
      elm__FastlanePaymentInfo && elm__FastlanePaymentInfo.classList.add('hidden')
      elm__ChangeShippingAddress && elm__ChangeShippingAddress.classList.add('hidden')
      elm__CheckoutButton && elm__CheckoutButton.classList.add('hidden')
    }
  }

  const __handleInitFastLane = (hasFastLane = false, fnCallback) => {
    /**
     * Handle display element
     */
    if (!hasFastLane) return

    if (FASTLANE_TYPE === 'PAY_NOW' && !ctrwowUtils.localStorage().get('orderInfo')) {
      /**
       * verify email and placeOrder
       */
      const paymentInfo = {
        customer: {
          email: __shippingAddress?.name?.email || document.querySelector('input[name=email]').value,
          firstName: __shippingAddress?.name?.firstName || '',
          lastName: __shippingAddress?.name?.lastName || '',
          phoneNumber: __shippingAddress?.phoneNumber?.nationalNumber || ''
        },
        payment: {
          paymentProcessorId: '89',
          PaymentFieldToken: __paymentToken?.id || ''
        },
        shippingAddress: {
          address1: __shippingAddress?.address?.addressLine1,
          address2: __shippingAddress?.address?.addressLine2,
          city: __shippingAddress?.address?.adminArea2,
          countryCode: __shippingAddress?.address?.countryCode,
          state: __shippingAddress?.address?.adminArea1,
          zipCode: __shippingAddress?.address?.postalCode
        }
      }
      ctrwowUtils.events.emit('triggerFastLanePayNow', paymentInfo)
    } else {
      /**
       * get data from fastlane and fill in the checkout page
       */
      const popupFastLane = new FastLanePopup()
      if (popupFastLane.hasPopup() && !ctrwowUtils.localStorage().get('orderInfo')) {
        popupFastLane.open()
        popupFastLane.handleClosePopup()
        popupFastLane.handleClickAgreeButton(fnCallback)
      } else {
        fnCallback()
      }
    }
  }

  const __enableUpsellButton = (enable = true) => {
    const __isFastLane = ctrwowUtils.localStorage().get('userPaymentType') === 'fastlane'
    if (!__isFastLane) return
    const elms = document.querySelectorAll('.btnUpsellOrder')
    elms.forEach((item) => {
      if (!enable) {
        item.classList.add('btn-disabled')
        item.classList.remove('btn-fastlane')
      } else {
        item.classList.remove('btn-disabled')
        item.classList.add('btn-fastlane')
      }
    })
  }

  const __verifyEmail = async () => {
    /**
     * Verify email input value with Fastlane
     */
    const verifyEmail = async (elm__Email) => {
      const emailValue = elm__Email.value

      if (emailValue.trim() === '') {

      } else {
        const { customerContextId } = await identity.lookupCustomerByEmail(emailValue)
        if (!customerContextId) {
          // No profile found with this email address. This is a guest payer
          console.warn('No customerContextId')
          __handleInitFastLane(false)
          return
        }

        // Email is associated with a Fastlane member or a PayPal member,
        // send customerContextId to trigger the authentication flow.
        try {
          const popupFastLane = new FastLanePopup()
          setTimeout(() => {
            if (popupFastLane.hasPopup() && !authResponse) {
              popupFastLane.open()
              popupFastLane.handleClosePopup()
              popupFastLane.handleClickAgreeButton()
              window.fastlane_popup_opened = true
            }
          }, TIME_DELAY)

          const authResponse = await identity.triggerAuthenticationFlow(customerContextId)

          if (authResponse.authenticationState === 'succeeded') {
            // Fastlane member successfully authenticated themselves
            // profileData contains their profile details
            __memberAuthenticatedSuccessfully = true
            __shippingAddress = authResponse?.profileData?.shippingAddress
            __paymentToken = authResponse?.profileData?.card

            /**
             * Has FastLane account
             */
            if (popupFastLane.hasPopup() && window.fastlane_popup_opened !== true) {
              popupFastLane.open()
              popupFastLane.handleClosePopup()
              popupFastLane.handleClickAgreeButton(() => {
                __handleInitFastLane(true, async () => {
                  setTimeout(() => {
                    // delay time call legend function
                    __bindShippingAndBillingAddress(__shippingAddress)
                  }, TIME_DELAY)
                  __changeShippingAddress()
                  __renderPaymentComponent()
                })
              })
            } else {
              __handleInitFastLane(true, async () => {
                setTimeout(() => {
                  // delay time call legend function
                  __bindShippingAndBillingAddress(__shippingAddress)
                }, TIME_DELAY)
                __changeShippingAddress()
                __renderPaymentComponent()
              })
            }
          } else {
            // Member failed or cancelled to authenticate. Treat them as a guest payer
            __displayFastLaneElement(false)
          }
        } catch (err) {
          console.log(err)
        }
      }
    }

    const orderInfo = ctrwowUtils.localStorage().get('orderInfo') ? JSON.parse(ctrwowUtils.localStorage().get('orderInfo')) : null

    if (orderInfo) {
      // case upsell
      const emailValue = orderInfo.cusEmail ? ctrwowUtils.localStorage().get('customer_email') : null
      const { customerContextId } = await identity.lookupCustomerByEmail(emailValue)
      if (!customerContextId) {
        // verify not success
        ctrwowUtils.link.redirectPage(__ctrPageConfiguration.confirmUrl)
      }
      __handleInitFastLane(true, async () => {
        const authResponse = await identity.triggerAuthenticationFlow(customerContextId)
        if (authResponse.authenticationState === 'succeeded') {
          __memberAuthenticatedSuccessfully = true
          __shippingAddress = authResponse.profileData?.shippingAddress
          __paymentToken = authResponse?.profileData?.card

          __enableUpsellButton(true)
          setTimeout(() => {
            __bindShippingAndBillingAddress(__shippingAddress)
          }, TIME_DELAY)
          __renderPaymentComponent()
        }
      })
    } else {
      const elm__Email = document.querySelector('input[name=email]')
      // [CASE PAYPAL POPUP] => NOT VERIFY FASTLANE
      if (elm__Email.closest('.section-2') || elm__Email.closest('.sub-form')) return

      if (elm__Email) {
        // [CASE CLIENT CLICKs BACK TO CHECKOUT PAGE]
        const emailValue = elm__Email.value
        if (emailValue) {
          // verifyEmail(elm__Email)
        }

        // [NORMAL CASE]: CLIENT FILL IN DATA INTO INPUT EMAIL
        elm__Email.addEventListener('blur', async (e) => {
          if (e.target.value.trim() === '' || !__isEmail(e.target.value.trim())) {
            return
          }
          verifyEmail(elm__Email)
        })
      }
    }
  }

  const __renderPaymentComponent = async () => {
    /**
     * Render payment component
     */
    if (ctrwowUtils.localStorage().get('orderInfo')) {
      const arrFastlanePaymentInfo = document.querySelectorAll('.fastlane-payment-info')
      arrFastlanePaymentInfo &&
        Array.prototype.slice.call(arrFastlanePaymentInfo).forEach((item, index) => {
          item.classList.contains(`fastlane-payment-info-${index}`) && PAYMENT_COMPONENT.render(`.fastlane-payment-info-${index}`)
        })
    } else {
      const elm__FastLanePaymentInfo = document.querySelector('#fastlane-payment-info')
      if (__paymentToken) {
        __displayFastLaneElement(true)
        elm__FastLanePaymentInfo && elm__FastLanePaymentInfo.classList.remove('hidden') && PAYMENT_COMPONENT.render('#fastlane-payment-info')
      } else {
        __displayFastLaneElement(false)
        elm__FastLanePaymentInfo && elm__FastLanePaymentInfo.classList.add('hidden')
      }
    }
  }

  const __changeShippingAddress = () => {
    const elm__ChangeShippingAddress = document.querySelector('.fastlane-change-shipping-address')
    if (elm__ChangeShippingAddress) {
      elm__ChangeShippingAddress.addEventListener('click', async () => {
        if (__memberAuthenticatedSuccessfully) {
          const { selectionChanged, selectedAddress } = await profile.showShippingAddressSelector()
          if (selectionChanged) {
            PAYMENT_COMPONENT.setShippingAddress(selectedAddress)
            __shippingAddress = selectedAddress
            __bindShippingAndBillingAddress(__shippingAddress)
          }
        }
      })
    }
  }

  const __bindShippingAndBillingAddress = (__shippingAddress) => {
    /**
     * Bind shipping and billing address from Fastlane to the shipping and billing form
     */
    console.log(__shippingAddress)
    if (ctrwowUtils.localStorage().get('orderInfo')) return
    Object.keys(objCUSTOMER_FORM).forEach((item) => {
      Object.keys(objCUSTOMER_FORM[item]).forEach((key) => {
        const inputElement = objCUSTOMER_FORM[item][key]
        if (inputElement) {
          inputElement.closest('.form-group').querySelector('label.error') &&
            (inputElement.closest('.form-group').querySelector('label.error').style.display = 'none')
          inputElement.closest('.form-group').querySelector('.control-label') &&
            inputElement.closest('.form-group').querySelector('.control-label').setAttribute('style', 'top: 5px; font-size: 0.8rem')
          inputElement.value = __shippingAddress[item][key]

          inputElement.dispatchEvent(new Event('blur'))
        }
      })
    })
    Object.keys(objSHIPPING_FORM).forEach((key) => {
      if (__shippingAddress.address[key]) {
        const elm__FormGroup = objSHIPPING_FORM[key].closest('.form-group')
        objSHIPPING_FORM[key].classList.remove('error')
        const event = new Event('change')
        objSHIPPING_FORM[key].dispatchEvent(event)

        if (key === 'adminArea1') {
          const event = new Event('change')
          objSHIPPING_FORM.countryCode.dispatchEvent(event)
          setTimeout(() => {
            objSHIPPING_FORM[key].value = __shippingAddress.address[key]
            elm__FormGroup.querySelector('.control-label') &&
              elm__FormGroup.querySelector('.control-label').setAttribute('style', 'top: 5px; font-size: 0.8rem')
          }, 300)
        } else if (__shippingAddress.address[key]) {
          objSHIPPING_FORM[key].value = __shippingAddress.address[key]
          elm__FormGroup.querySelector('.error') && (elm__FormGroup.querySelector('.error').style.display = 'none')
          elm__FormGroup.querySelector('.control-label') &&
            elm__FormGroup.querySelector('.control-label').setAttribute('style', 'top: 5px; font-size: 0.8rem')
        }
      }
    })
  }

  const __getShippingAddress = () => {
    /**
     * Handle change shipping address: update shipping address in Fastlane
     */
    return {
      address: {
        addressLine1: objSHIPPING_FORM.addressLine1.value,
        addressLine2: objSHIPPING_FORM.addressLine2.value,
        adminArea2: objSHIPPING_FORM.adminArea2.value,
        adminArea1: objSHIPPING_FORM.adminArea1.value,
        postalCode: objSHIPPING_FORM.postalCode.value,
        countryCode: objSHIPPING_FORM.countryCode.value
      },
      name: {
        firstName: objCUSTOMER_FORM.name.firstName.value,
        lastName: objCUSTOMER_FORM.name.lastName.value,
        fullName: [objCUSTOMER_FORM.name.firstName.value, objCUSTOMER_FORM.name.lastName.value].filter((field) => !!field).join(' ')
      },
      phoneNumber: {
        countryCode: '1',
        nationalNumber: objCUSTOMER_FORM.phoneNumber.nationalNumber.value
      }
    }
  }

  const __addElementPaymentInfo = () => {
    if (!ctrwowUtils.localStorage().get('orderInfo')) return
    const arrUpsellButtons = document.querySelectorAll('.js-btn-place-upsell-order')
    Array.prototype.slice.call(arrUpsellButtons).forEach(async (item, index) => {
      const template = `<div class="fastlane-payment-info fastlane-payment-info-${index}" style="display: none; width: 100%; margin-bottom: 10px"></div>`
      item.insertAdjacentHTML('beforebegin', template)
    })
  }

  return {
    init: async () => {
      __updateCssLoadingWrapper()
      await __verifyEmail()
      __addElementPaymentInfo()
      __enableUpsellButton(false)
    },
    getPaymentToken: async () => {
      try {
        const payment = await PAYMENT_COMPONENT?.getPaymentToken()
        return payment?.id || null
      } catch (error) {
        console.log('Failed to get payment token:', error)
        return __paymentToken.id || null
      }
    },
    updateShippingAddress: () => {
      const newShippingAddress = __getShippingAddress()
      PAYMENT_COMPONENT.setShippingAddress(newShippingAddress)
    }
  }
}
