export const ctrGooglePay = (googlePayConfig, fn__PlaceMainOrder) => {
  const baseRequest = {
    apiVersion: 2,
    apiVersionMinor: 0
  }
  const { objMid, allowedPaymentMethods, merchantInfo, countryCode } = googlePayConfig

  const get__GooglePayTransaction = () => {
    const tax = parseFloat(window.taxPercent) || 0
    let totalPrice = 0
    const product = window.ctrwowCheckout?.checkoutData?.getProduct() || null
    if (product) {
      const productPrice = product.productPrices.DiscountedPrice.Value
      const shippingPrice = product.shippings.length > 0 ? product.shippings[window.shippingIndex || 0].price : 0
      totalPrice = productPrice + shippingPrice
      if (window.diggy__HasUpgrade === true && window.miniUpsells !== undefined && window.miniUpsells.length > 0) {
        const diggy__productMiniUpsell = window.diggy__productMiniUpsell
        const diggy__ProductPrice = diggy__productMiniUpsell.productPrices.DiscountedPrice.Value
        const diggy__ShippingPrice = diggy__productMiniUpsell.shippings.length > 0 && diggy__productMiniUpsell.shippings[0].price
        totalPrice += diggy__ProductPrice + diggy__ShippingPrice
      }

      totalPrice = totalPrice * (1 + tax)
    } else {
      const upsellProduct = window.ctrwowUpsell?.productListData?.getProductList().prices[window.upsell_productindex]
      const upsellProductPrice = upsellProduct && upsellProduct.productPrices.DiscountedPrice.Value
      const upsellShippingPrice = upsellProduct && upsellProduct.shippings.length > 0 && upsellProduct.shippings[0].price
      totalPrice = (upsellProductPrice + upsellShippingPrice) * (1 + tax)
    }

    // dynamic data
    return {
      countryCode: window.ctrwowUtils.localStorage().get('ctr__countryCode').toUpperCase(),
      currencyCode: window.ctrwowUtils.localStorage().get('currencyCode').toUpperCase(),
      totalPriceStatus: 'FINAL',
      totalPriceLabel: 'Total',
      totalPrice: totalPrice.toFixed(2).toString()
    }
  }

  const __loadSDK = async () => {
    try {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://pay.google.com/gp/p/js/pay.js'

      return await new Promise((resolve, reject) => {
        script.onload = () => resolve(true)
        script.onerror = () => reject(new Error(`[GooglePay SDK] Failed to load: ${script.src}`))
        document.head.appendChild(script)
      })
    } catch (error) {
      console.error(`[GooglePay SDK] Loading error:`, error)
      return false
    }
  }

  const get__GoogleIsReadyToPayRequest = (allowedPaymentMethods) => {
    return Object.assign({}, baseRequest, {
      allowedPaymentMethods
    })
  }

  const get__GooglePayConfig = async () => {
    if (objMid.token && objMid.entity) {
      const cardPaymentMethod = Object.assign(
        {},
        {
          type: 'CARD',
          parameters: {
            allowedAuthMethods: ['AMEX', 'MASTERCARD', 'VISA'],
            allowedCardNetworks: ['PAN_ONLY', 'CRYPTOGRAM_3DS']
          }
        },
        {
          tokenizationSpecification: {
            type: 'PAYMENT_GATEWAY',
            parameters: {
              gateway: 'bluesnap',
              gatewayMerchantId: objMid.token
            }
          }
        }
      )
      return {
        allowedPaymentMethods: [cardPaymentMethod],
        merchantInfo: {
          merchantId: objMid.token.toString(),
          merchantName: objMid.entity.toString()
        }
      }
    } else {
      return {
        allowedPaymentMethods,
        merchantInfo
      }
    }
  }

  const get__GooglePaymentDataRequest = async () => {
    const paymentDataRequest = Object.assign({}, baseRequest)
    const { allowedPaymentMethods, merchantInfo } = await get__GooglePayConfig()
    paymentDataRequest.allowedPaymentMethods = allowedPaymentMethods
    // paymentDataRequest.transactionInfo = wowpayCheckout.get__GoogleTransaction()
    paymentDataRequest.merchantInfo = merchantInfo
    paymentDataRequest.callbackIntents = ['PAYMENT_AUTHORIZATION']
    paymentDataRequest.shippingAddressRequired = true
    paymentDataRequest.shippingAddressParameters = { phoneNumberRequired: true }
    paymentDataRequest.emailRequired = true
    return paymentDataRequest
  }

  const get__GooglePaymentsClient = () => {
    // const environment = window.eCRM.isTest ? 'TEST' : 'PRODUCTION'
    const __url = new URL(window.location.href.toLocaleLowerCase())
    const environment = __url.searchParams.get('iscardtest') === '1' ? 'TEST' : 'PRODUCTION'

    if (objMid.token && objMid.entity) {
      return new window.google.payments.api.PaymentsClient({ environment })
    } else {
      return new window.google.payments.api.PaymentsClient({
        environment,
        paymentDataCallbacks: {
          onPaymentAuthorized: fn__PlaceMainOrder
        }
      })
    }
  }

  const render__GooglePayButton = () => {
    const paymentsClient = get__GooglePaymentsClient()
    const button = paymentsClient.createButton({
      buttonColor: window.customizeGoogle?.buttonColor || 'default',
      buttonType: window.customizeGoogle?.buttonType || 'pay',
      buttonRadius: window.customizeGoogle?.buttonRadius || 8,
      buttonLocale: countryCode || 'en',
      buttonSizeMode: 'fill',
      onClick: onProcessGooglePay
    })
    const elm__GooglePayButton = document.querySelector('#button-google-pay')
    if (elm__GooglePayButton) {
      elm__GooglePayButton.innerHTML = ''
      elm__GooglePayButton.appendChild(button)
    }
  }

  const handleClick__GoogePayButton = () => {
    const elm__GooglePayButton = document.querySelector('.button-google-pay')
    elm__GooglePayButton &&
      elm__GooglePayButton.addEventListener('click', () => {
        onProcessGooglePay()
      })
  }

  const init = async () => {
    console.log('init Google Pay')

    const sdkLoaded = await __loadSDK()
    if (!sdkLoaded) {
      throw new Error('Failed to load GooglePay SDK')
    }

    const paymentsClient = get__GooglePaymentsClient()
    const { allowedPaymentMethods } = await get__GooglePayConfig()
    paymentsClient
      .isReadyToPay(get__GoogleIsReadyToPayRequest(allowedPaymentMethods))
      .then(function (response) {
        if (response.result) {
          render__GooglePayButton()
          handleClick__GoogePayButton()
        }
      })
      .catch(function (err) {
        console.error(err)
      })
  }

  const onProcessGooglePay = async (usePurchasePopup = googlePayConfig.usePurchasePopup || false) => {
    window.paymentProcessorId = 91
    if (!usePurchasePopup) {
      const paymentDataRequest = await get__GooglePaymentDataRequest()
      const paymentsClient = get__GooglePaymentsClient()
      paymentDataRequest.transactionInfo = get__GooglePayTransaction()
      paymentsClient.loadPaymentData(paymentDataRequest)
    } else {
      window.ctrwowUtils.events.emit('triggerPaypalOrder')
    }
  }

  return {
    init,
    onProcessGooglePay
  }
}
