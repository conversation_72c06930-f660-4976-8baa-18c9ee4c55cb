function getValue(inputHTML) {
  if (inputHTML !== null && inputHTML.value !== '') {
    return inputHTML.value
  }
  return ''
}
function setValue(formName, formItem, value) {
  const input = document.querySelector(`form[name=${formName}] input[name=${formItem}]`)
  if (input !== null && value !== '') {
    input.value = value
    input.nextSibling && input.nextSibling.setAttribute('style', 'font-size: 0.8rem; top: 5px;')
  }

  if (formItem === 'countryCode' && value !== '') {
    const selectCountry = document.querySelector(`form[name=${formName}] select[name=countryCode]`)
    window.ctrwowUtils.events.on('countriesLoaded', () => {
      if (selectCountry !== null) {
        selectCountry.value = value
        selectCountry.dispatchEvent(new Event('change'))
        selectCountry.nextSibling && selectCountry.nextSibling.setAttribute('style', 'font-size: 0.8rem; top: 5px;')
      }
    })
  }

  if (formItem === 'state' && value !== '') {
    const selectState = document.querySelector(`form[name=${formName}] select[name=state]`)
    window.ctrwowUtils.events.on('stateLoaded', () => {
      if (selectState !== null) {
        selectState.value = value
        selectState.dispatchEvent(new Event('change'))
        selectState.nextSibling && selectState.nextSibling.setAttribute('style', 'font-size: 0.8rem; top: 5px;')
      }
    })
  }
}
function saveShippingInfo() {
  const shippingInfo = {
    url: window.location.origin + window.location.pathname,
    productId: window.ctrwowCheckout.checkoutData.getProduct().productId,
    forms: {
      customer: {
        email: getValue(document.querySelector('form[name=customer] input[name=email]')),
        firstName: getValue(document.querySelector('form[name=customer] input[name=firstName]')),
        lastName: getValue(document.querySelector('form[name=customer] input[name=lastName]')),
        phoneNumber: getValue(document.querySelector('form[name=customer] input[name=phoneNumber]'))
      },
      shippingAddress: {
        address1: getValue(document.querySelector('form[name=shippingAddress] input[name=address1]')),
        city: getValue(document.querySelector('form[name=shippingAddress] input[name=city]')),
        countryCode: getValue(document.querySelector('form[name=shippingAddress] select[name=countryCode]')),
        state: getValue(document.querySelector('form[name=shippingAddress] select[name=state]')),
        numberAddress: getValue(document.querySelector('form[name=shippingAddress] input[name=numberAddress]')),
        streetAddress: getValue(document.querySelector('form[name=shippingAddress] input[name=streetAddress]')),
        zipCode: getValue(document.querySelector('form[name=shippingAddress] input[name=zipCode]'))
      },
      billingAddress: {
        address1: getValue(document.querySelector('form[name=billingAddress] input[name=address1]')),
        city: getValue(document.querySelector('form[name=billingAddress] input[name=city]')),
        countryCode: getValue(document.querySelector('form[name=billingAddress] select[name=countryCode]')),
        state: getValue(document.querySelector('form[name=billingAddress] select[name=state]')),
        numberAddress: getValue(document.querySelector('form[name=billingAddress] input[name=numberAddress]')),
        streetAddress: getValue(document.querySelector('form[name=billingAddress] input[name=streetAddress]')),
        zipCode: getValue(document.querySelector('form[name=billingAddress] input[name=zipCode]'))
      }
    }
  }
  sessionStorage.setItem('shippingInfo', JSON.stringify(shippingInfo))
}
function loadShippingInfo() {
  const shippingInfo = JSON.parse(sessionStorage.getItem('shippingInfo'))

  if (shippingInfo !== null) {
    if (window.location.href.indexOf(shippingInfo.url) < 0) {
      return
    }

    const elm__Product = document.querySelector(`.js-list div[data-id="${shippingInfo.productId}"]`)
    elm__Product && elm__Product.click()

    const forms = shippingInfo && shippingInfo.forms
    forms &&
      Object.keys(forms).forEach((formName) => {
        // item = form name
        const formData = forms[formName]

        if (formData !== undefined) {
          Object.keys(formData).forEach((formItem) => {
            setValue(formName, formItem, formData[formItem])
          })
        }
      })
  }
}
export { saveShippingInfo, loadShippingInfo }

// ====================================
import { ctrGooglePay } from './googlePay.helpers'
import { ctrApplePay } from './applePay.helpers'
import { get__Link } from './helpers'

const DEFINE__PAYMENT_METHODS = {
  GOOGLE: 'googlepay',
  FASTLANE: 'fastlane',
  APPLE: 'applepay',
  ACDC: 'buttons,card-fields'
}
export const ctrPayPal = () => {
  const __loadSDK = async (paymentMethods, { clientId, clientToken }) => {
    if (!clientId || !Array.isArray(paymentMethods) || paymentMethods.length === 0) {
      console.error(`[PayPal SDK] Missing clientId or paymentMethods`)
      return false
    }

    try {
      const components = paymentMethods.map((item) => DEFINE__PAYMENT_METHODS[item]).join(',')
      const sdkUrl = new URL('https://www.paypal.com/sdk/js')
      sdkUrl.search = new URLSearchParams({
        'client-id': clientId,
        components
      }).toString()

      const script = document.createElement('script')
      script.type = 'text/javascript'
      if (clientToken) {
        script.setAttribute('data-sdk-client-token', clientToken)
        script.setAttribute('data-client-metadata-id', clientToken)
        script.setAttribute('data-partner-attribution-id', 'APPLEPAY')
      }

      script.src = sdkUrl.toString()

      return await new Promise((resolve, reject) => {
        script.onload = () => resolve(true)
        script.onerror = () => reject(new Error(`[PayPal SDK] Failed to load: ${script.src}`))
        document.head.appendChild(script)
      })
    } catch (error) {
      console.error(`[PayPal SDK] Loading error:`, error)
      return false
    }
  }

  const Google = (googlePayConfig, fnPlaceOrder) => {
    if (!window.paypal) {
      console.error('[PayPal SDK] Not loaded')
      return
    }

    const init = async () => {
      console.log('init PayPal Google Pay')
      const __googlePay = await ctrGooglePay(googlePayConfig, fnPlaceOrder)
      __googlePay.init()
      window.__onProcessPayPalGooglePay = __googlePay.onProcessGooglePay
    }

    return {
      init
    }
  }

  const Apple = (applePayConfig, fnPlaceOrder) => {
    if (!window.paypal) {
      console.error('[PayPal SDK] Not loaded')
      return
    }

    const init = async () => {
      console.log('init PayPal Google Pay')
      const __applePay = await ctrApplePay({ applepay: window.paypal.Applepay() }, fnPlaceOrder)
      __applePay.init()
      window.__onProcessPayPalApplePay = __applePay.onProcessPayPalApplePay
    }

    return {
      init
    }
  }

  const FastLane = () => {}

  const Acdc = (fn__PlaceOrder) => {
    if (!window.PayPal__ACDC) {
      console.error('[PayPal SDK] Not loaded')
      return
    }

    const { PayPal__ACDC } = window
    let paypalCardForm

    const __initCardField = (fieldName = 'cardCvvField' | 'cardExpiryField' | 'cardNameField' | 'cardNumberFiled', isValid = true) => {
      return {
        style: {
          input: {
            border: 0,
            borderRadius: 0,
            padding: '10px',
            background: 'transparent',
            height: '40px'
          },
          'input:focus': {
            boxShadow: '0 0 0'
          },
          '.invalid': {
            border: 0,
            boxShadow: '0 0 0',
            background: 'transparent'
          },
          ':focus.invalid': {
            border: 0,
            boxShadow: '0 0 0',
            background: 'transparent'
          }
        },
        placeholder: '',
        inputEvents: {
          onChange: (data) => {
            const label = document.querySelector(`.${fieldName}.form-group .label`)
            label && label.setAttribute('style', 'top: 5px; font-size: 0.8rem;')
            const error = document.querySelector(`.${fieldName}.form-group .error`)
            if (!data.fields[fieldName]?.isEmpty) {
              error && error.setAttribute('style', 'display: none;')
            } else {
              error && error.setAttribute('style', 'display: block;')
            }
          },
          onFocus: (data) => {
            const label = document.querySelector(`.${fieldName}.form-group .label`)
            label && label.setAttribute('style', 'top: 5px; font-size: 0.8rem;')
          },
          onBlur: (data) => {
            const label = document.querySelector(`.${fieldName}.form-group .label`)
            const div = document.querySelector(`.${fieldName}.form-group div`)
            const error = document.querySelector(`.${fieldName}.form-group .error`)
            if (!data.fields[fieldName]?.isEmpty) {
              label && label.setAttribute('style', 'top: 5px; font-size: 0.8rem;')
              div && div.setAttribute('style', 'background-color: #ffffff; border-color: #cacaca')
              error && error.setAttribute('style', 'display: none;')
            } else {
              label && label.setAttribute('style', 'top: 11px; font-size: inherit;')
              div && div.setAttribute('style', 'background-color: #f9ecea; border-color: #cc4b37')
              error && error.setAttribute('style', 'display: block;')
            }
          }
        }
      }
    }

    const __renderCardFields = () => {
      paypalCardForm = PayPal__ACDC.CardFields({
        createOrder: async () => {
          const rs = await fn__PlaceOrder()
          return rs.trackingNumber || null
        },
        onApprove: (data, action) => {
          const nextURL = get__Link(window.__ctrPageConfiguration.successUrl, data.orderID)
          window.ctrwowUtils.link.redirectPage(nextURL)
          // window.ctrwowUtils.link.redirectPage(
          //   `${window.__ctrPageConfiguration.successUrl.substring(window.__ctrPageConfiguration.successUrl.indexOf('/') + 1)}?token=${data.orderID}`
          // )
        },
        onError: (error) => {
          console.error(`ERROR:`, error)
          if (localStorage.getItem('orderInfo')) {
            window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.declineUrl)
          }
        },
        style: {
          body: {
            paddingTop: 0,
            paddingBottom: 0,
            borderRadius: 0
          }
        }
      })

      if (paypalCardForm.isEligible()) {
        const nameField = paypalCardForm.NameField(__initCardField('cardNameField'))
        nameField.render('#card-name-field-container')

        const numberField = paypalCardForm.NumberField(__initCardField('cardNumberField'))
        numberField.render('#card-number-field-container')

        const cvvField = paypalCardForm.CVVField(__initCardField('cardCvvField'))
        cvvField.render('#card-cvv-field-container')

        const expiryField = paypalCardForm.ExpiryField(__initCardField('cardExpiryField'))
        expiryField.render('#card-expiry-field-container')
      }
    }

    const __validateForm = (isValid = false) => {
      const __paymentElements = ['#card-number-field-container', '#card-cvv-field-container', '#card-expiry-field-container']

      __paymentElements.forEach((elmItem) => {
        const elmCardField = document.querySelector(elmItem)
        if (elmCardField) {
          elmCardField.style.backgroundColor = !isValid ? '#f9ecea' : '#ffffff'
          elmCardField.style.borderColor = !isValid ? '#cc4b37' : '#cacaca'
          elmCardField.parentNode.querySelector('.error').style.display = !isValid ? 'block' : 'none'
        }
      })
    }

    const onSubmitPayPalCardForm = async ({ shippingAddress, billingAddress }, usePurchasePopup = false) => {
      const stateData = await paypalCardForm.getState()
      if (stateData.isFormValid) {
        // show local__PaymentProcessingLoading
        const local__PaymentProcessingLoading = document.querySelector('.paymentStripeProccessing')
        local__PaymentProcessingLoading && (local__PaymentProcessingLoading.style.display = 'block')
        if (!usePurchasePopup) {
          __validateForm(true)
          paypalCardForm.submit({
            billingAddress: {
              addressLine1: shippingAddress?.address1 || billingAddress?.address1 || '',
              addressLine2: shippingAddress?.address2 || billingAddress?.address2 || '',
              adminArea1: shippingAddress?.city || billingAddress?.city || '',
              adminArea2: shippingAddress?.state || billingAddress?.state || '',
              countryCode: shippingAddress?.countryCode || billingAddress?.countryCode || '',
              postalCode: shippingAddress?.zipCode || billingAddress?.zipCode || ''
            }
          })
        } else {
          window.ctrwowUtils.events.emit('triggerPaypalOrder')
        }
      } else {
        __validateForm(false)
      }
    }

    const init = () => {
      __renderCardFields()
    }

    return {
      init,
      onSubmitPayPalCardForm
    }
  }
  return {
    loadSDK: __loadSDK,
    Google,
    Apple,
    FastLane,
    Acdc
  }
}
