// export function handleWindowPopupWithUrl(url, funcHandleClosedPopup) {
//   const width = 700
//   const height = 400
//   var screenLeft = screen.width / 2 - width / 2
//   var screenTop = screen.height / 2 - height / 2
//   const win = window.open(
//     url,
//     'myWindow',
//     `toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${screenTop}, left=${screenLeft}`
//   )
//   const popup = window.open(
//     url,
//     'myWindow',
//     `toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${screenTop}, left=${screenLeft}`
//   )

//   window.onunload = function () {
//     var win = window.opener
//     if (!win.closed) {
//       win.someFunctionToCallWhenPopUpCloses()
//     }
//   }
//   function someFunctionToCallWhenPopUpCloses() {
//     window.setTimeout(function () {
//       if (popup.closed) {
//         // popup closed
//         funcHandleClosedPopup()
//       }
//     }, 1)
//   }
//   var pollTimer = window.setInterval(function () {
//     if (win.closed !== false) {
//       // !== is required for compatibility with Opera
//       window.clearInterval(pollTimer)
//       someFunctionToCallWhenPopUpCloses()
//     }
//   }, 200)
// }
export function handlePopupWithUrl(data, funcClosePopup) {
  const elm__CryptoPopup = document.querySelector('.ph__cryptopopup__predefinedData .popup_widget')
  // bind data
  const paymentContinueResult = data && data.paymentContinueResult && data.paymentContinueResult.items ? data.paymentContinueResult.items : null
  const arr__ElmCryptoPopup = {
    payment_timeout: document.querySelector('.payment_timeout'),
    payment_qrcode: document.querySelector('.payment_qrcode img'),
    payment_wallet_address: document.querySelector('.payment_wallet_address'),
    payment_total_price: document.querySelector('.payment_total_price'),
    payment_id: document.querySelector('.payment_id'),
    payment_verify_code: document.querySelector('.payment_verify_code'),
    payment_btn_cancel: document.querySelector('.payment_btn_cancel button'),
    payment_btn_complete: document.querySelector('.payment_btn_complete button'),
    payment_type: document.querySelectorAll('.payment_type'),
    btn_copy: document.querySelectorAll('.btn_copy')
  }
  if (paymentContinueResult) {
    // convert dataResponse to Object
    let cryptoInfo = {}
    paymentContinueResult.forEach((item) => {
      cryptoInfo = { ...cryptoInfo, [item.key]: item.value }
    })
    // =================== bind Crypto Info
    arr__ElmCryptoPopup.payment_wallet_address && (arr__ElmCryptoPopup.payment_wallet_address.innerText = cryptoInfo.WalletAddress)
    arr__ElmCryptoPopup.payment_total_price && (arr__ElmCryptoPopup.payment_total_price.innerText = cryptoInfo.Amount + cryptoInfo.CryptoCurrency)
    arr__ElmCryptoPopup.payment_qrcode && (arr__ElmCryptoPopup.payment_qrcode.src = cryptoInfo.QrCodeUrl)
    arr__ElmCryptoPopup.payment_type &&
      Array.prototype.slice.call(arr__ElmCryptoPopup.payment_type).forEach((item) => {
        item.innerText = cryptoInfo.CryptoCurrency
      })

    if (cryptoInfo.PaymentId && cryptoInfo.VerifyCode) {
      arr__ElmCryptoPopup.payment_id && (arr__ElmCryptoPopup.payment_id.innerText = cryptoInfo.PaymentId)
      arr__ElmCryptoPopup.payment_verify_code && (arr__ElmCryptoPopup.payment_verify_code.innerText = cryptoInfo.VerifyCode)
    } else {
      const elm__PaymentInfo = document.querySelector('.payment_info')
      elm__PaymentInfo && (elm__PaymentInfo.style.display = 'none')
    }

    // =================== handle button cancel
    arr__ElmCryptoPopup.payment_btn_cancel &&
      arr__ElmCryptoPopup.payment_btn_cancel.addEventListener('click', (e) => {
        e.preventDefault()

        const orderInfo = window.ctrwowUtils.localStorage().get('orderInfo')
        if (orderInfo) {
          funcClosePopup()
        } else {
          location.reload()
        }
      })

    // =================== handle button success
    arr__ElmCryptoPopup.payment_btn_complete &&
      arr__ElmCryptoPopup.payment_btn_complete.addEventListener('click', (e) => {
        e.preventDefault()
        funcClosePopup()
      })

    // =================== handle countdown
    const countDownDate = new Date().getTime() + Number(cryptoInfo.TimeoutInSeconds) * 1000
    var countdown = setInterval(function () {
      var now = new Date().getTime()
      var distance = countDownDate - now
      // var days = Math.floor(distance / (1000 * 60 * 60 * 24))
      // var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
      var seconds = Math.floor((distance % (1000 * 60)) / 1000)

      // Display the result in the element with id="demo"
      arr__ElmCryptoPopup.payment_timeout && (arr__ElmCryptoPopup.payment_timeout.innerHTML = minutes + 'm ' + seconds + 's ')

      // If the count down is finished, write some text
      if (distance < 0) {
        clearInterval(countdown)
        // show exprire
        const elm__PopupTimeout = document.querySelector('.popup_timeout')
        const elm__PopupMain = document.querySelector('.popup_widget_main')
        elm__PopupMain && (elm__PopupMain.style.display = 'none')
        elm__PopupTimeout && (elm__PopupTimeout.style.display = 'block')
      }
    }, 1000)

    // =================== handle copy
    Array.prototype.slice.call(arr__ElmCryptoPopup.btn_copy).forEach((itembtn) => {
      itembtn.addEventListener('click', (e) => {
        const elm__Parrent = e.currentTarget.parentElement.parentElement
        const elm__PaymentInfoValue = elm__Parrent.querySelector('.payment_info_value')
        elm__PaymentInfoValue && navigator.clipboard.writeText(elm__PaymentInfoValue.innerText)
      })
    })

    // =================== show popup
    elm__CryptoPopup && elm__CryptoPopup.classList.add('popup_widget--opened')
  } else {
    console.error('Handle Encrypt Popup Error')
  }
}
export function confirmCoinOrder(trackingNumber) {
  return new Promise((resolve, reject) => {
    const testParam = window.ctrwowUtils.link.getQueryParameter('isCardTest') === '1' ? '&isTest=true' : ''
    const endpoint = window.ctrwowUtils.getSalesPciCRMBaseUrl() || 'https://sales-pci.tryemanagecrm.com/api'
    const urlApiConfirmOrder = `${endpoint}/orders/${window.__ctrPageConfiguration.webKey}?trackingNumber=${trackingNumber}${testParam}`
    window.ctrwowUtils
      .callAjax(urlApiConfirmOrder, {
        method: 'PUT',
        headers: {
          X_CID: window.__ctrPageConfiguration.cid,
          'Content-Type': 'application/json'
        }
      })
      .then((result) => {
        resolve(result)
      })
      .catch((err) => {
        reject(err)
      })
  })
}
