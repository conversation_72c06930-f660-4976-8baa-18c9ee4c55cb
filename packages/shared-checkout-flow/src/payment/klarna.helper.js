function updateQuantityForDiggy() {
  if (window.mainQuantity) {
    console.log('update quantity for main order')
    const orderSave = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
    orderSave.quantity = window.mainQuantity
    window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(orderSave))
  }
}

function afterCheckoutWithStripe(result, upsellPopup) {
  updateQuantityForDiggy()

  if (upsellPopup === 'YES') {
    window.ctrwowUtils.events.emit('onDiggyGAPSuccess')
  } else {
    const klarnaResults =
      result.paymentContinueResult?.items?.map((item) => {
        return {
          [item.key]: item.value
        }
      }) || null

    displayKlarnaType({ klarnaResults, trackingNumber: result.trackingNumber })
  }
}

function displayKlarnaType({ trackingNumber, klarnaResults }) {
  const { __ctrPageConfiguration, ctrwowUtils } = window
  const elm__Klarna = document.querySelector('.klarna-type')

  if (!klarnaResults) {
    console.log(`[Warning] The client secret is missing from the API response.`)
    return
  }

  /**
   * Handle click close button
   */
  const elm__IconClose = elm__Klarna && elm__Klarna.querySelector('.icon-close')
  elm__IconClose &&
    elm__IconClose.addEventListener('click', (e) => {
      window.ctrwowUtils.localStorage().remove('orderOnePageFlag')
      window.sessionStorage.removeItem('paymentContinueResult')
      window.location.reload()
    })

  // Handle click continue
  const elm__ContinueButton = elm__Klarna && elm__Klarna.querySelector('button.btn-black')
  elm__ContinueButton &&
    elm__ContinueButton.addEventListener('click', async (e) => {
      ctrwowUtils.showGlobalLoading()
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url:
            __ctrPageConfiguration.successUrl +
            ctrwowUtils.link.mergeQueryStringParams(ctrwowUtils.link.getCustomPathName(), `trackingNumber=${trackingNumber}`)
        }
      })
      if (error) {
        ctrwowUtils.hideGlobalLoading()
        alert(error?.message)
      } else {
        alert('success !')
      }
    })

  document.querySelector('.paymentStripeProccessing') && (document.querySelector('.paymentStripeProccessing').style.display = 'none')
  elm__Klarna && (elm__Klarna.style.display = 'flex')
  elm__Klarna.querySelector('.popup_widget') && elm__Klarna.querySelector('.popup_widget').classList.add('popup_widget--opened')
  ctrwowUtils.hideGlobalLoading()
  // publicKey from Sales Support
  const __clientSecret = klarnaResults.find((item) => (item.client_secret ? item : null))
  const { token, clientId } = window.ctrwowUtils.localStorage().get('klarnaMid')
    ? JSON.parse(window.ctrwowUtils.localStorage().get('klarnaMid'))
    : null
  if (!token && !clientId) return

  const stripe = window.Stripe(token, {
    stripeAccount: !clientId && window.ctrwowUtils.handleParam.getQueryParameter('iscardtest') === '1' ? undefined : clientId
  })
  const elements = stripe.elements({ clientSecret: __clientSecret.client_secret })
  const paymentElement = elements.create('payment', { clientSecret: __clientSecret.client_secret })
  paymentElement.mount('#payment-element')
}

export { updateQuantityForDiggy, afterCheckoutWithStripe, displayKlarnaType }
