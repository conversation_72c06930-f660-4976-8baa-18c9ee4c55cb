function initPaypalSmartButton({ clientID, currency, elmPrice }, callback) {
  if (window.ctrwowUtils.link.getParameterByName('isshopifytest') === '1') {
    clientID = 'test'
    currency = 'USD'
  }
  window.ctrwowUtils
    .getDependencies([`https://www.paypal.com/sdk/js?client-id=${clientID}&currency=${currency}`], {
      delayUntilInteract: false
    })
    .then(() => {
      window.paypal
        .Buttons({
          fundingSource: window.paypal.FUNDING.PAYPAL,
          style: {
            shape: 'rect',
            height: 55,
          },
          createOrder: (data, actions) => {
            return actions.order.create({
              purchase_units: [
                {
                  amount: {
                    value: document.querySelector(`.${elmPrice}`) ? Number(document.querySelector(`.${elmPrice}`).innerText.replace(/[^0-9.]/g, '').replace(',', '.')) : 0
                  }
                }
              ]
            })
          },
          onApprove: (data, actions) => {
            return actions.order
              .capture()
              .then(function (orderData) {
                var transaction = orderData.purchase_units[0].payments.captures[0]
                console.log(`transaction id: ${transaction.id}`)

                callback()
              })
              .catch((err) => {
                console.log(`error: ${err}`)
              })
          }
        })
        .render('#paypal-smart-button-container')
    })
}

export { initPaypalSmartButton }
