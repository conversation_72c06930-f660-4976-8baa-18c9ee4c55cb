export function getTokenBluesnap() {
  const url__BluesnapGetToken =
    /**
     * use param useMidTest=true => api get mid test
     */
    window.ctrwowUtils.link.getQueryParameter('useMidTest')
      ? 'https://nodejs-api-clean-7fe6984838a2.herokuapp.com/api/v1/payment-bluesnap/mid'
      : `${window.ctrwowUtils.getSalesPciCRMBaseUrl()}/providers/bluesnap/token/${window.__ctrPageConfiguration.webKey}${
          window.ctrwowUtils.link.getQueryParameter('isCardTest') ? '?isTest=true' : '?isTest=false'
        }`

  const settings = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      X_CID: window.__ctrPageConfiguration.cid
    }
  }

  return new Promise((resolve, reject) => {
    window.ctrwowUtils
      .callAjax(url__BluesnapGetToken, settings)
      .then((data) => resolve(data))
      .catch((error) => reject(error))
  })
}

export function getProductNameAndPrice(pageType) {
  /**
   * pageType = checkout | upsell
   * return {
   *      totalPrice,
   *      productName
   * }
   */
  const unformatCountry = ['JPY', 'VND']
  let __totalPrice = 0
  let __productName = 'Total'
  switch (pageType) {
    case 'checkout': {
      const grandTotalElm = document.querySelector('.js-grand-total')
      __totalPrice = grandTotalElm && Number(grandTotalElm.innerText.replace(/[^0-9,.]/g, ''))
      __productName = document.querySelector('.widget-order-summary .js-product-name')
        ? document.querySelector('.widget-order-summary .js-product-name').innerText
        : 'Total'
      break
    }
    case 'upsell': {
      const spanUpsellPriceElm = document.querySelector('.spanUpsellPrice')
      __totalPrice = spanUpsellPriceElm && Number(spanUpsellPriceElm.innerText.replace(/[^0-9,.]/g, ''))
      __productName = document.querySelector('.js-name') ? document.querySelector('.js-name').innerText : 'Total'
      break
    }
  }

  if (window.additionPriceValue) {
    window.taxPercent = window.taxPercent || 0
    const additionPriceIncludeTax = window.additionPriceValue * (1 + window.taxPercent)
    // const add = additionPriceIncludeTax.toFixed(2).replace(/[^0-9]/g, '')
    let add = 0
    if (unformatCountry.indexOf(window.localStorage.getItem('currencyCode')) > -1) {
      add = additionPriceIncludeTax.toFixed(2)
    } else {
      add = additionPriceIncludeTax.toFixed(2).replace(/[^0-9,.]/g, '')
    }
    __totalPrice += Number(add)
  }

  return {
    totalPrice: __totalPrice.toFixed(2),
    productName: __productName
  }
}

export function initBluesnapButton({ strProductName, strTotalPrice, fnPlaceOrder }) {
  const elm__ButtonApplePay = document.querySelector('div[data-bluesnap="walletButton"]')
  elm__ButtonApplePay && (elm__ButtonApplePay.style.display = 'none')
  const SdkRequest = {
    token: window.bluesnapToken,
    googlePay: false, // OPTIONAL default is true.
    applePay: true, // OPTIONAL default is true.
    paymentData: {
      currencyCode: window.ctrwowUtils.localStorage().get('currencyCode'), // The three-letter ISO 4217 currency code for the payment.
      countryCode: window.ctrwowUtils.localStorage().get('ctr__countryCode'), // The merchant’s two-letter ISO 3166 country code.
      total: {
        label: strProductName !== undefined ? strProductName : 'Total',
        amount: strTotalPrice
      },
      // Use black buttons on white or light backgrounds to provide contrast.
      // Use white buttons on dark or colorful backgrounds.
      theme: 'black', // OPTIONAL black or white default is 'black'
      emailRequired: true, // OPTIONAL default false
      phoneRequired: true, // OPTIONAL default false
      fullBilling: true, // OPTIONAL default false
      shippingRequired: true, // OPTIONAL default false
      shippingOptions: [
        {
          identifier: 'FREE',
          detail: window.ctrwowUtils.localStorage().get('payment__ShippingTitle') || 'Shipping',
          label: window.ctrwowUtils.localStorage().get('payment__ShippingTitle') || 'Shipping',
          amount: '0.00'
        }
      ]
    },
    onEvent: {
      paymentAuthorized: function (success, error) {
        // close popup apple pay
        success()

        // show loading for checkout with CRM

        if (window.__CTRWOW_CONFIG.PAGE_TYPE === 6) {
          window.ctrwowUtils.showGlobalLoading()
        } else {
          const elmLoading = document.querySelector('.paymentStripeProccessing')
          elmLoading && (elmLoading.style.display = 'block')
        }

        // placeOrder
        fnPlaceOrder()
      },
      shippingOptionChange: function (shippingOptionsData, oldData, update) {
        update({})
      },
      shippingAddressChange: function (shippingAddressData, oldData, update) {
        // hide loading for case cancel
        clearTimeout(window.loadingTimeout)
        if (window.__CTRWOW_CONFIG.PAGE_TYPE === 6) {
          window.ctrwowUtils.hideGlobalLoading()
        } else {
          const elmLoading = document.querySelector('.paymentStripeProccessing')
          elmLoading && (elmLoading.style.display = 'none')
        }

        let currentProductNameAndPrice = null
        if (window.ctrwowUpsell !== undefined) {
          // case Upsell
          currentProductNameAndPrice = getProductNameAndPrice('upsell')
        } else {
          // case Checkout
          currentProductNameAndPrice = getProductNameAndPrice('checkout')
        }
        update({
          total: {
            ...oldData.total,
            amount: currentProductNameAndPrice.totalPrice,
            label: currentProductNameAndPrice.productName
          }
        })
      },
      error: function (event) {
        console.log('[ERROR]: ', event)
        // hidden button
        const elm__BluesnapApple = document.querySelector('.bluesnap-apple-submit-button')
        elm__BluesnapApple && elm__BluesnapApple.remove()
      },
      warning: function (event) {
        console.log('[WARNING]: ', event)
        // hidden button
        const elm__BluesnapApple = document.querySelector('.bluesnap-apple-submit-button')
        elm__BluesnapApple && elm__BluesnapApple.remove()
      }
    }
  }
  window.bluesnap.walletButtonSetup(SdkRequest)
}
