export const PAYMENT_TYPE = {
  PAYPAL: 'Paypal',
  CREDIT_CARD: 'CreditCard',
  IDEAL: 'Ideal',
  SOFORT: 'Sofort',
  SEZZLE: 'Sezzle',
  AFTERPAY: 'AfterPay',
  GAP: 'Google_Apple_Pay',
  STRIPE: 'stripe',
  BRAINTREE: 'Braintree',
  SEPA: 'Sepa',
  BRAINTREE_VENMO: 'BraintreeVenmo',
  BLUESNAPGOOGLE: 'BlueSnap_Google',
  AMAZON_PAY: 'Amazon_Pay',
  ALEPAY: 'AlePay',
  COIN: 'Coin',
  BANCONTACT: 'BanContact',
  KLARNA: '<PERSON>lar<PERSON>',
  BLUESNAP_APPLE: 'Bluesnap_Apple',
  FASTLANE: 'fastlane',
  PAYPAL_GOOGLE_PAY: 'PayPalGooglePay',
  STRIPE_ELEMENTS: 'Stripe_Elements',
  PAYPAL_APPLE: 'PayPalApple',
  PAYPAL_ACDC: 'PayPalAcdc'
}

/*
  id: PaymentProcessId
  name: get name of payment type and display on decline page
  className: get className handle display logo on diggy popup
*/
export const ARR_PAYMENT_TYPE = [
  {
    id: 94,
    name: PAYMENT_TYPE.PAYPAL_APPLE,
    className: 'paypal-apple-logo'
  },
  {
    id: 93,
    name: PAYMENT_TYPE.STRIPE_ELEMENTS,
    className: 'stripe-elements-logo'
  },
  {
    id: 92,
    name: PAYMENT_TYPE.PAYPAL_ACDC,
    className: 'paypal-acdc-logo'
  },
  {
    id: 91,
    name: PAYMENT_TYPE.PAYPAL_GOOGLE_PAY,
    className: 'paypal-google-pay-logo'
  },
  {
    id: 89,
    name: PAYMENT_TYPE.FASTLANE,
    className: 'fastlane-logo'
  },
  {
    id: 75,
    name: PAYMENT_TYPE.BLUESNAP_APPLE,
    className: 'bluesnap-apple-logo'
  },
  {
    id: 71,
    name: PAYMENT_TYPE.KLARNA,
    className: 'klarna-logo'
  },
  {
    id: 70,
    name: PAYMENT_TYPE.COIN,
    className: 'coin-logo'
  },
  {
    id: 66,
    name: PAYMENT_TYPE.ALEPAY,
    className: 'ale-logo'
  },
  {
    id: 61,
    name: PAYMENT_TYPE.AMAZON_PAY,
    className: 'amazon-logo'
  },
  {
    id: 58,
    name: PAYMENT_TYPE.BLUESNAPGOOGLE,
    className: 'bluesnap-google-logo'
  },
  {
    id: 55,
    name: PAYMENT_TYPE.BRAINTREE_VENMO,
    className: 'braintree-venmo-logo'
  },
  {
    id: 49,
    name: PAYMENT_TYPE.BANCONTACT,
    className: 'bancontact-logo'
  },
  {
    id: 47,
    name: PAYMENT_TYPE.SEPA,
    className: 'sepa-logo'
  },
  {
    id: 52,
    name: PAYMENT_TYPE.BRAINTREE,
    className: 'braintree-logo'
  },
  {
    id: 42,
    name: PAYMENT_TYPE.IDEAL,
    className: 'ideal-logo'
  },
  {
    id: 41,
    name: PAYMENT_TYPE.SOFORT,
    className: 'sofort-logo'
  },
  {
    id: 40,
    name: PAYMENT_TYPE.SEZZLE,
    className: 'sezzle-logo'
  },
  {
    id: 39,
    name: PAYMENT_TYPE.AFTERPAY,
    className: 'afterpay-logo'
  },
  {
    id: 54,
    name: PAYMENT_TYPE.GAP,
    className: 'google_apple-logo'
  },
  {
    id: 5,
    name: PAYMENT_TYPE.PAYPAL,
    className: 'paypal-logo'
  },
  {
    id: 31,
    name: PAYMENT_TYPE.PAYPAL,
    className: 'paypal-logo'
  },
  {
    id: 0,
    name: PAYMENT_TYPE.CREDIT_CARD,
    className: 'creditcard-logo'
  }
]
export const getPaymentName = (paymentProcessorId) => {
  const payment = ARR_PAYMENT_TYPE.find((item) => item.id === parseInt(paymentProcessorId))
  return payment ? payment.name : ''
}
export const getPaymentClassName = (paymentProcessorId) => {
  const payment = ARR_PAYMENT_TYPE.find((item) => item.id === parseInt(paymentProcessorId))
  return payment ? payment.className : ''
}
export const getPaymentProcessId = (payemtName) => {
  const payment = ARR_PAYMENT_TYPE.find((item) => item.name.toUpperCase() === payemtName.toUpperCase())
  return payment ? payment.id : ''
}
