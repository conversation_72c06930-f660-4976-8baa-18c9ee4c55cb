export const ctrApplePay = async ({ applepay }, fn__PlaceMainOrder) => {
  const { isEligible, countryCode, currencyCode, merchantCapabilities, supportedNetworks } = await applepay.config()

  if (!isEligible) {
    throw new Error('applepay is not eligible')
  }

  let apple__Session

  const __loadSDK = async () => {
    try {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://applepay.cdn-apple.com/jsapi/v1/apple-pay-sdk.js'

      return await new Promise((resolve, reject) => {
        script.onload = () => resolve(true)
        script.onerror = () => reject(new Error(`[GooglePay SDK] Failed to load: ${script.src}`))
        document.head.appendChild(script)
      })
    } catch (error) {
      console.error(`[GooglePay SDK] Loading error:`, error)
      return false
    }
  }

  const __renderApplePayButton = () => {
    const __elm = `<apple-pay-button 
      id="btn-apple" 
      buttonstyle=${window.customizeApple?.buttonStyle || 'black'} 
      type=${window.customizeApple?.type || 'buy'}
      locale=${countryCode || 'en'}></apple-pay-button>`

    if (document.querySelector('#button-apple-pay')) {
      document.querySelector('#button-apple-pay').insertAdjacentHTML('afterbegin', __elm)

      const elm__ButtonApple = document.querySelector('#btn-apple')
      elm__ButtonApple && elm__ButtonApple.addEventListener('click', handle__ClickAppleButton)

      const elm__ApplePayButton = document.querySelector('.button-apple-pay')
      elm__ApplePayButton && elm__ApplePayButton.addEventListener('click', handle__ClickAppleButton)
    }
  }

  const init = async () => {
    console.log('init Apple Pay')

    const sdkLoaded = await __loadSDK()
    if (!sdkLoaded) {
      throw new Error('Failed to load GooglePay SDK')
    }

    const { ApplePaySession } = window

    if (!ApplePaySession?.supportsVersion(4) && !ApplePaySession?.canMakePayments()) {
      console.log('Cannot support Apple Pay !')
    }

    __renderApplePayButton()
  }

  const __getAppleTransaction = () => {
    const tax = parseFloat(window.taxPercent) || 0
    let totalPrice = 0
    const product = window.ctrwowCheckout.checkoutData.getProduct()
    if (product) {
      const productPrice = product.productPrices.DiscountedPrice.Value
      const shippingPrice = product.shippings.length > 0 ? product.shippings[window.shippingIndex].price : 0
      totalPrice = productPrice + shippingPrice
      if (window.diggy__HasUpgrade === true && window.miniUpsells !== undefined && window.miniUpsells.length > 0) {
        const diggy__productMiniUpsell = window.diggy__productMiniUpsell
        const diggy__ProductPrice = diggy__productMiniUpsell.productPrices.DiscountedPrice.Value
        const diggy__ShippingPrice = diggy__productMiniUpsell.shippings.length > 0 && diggy__productMiniUpsell.shippings[0].price
        totalPrice += diggy__ProductPrice + diggy__ShippingPrice
      }

      totalPrice = totalPrice * (1 + tax)
    }

    return {
      amount: totalPrice.toFixed(2).toString(),
      type: 'final',
      label: product.productName
    }
  }

  const __getPaymentRequest = () => {
    const total = __getAppleTransaction()
    return {
      countryCode,
      currencyCode,
      merchantCapabilities,
      supportedNetworks,
      requiredBillingContactFields: ['name', 'phone', 'email', 'postalAddress'],
      requiredShippingContactFields: ['name', 'phone', 'email', 'postalAddress'],
      total
    }
  }

  const handle__ClickAppleButton = () => {
    const paymentRequest = __getPaymentRequest()
    apple__Session = new window.ApplePaySession(4, paymentRequest)

    apple__Session.onvalidatemerchant = (event) => {
      applepay
        .validateMerchant({
          validationUrl: event.validationURL
        })
        .then((payload) => {
          apple__Session.completeMerchantValidation(payload.merchantSession)
        })
        .catch((error) => {
          console.log(`[Error] Validate Merchant: ${error.message}`)
          apple__Session.abort()
          // redirect to declined
        })
    }

    apple__Session.onpaymentauthorized = async (event) => {
      const { trackingNumber } = await fn__PlaceMainOrder({ apple__Payment: event.payment })
      __confirmOrder(trackingNumber, event.payment)
    }

    apple__Session.oncancel = () => {
      // redirect to declined.
    }

    apple__Session.begin()
  }

  const __confirmOrder = async (trackingNumber, apple__Payment) => {
    // #1 Confirm Order from Apple Pay
    const apple__ConfirmOrder = await applepay.confirmOrder({
      orderId: trackingNumber,
      token: apple__Payment.token,
      billingContact: apple__Payment.billingContact,
      shippingContact: apple__Payment.shippingContact
    })
    console.log(`apple__ConfirmOrder: ${apple__ConfirmOrder}`)

    // #2 Complete Payment
    const completedPayment = apple__Session.completePayment({
      status: window.ApplePaySession.STATUS_SUCCESS
    })
    console.log(`completedPayment: ${completedPayment}`)

    // #3 continue
  }

  return {
    init,
    onProcessPayPalApplePay: handle__ClickAppleButton
  }
}
