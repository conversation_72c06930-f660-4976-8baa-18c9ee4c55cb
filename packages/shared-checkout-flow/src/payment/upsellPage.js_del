const __ctrPageConfiguration = window.__ctrPageConfiguration
const orderInfo = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
let eCRM
window.ctrwowUtils.getDependencies(['https://d3kdyumdtq5rp8.cloudfront.net/sales-prod.tryemanagecrm.com.1.0.min.js']).then(() => {
  window.ctrwowCheckout.ready().then(() => {
    eCRM = new window.EmanageCRMJS({
      webkey: __ctrPageConfiguration.webKey,
      cid: __ctrPageConfiguration.cid,
      lang: '',
      isTest: !!window.ctrwowUtils.link.getParameterByName('isCardTest')
    })
  })
})

// ********************************************************
export function placeUpsellOrderByGoogleOrApple(ctaButtons, { upsellProducts, isUpsellUpgrade = false }) {
  window.ctrwowUtils.getDependencies(['https://js.stripe.com/v3/']).then(() => {
    const obj = {
      upsellProducts: upsellProducts,
      isUpsellUpgrade: isUpsellUpgrade
    }
    initStripe(ctaButtons, obj)
  })
}

export function initStripe(ctaButtons, { upsellProducts, isUpsellUpgrade }) {
  eCRM.Order.getMidAndPrn((data) => {
    if (!window.Stripe) {
      console.log('no stripe loaded')
    }

    try {
      window.midId = data.midId
      const stripe = window.Stripe(data.prnCode.split(';')[0], { stripeAccount: data.prnCode.split(';')[1] })

      // init Stripe instance with default value
      let countryCode = window.ctrwowUtils.localStorage().get('countryCode')
      let currencyCode = window.ctrwowUtils.localStorage().get('currencyCode')

      // override for testing at the unsupported countries
      const isTestGAP = window.ctrwowUtils.link.getQueryParameter('isTestGAP')
      if (isTestGAP && isTestGAP === 'true') {
        countryCode = 'US'
        currencyCode = 'usd'
      }

      // init instance with default value
      const paymentRequest = stripe.paymentRequest({
        country: countryCode,
        currency: currencyCode.toLowerCase(),
        total: {
          label: 'Sample Product',
          amount: 0
        },
        requestPayerName: true,
        requestPayerEmail: true,
        requestPayerPhone: true
      })

      // Check the availability of the Payment Request API first.
      paymentRequest.canMakePayment().then(function (result) {
        if (result) {
          console.log(result)
          Array.prototype.slice.call(ctaButtons).forEach((ele) => {
            ele.addEventListener('click', function (e) {
              e.preventDefault()
              const spanUpsellPriceElm = document.querySelector('.spanUpsellPrice')
              let amount = 0
              if (spanUpsellPriceElm) {
                amount = Number(spanUpsellPriceElm.innerText.replace(/[^0-9]/g, ''))
              }
              paymentRequest.update({
                currency: window.ctrwowUtils.localStorage().get('currencyCode').toLowerCase(),
                total: {
                  label: ' ',
                  amount
                }
              })
              paymentRequest.show()
            })
          })
        } else {
          console.log('not support')
        }
      })

      paymentRequest.on('source', function (event) {
        console.log(event)
        event.complete('success')
        window.ctrwowUtils.localStorage().set('google_apple_upsell_email', event.payerEmail)
        if (isUpsellUpgrade) {
          placeUpsellUpgrade(upsellProducts, event)
        } else {
          // placeUpsellOrder(event)
        }
      })
    } catch (err) {
      console.log(err)
    }
  }, 54)
}
// ********************************************************
export function getUpsellData(upsell, upsell_productindex) {
  const checkPaymentProcess = (paymentProcessorId) => {
    if (paymentProcessorId === 5 || paymentProcessorId === 31) {
      return true
    }
    return false
  }
  // creditcard => get card id
  let pay = {
    cardId: orderInfo.cardId
  }

  // !creditcard => get paymentprocessid
  var isPaymentPaypal = (upsell.orderInfo.useCreditCard && checkPaymentProcess(parseInt(upsell.orderInfo.paymentProcessorId))) || false
  if (!orderInfo.useCreditCard && orderInfo.paymentProcessorId) {
    pay = {
      paymentProcessorId: Number(orderInfo.paymentProcessorId)
    }
  } else {
    // add installment
    if (!!orderInfo.installmentValue && orderInfo.installmentValue !== '') {
      pay.Instalments = orderInfo.installmentValue
    }
  }

  // add callback param to server to keep track
  const replacedParam = window.ctrwowUtils.link
    .getCustomPathName()
    .replace(/\?|\&*paymentId=[^&]*/g, '')
    .replace(/\?|\&*token=[^&]*/g, '')
    .replace(
      // eslint-disable-next-line no-useless-escape
      /\?|\&*PayerID=[^&]*/g,
      ''
    )
  pay.callBackParam = replacedParam !== '' ? '?' + replacedParam + '&' + getUpParam() : '?' + getUpParam()
  // add antiFraud
  let antiFraud
  try {
    antiFraud = JSON.parse(window.ctrwowUtils.localStorage().get('antiFraud'))
  } catch (ex) {
    console.log(ex)
    antiFraud = null
  }

  const _upsellData = {
    campaignUpsell: {
      webKey: upsell.mainWebKey,
      relatedOrderNumber: orderInfo.orderNumber
    },
    shippingMethodId:
      upsell.products[upsell_productindex].shippings.length > 0 ? upsell.products[upsell_productindex].shippings[0].shippingMethodId : null,
    comment: '',
    useShippingAddressForBilling: true,
    productId: upsell.products[upsell_productindex].productId,
    customer: { email: !isPaymentPaypal ? orderInfo.cusEmail : null },
    payment: pay,
    shippingAddress: orderInfo.addressId != null ? { id: orderInfo.addressId } : null,
    funnelBoxId: 0,
    antiFraud: {
      sessionId: antiFraud ? antiFraud.sessionId : ''
    }
  }

  if (Number(orderInfo.paymentProcessorId) === 54) {
    _upsellData.mid = {
      midId: window.midId
    }
  }
  return _upsellData
}
export function saveLocalUpsellData(responseData, upsell, upsell_productindex) {
  const processing = new Promise((resolve, reject) => {
    if (responseData != null && responseData.success) {
      resolve(responseData, upsell, upsell_productindex)
    } else {
      reject(responseData, upsell, upsell_productindex)
    }
  })

  return processing.then((responseData, upsell, upsell_productindex) => {
    // window.ctrwowUtils.localStorage().set('fireUpsellForGTMPurchase', getUpParam() + '=1')
    window.ctrwowUtils.localStorage().set('paypal_isMainOrder', 'upsell')
    window.ctrwowUtils.localStorage().set('upsellOrderNumber', responseData.orderNumber)
    // success page will use this trackingNumber to call confirm payment api
    if (responseData.trackingNumber) {
      window.ctrwowUtils.localStorage().set('trackingNumber', responseData.trackingNumber)
    }

    const { upsellUrls = [] } = upsell.orderInfo
    const selectedProduct = upsell.products[upsell_productindex]
    upsellUrls.push({
      campaignWebKey: upsell.upsellWebKey,
      campaignName: upsell.upsellCampaignName,
      orderNumber: responseData.orderNumber,
      customerId: responseData.customerResult.customerId,
      url: location.href,
      orderedProducts: [
        {
          sku: selectedProduct.sku,
          pid: selectedProduct.productId,
          name: selectedProduct.productName
        }
      ]
    })
    const savedOfUpsell = selectedProduct.productPrices.FullRetailPrice.Value - selectedProduct.productPrices.DiscountedPrice.Value
    upsell.orderInfo.upsellIndex += 1
    upsell.orderInfo.savedTotal += savedOfUpsell
    upsell.orderInfo.upsellPriceToUpgrade = selectedProduct.productPrices.DiscountedPrice.Value
    upsell.orderInfo.upsellUrls = upsellUrls

    window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(upsell.orderInfo))
    window.ctrwowUtils.localStorage().set('webkey_to_check_paypal', upsell.upsellWebKey)
  })
}
// export function _handleLastUpsellOrError() {
//   let upParam = ''
//   const upsellName = getUpsellNameFromUrl()
//   if (upsellName) {
//     upParam = '?up_' + upsellName

//     if (upsell.orderInfo.isUpsellOrdered === 1) {
//       upParam += '=1'
//     } else {
//       upParam += '=0'
//     }
//   }

//   const redirectUrl = __ctrPageConfiguration.successUrl
//   window.ctrwowUtils.link.redirectPage(redirectUrl + upParam)
// }
export function getUpParam() {
  let upParam = ''
  if (location.href.split('special-offer-', 2).length > 1) {
    upParam = 'up_' + location.href.split('special-offer-', 2)[1].split('.html', 1) + '=1'
  }
  return upParam
}
// ********************************************************
// export function placeUpsellOrder(source) {
//   const upsellData = getUpsellData()
//   const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
//   eCRM.Order.placeUpsellOrder(upsellData, eCRM.webKey, function (dataResponse) {
//     if (paymentType && paymentType === 'google_apple_pay') {
//       // confirm payment for only google apple pay
//       let midId
//       if (eCRM.isTest) {
//         midId = window.midId.toString().substr(0, 3)
//       } else {
//         midId = window.midId
//       }
//       // don't need to wait response of confirmation payment to redirect to upsell
//       eCRM.Order.confirmGoogleApplePay(dataResponse.trackingNumber, source.source.id, midId, (dataResponse) => {
//         console.log(dataResponse)
//       })
//     }
//     setTimeout(() => {
//       saveLocalUpsellData()
//         .then((responseData, upsell, upsell_productindex) => {
//           if (responseData.callBackUrl) {
//             document.location = responseData.callBackUrl
//           } else if (responseData.paymentContinueResult && responseData.paymentContinueResult.actionUrl !== '') {
//             document.location = responseData.paymentContinueResult.actionUrl
//           } else if (upsell.orderInfo.upsellIndex < upsell.orderInfo.upsells.length) {
//             const upsellUrl = upsell.orderInfo.upsells[upsell.orderInfo.upsellIndex].upsellUrl
//             const redirectUrl = upsellUrl.substring(upsellUrl.lastIndexOf('/') + 1, upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length)
//             window.ctrwowUtils.link.redirectPage(redirectUrl + '?' + getUpParam() + '=1')
//           } else {
//             _handleLastUpsellOrError()
//           }
//         })
//         .catch(() => {
//           _handleLastUpsellOrError()
//         })
//     }, 3000)
//   })
// }
// export function placeUpsellListicle() {
//   let upsellData = getUpsellData()
//   const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
//   eCRM.Order.placeUpsellOrder(upsellData, upsellWebKey, function(dataResponse){
//     if (paymentType && paymentType === 'google_apple_pay') {
//       // confirm payment for only google apple pay
//       let midId
//       if (eCRM.isTest) {
//         midId = window.midId.toString().substr(0, 3)
//       } else {
//         midId = window.midId
//       }
//       // don't need to wait response of confirmation payment to redirect to upsell
//       eCRM.Order.confirmGoogleApplePay(result.trackingNumber, source.source.id, midId, (dataResponse) => {
//         console.log(dataResponse)
//       })
//     }
//     setTimeout(() => {
//       saveLocalUpsellData().then((responseData, upsell, upsell_productindex) => {
//         console.log('abc')
//       })
//     }, 3000);
//   })
// }
export function placeUpsellUpgrade(upsellProducts, source) {
  const orderInfo = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
  const upsell = {}
  upsell.product = upsellProducts.prices.find((item) => item.quantity === orderInfo.quantity)

  let pay = {
    cardId: orderInfo.cardId
  }

  if (!orderInfo.useCreditCard && orderInfo.paymentProcessorId) {
    pay = {
      paymentProcessorId: Number(orderInfo.paymentProcessorId)
    }
  } else {
    // add installment
    if (!!orderInfo.installmentValue && orderInfo.installmentValue !== '') {
      pay.Instalments = orderInfo.installmentValue
    }
  }

  const replacedParam = location.search
    .replace(/\?|\&*paymentId=[^&]*/g, '')
    .replace(/\?|\&*token=[^&]*/g, '')
    .replace(/\?|\&*PayerID=[^&]*/g, '')
  pay.callBackParam = replacedParam !== '' ? '?' + replacedParam + '&' + getUpParam() : '?' + getUpParam()

  let antiFraud
  try {
    antiFraud = JSON.parse(window.ctrwowUtils.localStorage().get('antiFraud'))
  } catch (ex) {
    console.log(ex)
    antiFraud = null
  }

  window.ctrwowUtils.showGlobalLoading()

  const postAPI = `${window.__ctrPageConfiguration.crmEndpoint}/orders/${orderInfo.orderNumber}/${upsell.product.productId}`
  const orderData = {
    productId: orderInfo.orderedProducts[0].pid,
    shippingMethodId: upsell.product.shippings.length > 0 ? upsell.product.shippings[0].shippingMethodId : null,
    comment: '',
    useShippingAddressForBilling: true,
    customer: {
      email: orderInfo.cusEmail
    },
    payment: pay,
    funnelBoxId: 0,
    shippingAddress: orderInfo.addressId != null ? { id: orderInfo.addressId } : null,
    billingAddress: null,
    antiFraud: {
      sessionId: antiFraud ? antiFraud.sessionId : ''
    }
  }
  if (pay.paymentProcessorId === 54) {
    orderData.mid = {
      midId: window.midId.toString().substr(0, 3)
    }
  }
  const paymentType = window.ctrwowUtils.localStorage().get('userPaymentType')
  window.ctrwowUtils
    .callAjax(postAPI, {
      method: 'POST',
      headers: {
        X_CID: window.__ctrPageConfiguration.cid,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(orderData)
    })
    .then((result) => {
      if (paymentType && paymentType === 'google_apple_pay') {
        // confirm payment for only google apple pay
        let midId
        if (eCRM.isTest) {
          midId = window.midId.toString().substr(0, 3)
        } else {
          midId = window.midId
        }
        // don't need to wait response of confirmation payment to redirect to upsell
        eCRM.Order.confirmGoogleApplePay(result.trackingNumber, source.source.id, midId, (dataResponse) => {
          console.log(dataResponse)
        })
      }
      setTimeout(() => {
        if (result && result.success) {
          // store param in localStorage to fire gtm event of purchase
          window.ctrwowUtils.localStorage().set('fireUpsellForGTMPurchase', getUpParam().split('=')[0])

          var orderInfoData = JSON.parse(window.ctrwowUtils.localStorage().get('orderInfo'))
          orderInfoData.upsellIndex += 1
          // upsell.orderInfo.upsellIndex += 1;
          orderInfoData.orderTotal = upsell.product.productPrices.DiscountedPrice.Value
          orderInfoData.savedTotal = upsell.product.productPrices.FullRetailPrice.Value - orderInfoData.orderTotal
          orderInfoData.orderedProducts.sku = upsell.product.sku
          window.ctrwowUtils.localStorage().set('orderInfo', JSON.stringify(orderInfoData))
          window.ctrwowUtils.localStorage().set('paypal_isMainOrder', 'upsell')
          window.ctrwowUtils.localStorage().set('upsellOrderNumber', result.orderNumber)
          window.ctrwowUtils.localStorage().set('webkey_to_check_paypal', window.__ctrPageConfiguration.webKey)

          if (result.upsells.length < 1 && orderInfoData.upsells.length > 0) {
            result.upsells = orderInfoData.upsells
          }
          if (result.callBackUrl) {
            document.location = result.callBackUrl
          } else if (result.paymentContinueResult && result.paymentContinueResult.actionUrl !== '') {
            document.location = result.paymentContinueResult.actionUrl
          } else if (orderInfoData.upsellIndex < orderInfoData.upsells.length) {
            const upsellUrl = orderInfoData.upsells[orderInfoData.upsellIndex].upsellUrl
            const redirectUrl = upsellUrl.substring(
              upsellUrl.lastIndexOf('/') + 1,
              upsellUrl.indexOf('?') >= 0 ? upsellUrl.indexOf('?') : upsellUrl.length
            )
            window.ctrwowUtils.link.redirectPage(redirectUrl + '?' + getUpParam())
          } else {
            window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
          }
        } else {
          window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
        }
      }, 3000)
    })
    // eslint-disable-next-line handle-callback-err
    .catch((err) => {
      window.ctrwowUtils.link.redirectPage(window.__ctrPageConfiguration.confirmUrl)
    })
}
