export default function getPageSettings() {
  try {
    const pageConfig = window.__ctrPageConfiguration || {}

    if (!pageConfig.successUrl || pageConfig.successUrl.length < 1) {
      pageConfig.successUrl = './success.html'
    }

    if (!pageConfig.declineUrl || pageConfig.declineUrl.length < 1) {
      pageConfig.declineUrl = './decline.html'
    }

    if (!pageConfig.confirmUrl || pageConfig.confirmUrl.length < 1) {
      pageConfig.confirmUrl = './confirm.html'
    }

    return pageConfig
  } catch (e) {
    console.error('[ctrwow-checkout-flow][checkoutData][getPageSettings] cannot read __ctrPageConfiguration')
    console.error(window.__ctrPageConfiguration)
  }
}

export async function getPageSettingsAsync() {
  const pageConfig = getPageSettings()
  const hasConfig = (config) => config && config.webKey && config.cid

  if (hasConfig(pageConfig)) {
    return Promise.resolve(pageConfig)
  }

  // delay & retry to get page-configuration : total wait = 20*100ms (20 times, 100ms each)
  let retryTimes = 20
  const waiting = () => new Promise((resolve) => setTimeout(resolve, 100))

  const retry = async function () {
    try {
      console.log('=========== retry >>>' + retryTimes + '>>>' + Date.now())
      const config = getPageSettings()
      if (hasConfig(config) || !retryTimes--) {
        return await config
      }
      throw Error('pageConfig is undefined')
    } catch (e) {
      await waiting()
      return retry()
    }
  }

  return retry()
}
