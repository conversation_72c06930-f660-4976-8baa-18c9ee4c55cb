/* eslint-disable */
import getDeviceType from 'shared-trackings/src/getDeviceType'
import getUrlParams from 'shared-common/src/getUrlParams'

// Global module
const API_END_POINT_URL =
  window.__CTR_FUNNEL_TRACKING_SETTINGS && window.__CTR_FUNNEL_TRACKING_SETTINGS.MODE === 'dev'
    ? 'https://ctrwow-dev-funnelbuilder-microservice.azurewebsites.net/api'
    : 'https://funnel.ctrwow.com/api'



function apiClient(url, settings) {
  let settingsClone = {}
  if (!url) {
    throw new Error('API URL is missing!')
  }
  const headersOptions = {
    'content-type': 'application/json'
  }
  if (settings && typeof settings === 'object') {
    settingsClone = Object.assign(
      {
        method: 'GET',
        headers: settings.headers ? Object.assign(headersOptions, settings.headers) : headersOptions
      },
      settings
    )
  }
  return window.fetch(url, settingsClone).then(function (response) {
    if (response.status === 204 || response.status === 205) {
      return null
    }

    if (response.status >= 200 && response.status < 300) {
      // return response.json();
      return response.text().then(function (text) {
        try {
          return text ? JSON.parse(text) : {}
        } catch (e) {
          return {}
        }
      })
    }
    throw JSON.stringify({
      status: response.status,
      statusText: response.statusText,
      response: response
    })
  })
}

function getQueryParameter(name, url) {
  if (!url) url = window.location.href
  // eslint-disable-next-line no-useless-escape
  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
    results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

function isFunction(functionToCheck) {
  return functionToCheck && {}.toString.call(functionToCheck) === '[object Function]'
}

function validateTrackingConversionParams() {
  const hId = getQueryParameter('ctr_hid')
  if (!hId) {
    throw new Error('__CTR_FUNNEL_TRACKING: Missing ctr_hid')
  }
}

function getFunnelParams({ ctr_hid, ctr_payout }) {
  return `ctr_hid=${ctr_hid}&ctr_payout=${ctr_payout}&ctr_device_type=${getDeviceType()}`
}

/**
 *
 * @param onSuccess
 * @param onError
 * @param conversionData
 * @returns {boolean}
 */
export function trackConversion(onSuccess, onError, conversionData) {
  try {
    validateTrackingConversionParams()
    const ctr_hid = getQueryParameter('ctr_hid')
    const ctr_payout = getQueryParameter('ctr_payout') || 0
    apiClient(API_END_POINT_URL + `/funnelConversion?ctr_device_type=${getDeviceType()}`,
      {
        method: 'POST',
        body: JSON.stringify({
          urlParams: getUrlParams(),
          ...(conversionData || {}),
          hitId: ctr_hid,
          payout: ctr_payout
        })
      })
      .then(function (rs) {
        isFunction(onSuccess) && onSuccess(rs)
      })
      .catch(function (error) {
        isFunction(onError) && onError(error)
      })
  } catch (e) {
    console.warn('__CTR_FUNNEL_TRACKING: trackConversion error: ' + e)
    console.warn('__CTR_FUNNEL_TRACKING: trackConversion error: ' + e)
    return false
  }
  return true
}

/**
 *
 * @param onSuccess
 * @param onError
 * @returns {boolean}
 */
export function trackExConversion(onSuccess, onError) {
  try {
    validateTrackingConversionParams()
    const ctr_hid = getQueryParameter('ctr_hid')
    const ctr_payout = getQueryParameter('ctr_payout') || 0
    apiClient(API_END_POINT_URL + `/funnelConversion?${getFunnelParams({ ctr_hid, ctr_payout })}`, { method: 'POST' })
      .then(function (rs) {
        window.localStorage.setItem(key, trackingIdNew)
        isFunction(onSuccess) && onSuccess(rs)
      })
      .catch(function (error) {
        isFunction(onError) && onError(error)
      })
  } catch (e) {
    console.warn('__CTR_FUNNEL_TRACKING: trackConversion error: ' + e)
    return false
  }
  return true
}

function init() {
  console.log('__CTR_FUNNEL_TRACKING: init')
}

init()
