import { generateCurrencyNumber } from 'shared-29next/src/helper/generateCurrencyNumber'
import { defineTemplateItem } from './defineTemplateItem'

function replaceProductName(html, names) {
  try {
    if (names) {
      const nameArr = names.split('\n')
      for (let i = 0; i < nameArr.length; i++) {
        html = html.replace(nameArr[i].split('::')[0].trim(), nameArr[i].split('::')[1].trim())
      }
      return html
    } else {
      return html
    }
  } catch (e) {
    console.log(e)
    return html
  }
}

function bindItemData(html, data, names) {
  let tmp = html
    .replace(/\{productName\}/g, `${data.quantity}x ${data.product_title}`)
    .replace(/\{productPrice\}/g, data.orderProductPriceFormatted)
    .replace(/\{productTotal\}/g, data.totalPriceFormatted)
    .replace(/\{shippingPrice\}/g, data.shippingPriceFormatted)
  tmp = replaceProductName(tmp, names)
  return tmp
}

export function getOrderDetails(data, elm) {
  window.q('.ordered-items-wrap').addClass('loaded')

  const productNames = elm.getAttribute('product-names')
  const defineTemplate = defineTemplateItem(elm)
  window.currencyCode = data.currency
  const orderTotal = generateCurrencyNumber(data.total_incl_tax)
  elm.querySelector('.confirm-info-summary').innerHTML = elm.querySelector('.confirm-info-summary').innerHTML.replaceAll('{orderTotal}', orderTotal)

  const orderedItems = data.lines
  elm.querySelector('.ordered-items').innerHTML = ''
  elm.querySelector('.ordered-items').classList.remove('hidden')
  for (const [i, item] of orderedItems.entries()) {
    item.orderProductPriceFormatted = generateCurrencyNumber(item.price_incl_tax)
    item.shippingPriceFormatted = generateCurrencyNumber(0)
    let total = Number(item.price_incl_tax)
    if (i === 0) {
      item.shippingPriceFormatted = generateCurrencyNumber(data.shipping_incl_tax)
      total += Number(data.shipping_incl_tax || 0)
    }

    item.totalPriceFormatted = generateCurrencyNumber(total)

    const productItem = bindItemData(defineTemplate.productItemMainTmp, item, productNames)
    elm.querySelector('.ordered-items').insertAdjacentHTML('beforeend', productItem)
  }

  window.ctrwowUtils.events.emit('onAfterPopulateConfirmData')
}

export function checkAppendNextPurChase(elm) {
  const getExtendedText = () => window.localStorage.getItem('extendedtext')
  const isNextPurChase = getExtendedText()
  const firstItemsList = elm.querySelectorAll('.ordered-items-wrap .ordered-items .ordered-item')[0]
  if (firstItemsList && isNextPurChase) {
    firstItemsList.querySelector('span').innerText = firstItemsList.querySelector('span').innerText + ' ' + getExtendedText()
  }
}
