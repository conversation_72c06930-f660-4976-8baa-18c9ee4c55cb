import { getOrderInfo } from 'shared-checkout-flow/src/orderInfo/orderInfo'
export function generateOrderInfo() {
  const data = getOrderInfo()
  if (!data) return false

  if (data.orderedProducts) {
    const mainProduct = data.orderedProducts.find((elm) => elm.type === 'main')
    data.productName = mainProduct.name
    data.orderProductPriceFormatted = mainProduct.formatedPrice
    data.orderPriceFormatted = mainProduct.orderTotalFormated
    data.shippingPriceFormatted = mainProduct.shippingFormatPrice
  }

  if (data.upsellUrls) {
    data.upsellUrls = data.upsellUrls.map((item) => {
      item.productName = item.name
      item.orderProductPriceFormatted = item.formatedPrice
      item.orderPriceFormatted = item.orderTotalFormated
      item.shippingPriceFormatted = item.shippingFormatPrice
      return item
    })
    data.relatedOrders = data.upsellUrls
  }

  return data
}
