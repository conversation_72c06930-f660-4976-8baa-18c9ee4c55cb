export function getOrderTotal(data) {
  const total = data.orderedProducts.reduce((dummy, product) => {
    return dummy + product.orderTotal
  }, 0)
  return window.ctrwowUtils.number.formaterNumberByFormattedValue(total, data.formattedNumber)
}

export function getFormattedSavedTotal(data) {
  return window.ctrwowUtils.number.formaterNumberByFormattedValue(data.savedTotal || 0, data.formattedNumber)
}
