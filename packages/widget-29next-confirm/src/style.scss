/** widget-confirm-sticky **/
#id {
  font-family: '<PERSON><PERSON>', sans-serif;
  margin: 0;
  font-size: 15px;
}
.hidden {
  display: none !important;
  &[data-gjs-type] {
    display: block !important;
    &.flex {
      display: flex !important;
    }
  }
}
.confirm-banner {
  padding: 30px 0;
  text-align: center;
  background: url("https://cdn.wowsuite.ai/ctrwow/public-assets/images/confirm-bg.png") top center/cover no-repeat #2A3C9A;
  color: #fff;
  .inner {
    width: 100%;
    max-width: 930px;
    margin: 0 auto;
    padding: 0 15px;
  }
  h2 {
    font-size: 28px;
    margin: 0;
    font-weight: normal;
    line-height: 1.4;
  }
  .email-icon {
    width: 150px;
    height: auto;
  }
  @media (max-width: 767px) {
    h2 {
      font-size: 22px;
    }
  }
}

.confirm-information {
  width: 100%;
  max-width: 630px;
  margin: 0 auto;
  padding: 40px 15px 0;
  margin-bottom: 10px;
  .confirm-email {
    padding-bottom: 20px;
    border-bottom: 1px solid #ccc;
  }
}

.confirm-delivery {
  padding: 20px 20px;
  background-color: #f2f0f0;
  margin-top: 20px;
  display: flex;
  align-items: start;
  img {
    margin-right: 15px;
  }
  p {
    margin: 0;
  }
}

.confirm-info-summary {
  padding-bottom: 30px;
  h4 {
    margin: 0 0 15px;
    font-size: 16px;
  }
  p {
    margin: 0 0 5px;
    font-size: 15px;
    line-height: 1.5;
  }
}

.confirm-address {
  display: flex;
  justify-content: space-between;
  margin: 0 -10px;
  .shipping-address,
  .billing-address {
    min-width: 200px;
    flex: 1 0 0%;
    margin: 20px 10px;
    padding: 20px;
    background-color: #eeecec;
  }
  .shipping-content,
  .billing-content {
    color: #707070;
    line-height: 1.5;
  }
  h3 {
    font-size: 16px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    margin: 0 0 10px;
    img {
      margin-right: 10px;
    }
  }
  @media (max-width: 767px) {
    flex-direction: column;
    margin: 10px 0;
    .shipping-address,
    .billing-address {
      margin: 10px 0;
    }
  }
}

.receipt-list {
  margin-bottom: 20px;
  .title {
    display: flex;
    align-items: center;
    margin: 0 0 10px;
    position: relative;
    padding: 8px 20px;
    font-weight: 700;
    font-size: 16px;
    color: #000;
    background-color: #F2F0F0;
    img {
      margin-right: 10px;
    }
  }
}
@keyframes _fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.ordered-items-wrap {
  margin-top: 10px;
  min-height: 100px;
  background: url(https://d16hdrba6dusey.cloudfront.net/sitecommon/images/loading.svg) no-repeat center center / 40px;
  &.loaded {
    background: none;
  }
}
.ordered-item {
  opacity: 0;
  animation: _fadeIn 500ms ease forwards;
  margin-bottom: 10px;
  @for $i from 2 to 11 {
    &:nth-child(#{$i}) { animation-delay: $i * 150ms; }
  }
}
.ordered-item_row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 6px 0;
  color: #707070;
  & + .ordered-item_row {
    border-top: 1px solid #ddd;
  }
  span {
    width: 70%;
    & + span {
      text-align: right;
      width: 30%;
      font-weight: 600;
    }
  }
  &.total {
    font-weight: bold;
    color: #2A3C9A;
  }
  &.statement {
    padding-top: 10px;
    span {
      width: 100%;
      font-size: 14px;
      font-style: italic;
      line-height: 1.3em;
    }
  }
}

.confirm-help-center {
  background-color: #f2f0f0;
  padding: 20px 0;
  .wrapper {
    max-width: 630px;
    margin: auto;
    padding: 0 15px;
    display: flex;
    align-items: flex-start;
  }
  .content {
    padding-left: 15px;
  }
  .content h3 {
    margin: 0 0 15px;
  }
  .content p {
    margin: 0 0 5px;
    font-size: 15px;
    color: #707070;
  }
  .content p a {
    color: #1B60D6;
  }
  @media (max-width: 767px) {
    .wrapper {
      flex-direction: column;
    }
    .content {
      padding-left: 0;
      padding-top: 10px;
    }
  }
}
