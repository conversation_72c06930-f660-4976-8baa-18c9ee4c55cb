/**
 * <AUTHOR> <<EMAIL>>
 * widget-29next-confirm - Widget controller
 * @param element<DomElement>
 * @param props<Object>
 */

import { getBaseEndpoint } from 'shared-29next/src/helper'
import { detectRedirectConfirmPage } from 'shared-29next/src/confirm'
import * as helper from './helper'
import * as tracking from 'shared-checkout-flow/src/tracking'
export { tracking }

export default function initComponent(element, props) {
  detectRedirectConfirmPage()

  // ctrwowUtils.localStorage().remove('orderOnePageFlag')
  if (window.sessionStorage.getItem('orderToken')) console.log('This confirmation page has the order token!')

  const orderInfo = helper.generateOrderInfo()
  if (!orderInfo) return
  console.log(`used field useCreditCard ${orderInfo.useCreditCard}`)

  function hideNoDataElm() {
    // if (window.localStorage.getItem('userPaymentType') === 'paypal') {
    //   window.q('.pp').hide()
    // }
  }
  function populateData(data) {
    const customerName = helper.getDisplayName(data.user)
    const orderDate = helper.getFormattedDateNow(new Date().toISOString().split('T')[0])
    const savedTotal = helper.getFormattedSavedTotal(orderInfo)
    const shippingAddress = helper.getFormattedAddress(data.shipping_address)
    const billingDetails = helper.getFormattedAddress(data.billing_address)

    element.innerHTML = element.innerHTML
      .replaceAll('{orderNumber}', data.number)
      .replaceAll('{customerEmail}', data.user.email || '')
      .replaceAll('{customerName}', customerName)
      .replaceAll('{orderDate}', orderDate)
      .replaceAll('{savedTotal}', savedTotal)
      .replaceAll('{shippingAddress}', shippingAddress)
      .replaceAll('{billingDetails}', billingDetails)
  }

  function init() {
    tracking.trackingPlaceOrder()
    hideNoDataElm()
  }
  init()

  // const url = `https://ctrwow-dev-ecommerceplatformintegrationpublic-microservice.azurewebsites.net/api/orders?order_number=${orderInfo.orderNumber}`
  const url = `${getBaseEndpoint()}/orders/${orderInfo.ref_id}`
  window.ctrwowUtils
    .callAjax(url, {
      method: 'GET',
      headers: {
        Authorization: window.ctrwowUtils.localStorage().get('29NextApiKey')
      }
    })
    .then((res) => {
      if (res && res.number) {
        populateData(res)
        helper.getOrderDetails(res, element)
        helper.checkAppendNextPurChase(element)
      }
    })
}
