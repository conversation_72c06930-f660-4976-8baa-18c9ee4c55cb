/**
 * @widgetName widget-29next-confirm
 * @version 1.0.0
 * @see WIDGET_STORY
 * <AUTHOR> <<EMAIL>>
 */

import { widgetControllerWrapper } from 'shared-gjs-utils/src/widgetControllerWrapper'
import componentController from './controller.mod'
import widgetTemplate from './template.html'
import widgetStyle from './style.scss'
import { WIDGET_SETTINGS } from './settings'

export default (editor, config) => {
  const { widgetName, widgetTypeId, packageName } = config
  const domComponents = editor.DomComponents
  domComponents.addType(widgetTypeId, {
    model: {
      defaults: {
        name: widgetName,
        css: widgetStyle.toString(),
        ...WIDGET_SETTINGS,
        components: widgetTemplate,
        'script-props': [],
        'script-export': widgetControllerWrapper(componentController, { packageName })
      }
    }
  })
}
