export const scrollMapCanvasId = '_ctr_hm_scrollmap_canvas'
export const ReachedPercentageTooltipId = `ReachedPercentageTooltip`
export const AvarageFoldLineId = `__ctr_hm_AvarageFoldLine`
const STEP_PERCENT = 5;

function findReportItem(reportData, posY) {
  const index = findReportItemIndex(reportData, posY)
  return reportData[index]
}

function findReportItemIndex(reportData, posY) {
  const stepHeight = reportData[0].pixels_scrolled
  return reportData.findIndex((rpItem) => {
    return posY >= rpItem.pixels_scrolled - stepHeight && posY <= rpItem.pixels_scrolled
  })
}

function getCanvasColor(value, opacity = 0.8) {
  const h = (1.0 - (value > 1 ? 1 : value)) * 240
  return 'hsla(' + h + ', 100%, 50%, ' + opacity + ')'
}

function renderScrollMapCanvas(canvas, reportData) {
  const width = canvas.width
  const height = canvas.height
  const context = canvas.getContext('2d')
  context.globalAlpha = 0.8
  console.log({
    canvasHeight: height
  })
  for (let i = 0; i < height; i++) {
    try {
      const reportItem = findReportItem(reportData, i)
      const value = reportItem.percent_of_total / 100
      context.beginPath()
      context.moveTo(0, i)
      context.lineTo(width, i)
      context.lineWidth = 1
      context.strokeStyle = getCanvasColor(value)
      context.stroke()
    } catch (e) {
      console.log({
        iError: i
      })
    }
  }
}

function initScrollReportData(pHeight) {
  const rs = []
  console.log({
    pHeight
  })
  for (let i = STEP_PERCENT; i <= 100; i += STEP_PERCENT) {
    const item = {
      percentage_scrolled: i,
      pixels_scrolled: Math.round(pHeight * (i / 100)),
      percent_of_total: 0,
      count: 0
    }
    rs.push(item)
  }
  return rs
}

function populateReportData(reportData, trackData, totalSessions) {
  const rs = [...reportData]
  const positions = []
  Object.keys(trackData).forEach((key) => {
    const posY = trackData[key][0]
    positions.push(posY)
  })
  positions.forEach((posY) => {
    const updateIndex = findReportItemIndex(reportData, posY)
    if (updateIndex > -1) {
      for (let i = 0; i <= updateIndex; i++) {
        reportData[i].count += 1
        reportData[i].percent_of_total = (reportData[i].count / totalSessions) * 100
        reportData[i].percent_of_total = Math.round(reportData[i].percent_of_total)
      }
    }
  })
  return rs
}

export function resetScrollMap() {
  const scrollMapContainerEle = document.getElementById(scrollMapCanvasId)
  if (scrollMapContainerEle) {
    scrollMapContainerEle.innerHTML = ''
  }
}

function groupAndMaxBy(array = [], groupByKey = '', subGroupByKey = "") {
  const object = {}
  array.forEach((item) => {
    const groupByValueKey = item[groupByKey] || item[subGroupByKey]
    object[groupByValueKey] = [Math.max(...(object[groupByValueKey] || []), item.y)]
  })
  return object
}

function parseScrollMapData(data = [], pageHeight) {
  const sessionData = groupAndMaxBy(data, 'sessionId', 's')
  const totalSessions = Object.keys(sessionData).length
  const smReportData = populateReportData(initScrollReportData(pageHeight), sessionData, totalSessions)
  console.log({
    smReportData
  })
  const totalHeight = data.reduce((acc, { height }) => {
    return acc + height
  }, 0)

  const averageScreenHeight = Math.round(totalHeight / data.length)

  return {
    smReportData,
    averageFoldY: averageScreenHeight,
    totalSessions,
    sessionData
  }
}

function initHeatmapCanvas() {
  const scrollmapCanvasEle = document.createElement('canvas')
  scrollmapCanvasEle.id = scrollMapCanvasId

  scrollmapCanvasEle.width = document.body.scrollWidth
  scrollmapCanvasEle.height = document.body.scrollHeight

  scrollmapCanvasEle.style.opacity = '0.7'
  scrollmapCanvasEle.style['z-index'] = 999
  scrollmapCanvasEle.style.position = 'absolute'
  scrollmapCanvasEle.style.top = '0px'
  scrollmapCanvasEle.style.left = '0px'
  document.body.append(scrollmapCanvasEle)
}

function renderReachedPercentageTooltip(value, top) {
  let divContainer = document.getElementById(ReachedPercentageTooltipId)
  let spanContent = document.querySelector(`#${ReachedPercentageTooltipId} span`)

  if (!document.getElementById(ReachedPercentageTooltipId)) {
    divContainer = document.createElement('div')
    divContainer.id = ReachedPercentageTooltipId
    divContainer.style.width = '100%'
    divContainer.style['text-align'] = 'center'
    divContainer.style.position = 'absolute'
    divContainer.style.opacity = '0.7'
    divContainer.style.transition = 'opacity 240ms ease-in-out 0s'
    divContainer.style['z-index'] = '1000'
    divContainer.style['border-bottom'] = '1px dashed black'

    spanContent = document.createElement('span')
    spanContent.style.color = `rgb(255, 255, 255)`
    spanContent.style.background = `rgb(10, 10, 10)`
    spanContent.style['border-radius'] = '4px'
    spanContent.style.padding = '4px 8px'

    divContainer.appendChild(spanContent)
    document.body.appendChild(divContainer)
  }

  spanContent.innerHTML = `${value}% of users reached this point`
  divContainer.style.top = `${top}px`
}

function renderAvarageFoldLine(positionY = 0) {
  const divContainer = document.createElement('div')
  divContainer.id = AvarageFoldLineId
  const spanContent = document.createElement('span')
  spanContent.innerHTML = 'Average fold'
  spanContent.style.background = 'rgb(255, 255, 255)'
  spanContent.style['border-radius'] = '0px 4px 0px 0px'
  spanContent.style.color = 'rgb(10, 10, 10)'
  spanContent.style.display = 'inline-block'
  spanContent.style.padding = '4px 8px'

  divContainer.style['box-shadow'] = 'rgb(50 50 50 / 25%) 0px 3px 5px 0px'
  divContainer.style['font-size'] = '11px'
  divContainer.style['font-weight'] = 'bold'
  divContainer.style.position = 'absolute'
  divContainer.style['text-transform'] = 'uppercase'
  divContainer.style.width = '100%'
  divContainer.style['grid-row-start'] = '1'
  divContainer.style['grid-column-start'] = '1'
  divContainer.style.top = `${positionY}px`
  divContainer.style['z-index'] = `1000`
  divContainer.style['border-bottom'] = `3px white solid`

  divContainer.appendChild(spanContent)
  document.body.appendChild(divContainer)
}

function renderMileStone(value, top) {
  const tooltipHeight = 20;
  const divContainer = document.createElement('div')
  const spanContent = document.createElement('span')

  spanContent.innerHTML = `${value}%`
  spanContent.style.background = 'rgb(10, 10, 10)'
  spanContent.style['border-radius'] = '0px 4px 0px 0px'
  spanContent.style.color = 'rgb(255, 255, 255)'
  spanContent.style.display = 'inline-block'
  spanContent.style.padding = '4px 8px'

  divContainer.style['box-shadow'] = 'rgb(50 50 50 / 25%) 0px 3px 5px 0px'
  divContainer.style['font-size'] = '11px'
  divContainer.style['font-weight'] = 'bold'
  divContainer.style.position = 'absolute'
  divContainer.style['text-transform'] = 'uppercase'
  divContainer.style.width = '100%'
  divContainer.style.height = `${tooltipHeight}px`
  divContainer.style.top = `${top - tooltipHeight}px`
  divContainer.style['z-index'] = `1000`

  divContainer.appendChild(spanContent)
  document.body.appendChild(divContainer)
}

function renderScrollMap(smReportData) {
  renderScrollMapCanvas(document.getElementById(scrollMapCanvasId), smReportData)
}

function renderMilestones(smReportData) {
  const milestones = [25, 50, 75]
  milestones.forEach(milestone => {
    const reportItem = smReportData.find((smItem) => {
      return Math.round(smItem.percent_of_total) === milestone
    })
    if (reportItem) {
      renderMileStone(milestone, reportItem.pixels_scrolled - smReportData[0].pixels_scrolled)
    }
  })
}

function resetInitHeatmapScrollDOM() {
  const heatMapScrollCanvas = [document.getElementById(scrollMapCanvasId), document.getElementById(AvarageFoldLineId)]

  if (heatMapScrollCanvas.filter(i => i)?.length) {
    heatMapScrollCanvas.forEach(ele => ele?.remove?.())
  }
}

export function loadScrollMap(data) {
  const scrollMapData = parseScrollMapData(data, document.body.scrollHeight)
  console.log({
    scrollMapData
  })

  resetInitHeatmapScrollDOM()

  initHeatmapCanvas()
  renderAvarageFoldLine(scrollMapData.averageFoldY)
  renderScrollMap(scrollMapData.smReportData)
  renderMilestones(scrollMapData.smReportData)

  function handleOnMove(event) {
    const reportItem = findReportItem(scrollMapData.smReportData, event.pageY)
    const value = reportItem ? reportItem.percent_of_total : 0
    renderReachedPercentageTooltip(value > 100 ? 100 : value, event.pageY)
  }

  document.getElementById(scrollMapCanvasId).removeEventListener('mousemove', handleOnMove)
  document.getElementById(scrollMapCanvasId).addEventListener('mousemove', handleOnMove)
}
