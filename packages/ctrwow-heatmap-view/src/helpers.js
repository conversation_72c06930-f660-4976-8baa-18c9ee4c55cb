import {LAZY_LOAD_CLASS} from "./constants";
export function elementOrParentIsFixed(element) {
  const $element = $(element)
  const $checkElements = $element.add($element.parents())
  let isFixed = false
  $checkElements.each(function () {
    if ($(this).css('position') === 'fixed') {
      isFixed = true
      return false
    }
  })
  return isFixed
}

export function getParameterByName(name, url) {
  if (!url) url = window.location.href // eslint-disable-next-line no-useless-escape
  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

export function isPreviewMode() {
  return getParameterByName('ctr_sl_heatmap_preview') === '1' || getParameterByName('ctr_heatmap_preview') === '1'
}

function isCheckSrcImgOutside(link) {
  return link.indexOf("http://") === 0 || link.indexOf("https://") === 0
}

export function handleRemoveLazyLoad() {
  const windowOffsetTop = window.pageYOffset

  if(windowOffsetTop < 200) {
    const imagesOnTheTopPage = document.querySelectorAll("img")
    imagesOnTheTopPage.forEach(function(image) {
      const isLazyImage = image.getAttribute("data-ctr-islazy")
      if(isLazyImage !== null){
        image.removeAttribute("data-ctr-islazy")
        image.removeAttribute("loading")
        image.classList.remove(LAZY_LOAD_CLASS)

        const ctrLazySrc = image.getAttribute("data-ctr-lazy-src")
        const ctrSrc = image.getAttribute("src")
        if((ctrLazySrc && !ctrSrc) || (ctrLazySrc && isCheckSrcImgOutside(ctrLazySrc))){
          image.setAttribute("src", ctrLazySrc)
        }
      }
    })
  }
}

