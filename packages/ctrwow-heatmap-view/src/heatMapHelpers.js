// import { elementOrParentIsFixed } from './helpers'
import { heatMapContainerDomEleId, LIMIT_HEIGHT_HEATMAP_CANVAS, SPACING_POSITION, LAZY_LOAD_CLASS, HEATMAP_CLASSNAME, HEATMAP_CONFIG, clickMapContainerDomEleId } from './constants'
import h337 from 'heatmap.js'

const lazyLoadRegex = new RegExp(`.${LAZY_LOAD_CLASS}`, "g");
export function formatElementPath(elementPath = '') {
  if (elementPath) {
    // Remove lazy load class from element
    const formattedPath = elementPath.replace(lazyLoadRegex, '');

    // Replace the first element with 'body'
    const elements = formattedPath.split(' ');
    elements[0] = 'body';

    return elements.join(' ');
  }
}

function formatHeatmapData(heatMapData) {
  heatMapData = heatMapData.map((item) => {
    item.e = item.elementPath || item.e;
    item.w = item.width || item.w;
    item.h = item.height || item.h;
    item.c = item.totalClicks || item.c;
    return item;
  });
  return (heatMapData || [])
    .map((hd) => {
      const { c, e, x, y, w, h } = hd
      let domRecCurrent = null
      try {
        domRecCurrent = e ? document.querySelector(formatElementPath(e)) : null
      } catch (e) {
        console.warn(e)
      }

      if (!domRecCurrent) {
        return
      }

      const offset = $(domRecCurrent).offset()
      // const isFixed = elementOrParentIsFixed($(domRecCurrent))
      const isFixed = false
      const scaleWidth = domRecCurrent.offsetWidth / w
      const scaleHeight = domRecCurrent.offsetHeight / h
      const offsetX = x * scaleWidth
      const offsetY = y * scaleHeight

      function getYFixed() {
        const heatmapPreview = document.querySelector('#ctr_heatmap_preview_iframe');
        const slHeatmapPreview = document.querySelector('#ctr_sl_heatmap_preview_iframe');
        const canvasOffsetHeight = heatmapPreview?.offsetHeight || slHeatmapPreview?.offsetHeight;

        return canvasOffsetHeight - domRecCurrent.offsetHeight + offsetY
      }

      return {
        x: Math.round(offset.left + offsetX),
        y: Math.round(isFixed ? getYFixed() : offset.top + offsetY),
        value: c
      }
    })
    .filter((hd) => hd)
    .sort((a, b) => a.y - b.y);
}

function initHeatMapContainer(index, heightComponent) {
  const heatMapContainerDomEle = document.getElementById(heatMapContainerDomEleId+`${index}`) || document.createElement('div');
  heatMapContainerDomEle.id = heatMapContainerDomEleId + `${index}`;
  heatMapContainerDomEle.className = `heatmap_container`;
  heatMapContainerDomEle.style.width = `${document.body.scrollWidth}px`;
  heatMapContainerDomEle.style.height = `${heightComponent + SPACING_POSITION}px`;

  document.body.append(heatMapContainerDomEle)
  return heatMapContainerDomEle
}

function formatYPositionHeight(hmData = [], index) {
  const objectFormat = [];
  hmData.map((item) => {
    const positionY = item.y - (index * LIMIT_HEIGHT_HEATMAP_CANVAS)
    objectFormat.push({...item, y: positionY})
  })
  return objectFormat
}

function resetInitHeatmapDOM() {
  const heatMapCanvas = document.querySelectorAll(`.heatmap_container .${HEATMAP_CLASSNAME}`)
  if (heatMapCanvas?.length) {
    for (const canvas of heatMapCanvas) {
      canvas.remove()
    }
  }
}

export function loadHeatMap(heatMapData, options = {}) {
  resetInitHeatmapDOM()

  const hmData = formatHeatmapData(heatMapData);
  const heightBody = document.body.scrollHeight;
  const lengthCanvas = Math.ceil(heightBody / LIMIT_HEIGHT_HEATMAP_CANVAS)

  for(let i = 0; i<lengthCanvas; i++) {
    const heightComponent = i+1 === lengthCanvas ? document.body.scrollHeight - (i*LIMIT_HEIGHT_HEATMAP_CANVAS) : LIMIT_HEIGHT_HEATMAP_CANVAS;
    const heatMapContainerDomEle = initHeatMapContainer(i, heightComponent)
    const heatmap = h337.create({
      ...HEATMAP_CONFIG,
      ...options,
      container: heatMapContainerDomEle
    })

    const findIndexPosition = hmData.findLastIndex((item) => item.y <= (i+1)*LIMIT_HEIGHT_HEATMAP_CANVAS - SPACING_POSITION);
    const findIndexPositionCanvas = hmData.findLastIndex((item) => item.y <= (i+1)*LIMIT_HEIGHT_HEATMAP_CANVAS + SPACING_POSITION);

    const newHmData = hmData.slice(0, findIndexPositionCanvas+1)
    hmData.splice(0, findIndexPosition+1)
    const hmDataPositionY = i !==0 ? formatYPositionHeight(newHmData, i) : newHmData
    heatmap.setData({
      data: hmDataPositionY || []
    })

    const heatMapCanvas = document.querySelector(`#${heatMapContainerDomEleId}${i} .${HEATMAP_CLASSNAME}`)
    heatMapCanvas.style.opacity = 0.7
    heatMapCanvas.style['z-index'] = 999 - i;
    heatMapContainerDomEle.style['z-index'] = 9999 - i;
    heatMapContainerDomEle.style.position = 'absolute'
    heatMapContainerDomEle.style.top = i*LIMIT_HEIGHT_HEATMAP_CANVAS + `px`
    heatMapContainerDomEle.style.left = '0px'
    heatMapContainerDomEle.style.height = `${heightComponent}px`;
    heatMapContainerDomEle.style.overflow = 'hidden'
  }
}

export function handleShowHideHeatmap(isShowHeatmap) {
  const heatMapContainerEle = document.querySelector(`.heatmap_container`)
  const clickMapContainerEle = document.querySelector(`#${clickMapContainerDomEleId}`)

  heatMapContainerEle.style.visibility = isShowHeatmap ? 'visible' : 'hidden'
  clickMapContainerEle.style.visibility = isShowHeatmap ? 'visible' : 'hidden'

}