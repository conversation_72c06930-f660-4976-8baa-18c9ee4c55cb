import { getFormattedDateNow } from './utils'

export const handleExportHeatmap = () => {
  const handleOnLoaded = () => {
    // eslint-disable-next-line no-undef
    html2canvas(document.body, {
      useCORS: true
    }).then(function (canvas) {
      const link = document.createElement('a')
      link.href = canvas.toDataURL('image/jpeg').replace('image/jpeg', 'image/octet-stream')
      link.download = `export_heatmap_image_${getFormattedDateNow()}.jpeg`
      link.click()
    })
  }

  window.ctrwowUtils.getDependencies([window.ctrwowUtils.getCtrLibLink('ctrwowHtmlToImg', '3.0.0', true)]).then(handleOnLoaded)
}
