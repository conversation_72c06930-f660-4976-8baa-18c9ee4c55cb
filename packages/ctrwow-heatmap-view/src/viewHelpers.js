import { handleShowHideHeatmap, loadHeatMap } from './heatMapHelpers'
import { loadClickMap, resetClickMap } from './clickMapHelpers'
import { loadScrollMap, resetScrollMap } from './viewScrollMapHelper'
import { handleExportHeatmap } from './handleExportHeatmap'
import { handleRemoveLazyLoad } from "./helpers"
import { initObserveDOM_Heatmap } from './utils'

/* eslint-disable */
function initViewHeatMapEvents() {
  handleRemoveLazyLoad();
  window.parent.postMessage(
    {
      eventName: 'ctr_heatmap_pageloaded',
      pageWidth: document.body.offsetWidth,
      pageHeight: document.body.offsetHeight
    },
    '*'
  )
  window.addEventListener(
    'message',
    (event) => {
      const key = event.message ? 'message' : 'data'
      const dataEvent = event[key]

      if(dataEvent.eventName && dataEvent.eventName !== "collect-end") {
        resetScrollMap()
        resetClickMap()
      }

      if(dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_render_heatmap_status') {
        handleShowHideHeatmap(dataEvent.data)
      }

      if (dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_render') {
        initObserveDOM_Heatmap(() => {
          loadHeatMap(dataEvent.data, {
            radius: 15
          })
          loadClickMap(dataEvent.data)
        })
      } else if (dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_render_move_map') {
        initObserveDOM_Heatmap(() => {
          loadHeatMap(dataEvent.data, {
            radius: 90
          })
        })
      } else if (dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_render_click_map') {
        initObserveDOM_Heatmap(() => {
          loadHeatMap(dataEvent.data, {
            radius: 15
          })
          loadClickMap(dataEvent.data, 'click')
        })
      } else if (dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_render_tap_map') {
        initObserveDOM_Heatmap(() => {
          loadHeatMap(dataEvent.data, {
            radius: 15
          })
          loadClickMap(dataEvent.data, 'tap')
        })
      } else if (dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_render_scroll_map') {
        initObserveDOM_Heatmap(() => {
          loadScrollMap(dataEvent.data)
        })
      } else if (dataEvent.eventName && dataEvent.eventName === 'ctr_heatmap_export') {
        handleExportHeatmap()
      }

      window.parent.postMessage(
        {
          eventName: 'ctr_heatmap_rendered',
          pageWidth: document.body.offsetWidth,
          pageHeight: document.body.offsetHeight
        },
        '*'
      )
    },
    false
  )
}

export function init() {
  initViewHeatMapEvents()
}
