import { clickMapContainerDomEleId } from './constants'
import { formatElementPath } from './heatMapHelpers'

function createClickBox({ left, top, width, height, displayText }) {
  const box = document.createElement('div')
  const span = document.createElement('span')

  box.className = 'click-map-container'
  box.style.position = 'absolute'
  box.style.width = `${width}px`
  box.style.height = `${height}px`
  box.style.top = top + 'px'
  box.style.left = left + 'px'
  box.style.zIndex = '1000'
  box.style.background = 'rgba(0, 158, 253, 0.25)'
  box.style.border = '1px dashed rgb(0, 162, 242)'
  box.style.opacity = '0'

  span.innerHTML = `${displayText}`
  span.className = 'click-map-total-clicks'
  span.style.position = 'absolute'

  // span.style.top = top === 0 ? 'auto' : '-36px'
  // span.style.bottom = top === 0 ? '-36px' : 'auto'

  span.style.top = 'calc(100% - 30px)'
  span.style.bottom = 'auto'

  span.style.left = '-1px'

  span.style.background = 'rgb(0, 162, 242)'
  span.style.border = '1px dashed rgb(0, 162, 242)'
  span.style.color = 'rgb(255, 255, 255)'
  span.style.padding = '4px 8px'
  span.style['border-radius'] = '4px'
  span.style['font-weight'] = '600'
  span.style['font-size'] = '14px'
  span.style['letter-spacing'] = '0.1px'
  span.style['line-height'] = '20px'

  box.appendChild(span)
  return box
}

function createClickMapLayer() {
  let clickMapContainerEle = document.getElementById(clickMapContainerDomEleId)
  if (clickMapContainerEle) {
    clickMapContainerEle.innerHTML = ''
  } else {
    clickMapContainerEle = document.createElement('div')
    clickMapContainerEle.id = clickMapContainerDomEleId
  }
  clickMapContainerEle.style['z-index'] = 99999
  clickMapContainerEle.style.width = document.body.scrollWidth + `px`
  clickMapContainerEle.style.height = document.body.scrollHeight + `px`
  clickMapContainerEle.style.overflow = `hidden`
  document.body.append(clickMapContainerEle)
  return clickMapContainerEle
}

function parseClickMapData(heatMapData) {
  const groupCountClicks = {}
  heatMapData.forEach((hm) => {
    if (!groupCountClicks[`${hm.e}`]) {
      groupCountClicks[`${hm.e}`] = 0
    }
    groupCountClicks[`${hm.e}`] = groupCountClicks[`${hm.e}`] + hm.c
  })
  return groupCountClicks
}

function calcSumOfClicks(heatMapData = []) {
  return heatMapData.reduce((currentValue, item) => {
    return currentValue + item.c
  }, 0)
}

function getDisplayText(c, heatMapData, type) {
  const sumClicks = calcSumOfClicks(heatMapData)
  const percentage = (c / sumClicks) * 100
  const percentageRouned = parseFloat(percentage.toString()).toFixed(2)
  const unit = type === 'click' ? 'click' : 'tap'

  return `${c} ${unit}${c > 1 ? 's' : ''} (${percentageRouned}%)`
}

export function resetClickMap() {
  const clickMapContainerEle = document.getElementById(clickMapContainerDomEleId)
  if (clickMapContainerEle) {
    clickMapContainerEle.innerHTML = ''
  }
}
function getPosition(element) {
  const rect = element.getBoundingClientRect()
  return {
    x: rect.left + window.scrollX,
    y: rect.top + window.scrollY
  }
}

export function loadClickMap(heatMapData, type = 'click') {
  resetClickMap()
  const clickMapContainerEle = createClickMapLayer()
  const clicksMapData = parseClickMapData(heatMapData)
  const sortedKeys = Object.keys(clicksMapData).sort((key1, key2) => {
    return key1.split(' ').length - key2.split(' ').length
  })
  sortedKeys.forEach((e) => {
    const clickedElement = document.querySelector(formatElementPath(e))
    if (clickedElement) {
      const c = clicksMapData[e]
      const { offsetWidth: width, offsetHeight: height } = clickedElement
      const { x: left, y: top } = getPosition(clickedElement)

      const displayText = getDisplayText(c, heatMapData, type)
      const boxEle = createClickBox({
        left,
        top,
        width,
        height,
        displayText
      })
      clickMapContainerEle.appendChild(boxEle)
    }
  })
  clickMapContainerEle.style.left = 0
  clickMapContainerEle.style.top = 0
  clickMapContainerEle.style.position = `absolute`
}
