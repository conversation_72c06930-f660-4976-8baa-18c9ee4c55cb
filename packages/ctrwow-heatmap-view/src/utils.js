import { clickMapContainerDomEleId, HEATMAP_CLASSNAME, heatMapContainerDomEleId } from './constants'
import { AvarageFoldLineId, ReachedPercentageTooltipId, scrollMapCanvasId } from './viewScrollMapHelper'

export function formatDate(date, formatString, splitSymbol) {
  let result = ''
  const arrDate = date.split('-')

  switch (formatString) {
    case 'dd/mm/yyyy':
      result = arrDate[2] + splitSymbol + arrDate[1] + splitSymbol + arrDate[0]
      break
    case 'mm/dd/yyyy':
      result = arrDate[1] + splitSymbol + arrDate[2] + splitSymbol + arrDate[0]
      break
    default:
      result = date
  }

  return result
}

export function getFormattedDateNow() {
  return formatDate(new Date().toISOString().split('T')[0], `mm/dd/yyyy`, ``)
}

function handleOnChange_WindowEvent(cb) {
  window.addEventListener(
    'resize',
    throttle(() => {
      cb && cb()
    }, 1000)
  )

  window.addEventListener('scrollend', () => cb && cb())
}

function handleOnAnimation(ele, cb) {
  ele.addEventListener('animationend', () => cb && cb())
  ele.addEventListener('transitionend', () => cb && cb())
}

function checkEleHasAnimating(ele) {
  const style = window.getComputedStyle(ele)
  const animationDuration = parseFloat(style.animationDuration) * 1000
  const transitionDuration = parseFloat(style.transitionDuration) * 1000

  return animationDuration > 0 || transitionDuration > 0
}

function handleOnChange_Interactive(ele, cb) {
  try {
    if (checkEleHasAnimating(ele)) {
      return handleOnAnimation(ele, cb)
    }

    return handleOnChange_WindowEvent(cb)
  } catch (error) {
    console.log(`[error_handleOnChange_Interactive::]`, error)
  }
}

export function initObserveDOM(cb) {
  try {
    console.log(`[initObserveDOM::]`)
    return new MutationObserver(cb)
  } catch (error) {
    console.log(`[error_initObserveDOM::]`, error)
  }
}

export function initObserveDOM_Heatmap(cb, ele = document.body) {
  let hasObserveDOM = false

  const throttled_cb = throttle(() => {
    console.log(`[hm_updated::]`)
    cb && cb()
  }, 500)

  if (!hasObserveDOM) throttled_cb()

  try {
    const config = {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'id', 'style']
    }
    const LIST_ELE_IDS_IGNORE = [
      clickMapContainerDomEleId,
      AvarageFoldLineId,
      heatMapContainerDomEleId,
      ReachedPercentageTooltipId,
      scrollMapCanvasId
    ]
    const LIST_ELE_CLASSES_IGNORE = [HEATMAP_CLASSNAME]

    if (hasObserveDOM) return

    const throttled_observeDOMCallback = throttle(function (mutationsList) {
      for (const mutation of mutationsList) {
        if (mutation.type === 'attributes' && config.attributeFilter.includes(mutation?.attributeName)) {
          // detect ignore eles of heatmap
          if (
            LIST_ELE_IDS_IGNORE.some((i) => mutation.target.id.startsWith(i)) ||
            LIST_ELE_CLASSES_IGNORE.some((i) => mutation.target.className.startsWith(i))
          ) {
            continue
          }

          handleOnChange_Interactive(mutation.target, throttled_cb)
          throttled_cb()
        }
      }
    }, 1000)
    const domObserver = initObserveDOM(throttled_observeDOMCallback)
    domObserver.observe(ele, config)

    hasObserveDOM = true
    console.log(`[initObserveDOM_Heatmap::]`)
  } catch (error) {
    console.log(`[error_initObserveDOM_Heatmap::]`, error)
  }
}

export function throttle(cb, limit) {
  let runCb
  let last

  return function () {
    const context = this
    const args = arguments

    if (!last) {
      cb.apply(context, args)
      last = Date.now()
    } else {
      clearTimeout(runCb)

      runCb = setTimeout(function () {
        if (Date.now() - last >= limit) {
          cb.apply(context, args)
          last = Date.now()
        }
      }, limit - (Date.now() - last))
    }
  }
}
