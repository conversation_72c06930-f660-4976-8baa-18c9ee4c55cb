export function changeThumbnailSrcsets(imgSrc) {
  const sourceSumary = window._qAll('.order-summary__item .blockname source')
  // Remove source from picture tag.
  if (sourceSumary.length > 0) {
    sourceSumary.forEach((item) => {
      item.remove()
    })
  }

  const imageSummarys = window._qAll('.order-summary__item .blockname img')
  Array.prototype.slice.call(imageSummarys).forEach((imageSummary) => {
    imageSummary.src = imgSrc
    // Replace srcsets
    const imgDatasets = imageSummary.dataset
    for (const attr in imgDatasets) {
      imageSummary.dataset[attr] = imgSrc
    }
  })
}

export function renderThumbnailToSummary() {
  try {
    const productList = window._qAll('.js-list .js-list-item')
    Array.prototype.slice.call(productList).forEach((item) => {
      if (parseInt(item.getAttribute('data-id')) === window.ctrwowCheckout.checkoutData.getProduct().productId) {
        const productImgElm = item.querySelector('.product-img') || item.querySelector('.list-item__thumb img')
        if (productImgElm) {
          changeThumbnailSrcsets(
            productImgElm.getAttribute('data-ctr-lazy-src') ? productImgElm.getAttribute('data-ctr-lazy-src') : productImgElm.src
          )
        }
      }
    })
  } catch (e) {
    console.log(e)
  }
}
