import { adjustShippingOrder } from 'shared-29next/src/helper/adjustShippingOrder'

function getTotalMiniUpsell() {
  let totalMiniUpsell = 0
  const miniUpsellArr = window.ctrwowCheckout.checkoutData.getMiniUpsell() || []
  if (miniUpsellArr.length > 0) {
    for (const miniItem of miniUpsellArr) {
      if (!Object.prototype.hasOwnProperty.call(miniItem, 'addToSummary') || miniItem.addToSummary) {
        totalMiniUpsell += miniItem && miniItem.price ? miniItem.price : 0
      }
    }
  }

  return totalMiniUpsell
}

export function formatData(props) {
  window.shippingIndex = 0
  let warrantyPrice = 0
  const lw_product = window.ctrwowCheckout.checkoutData.getLifetimeWarrantyConfig()
  if (lw_product && lw_product.productPrices) {
    warrantyPrice = lw_product.productPrices.DiscountedPrice.Value
  }
  let data = window.ctrwowCheckout.checkoutData.getProduct() || {}
  try {
    data = adjustShippingOrder(data)
    const totalMiniUpsell = getTotalMiniUpsell()

    let totalDiscountValue = data.productPrices.FullRetailPrice.Value - data.productPrices.DiscountedPrice.Value
    if (totalDiscountValue < 0) {
      totalDiscountValue = 0
    }

    data.discountPercent = data.productPrices.Discount && data.productPrices.Discount.percent ? data.productPrices.Discount.percent : 0
    data.fullRetailPrice = data.productPrices.FullRetailPrice.FormattedValue
    data.discountPrice = data.productPrices.DiscountedPrice.FormattedValue

    const shippingFee = data.shippings[window.shippingIndex] ? data.shippings[window.shippingIndex].price : 0
    data.shipping = window.ctrwowUtils.number.formaterNumberByFormattedValue(shippingFee, data.discountPrice)

    const totalDiscountPrice = window.ctrwowUtils.number.formaterNumberByFormattedValue(totalDiscountValue, data.discountPrice)
    data.totalDiscountPrice = '-' + totalDiscountPrice
    if (props['not-use-minus']) {
      data.totalDiscountPrice = totalDiscountPrice
    }

    const subTotal = data.productPrices.DiscountedPrice.Value + totalMiniUpsell + warrantyPrice
    data.subTotal = window.ctrwowUtils.number.formaterNumberByFormattedValue(subTotal, data.discountPrice)

    data.grandTotal = window.ctrwowUtils.number.formaterNumberByFormattedValue(subTotal + shippingFee, data.discountPrice)

    data.ctrwow__warrantyPrice = window.ctrwowUtils.number.formaterNumberByFormattedValue(warrantyPrice, data.discountPrice)
    // data.ctrwow__funnelId = funnelId
  } catch (e) {
    console.log(e)
  }

  return data
}
