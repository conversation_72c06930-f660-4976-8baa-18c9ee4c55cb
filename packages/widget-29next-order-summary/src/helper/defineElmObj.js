export const elmObj = {
  jsDiscountPrice: window._qAll('.jsDiscountPrice'),
  jsDiscountPriceTotal: window._qAll('.jsDiscountPriceTotal'),
  jsDiscountPercent: window._qAll('.jsDiscountPercent'),
  jsShippingPrice: window._qAll('.jsShippingPrice'),
  jsSubTotal: window._qAll('.jsSubTotal'),
  jsGrandTotal: window._qAll('.jsGrandTotal'),
  jsQuantity: window._qAll('.js-product-qty')
}

export const updateFields = {
  productName: '.js-product-name',
  fe_quantity: '.js-fe-product-qty',
  quantity: '.js-product-qty',
  packageQuantity: '.js-package-qty',
  discountPrice: '.js-product-price',
  shipping: '.js-shipping-price',
  totalDiscountPrice: '.js-discount-price-total',
  grandTotal: '.js-grand-total',
  subTotal: '.js-sub-total',
  discountPercent: '.js-discount-percent',
  ctrwow__warrantyPrice: '.js-lifetime-warranty',
  fullRetailPrice: '.js-fullretail-price'
}
