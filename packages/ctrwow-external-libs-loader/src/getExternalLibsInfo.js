export const getExternalLibsInfo = (externalLinks) => {
  return externalLinks.reduce(
    ({ filePaths, htmlScripts, packageInfo }, { packageName, fileName, packagePath }) => ({
      filePaths: [...filePaths, packagePath],
      htmlScripts: `${htmlScripts}<script src="${packagePath}" defer></script>`,
      packageInfo: { ...packageInfo, [packageName]: fileName }
    }),
    { filePaths: [], htmlScripts: '', packageInfo: {} }
  )
}
