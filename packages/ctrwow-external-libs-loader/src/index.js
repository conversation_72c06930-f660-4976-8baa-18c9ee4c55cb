import { loadCtrPackageVersions } from 'shared-external-libs-loader/src/loadCtrPackageVersions'
import { getCtrLibLinkWithPackageInfo as getCtrLibLinkWithPackageInfoShared } from 'shared-external-libs-loader/src/getCtrLibLink'

console.log('loadCtrPackageVersionsloadCtrPackageVersionsloadCtrPackageVersions')
loadCtrPackageVersions()

export { default as getDependencies } from 'shared-external-libs-loader/src/getDependencies'

export { getExternalLibsInfo } from './getExternalLibsInfo'

export const getCtrLibLinkWithPackageInfo = getCtrLibLinkWithPackageInfoShared

export const getCtrCommonLibLink = (publishedVersion, packageName, defaultVersion, isGetAbsolutePath) => {
  isGetAbsolutePath === undefined && defaultVersion === true && (isGetAbsolutePath = true)
  console.log(`[getCtrCommonLibLink] - ${packageName} - ${publishedVersion} - ${isGetAbsolutePath}`)
  return getCtrLibLinkWithPackageInfo(packageName, publishedVersion || defaultVersion, isGetAbsolutePath)
}
