export function adjustShippingOrder(data) {
  if (!data || !data.shippings || (data.shippings && data.shippings.length <= 1)) {
    window.shippingIndex = 0
    return data
  }

  // ? Sort Shipping Fee. from free -> fee: a-b
  data.shippings.sort((a, b) => {
    return a.price - b.price
  })

  // ! Check rm param => update shippingIndex
  // ? If rm=1 => Free ship
  // ? else => Have Shipping Fee
  const rmParam = window.ctrwowUtils.link.queryURLParameter('rm')
  if (rmParam === '1' || window.isFreeShip) {
    window.shippingIndex = 0
  } else {
    window.shippingIndex = data.shippings.length - 1
  }

  data.shippings.forEach((shipping, index) => {
    if (shipping.isExpress === window.isExpressShipping && typeof window.isExpressShipping === 'boolean') {
      window.shippingIndex = index
    }
  })
  window.shippingIndex = typeof window.multipleShippingIndex === 'number' ? window.multipleShippingIndex : window.shippingIndex
  return data
}
