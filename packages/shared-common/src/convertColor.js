export function RGBToRGBA(rgb) {
  return rgb.replace(/rgb/i, 'rgba').replace(/\)/i, ',1)')
}

export function hexToRGBA(hex) {
  let c
  if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
    c = hex.substring(1).split('')
    if (c.length === 3) {
      c = [c[0], c[0], c[1], c[1], c[2], c[2]]
    }
    c = '0x' + c.join('')
    return 'rgba(' + [(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',') + ',1)'
  }
}

export function convertRGBAToARGB(rgba) {
  let a
  const rgb = rgba.replace(/\s/g, '').match(/^rgba?\((\d+),(\d+),(\d+),?([^,\s)]+)?/i)
  const alpha = ((rgb && rgb[4]) || '').trim()
  let argb = rgb
    ? (rgb[1] | (1 << 8)).toString(16).slice(1) + (rgb[2] | (1 << 8)).toString(16).slice(1) + (rgb[3] | (1 << 8)).toString(16).slice(1)
    : rgba

  if (alpha !== '') {
    a = alpha
  } else {
    a = 1
  }

  // multiply before convert to argb
  a = ((a * 255) | (1 << 8)).toString(16).slice(1)
  argb = argb + a

  return argb
}
