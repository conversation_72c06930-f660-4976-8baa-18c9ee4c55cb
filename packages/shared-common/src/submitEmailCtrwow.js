function submitEmailToSubscription(payload = {}, subscriptionId = '') {
  // Optional payload isFromEmailWidget: true, firstName: '', lastName: '', phoneNumber: ''
  const endpoint =
    window.ctrwowUtils && window.ctrwowUtils.isProd()
      ? `https://ctrwow-prod-emaillistspublicapi-microservice.azurewebsites.net/api/email-lists/${subscriptionId}/subscribe`
      : `https://ctrwow-dev-emaillistspublicapi-microservice.azurewebsites.net/api/email-lists/${subscriptionId}/subscribe`

  const headers = {
    'content-type': 'application/json'
  }

  return window.ctrwowUtils.callAjax(endpoint, {
    method: 'POST',
    body: JSON.stringify(payload),
    headers: headers
  })
}

export { submitEmailToSubscription }
