export async function getAjax(url, settings) {
  try {
    let headersOptions = {}
    if (settings && settings['content-type']) {
      headersOptions = {
        'content-type': settings['content-type']
      }
    }
    const res = await fetch(url, {
      method: 'GET',
      headers: settings && settings.headers ? Object.assign(headersOptions, settings.headers) : headersOptions
    })
    if (res.ok) {
      try {
        const jsonData = await res.json()
        return jsonData
      } catch (err) {
        return Promise.resolve('Get ajax successfully')
      }
    } else {
      return Promise.reject(`Error code : ${res.status} - ${res.statusText}`)
    }
  } catch (err) {
    return err
  }
}

export async function postAjax(url, settings) {
  try {
    let headersOptions = {}
    if (settings && settings['content-type']) {
      headersOptions = {
        'content-type': settings['content-type']
      }
    }
    const res = await fetch(url, {
      method: 'POST',
      headers: settings && settings.headers ? Object.assign(headersOptions, settings.headers) : headersOptions,
      body: settings && settings.data && JSON.stringify(settings.data)
    })
    if (res.ok) {
      try {
        const jsonData = await res.json()
        return jsonData
      } catch (err) {
        return Promise.resolve('Post ajax successfully')
      }
    } else {
      return Promise.reject(`Error code : ${res.status} - ${res.statusText}`)
    }
  } catch (err) {
    return Promise.reject(err)
  }
}

export async function fetchUrlsParallel(objs) {
  const results = await Promise.all(
    objs.map((obj) => {
      if (obj.postData) {
        return postAjax(obj.url, obj.postData)
      }
      return getAjax(obj.url)
    })
  )
  const validResults = results.filter((result) => !(result instanceof Error))
  return validResults
}
