import { isVisibleElm } from './isVisibleElm'
export function errorHandler(form) {
  const firstError = form.querySelector('.error')
  firstError && firstError.scrollIntoView({ block: 'center' })
}
export function getFormData(form) {
  const formData = {}
  const inputs = $(form).find('input, textarea, select')
  if (inputs.length) {
    for (let i = 0; i < inputs.length; i++) {
      const value = $(inputs[i]).val()
      const type = $(inputs[i]).attr('type')
      if (type === 'radio' && !$(inputs[i]).prop('checked')) {
        break
      }
      if (value && inputs[i].name !== '') {
        formData[inputs[i].name] = value
      }
    }
  }
  return formData
}
export function submitHandler(forms) {
  let submitData = {}
  for (let i = 0; i < forms.length; i++) {
    const validator = $(forms[i]).validate()
    if (!isVisibleElm(forms[i])) {
      continue
    }
    const isValid = validator && validator.form()
    if (isValid && submitData) {
      const asyncValidator = window.ctrwowUtils.form.asyncValidateForm()
      if (!asyncValidator.isValid) {
        submitData = null
        alert(asyncValidator.message)
      } else {
        submitData[$(forms[i]).attr('name')] = getFormData(forms[i])
      }
    } else {
      submitData = null
      errorHandler(forms[i])
    }
  }
  return submitData || false
}
function errorMsg(formElm) {
  const messages = {}
  const inputs = formElm.find('input, textarea, select')
  if (inputs.length) {
    for (let i = 0; i < inputs.length; i++) {
      messages[inputs[i].name] = {}
      const requiredText = $(inputs[i]).attr('required-text')
      const regexText = $(inputs[i]).attr('regex-text')
      const messageRule = $(inputs[i]).attr('message-rule-language')
      requiredText && (messages[inputs[i].name].required = requiredText)
      regexText && (messages[inputs[i].name].regex = regexText)

      if (messageRule) {
        messages[inputs[i].name].remote = messageRule
        messages[inputs[i].name].email = messageRule
        messages[inputs[i].name].url = messageRule
        messages[inputs[i].name].date = messageRule
        messages[inputs[i].name].dateISO = messageRule
        messages[inputs[i].name].number = messageRule
        messages[inputs[i].name].digits = messageRule
        messages[inputs[i].name].equalTo = messageRule
        messages[inputs[i].name].accept = messageRule
        messages[inputs[i].name].maxlength = messageRule
        messages[inputs[i].name].minlength = messageRule
        messages[inputs[i].name].rangelength = messageRule
        messages[inputs[i].name].range = messageRule
        messages[inputs[i].name].max = messageRule
        messages[inputs[i].name].min = messageRule
        messages[inputs[i].name].postcode = messageRule
        messages[inputs[i].name].creditcard = messageRule
        if (inputs[i].name === 'creditcard' || inputs[i].name === 'expirydate') {
          messages[inputs[i].name].text = messageRule
        }
      }
    }
  }
}
export function initValidation(formElm) {
  const messages = errorMsg(formElm)

  formElm.validate({
    messages: messages,
    highlight: function (element) {
      $(element).addClass('error')
    },
    unhighlight: function (element) {
      $(element).removeClass('error')

      let labelErrorId = $(element).attr('id')
      if ($(element).is(':radio')) {
        labelErrorId = $(element).attr('name')
      }
      formElm.find(`[id="${labelErrorId}-error"]`).remove()
    },
    errorPlacement: function (error, element) {
      if ($(element).is(':radio')) {
        const radioName = $(element).attr('name')
        const lastRadio = formElm.find(`[name="${radioName}"]`).last().closest('.form-group')
        error.insertAfter(lastRadio)
      } else {
        error.appendTo(element.closest('.form-group'))
      }
    }
  })
}
