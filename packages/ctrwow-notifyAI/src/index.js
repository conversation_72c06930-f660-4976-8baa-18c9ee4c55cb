import './style.scss'

export const genNotifyAIScript = function ({ partnerID, attr, siteId }) {
  const script = `<script ${attr}> (function(document, window) {
      var script = document.createElement("script");
      script.type = "text/javascript";
      script.setAttribute("${attr}", "");
      script.src = "https://trk-consulatu.com/scripts/push/script/${partnerID}?url=" + "${siteId}" + "&alturl=" + encodeURI(self.location.pathname);
      script.onload = function() {
        push_init();
        if(!localStorage.getItem("notifyAI" + self.location.hostname + "${siteId}") && document.querySelector('#notifyai-popover-container')){
          document.querySelector('#notifyai-popover-container').style.display = "block"
        }
      };
      document.getElementsByTagName("head")[0].appendChild(script);
      })(document, window); </script>`

  return script
}

export const genDisableNotifyAIScript = ({ siteId }) => {
  const script = `<script type="text/javascript">
    localStorage.removeItem("notifyAI" + self.location.hostname + "${siteId}");
    var notifyAISWRegistedScope = location.origin + "/" + location.pathname.split('/')[1] + "/"
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(function(registrations) {
        for(let registration of registrations) {
          if(notifyAISWRegistedScope === registration.scope) {
            registration.unregister()
          }
        } 
      })
    }
  </script>`

  return script
}

export const optInNotifyAI = ({ siteId }) => {
  const ID_NOTIFYAI_POPUP = `notifyai-popover-container`
  const scriptSWhandler = `<script type="text/javascript">
    var notifyAIPopupContainerEl = document.querySelector('#${ID_NOTIFYAI_POPUP}')
    var keyLocalNotifyAI = "notifyAI" + self.location.hostname + "${siteId}"

    function handleNotifyAIOK () {
      notifyAIPopupContainerEl.style.display="none"
      if(!localStorage.getItem(keyLocalNotifyAI)){
        localStorage.setItem(keyLocalNotifyAI, "1");
      }
    }

    function handleNotifyAICanceled () {
      notifyAIPopupContainerEl.style.display="none"
      localStorage.setItem(keyLocalNotifyAI, "0");
    }
  </script>`

  return `
    <div id="${ID_NOTIFYAI_POPUP}" class="notifyai-popover-container notifyai-reset slide-down" style="display: none;">
      <div id="notifyai-popover-dialog" class="notifyai-popover-dialog">
      <div id="normal-popover">
      <div class="popover-body">
      <div class="popover-body-icon"><img alt="notification icon" class="" src="https://dev.your-push-site.com/pre-dialog/notify-A.png"></div>
      <div class="popover-body-message">We'd like to show you notifications for the latest news and updates.</div>
      <div class="clearfix"></div>
      </div>
      <div class="popover-footer"><a onclick="push_subscribe()"><button id="notifyai-popover-allow-button" class="align-right primary popover-button" onclick="handleNotifyAIOK()">Allow</button></a><button id="notifyai-popover-cancel-button" class="align-right secondary popover-button" onclick="handleNotifyAICanceled()">No Thanks</button>
      <div class="clearfix"></div>
      </div>
      </div>
      </div>
    </div>
    ${scriptSWhandler}
    `
}
