export const API_END_POINT_URL = (isApplySplitTest) => isApplySplitTest ? `${process.env.HEATMAP_API_ENDPOINT_URL_HM}` : `${process.env.API_ENDPOINT_URL_HM}`;
export const apiEndpointTracking = (isApplySplitTest) => isApplySplitTest ? `${API_END_POINT_URL(isApplySplitTest)}/heatmaps` : `${API_END_POINT_URL(isApplySplitTest)}/heatmap?v=2`;
export const userSessionKey = '__ctr_hm_ssid'
export const key_handleLimitClick = 'limit_maxClicks'

export const __CTR_HM_SETTINGS = {
  maxClicks: 3,
  ...(window.__CTR_HM_SETTINGS || {})
}

export const HEATMAP_DEVICE_TYPE = Object.freeze({
  mobile: 1,
  tablet: 2,
  desktop: 3,
})

export const ACTION_TYPE = {
  Clicks: 1,
  Move: 2,
  Scroll: 3
}
export const SPLIT_TEST_PARAM_ID = 'ctr_spl_id'
