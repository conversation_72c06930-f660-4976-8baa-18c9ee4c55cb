/* eslint-disable */
import { __CTR_HM_SETTINGS, ACTION_TYPE, userSessionKey } from './constants'
import {
  checkPointer_Click,
  checkSessionClickWithExpired,
  create_UUID,
  elementOrParentIsFixed,
  getCountClickWithSsId,
  getDeviceTypeHM,
  getDomPath,
  getFunnelId,
  getFunnelNodeId,
  getSessionId,
  getSplitTestId,
  getTotalClicks,
  initDependencies,
  isElementPathValid,
  sessionStorage,
  setCountClickWithSsId,
  getSliderSessionId,
  isSlickSlider
} from './helpers'
import { checkCanTrack, trackBeacon, trackXMLRequest } from './apiHelpers'

function checkSessionCanTrack(type = ACTION_TYPE.Clicks) {
  const totalClicks = sessionStorage().getItem(getSessionId()) || 0

  if (type === ACTION_TYPE.Clicks) {
    return totalClicks < __CTR_HM_SETTINGS.maxClicks
  } else if (type === ACTION_TYPE.Move) {
    return totalClicks < __CTR_HM_SETTINGS.maxMove || 1000
  }
  return true
}

function trackClick({ x, y, elementPath, width, height, slideId }, isApplySplitTest) {
  const payload = [
    {
      x,
      y,
      elementPath,
      width,
      height,
      actionType: ACTION_TYPE.Clicks,
      deviceType: getDeviceTypeHM(),
      funnelId: getFunnelId(),
      nodeId: getFunnelNodeId(),
      totalClicks: getTotalClicks(),
      sessionId: slideId ? getSliderSessionId(slideId) : getSessionId()
    }
  ]
  if (navigator.sendBeacon) {
    trackBeacon(payload, isApplySplitTest)
  } else {
    trackXMLRequest(payload, isApplySplitTest)
  }
  !slideId && (setCountClickWithSsId(isApplySplitTest))
}

function trackMove(payloads, isApplySplitTest) {
  if (navigator.sendBeacon) {
    trackBeacon(payloads, isApplySplitTest)
  } else {
    trackXMLRequest(payloads, isApplySplitTest)
  }
  const movesKey = `${getSessionId()}_moves`
  const totalMoves = sessionStorage().getItem(movesKey) || 0
  sessionStorage().setItem(movesKey, Number(totalMoves) + payloads.length)
}

function trackScroll(payloads, isApplySplitTest) {
  const movesKey = `${getSessionId()}${isApplySplitTest ? '_splitTest' : ""}_scrolls`
  const currentScrollY = sessionStorage().getItem(movesKey) || -1

  const filterMaxPayloads = payloads.filter((p) => {
    return p.y > Number(currentScrollY)
  })
  if (filterMaxPayloads.length) {
    if (navigator.sendBeacon) {
      trackBeacon(filterMaxPayloads, isApplySplitTest)
    } else {
      trackXMLRequest(filterMaxPayloads, isApplySplitTest)
    }
    const maxY = Math.max(...filterMaxPayloads.map((p) => p.y), currentScrollY)
    sessionStorage().setItem(movesKey, `${maxY}`)
  }
}

function initSessionId() {
  if (!getSessionId()) {
    sessionStorage().setItem(userSessionKey, `ctr_hm_ssid_${create_UUID()}`)
  }
}

function checkHandleLimitClicks(isApplySplitTest) {
  let totalClicks = getCountClickWithSsId(isApplySplitTest)
  totalClicks = Number(totalClicks)

  return totalClicks < __CTR_HM_SETTINGS.maxClicks
}

let isClickTrackingInitialized = false;
function initClickEvents(isApplySplitTest) {
  if (isClickTrackingInitialized) return; // Prevent double initialization
  isClickTrackingInitialized = true;

  function handleTrack(payload) {
    const { elementPath } = payload

    if (!checkSessionCanTrack() || !isElementPathValid(elementPath) || isSlickSlider(elementPath) || !checkHandleLimitClicks(isApplySplitTest)) {
      return
    }

    trackClick(payload, isApplySplitTest)
  }

  $(document).on('click', (e) => {
    if (checkPointer_Click(e)) {
      const elementPath = getDomPath(e.target)
      const isFixed = elementOrParentIsFixed($(e.target))
      handleTrack({
        x: e.offsetX,
        y: e.offsetY,
        elementPath,
        totalClicks: 1,
        width: e.target.offsetWidth,
        height: e.target.offsetHeight,
        isFixed
      })
    }

  })

  $(document).on('touchstart', (e) => {
    const bcr = e.target.getBoundingClientRect()
    const x = e.targetTouches[0].clientX - bcr.x
    const y = e.targetTouches[0].clientY - bcr.y
    const elementPath = getDomPath(e.target)
    const isFixed = elementOrParentIsFixed($(e.target))
    handleTrack({
      x: Math.round(x),
      y: Math.round(y),
      elementPath,
      totalClicks: 1,
      width: e.target.offsetWidth,
      height: e.target.offsetHeight,
      isFixed
    })
  })
}

function initSliderClickTracking() {
  document.querySelectorAll('[data-cslider]').forEach((slider) => {
    $(slider).on('click', '.slick-slide', (event) => {
      const sessionKey = `${getSessionId()}_sliderClicks`;
      const sliderClickCounts = JSON.parse(sessionStorage().getItem(sessionKey) || '{}');
      const slide = event.currentTarget;
      const slideId = slide.id || slide.dataset.slideId;

      if (!slideId) return;

      const currentCount = sliderClickCounts[slideId] || 0;

      if (currentCount < 3) {
        sliderClickCounts[slideId] = currentCount + 1;

        const elementPath = getDomPath(slide);
        const isFixed = elementOrParentIsFixed($(slide));

        trackClick(
          {
            x: event.offsetX,
            y: event.offsetY,
            elementPath,
            width: slide.offsetWidth,
            height: slide.offsetHeight,
            totalClicks: currentCount + 1,
            isFixed,
            slideId
          },
          false
        );

        // Save updated counts to session storage
        sessionStorage().setItem(sessionKey, JSON.stringify(sliderClickCounts));
      }
    });
  });
}

function initClickTracking(isApplySplitTest) {
  if (checkSessionCanTrack(ACTION_TYPE.Clicks)) {
    checkCanTrack({
      funnelId: getFunnelId(),
      nodeId: getFunnelNodeId(),
      actionType: ACTION_TYPE.Clicks,
      sessionId: getSessionId(),
      isApplySplitTest
    }).then((canTrack) => {
      if (canTrack) {
        initClickEvents(isApplySplitTest)
      }
    })
  }
}

function initMoveEvents(isApplySplitTest) {
  function addToQue(item, isApplySplitTest) {
    if (isApplySplitTest) {
      window.__ctr_track_items_split_test ??= [];
      window.__ctr_track_items_split_test.push(item);
    } else {
      window.__ctr_track_items ??= [];
      window.__ctr_track_items.push(item);
    }
  }


  function consumeQue() {
    if (window.__ctr_track_items?.length) {
      trackMove(window.__ctr_track_items, false);
      window.__ctr_track_items = [];
    }

    if (window.__ctr_track_items_split_test?.length) {
      trackMove(window.__ctr_track_items_split_test, true);
      window.__ctr_track_items_split_test = [];
    }
  }

  function initInterval() {
    window._ctrHeatMapTrackMove = setInterval(() => consumeQue(), 1000)
  }

  initInterval()

  $(document).on('mouseover', (e) => {
    const elementPath = getDomPath(e.target)
    const isFixed = elementOrParentIsFixed($(e.target))

    if (isElementPathValid(elementPath)) {
      addToQue({
        x: e.offsetX,
        y: e.offsetY,
        elementPath,
        totalClicks: 1,
        width: e.target.offsetWidth,
        height: e.target.offsetHeight,
        isFixed,
        actionType: ACTION_TYPE.Move,
        deviceType: getDeviceTypeHM(),
        funnelId: getFunnelId(),
        nodeId: getFunnelNodeId(),
        sessionId: getSessionId()
        },
        isApplySplitTest
      )
    }
  })
}

function initMoveTracking(isApplySplitTest) {
  if (checkSessionCanTrack(ACTION_TYPE.Move)) {
    checkCanTrack({
      funnelId: getFunnelId(),
      nodeId: getFunnelNodeId(),
      actionType: ACTION_TYPE.Move,
      sessionId: getSessionId(),
      isApplySplitTest
    }).then((canTrack) => {
      if (canTrack) {
        initMoveEvents(isApplySplitTest)
      }
    })
  }
}

function initScrollEvents(isApplySplitTest) {
  function onScrollEnd(callback, timeout, isApplySplitTest) {
    $(document).on("scroll", function () {
      const scrollTimeoutKey = isApplySplitTest ? "_ctrScrollTimeoutSp" : "_ctrScrollTimeout";

      if (window[scrollTimeoutKey]) {
        clearTimeout(window[scrollTimeoutKey]);
      }

      window[scrollTimeoutKey] = setTimeout(callback, timeout);
    });
  }

  onScrollEnd(function () {
    trackScroll([
      {
        x: Math.round(window.scrollX),
        y: Math.round(window.scrollY + window.innerHeight),
        elementPath: 'body',
        totalClicks: 1,
        width: window.innerWidth,
        height: window.innerHeight,
        isFixed: false,
        actionType: ACTION_TYPE.Scroll,
        deviceType: getDeviceTypeHM(),
        funnelId: getFunnelId(),
        nodeId: getFunnelNodeId(),
        sessionId: getSessionId()
      }
    ], isApplySplitTest)
  }, 200, isApplySplitTest)
}

function initScrollTracking(isApplySplitTest) {
  if (checkSessionCanTrack(ACTION_TYPE.Scroll)) {
    checkCanTrack({
      funnelId: getFunnelId(),
      nodeId: getFunnelNodeId(),
      actionType: ACTION_TYPE.Scroll,
      sessionId: getSessionId(),
      isApplySplitTest
    }).then((canTrack) => {
      if (canTrack) {
        initScrollEvents(isApplySplitTest)
      }
    })
  }
}

function initTimestampLocal(isApplySplitTest) {
  checkSessionClickWithExpired(() => {
    sessionStorage().setItem(userSessionKey, `ctr_hm_ssid_${create_UUID()}`)
    initClickTracking(isApplySplitTest)
  })
}

function initEvents(isApplySplitTest) {
  initSessionId()
  initClickTracking(isApplySplitTest)
  initMoveTracking(isApplySplitTest)
  initScrollTracking(isApplySplitTest)
  initTimestampLocal(isApplySplitTest)
  initSliderClickTracking(isApplySplitTest)
}

function initSplitTestTracking() {
  const splitTestId = getSplitTestId()
  if(splitTestId) {
    initEvents(true)
  }
}

export function init() {
  if (__CTR_HM_SETTINGS.isExternal) {
    initDependencies(() => {
      initEvents()
      initSplitTestTracking()
    });
  } else {
    initEvents();
    initSplitTestTracking()
  }
}
