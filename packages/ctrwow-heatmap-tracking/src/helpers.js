import device from 'current-device'
import { HEATMAP_DEVICE_TYPE, userSessionKey, SPLIT_TEST_PARAM_ID } from './constants'
export function initDependencies(callback) {
  try {
    const s = document.createElement('script')
    s.setAttribute('src', 'https://cdn.wowsuite.ai/ctrwow/common/js/jquery-3.4.1.custom.min.js')
    s.setAttribute('async', '')
    s.setAttribute('integrity', 'sha384-vk5WoKIaW/vJyUAd9n/wmopsmNhiy+L2Z+SBxGYnUkunIxVxAv/UtMOhba/xskxh')
    s.setAttribute('crossorigin', 'anonymous')
    document.head.appendChild(s)
    s.onload = function () {
      callback && callback()
    }
  } catch (e) {
    console.warn('injectDependencies error: ' + e)
  }
}

export function elementOrParentIsFixed(element) {
  const $element = $(element)
  const $checkElements = $element.add($element.parents())
  let isFixed = false
  $checkElements.each(function () {
    if ($(this).css('position') === 'fixed') {
      isFixed = true
      return false
    }
  })
  return isFixed
}

export function checkPointer_Click(e) {
  // jquery originalEvent
  return e?.originalEvent?.pointerId === 1
}

export function getDomPath(ele) {
  const domPaths = []
  let elm
  let entry
  for (elm = ele; elm; elm = elm.parentNode) {
    entry = elm.tagName.toLowerCase()
    if (entry === 'html') {
      break
    }
    if (elm?.id && typeof elm.id === `string` && elm.id.trim() !== ``) {
      entry += '#' + elm.id
    }
    if (elm.className) { 
      entry += '.' + elm.className.replace(/ /g, '.');

      // remove if iamge has ctr-lazy-image or b-loaded class.
      entry = entry.replace(/.ctr-lazy-image|.b-loaded/g, '').trim();
    }
    domPaths.push(entry)
  }
  domPaths.reverse()
  return domPaths.join(' ')
}

export function create_UUID() {
  let dt = new Date().getTime()
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (dt + Math.random() * 16) % 16 | 0
    dt = Math.floor(dt / 16)
    // eslint-disable-next-line eqeqeq
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
  return uuid
}

export function getParameterByName(name, url) {
  if (!url) url = window.location.href // eslint-disable-next-line no-useless-escape

  name = name.replace(/[\[\]]/g, '\\$&')
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

export function getFunnelId() {
  return getParameterByName('ctr_fid')
}

export function getFunnelNodeId() {
  return getParameterByName('ctr_fnid')
}

export function getSiteId() {
  if (window.__CTR_HM_SETTINGS && window.__CTR_HM_SETTINGS.siteId) {
    return window.__CTR_HM_SETTINGS.siteId
  } else if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.siteId) {
    return window.__CTRWOW_CONFIG.siteId
  }
  return null
}

export function getPageId() {
  if (window.__CTR_HM_SETTINGS && window.__CTR_HM_SETTINGS.pageId) {
    return window.__CTR_HM_SETTINGS.pageId
  } else if (window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.pageId) {
    return window.__CTRWOW_CONFIG.pageId
  }
  return null
}

export function getDeviceTypeHM() {
  if (device.desktop()) {
    return HEATMAP_DEVICE_TYPE.desktop
  }
  if (device.mobile()) {
    return HEATMAP_DEVICE_TYPE.mobile
  }
  if (device.tablet()) {
    return HEATMAP_DEVICE_TYPE.tablet
  }
}

export function getTotalClicks() {
  return 1
}

export function checkSessionClickWithExpired(cb, expired = 3 * 60 * 1000) {
  const currentTimestamp = new Date().getTime()
  const timestampLocal = localStorage.getItem('timestamp')

  if (timestampLocal && Number(currentTimestamp) - Number(timestampLocal) > expired) {
    // eslint-disable-next-line standard/no-callback-literal
    cb && cb()
  }

  setInterval(function () {
    // eslint-disable-next-line standard/no-callback-literal
    cb && cb()
  }, expired)

  setInterval(function () {
    localStorage.setItem('timestamp', new Date().getTime())
  }, 1000)
}

export function sessionStorage() {
  return window.sessionStorage
}

export function getSessionId() {
  return sessionStorage().getItem(userSessionKey)
}

export function getSliderSessionId(slideId) {
  return sessionStorage().getItem(userSessionKey) + `_${slideId}`
}

export function isElementPathValid(elementPath) {
  try {
    return document.querySelector(elementPath)
  } catch (e) {
    return false
  }
}

export function isSlickSlider(element) {
  return !!$(element).closest('.slick-slider').length
}

export const getCountClickWithSsId = (isApplySplitTest) => {
  const clicksKey = `${getSessionId()}${isApplySplitTest ? '_splitTest' : ''}_clicks`
  const totalClicks = sessionStorage().getItem(clicksKey) || 0

  return Number(totalClicks)
}

export const setCountClickWithSsId = (isApplySplitTest) => {
  const clicksKey = `${getSessionId()}${isApplySplitTest ? '_splitTest' : ''}_clicks`
  const totalClicks = sessionStorage().getItem(clicksKey) || 0
  sessionStorage().setItem(clicksKey, Number(totalClicks) + 1)
}

export function isPreviewMode() {
  return getParameterByName('ctr_sl_heatmap_preview') === '1' || getParameterByName('ctr_heatmap_preview') === '1'
}

export function getSplitTestId() {
  return window.__CTR_SPL_TRACKING_SETTINGS ? window.__CTR_SPL_TRACKING_SETTINGS.SPL_ID : getParameterByName(SPLIT_TEST_PARAM_ID)
}
