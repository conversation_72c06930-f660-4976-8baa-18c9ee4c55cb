import { API_END_POINT_URL, apiEndpointTracking } from './constants'
import { getDeviceTypeHM, getPageId, getSiteId, getSplitTestId } from './helpers'

export function checkCanTrack({ funnelId, nodeId, actionType, sessionId, isApplySplitTest }) {
  if (funnelId && nodeId && window.__CTR_HM_SETTINGS && window.__CTR_HM_SETTINGS.isFunnelHeatmapTrackingApplied) {
    return fetch(
      `${API_END_POINT_URL(isApplySplitTest)}/canTrack?sessionId=${sessionId}&funnelId=${funnelId}&nodeId=${nodeId}&actionType=${actionType}&deviceType=${getDeviceTypeHM()}&v=2`,
      {
        method: 'GET',
        'content-type': 'application/json'
      }
    ).then((rs) => rs.json());
  }

  if (isApplySplitTest) {
    return checkFpCanTrack({ splitTestId: getSplitTestId(), actionType }, isApplySplitTest);
  }

  if (getSiteId() && getPageId()) {
    return checkSiteCanTrack({ siteId: getSiteId(), pageId: getPageId(), actionType, sessionId }, isApplySplitTest);
  }

  return Promise.resolve(false);
}

export function checkSiteCanTrack({ siteId, pageId, actionType, sessionId }, isApplySplitTest) {
  const { isHeatmapTrackingApplied } = window.__CTR_HM_SETTINGS || {};
  if (isHeatmapTrackingApplied) {
    const url = `${API_END_POINT_URL(isApplySplitTest)}/canTrack?sessionId=${sessionId}&siteId=${siteId}&pageId=${pageId}&actionType=${actionType}&deviceType=${getDeviceTypeHM()}&v=2`;
    const options = {
      method: 'GET',
      'content-type': 'application/json'
    };
    return fetch(url, options).then((rs) => rs.json());
  }
  return Promise.resolve(false);
}


export function checkFpCanTrack({ splitTestId, actionType }, isApplySplitTest) {
  const url = `${API_END_POINT_URL(isApplySplitTest)}/canTrack?splitTestId=${splitTestId}&actionType=${actionType}&deviceType=${getDeviceTypeHM()}`;
  const options = {
    method: 'GET',
    'content-type': 'application/json'
  };
  return fetch(url, options).then((rs) => rs.json());
}

function populateSiteIdAndPageId(payload = []) {
  return payload.map((p) => {
    return {
      ...p,
      siteId: getSiteId(),
      pageId: getPageId(),
      url: window.location.href,
      splitTestId: getSplitTestId() || undefined
    }
  })
}

export function trackBeacon(payload = [], isApplySplitTest) {
  navigator.sendBeacon(apiEndpointTracking(isApplySplitTest), JSON.stringify(populateSiteIdAndPageId(payload)))
}

export function trackXMLRequest(payload = [], isApplySplitTest) {
  fetch(apiEndpointTracking(isApplySplitTest), {
    method: 'POST',
    'content-type': 'application/json',
    body: JSON.stringify(populateSiteIdAndPageId(payload))
  }).then((rs) => rs.json())
}
