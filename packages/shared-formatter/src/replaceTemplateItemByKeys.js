const parseResponsiveImgSrc = (imgSource) => {
  let imgSrc = imgSource
  if (typeof imgSource === 'string' || imgSource instanceof String) {
    return imgSource
  } else if (Array.isArray(imgSource) && imgSource.length > 0) {
    // get first item
    imgSrc = imgSource[0]
  }

  if (imgSrc && (imgSrc.desktop || imgSrc.tablet || imgSrc.mobile)) {
    return 'imgResponsive' + [imgSrc.mobile || '', imgSrc.tablet || '', imgSrc.desktop || ''].join('__x1x__imgSrc__x1x__')
  }
  return imgSource
}

const parseValue = (value, placeholder) => {
  if (placeholder.indexOf('imgSrc::') > -1) {
    return parseResponsiveImgSrc(value)
  }
  return value
}

export const replaceTemplateItemByKeys = (itemDetails) => (textContent) => {
  const placeholders = decodeURI(textContent).match(/{([a-zA-Z0-9|\.|\[|\]:]+)}/gm) || []

  return placeholders.reduce((acc, placeholder) => {
    const path = getPathFromPlaceholder(placeholder)
    const value = getValue(itemDetails, path)
    return value !== undefined ? acc.replaceAll(placeholder, parseValue(value, placeholder, path)) : acc
  }, decodeURI(textContent))

  function getPathFromPlaceholder(placeholder) {
    const path = placeholder.substring(1, placeholder.length - 1).split('::')
    return path[path.length - 1]
  }

  function getValue(details, path) {
    const parts = path
      .replaceAll(/\[|\]|'|"/g, '.')
      .replaceAll(/\.+/g, '.')
      .split('.')
      .filter((i) => i.length)

    let index = -1

    let re = details

    try {
      while (++index < parts.length) {
        if (parts[index] in re) {
          re = re[parts[index]]
        } else {
          re = ''
          break
        }
      }
    } catch (e) {
      console.log(e)
    }
    return re || ''
  }
}
