function formatDate(date, formatString, splitSymbol) {
  let result = ''
  const arrDate = date.split('-')

  switch (formatString) {
    case 'dd/mm/yyyy':
      result = arrDate[2] + splitSymbol + arrDate[1] + splitSymbol + arrDate[0]
      break
    case 'mm/dd/yyyy':
      result = arrDate[1] + splitSymbol + arrDate[2] + splitSymbol + arrDate[0]
      break
    default:
      result = date
  }

  return result
}

export default function getFormattedDateNow() {
  return formatDate(new Date().toISOString().split('T')[0])
}
