/**
 *
 * @param node
 * @param fnGetNewTextContent
 * @returns {null}
 */
const replaceNodeContentWithNewContent = (node, fnGetNewTextContent) => {
  const textContent = node.textContent
  const newTextContent = textContent ? fnGetNewTextContent(textContent) : null
  return newTextContent !== textContent ? newTextContent : null
}

/**
 * Replace inner HTML
 * @param node
 * @param newHtmlContent
 * @returns {*}
 */
const updateHtml = (node, newHtmlContent) => node && newHtmlContent && (node.innerHTML = newHtmlContent)

/***
 * Replace [templateItem] with real data
 * @param elm                   DomElement
 * @param fnGetNewTextContent   Function
 * @param attributes            Array of String
 */
export function updateElementContentWithPlaceholder(elm, fnGetNewTextContent, attributes = []) {
  // 1.populate data to attributes (if any)
  attributes.forEach((attrKey) => {
    const currentValue = elm.getAttribute(attrKey)
    const newVal = currentValue && fnGetNewTextContent(currentValue)
    newVal && elm.setAttribute(attrKey, newVal)
  })

  // 2.populate data to node content
  const isTextNode = (node) => node && node.nodeType === 3
  const childNodes = elm.childNodes
  const numChildNodes = elm.childNodes.length

  // 2.1 [elm] is a "textNode" or dont have any children >> replace its innerContent
  if (numChildNodes === 1 && isTextNode(childNodes[0])) {
    const newContent = replaceNodeContentWithNewContent(elm, fnGetNewTextContent)
    updateHtml(elm, newContent)
    return
  }

  // 2.1 [elm] have mixed children: textNode(nodeType=3) & element(nodeType!=3)
  childNodes.forEach((node) => {
    if (!isTextNode(node)) {
      // node is not TextNode >> do nothing
      return true
    }

    const newContent = replaceNodeContentWithNewContent(node, fnGetNewTextContent)
    if (newContent) {
      const newNode = document.createTextNode(newContent)
      elm.replaceChild(newNode, node)
    }
  })
}

export function populateDataToNodeContent({ fnGetNewTextContent, attributes, parentWrapper }) {
  const { _qAll } = window
  const allElements = parentWrapper ? parentWrapper.querySelectorAll('*') : _qAll('body *')
  for (const elem of allElements) {
    updateElementContentWithPlaceholder(elem, fnGetNewTextContent, attributes)
  }
}
