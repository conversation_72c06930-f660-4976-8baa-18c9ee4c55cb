export const updateTemplateSourceValue = (mainElement) => {
  mainElement.querySelectorAll('img[src-template]').forEach((imgElm) => {
    // clear value-config from builder
    imgElm.setAttribute('src', '')
    imgElm.setAttribute('data-src', '')
    imgElm.setAttribute('data-lazy-srcsets', '')
    imgElm.setAttribute('data-srcsets', '')

    // update value of src-template to native-attribute
    const parsedImageSrc = imgElm.getAttribute('src-template')
    const responsiveSrc = parseImgSrcResponsive(parsedImageSrc)
    if (responsiveSrc) {
      imgElm.setAttribute('data-srcsets', responsiveSrc)
    } else {
      imgElm.setAttribute('src', parsedImageSrc)
    }
  })

  function parseImgSrcResponsive(imgSrcStr) {
    if (imgSrcStr.indexOf('imgResponsive') < 0) {
      return
    }

    let [prefix, imgSrcs] = imgSrcStr.split('imgResponsive')

    if (!imgSrcs || imgSrcs.length < 1) {
      imgSrcs = prefix
      prefix = ''
    }

    const getFullPath = (val) => (val && val.length > 3 ? prefix + val : '')

    const [mobile, tablet, desktop] = imgSrcs.split('__x1x__imgSrc__x1x__')
    return `<767:${getFullPath(mobile)},<1023:${getFullPath(tablet)},>1023:${getFullPath(desktop)}`
  }

  setTimeout(() => window.dispatchEvent(new Event('resize')), 100)
}
