export default function convertCurrency(formatPrice) {
  const utils = window.ctrwowUtils
  const { _qAll } = window

  const jsCurrencyCode = utils.localStorage().get('jsCurrency')
  const currencyElm = _qAll('.jsCurrencyNumber')

  if (jsCurrencyCode) {
    try {
      for (const item of currencyElm) {
        item.innerText = jsCurrencyCode.replace('######', item.textContent)
      }
    } catch (err) {
      console.log(err)
    }
  } else if (formatPrice) {
    try {
      for (const item of currencyElm) {
        const number = window.ctrwowUtils.number.formaterNumberByFormattedValue(Number(item.textContent), formatPrice)
        item.textContent = number
      }
    } catch (err) {
      console.log(err)
    }
  }
}
