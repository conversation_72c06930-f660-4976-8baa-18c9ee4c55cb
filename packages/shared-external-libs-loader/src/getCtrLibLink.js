import CONFIGURABLE_CONSTANTS from './configurable.constants'
import { isLiveMode } from './viewMode'
import { getPackageInfo } from './externalList.domainData'

const getCustomPackageConfig = (packageName) => {
  let versions = window.__ctrwowPackageVersion
  if (!versions) {
    return
  }

  versions = versions.split(';')
  const path = versions.find((item) => item.indexOf(packageName) > -1)
  return !path ? null : path.indexOf('http') === 0 ? path : `${CONFIGURABLE_CONSTANTS.JS_ROOT__COMMON_STORAGE_SRC}/${path}`
}

const getFileName = (packageName, packageVersion) => {
  // version for specific code - can be defined in custom-code
  let fileName = (window.__ctrExtenalPackageVersions || {})[packageName]
  if (fileName) {
    return fileName
  }

  // version at page-level - for published page
  fileName = getPackageInfo(packageName)
  if (fileName) {
    return fileName
  }

  // version at page-level - for page rendered in builder
  fileName = (getCtrPackageVersions() || {})[packageName]
  if (fileName) {
    return fileName
  }

  return `${packageName}-v${packageVersion}.${CONFIGURABLE_CONSTANTS.ENV === 'dev' ? 'dev' : 'min'}.js`

  function getCtrPackageVersions() {
    try {
      return JSON.parse(localStorage.getItem(CONFIGURABLE_CONSTANTS.CTR_EXTERNAL_PACKAGE_VERSIONS))
    } catch (e) {
      return {}
    }
  }
}

const getCtrLibLinkBase = (packageName, defaultVersion = '1.0.0', isGetAbsolutePath = false) => {
  console.log(`>>>>>>>>>>>>>>>>isGetAbsolutePath: ${packageName} -  ${isGetAbsolutePath}`)

  const BASE_VERSION = (window && window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.publishedVersion) || defaultVersion || '1.10.0'

  const customPackagePath = getCustomPackageConfig(packageName)

  if (customPackagePath) {
    return { packageName, packagePath: `${customPackagePath}?ref=${BASE_VERSION}` }
  }

  const fileName = getFileName(packageName, defaultVersion)
  const rootPath = CONFIGURABLE_CONSTANTS[!isLiveMode() || isGetAbsolutePath ? 'JS_ROOT__COMMON_STORAGE_SRC' : 'JS_ROOT__RELATIVE_PATH']

  return {
    packageName,
    fileName,
    packagePath: `${rootPath}/${fileName}?ref=${BASE_VERSION}`
  }
}

export default function getCtrLibLink(...args) {
  const { packagePath } = getCtrLibLinkBase(...args)
  return packagePath
}

export const getCtrLibLinkWithPackageInfo = getCtrLibLinkBase
