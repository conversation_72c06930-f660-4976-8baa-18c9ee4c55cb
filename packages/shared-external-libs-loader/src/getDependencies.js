import CONFIGURABLE_CONSTANTS from './configurable.constants'

const hasEmanageSrc = arr => arr.indexOf('https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js') > -1



/*
 prevent window.PubSub re-init many times and clear up messages which have been added
 */
const checkPubSubVersion = (arr) => {
  if (!window.ctrDevDebugger__UtilsTesting) {
    return arr
  }
  return arr.filter((arr) => !(arr.indexOf('pubsub-js/1.7.0/pubsub.min.js') > 0 && window.PubSub))
}

const reMapJQueryValidation = (arr) => {
  if (!arr) return null
  const newURL = []
  const currentURL = location.href.toLocaleLowerCase()
  arr.forEach((aLink) => {
    if (aLink.indexOf('cdn.jsdelivr.net/npm/jquery-validation@1.19.0/dist/jquery.validate.min.js') > -1) {
      aLink = 'https://cdn.wowsuite.ai/ctrwow/common/js/jquery.validate.min.js'
    }

    if (
      aLink.indexOf('https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js') > -1 &&
      (currentURL.indexOf('www.getuvbrite.com') > -1 ||
        currentURL.indexOf('www.zenfluffsleep.com') > -1 ||
        currentURL.indexOf('www.getchargecard.com') > -1 ||
        currentURL.indexOf('www.nomoresnoringnow.com') > -1 ||
        currentURL.indexOf('www.thermowearpro.com') > -1 ||
        currentURL.indexOf('www.dreamheromouthguard.com') > -1)
    ) {
      aLink = 'https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.1.min.js'
    }

    newURL.push(aLink)
  })

  return newURL
}

/**
 * Load source of array path
 * @param arr
 * @param dataType
 * @param cache
 * @returns {Promise<unknown[]>|Promise<void>}
 */
function getArrSrc(arr, { dataType = 'script', cache = true } = {}) {
  if (CONFIGURABLE_CONSTANTS.ENV === 'dev' && hasEmanageSrc(arr)) {
    const crmIndex = arr.indexOf('https://d3kdyumdtq5rp8.cloudfront.net/emanagecrmjs.1.0.min.js')
    arr[crmIndex] = 'https://cdn.wowsuite.ai/ctrwow/public-assets/emanagecrmjs.1.0.min.temp-change-api.js'
  }
  arr = checkPubSubVersion(arr)
  arr = reMapJQueryValidation(arr)

  if (!arr || !arr.length) {
    return Promise.resolve()
  }

  return Promise.all(
    arr.map((path) =>
      $.ajax({
        url: path,
        dataType,
        cache,
        global: false,
        crossDomain: true
      })
    )
  )
}

function loadJquery() {
  if (window.jQuery && window.jQuery.ctr__SlimVersion) {
    return new Promise(resolve => {
      var script = document.createElement('script')
      script.src = 'https://cdn.wowsuite.ai/ctrwow/common/js/jquery-3.4.1-no-ajax.min.js'
      script.async = true
      script.onload = () => resolve(true)
      document.body.appendChild(script)
    })
  }
  return Promise.resolve(true)
}

export default function getDependencies(arr, { delayTime, delayUntilInteract = true, isUsedJquery, ...options } = {}) {
  arr = Array.isArray(arr) ? arr : [arr]
  if (!window.ctrDevDebugger__UtilsTesting) {
    return getArrSrc(arr, options)
  }

  const downloadAll = () => {
    if (isUsedJquery) {
      return loadJquery().then(() => getArrSrc(arr, options))
    }
    return getArrSrc(arr, options)
  }

  if (!delayUntilInteract || (window.screenY > 100) || window.__ctrHasUserInteraction || hasEmanageSrc(arr)) {
    return downloadAll()
  }

  delayTime = delayTime || window.ctrDevDebugger__UtilsTesting__delayTimeDependencies || 1000*7

  return new Promise((resolve, reject) => {
    try {
      let isExecuted = false
      $(document).one('touchstart click scroll', function() {
        if (!isExecuted) {
          isExecuted = true
          resolve(downloadAll())
        }
      })

      setTimeout(() => {
        if (!isExecuted) {
          isExecuted = true
          resolve(downloadAll())
        }
      }, delayTime)
    } catch (e) {
      reject(e)
    }
  })
}

// recommit
function watchUserInteraction() {
  try {
    ['touchstart', 'click', 'scroll'].forEach((eventName) => {
      window.addEventListener(eventName,
        (_) => {
        window.__ctrHasUserInteraction = true
      }, {once: true})
    })
  } catch (e) {
    console.log('[getDependencies::watchUserInteract] watchUserInteract', e)
  }
}

// DEV-NOTES: always run when script is loaded
watchUserInteraction()
