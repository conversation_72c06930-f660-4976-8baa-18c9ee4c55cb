const CTR_EXTERNAL_PACKAGE_VERSIONS = 'CTR_EXTERNAL_PACKAGE_VERSIONS'

export const setPackageInfo = (packageInfo) => {
  try {
    localStorage.setItem(CTR_EXTERNAL_PACKAGE_VERSIONS, JSON.stringify(packageInfo))
  } catch (e) {
    console.log(e)
    window.___CTR_EXTERNAL_PACKAGE_VERSIONS = packageInfo
  }
}

export const getPackageInfo = (packageName) => {
  try {
    return (JSON.parse(localStorage.getItem(CTR_EXTERNAL_PACKAGE_VERSIONS) || localStorage[CTR_EXTERNAL_PACKAGE_VERSIONS]) || [])[packageName]
  } catch (e) {
    console.log(e)
    return window.___CTR_EXTERNAL_PACKAGE_VERSIONS ? window.___CTR_EXTERNAL_PACKAGE_VERSIONS[packageName] : undefined
  }
}
