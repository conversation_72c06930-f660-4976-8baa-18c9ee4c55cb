export function isBuilderMode() {
  return !!document.querySelector('[data-gjs-type="wrapper"]')
}

export function isPreviewMode() {
  try {
    return inIframe() && window.parent && window.parent.location.pathname.startsWith('/preview')
  } catch(e) {
    return false
  }
}

export const isLiveMode = () => !isBuilderMode() && !isPreviewMode()

export function inIframe() {
  try {
    return window.self !== window.parent
  } catch (e) {
    return true
  }
}

const getGlobalLoadingElm = () => document.querySelector('.loading-wrapper')

export function showGlobalLoading(isRemovedOpacity = false) {
  try {
    const elm = getGlobalLoadingElm()
    elm.style.display = 'block'
    isRemovedOpacity && (elm.style.opacity = 1)
  } catch (e) {
    console.log(e)
  }
}

export function hideGlobalLoading(isRemovedOpacity) {
  try {
    const elm = getGlobalLoadingElm(isRemovedOpacity)
    elm.style.display = 'none'
    isRemovedOpacity && (elm.style.opacity = null)
  } catch (e) {
    console.log(e)
  }
}

const isDevMode = () => window.__CTRWOW_CONFIG && window.__CTRWOW_CONFIG.ENV === 'development'
export const isProd = () => !isDevMode()
