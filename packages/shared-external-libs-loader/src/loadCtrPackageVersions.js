import CONFIGURABLE_CONSTANTS from './configurable.constants'
import getDependencies from './getDependencies'
import { setPackageInfo } from './externalList.domainData'

export const loadCtrPackageVersions = () =>
  getDependencies(
    window.__ctrCommonLibVersionPath ||
      // `${CONFIGURABLE_CONSTANTS.JS_ROOT__COMMON_STORAGE_CDN.replace('/common/js', '/common')}/short-lifetime/ctrLibVersion.${CONFIGURABLE_CONSTANTS.ENV === 'dev' ? 'dev' : 'prod'}.json`,
      `${CONFIGURABLE_CONSTANTS.JS_ROOT__COMMON_STORAGE_CDN.replace('/common/packages', '/common')}/short-lifetime/ctrLibVersion.manual.json`,
    {
      dataType: 'json',
      cache: true
    }
  ).then(([response]) => setPackageInfo(response))
