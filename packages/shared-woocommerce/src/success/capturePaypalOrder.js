const authorToken = `Basic QVVMNm5OaUJSZUkxV3VFV1pZSmRpVlJWT3dWdTFacE1xbnlWYnkyU0RieHdOYWJCVFRDOUJoZ3hPRkVHYTlZRE1JSW5GdmphNXBiVUVFNmg6RUE5Mk8wWHN1R3otWU5CMjZsNmd6Z094V2VBTGhHZEo4NjZOMzBiaUxXQ1hSZmxobXNjVXVZWFo0M1RkOFQ4T18tNmtnQUVDYXpBQkZPQWc=`
const PAYPAL_API_DOMAIN = 'https://api-m.sandbox.paypal.com/v2'

function extractBillingAddress(purchase_units) {
  const { shipping } = purchase_units[0]
  const { address } = shipping

  return {
    address_1: address?.address_line_1 || '',
    address_2: address?.address_line_2 || '',
    city: address?.admin_area_2 || '',
    state: address?.admin_area_1 || '',
    country: address?.country_code || '',
    postcode: address?.postal_code || ''
  }
}
function getHeaderPayload() {
  return {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: authorToken
    }
  }
}

export async function capturePaypalOrder(orderID) {
  let data
  const PAYPAL_ORDER_API = `${PAYPAL_API_DOMAIN}/checkout/orders/${orderID}/capture`
  const payload = getHeaderPayload()
  const result = await fetch(PAYPAL_ORDER_API, payload).then((response) => response.json())

  if (result.status === 'COMPLETED') {
    const { payer, purchase_units } = result
    data = {
      id: result.id,
      status: result.status,
      email: payer?.email_address,
      firstName: payer?.name?.given_name,
      lastName: payer?.name?.surname,
      ...extractBillingAddress(purchase_units)
    }
    return data
  } else {
    throw new Error(result)
  }
}
