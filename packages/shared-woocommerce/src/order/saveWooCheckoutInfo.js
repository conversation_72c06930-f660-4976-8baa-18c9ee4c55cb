import { setOrderInfo } from './orderInfo'

export function saveWooCheckoutInfo(orderResponse, productSelected) {
  const _localStorage = window.localStorage

  // Update total product quantity if checkout multiple product
  const totalProductQty = Number(window.ctrwowUtils.localStorage().get('totalProductQty'))
  if (orderResponse.token) {
    window.sessionStorage.setItem('orderToken', orderResponse.token)
  }

  /**
   * totalPriceMiniUpsell => using for fireGtmEventForUpsell
   */

  var orderInfo = {
    orderParams: window.ctrwowUtils.link.getCustomPathName().substr(1),
    upsells: orderResponse.upsells,
    upsellIndex: window.upsellIndex || 0,
    countryCode: orderResponse.shipping.country,
    campaignName: _localStorage.getItem('mainCampaignName') || '',
    orderNumber: orderResponse.orderNumber,
    cusEmail: orderResponse?.billing?.email || '',
    cardId: orderResponse?.cardId || '',
    orderTotal: orderResponse.total,
    formattedNumber: productSelected.productPrices.DiscountedPrice.FormattedValue,
    orderTotalFull: window.localStorage.getItem('conventionTrackingPrice'),
    savedTotal: productSelected.productPrices.FullRetailPrice.Value - productSelected.productPrices.DiscountedPrice.Value,
    quantity: totalProductQty || productSelected.quantity,
    confirmUrl: orderResponse.confirmUrl,
    successUrl: orderResponse.successUrl,
    declineUrl: orderResponse.declineUrl,
    currencyCode: window.currencyCode
  }
  try {
    orderInfo = {
      ...orderInfo,
      cusPhone: orderResponse?.billing?.phone || '',
      cusFirstName: orderResponse?.billing?.first_name || '',
      cusLastName: orderResponse?.billing?.last_name || '',
      cusCity: orderResponse?.shipping?.city || '',
      cusState: orderResponse?.shipping?.state || '',
      cusCountry: orderResponse?.shipping?.country || '',
      cusZip: orderResponse?.shipping?.postcode || ''
    }
  } catch (e) {
    console.log(e)
  }

  setOrderInfo(orderInfo)
}
