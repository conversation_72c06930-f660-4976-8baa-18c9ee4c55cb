import { connectWooCommerce } from '../helper'

export function updateOrderData(orderNumber, additionalInfo = {}) {
  const wooCommerce = connectWooCommerce()

  return new Promise((resolve, reject) => {
    wooCommerce
      .put(`orders/${orderNumber}`, additionalInfo)
      .then((response) => {
        console.log('completed updating order data', response.data)
        resolve(response.data)
      })
      .catch((error) => {
        console.log(error.response.data)
        reject(error.response.data)
      })
  })
}