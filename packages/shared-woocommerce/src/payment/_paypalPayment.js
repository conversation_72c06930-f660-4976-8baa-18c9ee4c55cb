// import { processOrder } from 'shared-woocommerce/src/payment/processOrder'
// function processPaypalOrder(elm, paymentInfo) {
//   window.ctrwowCheckout.payment
//     .checkoutWithThirdPartySticky(elm, paymentInfo)
//     .catch(() => (elm.querySelector('.paymentStripeProccessing').style.display = 'none'))
// }
// export function initPaypalPayment(elm) {
//   processOrder(elm, paymentInfo, processPaypalOrder.bind(this, elm, paymentInfo))
// }
