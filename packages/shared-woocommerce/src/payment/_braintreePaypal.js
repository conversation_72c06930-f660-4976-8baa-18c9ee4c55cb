// import { processOrder } from 'shared-29next/src/payment/processOrder'
// function processBraintreePaypalOrderSticky(elm, paymentInfo) {
//   window.ctrwowCheckout.payment
//     .checkoutWithThirdPartySticky(elm, paymentInfo)
//     .catch(() => (elm.querySelector('.paymentStripeProccessing').style.display = 'none'))
// }
// export function initBraintreePaypal(elm) {
//   // Click =>
//   processOrder(elm, paymentInfo, processBraintreePaypalOrderSticky.bind(this, elm, paymentInfo))
// }
