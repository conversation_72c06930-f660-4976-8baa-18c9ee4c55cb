import { convertToFormUrlEncode } from './convertToFormUrlEncode'

// const publishKey = 'pk_test_51MyszgKXb4mtKKOuu6ez2YkWYvtg6hkjCswEmLuSfmGCQXm3XML3GExZHTplEoGSwa8gG3OWaDZrTw9s85laTI4I00SUtZd52e'
const secretKey = 'sk_test_51MyszgKXb4mtKKOufOzl1itMqn6Uynjsb9v5hNGxhSvOS2Q5hDEBLSI8qBzczl68DOUDez8zQw3BLElcnOAlK3iB00SDFRtwfQ'
const STRIPE_API_DOMAIN = 'https://api.stripe.com'
const createProductAPI = `${STRIPE_API_DOMAIN}/v1/products`
const createSessionAPI = `${STRIPE_API_DOMAIN}/v1/checkout/sessions`
const createCardTokenAPI = `${STRIPE_API_DOMAIN}/v1/tokens`

function createStripeProduct({ name, price }, currency) {
  const payload = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Bearer ${secretKey}`
    },
    body: convertToFormUrlEncode({
      name: name,
      default_price_data: {
        unit_amount_decimal: price * 100, // cent is currency unit
        currency: currency.toLowerCase()
      }
    })
  }
  return new Promise((resolve, reject) => {
    window.ctrwowUtils
      .callAjax(createProductAPI, payload)
      .then((response) => {
        resolve(response.default_price)
      })
      .catch((err) => {
        reject(err)
        console.warn(err)
      })
  })
}

export async function createStripeSessionCheckout({ line_items, upsells, billing, currency }) {
  const productPriceID = await createStripeProduct(line_items[0], currency)

  return new Promise((resolve, reject) => {
    const payload = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Bearer ${secretKey}`
      },
      body: convertToFormUrlEncode({
        success_url: upsells.length > 0 ? upsells[0].url : 'https://testwoo.azdigi.shop/success',
        cancel_url: 'https://testwoo.azdigi.shop/cancel',
        line_items: [{ price: productPriceID, quantity: line_items[0].quantity }],
        customer_email: billing.email,
        mode: 'payment'
      })
    }

    window.ctrwowUtils
      .callAjax(createSessionAPI, payload)
      .then((response) => {
        resolve(response.id)
      })
      .catch((err) => {
        reject(err)
        console.warn(err)
      })
  })
}

export function createStripeCardToken(cardInfo) {
  return new Promise((resolve, reject) => {
    const payload = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Bearer ${secretKey}`
      },
      body: convertToFormUrlEncode({
        card: cardInfo
      })
    }

    window.ctrwowUtils
      .callAjax(createCardTokenAPI, payload)
      .then((response) => {
        resolve(response.id)
      })
      .catch((err) => {
        reject(err)
        console.warn(err)
      })
  })
}