import { convertToFormUrlEncode } from './convertToFormUrlEncode'

const secretKey = 'sk_test_51MyszgKXb4mtKKOufOzl1itMqn6Uynjsb9v5hNGxhSvOS2Q5hDEBLSI8qBzczl68DOUDez8zQw3BLElcnOAlK3iB00SDFRtwfQ'
const STRIPE_API_DOMAIN = 'https://api.stripe.com'
const createCardTokenAPI = `${STRIPE_API_DOMAIN}/v1/tokens`
const stripeChargeAPI = `${STRIPE_API_DOMAIN}/v1/charges`
const stripeCustomerAPI = `${STRIPE_API_DOMAIN}/v1/customers`

const getCustomerStripeId = () => window.localStorage.getItem('customerStripeId')
const setCustomerStripeId = (id) => window.localStorage.setItem('customerStripeId', id)

function getHeaderPayloadStripe(bodyPayload) {
  return {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Bearer ${secretKey}`
    },
    body: convertToFormUrlEncode(bodyPayload)
  }
}

function createCardTokenStripe(cardInfo) {
  return new Promise((resolve, reject) => {
    const bodyReq = { card: cardInfo }
    const payload = getHeaderPayloadStripe(bodyReq)

    window.ctrwowUtils
      .callAjax(createCardTokenAPI, payload)
      .then((response) => {
        resolve(response.id)
      })
      .catch((err) => {
        reject(err)
        console.warn(err)
      })
  })
}

async function createCustomerStripe(description, cardId) {
  const bodyReq = {
    description,
    source: cardId
  }
  const payload = getHeaderPayloadStripe(bodyReq)

  const response = await fetch(stripeCustomerAPI, payload).then((response) => response.json())
  return response?.id
}

export async function createStripeCharge(amount, currency, cardInfo, chargeDescription) {
  let customerStripeId
  if (cardInfo?.number) {
    const cardToken = await createCardTokenStripe(cardInfo)
    customerStripeId = await createCustomerStripe(chargeDescription.customerInfo, cardToken)
    setCustomerStripeId(customerStripeId)
  } else {
    customerStripeId = getCustomerStripeId()
  }

  const bodyReq = {
    amount: Number(amount) * 100, // cent is currency unit
    currency: currency.toLowerCase(),
    customer: customerStripeId,
    description: chargeDescription.orderNumber
  }
  const payload = getHeaderPayloadStripe(bodyReq)
  const result = fetch(stripeChargeAPI, payload).then((response) => response.json())

  return result
}
