import { generateCurrencyNumber } from './generateCurrencyNumber'

export function generatePriceObj(product, fe_quantity) {
  const discountedPrice = Number(product.price)
  const fullPrice = Number(product.regular_price)
  const savePrice = Number((fullPrice - discountedPrice).toFixed(2))
  const unitPrice = Number((discountedPrice / fe_quantity).toFixed(2))
  const divBox = document.createElement('div')

  divBox.innerHTML = product.price_html
  const priceFormatElement = divBox.querySelectorAll('.woocommerce-Price-amount')
  const fullPriceFormat = priceFormatElement[0].textContent
  const discountPriceFormat = priceFormatElement[1]?.textContent || fullPriceFormat

  return {
    productPrices: {
      DiscountedPrice: {
        FormattedValue: discountPriceFormat,
        Value: discountedPrice,
        GlobalCurrencyCode: window.currencyCode || ''
      },
      SavePrice: {
        FormattedValue: generateCurrencyNumber(fullPriceFormat, savePrice),
        Value: savePrice,
        GlobalCurrencyCode: window.currencyCode || ''
      },
      FullRetailPrice: {
        FormattedValue: fullPriceFormat,
        Value: fullPrice,
        GlobalCurrencyCode: window.currencyCode || ''
      },
      UnitDiscountRate: {
        FormattedValue: generateCurrencyNumber(fullPriceFormat, unitPrice),
        Value: unitPrice,
        GlobalCurrencyCode: window.currencyCode || ''
      }
    }
  }
}
