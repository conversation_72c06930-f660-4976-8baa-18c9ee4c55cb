// import { connectWooCommerce } from "./wooCommerceModule"
export function generateShippingProfiles(product, shippingMethods) {
  const divBox = document.createElement('div')
  divBox.innerHTML = product.price_html
  const priceFormatElement = divBox.querySelector('.woocommerce-Price-amount')
  const priceFormat = priceFormatElement.textContent

  const shippings = shippingMethods.map((item) => {
    const costNumber = Number(item.settings?.cost?.value || 0)
    return {
      methodId: item.method_id,
      price: costNumber,
      formattedPrice: window.ctrwowUtils.number.formaterNumberByFormattedValue(costNumber, priceFormat),
      shippingMethodId: item.id,
      title: item.title
    }
  })

  const sortResult = shippings.sort((a, b) => b.price - a.price)
  return sortResult
}
