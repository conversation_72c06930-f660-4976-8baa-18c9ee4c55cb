export async function getAjax(url, xid, clientId) {
  try {
    const res = await fetch(url, {
      method: 'GET',
      headers: {
        clientId: clientId,
        clientSecret: window.atob(xid)
      }
    })
    if (res.ok) {
      try {
        const jsonData = await res.json()
        return jsonData
      } catch (err) {
        return Promise.resolve('Get ajax successfully')
      }
    } else {
      return Promise.reject(`Error code : ${res.status} - ${res.statusText}`)
    }
  } catch (err) {
    return err
  }
}

export async function postAjax(url, data, xid, clientId) {
  try {
    const res = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        clientId: clientId,
        clientSecret: window.atob(xid)
      },
      body: JSON.stringify(data)
    })
    if (res.ok) {
      try {
        const jsonData = await res.json()
        return jsonData
      } catch (err) {
        return Promise.resolve('Post ajax successfully')
      }
    } else {
      return Promise.reject(`Error code : ${res.status} - ${res.statusText}`)
    }
  } catch (err) {
    return Promise.reject(err)
  }
}

export async function fetchUrlsParallel(objs, xid, clientId) {
  const results = await Promise.all(
    objs.map((obj) => {
      if (obj.postData) {
        return postAjax(obj.url, obj.postData, xid, clientId)
      }
      return getAjax(obj.url, xid, clientId)
    })
  )
  const validResults = results.filter((result) => !(result instanceof Error))
  return validResults
}
