export function generateSubDomainAndKey(<PERSON>uild<PERSON>, model) {
  try {
    const config = Builder.getConfig().data.settings
    console.log(999999)
    console.log(config.i29Next.subDomain, config.i29Next.token)
    model.addAttributes({
      _29NextDomain: config.i29Next.subDomain || 'ctrwow',
      _29NextXid: config.i29Next.token || window.btoa('aa0403b1b05cc562bc5f7f3067ae8bf70c84d7e3')
    })
  } catch (e) {
    alert('Please Setup Sub Domain, 29Next API Key!')
    console.log(e)
  }
}
