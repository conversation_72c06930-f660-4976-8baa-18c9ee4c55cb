const authorToken = `Basic QVVMNm5OaUJSZUkxV3VFV1pZSmRpVlJWT3dWdTFacE1xbnlWYnkyU0RieHdOYWJCVFRDOUJoZ3hPRkVHYTlZRE1JSW5GdmphNXBiVUVFNmg6RUE5Mk8wWHN1R3otWU5CMjZsNmd6Z094V2VBTGhHZEo4NjZOMzBiaUxXQ1hSZmxobXNjVXVZWFo0M1RkOFQ4T18tNmtnQUVDYXpBQkZPQWc=`
const PAYPAL_API_DOMAIN = 'https://api-m.sandbox.paypal.com/v2'
const paypalCheckoutAPI = `${PAYPAL_API_DOMAIN}/checkout/orders`

function getHeaderPayload(bodyPayload) {
  return {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: authorToken
    },
    body: JSON.stringify(bodyPayload)
  }
}

export function createPaypalCharge(amount, currency, pageUrls) {
  const url = window.location.href
  const pathname = window.location.pathname
  const curentUrlPath = pathname.substring(pathname.lastIndexOf('/') + 1)
  const arrUrlLocaton = url.split(curentUrlPath)

  const bodyReq = {
    intent: 'CAPTURE',
    purchase_units: [
      {
        amount: {
          currency_code: currency,
          value: amount
        }
      }
    ],
    application_context: {
      cancel_url: `${arrUrlLocaton[0]}${pageUrls.declineUrl}`,
      return_url: `${arrUrlLocaton[0]}${pageUrls.successUrl}`
    }
  }
  const payload = getHeaderPayload(bodyReq)
  const result = fetch(paypalCheckoutAPI, payload).then((response) => response.json())

  return result
}
