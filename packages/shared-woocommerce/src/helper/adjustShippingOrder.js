export function adjustShippingOrder(data) {
  if (!data || !data.shippings || (data.shippings && data.shippings.length <= 1)) {
    window.shippingIndex = 0
    return data
  }

  // ? Sort Shipping Fee. from free -> fee: a-b
  data.shippings.sort((a, b) => {
    return a.price - b.price
  })

  // ! Check rm param => update shippingIndex
  const rmParam = window.ctrwowUtils.link.queryURLParameter('rm')
  // ? If rm=1 => Free ship
  // ? else => Have Shipping Fee
  if (rmParam === '1' || window.isFreeShip) {
    window.shippingIndex = 0
  } else {
    window.shippingIndex = data.shippings.length - 1
  }

  return data
}
