import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api'

export function connectWooCommerce(urlCustom, keyIdCustom, secretIdCustom) {
  const endpointURL = window.ctrwowUtils.localStorage().get('endpointURL') || ''
  const apiKey = window.ctrwowUtils.localStorage().get('apiKey') || ''
  const apiSecretKey = window.ctrwowUtils.localStorage().get('apiSecretKey') || ''
  // const url = urlCustom || 'https://testwoo.azdigi.shop/'
  // const keyId = keyIdCustom || 'ck_cc9c13f2bbabb34bb19f4843e89f534f7e4eaac0'
  // const secretId = secretIdCustom || 'cs_60f7df075a7b6910b70460704b2871170d1688d9'

  const wooConnectModule = new WooCommerceRestApi({
    url: endpointURL,
    consumerKey: apiKey,
    consumerSecret: apiSecretKey,
    version: 'wc/v3'
  })
  return wooConnectModule
}
