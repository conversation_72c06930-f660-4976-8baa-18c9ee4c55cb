export function implementBillingModelIndex(offerDatas) {
  offerDatas.forEach((offerData) => {
    offerData.billing_models = offerData.billing_models.map((billing_model) => {
      switch (billing_model.id) {
        case 2: // One Time Purchase
          billing_model.index = 0
          break
        case 3: // Subscribe 30
          billing_model.index = 1
          break
        case 6: // Subscribe 45
          billing_model.index = 3
          break
        case 4: // Subscribe 60
          billing_model.index = 2
          break
      }
      return billing_model
    })
  })
}
