export function fireFpProductPriceEvent(data) {
  try {
    // eslint-disable-next-line eqeqeq
    const productsData = data.prices && data.prices.length ? data.prices.filter((p) => p.quantity && p.quantity == 1) : []

    if (productsData.length > 0) {
      const product = productsData[0]
      const price = product.productPrices.DiscountedPrice ? product.productPrices.DiscountedPrice.Value : 0
      const fullRetailPrice = product.productPrices.FullRetailPrice ? Number(product.productPrices.FullRetailPrice.Value) : 0
      const discountedPrice = product.productPrices.DiscountedPrice ? Number(product.productPrices.DiscountedPrice.Value) : 0
      const discountedPercent = ((fullRetailPrice - discountedPrice) / fullRetailPrice) * 100

      const trackData = {
        productPrice: price,
        productId: product.productId,
        productName: product.productName,
        sku: product.sku,
        discountedPercent: Math.round(discountedPercent),
        fullPrice: fullRetailPrice,
        currencyCode: data.location.currencyCode
      }
      window.__CTR_FP_TRACKING.trackProductPrice(trackData)
      console.log('Track Product Price ok', trackData)
    }
  } catch (e) {
    console.warn('fireFpProductPriceEvent error: ', e)
  }
}
