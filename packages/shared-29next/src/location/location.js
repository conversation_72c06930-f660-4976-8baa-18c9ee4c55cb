import { countrylist } from './countrylist'
export function getCountryName({ country }) {
  const findCountryItem = countrylist.find((item) => {
    return item.ISO === country
  })
  if (findCountryItem) {
    return findCountryItem.Country
  }
  return ''
}

export function location(response) {
  try {
    const info = response.find((res) => res && res.ip)
    window.ipAddress = info.ip
    return {
      ip: info.ip || '*********',
      city: info.city,
      countryCode: info.country,
      countryName: getCountryName(info),
      currencyCode: window.currencyCode || '',
      regionName: info.region,
      zipCode: ''
    }
  } catch (e) {
    window.ipAddress = '*********'
    return {
      ip: '*********',
      city: '',
      countryCode: '',
      countryName: '',
      currencyCode: '',
      regionName: '',
      zipCode: ''
    }
  }
}
