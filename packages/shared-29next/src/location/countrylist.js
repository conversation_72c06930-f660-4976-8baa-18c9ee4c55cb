export const countrylist = [
  {
    Country: 'Afghanistan',
    ISO: 'AF',
    countryCode: 'AF',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Åland Islands',
    ISO: 'AX',
    countryCode: 'AX',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Albania',
    ISO: 'AL',
    countryCode: 'AL',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Algeria',
    ISO: 'DZ',
    countryCode: 'DZ',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'American Samoa',
    ISO: 'AS',
    countryCode: 'AS',
    postalCodeRegex: '^\\d{5}(-{1}\\d{4,6})$'
  },
  {
    Country: 'Andorra',
    ISO: 'AD',
    countryCode: 'AD',
    postalCodeRegex: '^[Aa][Dd]\\d{3}$'
  },
  {
    Country: 'Angola',
    ISO: 'AO',
    countryCode: 'AO',
    postalCodeRegex: ''
  },
  {
    Country: 'Anguilla',
    ISO: 'AI',
    countryCode: 'AI',
    postalCodeRegex: '^[Aa][I][-][2][6][4][0]$'
  },
  {
    Country: 'Antigua and Barbuda',
    ISO: 'AG',
    countryCode: 'AG',
    postalCodeRegex: ''
  },
  {
    Country: 'Argentina',
    ISO: 'AR',
    countryCode: 'AR',
    postalCodeRegex: '^\\d{4}|[A-Za-z]\\d{4}[a-zA-Z]{3}$'
  },
  {
    Country: 'Armenia',
    ISO: 'AM',
    countryCode: 'AM',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Aruba',
    ISO: 'AW',
    countryCode: 'AW',
    postalCodeRegex: ''
  },
  {
    Country: 'Ascension island',
    ISO: 'AC',
    countryCode: 'AC',
    postalCodeRegex: '^[Aa][Ss][Cc][Nn]\\s{0,1}[1][Zz][Zz]$'
  },
  {
    Country: 'Australia',
    ISO: 'AU',
    countryCode: 'AU',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Austria',
    ISO: 'AT',
    countryCode: 'AT',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Azerbaijan',
    ISO: 'AZ',
    countryCode: 'AZ',
    postalCodeRegex: '^[Aa][Zz]\\d{4}$'
  },
  {
    Country: 'Bahamas',
    ISO: 'BS',
    countryCode: 'BS',
    postalCodeRegex: ''
  },
  {
    Country: 'Bahrain',
    ISO: 'BH',
    countryCode: 'BH',
    postalCodeRegex: '^\\d{3,4}$'
  },
  {
    Country: 'Bangladesh',
    ISO: 'BD',
    countryCode: 'BD',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Barbados',
    ISO: 'BB',
    countryCode: 'BB',
    postalCodeRegex: '^[Aa][Zz]\\d{5}$'
  },
  {
    Country: 'Belarus',
    ISO: 'BY',
    countryCode: 'BY',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Belgium',
    ISO: 'BE',
    countryCode: 'BE',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Belize',
    ISO: 'BZ',
    countryCode: 'BZ',
    postalCodeRegex: ''
  },
  {
    Country: 'Benin',
    ISO: 'BJ',
    countryCode: 'BJ',
    postalCodeRegex: ''
  },
  {
    Country: 'Bermuda',
    ISO: 'BM',
    countryCode: 'BM',
    postalCodeRegex: '^[A-Za-z]{2}\\s([A-Za-z]{2}|\\d{2})$'
  },
  {
    Country: 'Bhutan',
    ISO: 'BT',
    countryCode: 'BT',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Bolivia',
    ISO: 'BO',
    countryCode: 'BO',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Bonaire, Sint Eustatius and Saba',
    ISO: 'BQ',
    countryCode: 'BQ',
    postalCodeRegex: ''
  },
  {
    Country: 'Bosnia and Herzegovina',
    ISO: 'BA',
    countryCode: 'BA',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Botswana',
    ISO: 'BW',
    countryCode: 'BW',
    postalCodeRegex: ''
  },
  {
    Country: 'Brazil',
    ISO: 'BR',
    countryCode: 'BR',
    postalCodeRegex: '^\\d{5}-\\d{3}$'
  },
  {
    Country: 'British Antarctic Territory',
    ISO: '',
    countryCode: '',
    postalCodeRegex: '^[Bb][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'British Indian Ocean Territory',
    ISO: 'IO',
    countryCode: 'IO',
    postalCodeRegex: '^[Bb]{2}[Nn][Dd]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'British Virgin Islands',
    ISO: 'VG',
    countryCode: 'VG',
    postalCodeRegex: '^[Vv][Gg]\\d{4}$'
  },
  {
    Country: 'Brunei',
    ISO: 'BN',
    countryCode: 'BN',
    postalCodeRegex: '^[A-Za-z]{2}\\d{4}$'
  },
  {
    Country: 'Bulgaria',
    ISO: 'BG',
    countryCode: 'BG',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Burkina Faso',
    ISO: 'BF',
    countryCode: 'BF',
    postalCodeRegex: ''
  },
  {
    Country: 'Burundi',
    ISO: 'BI',
    countryCode: 'BI',
    postalCodeRegex: ''
  },
  {
    Country: 'Cambodia',
    ISO: 'KH',
    countryCode: 'KH',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Cameroon',
    ISO: 'CM',
    countryCode: 'CM',
    postalCodeRegex: ''
  },
  {
    Country: 'Canada',
    ISO: 'CA',
    countryCode: 'CA',
    postalCodeRegex: '^(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\d(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\s{0,1}\\d(?=[^DdFfIiOoQqUu\\d\\s])[A-Za-z]\\d$'
  },
  {
    Country: 'Cape Verde',
    ISO: 'CV',
    countryCode: 'CV',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Cayman Islands',
    ISO: 'KY',
    countryCode: 'KY',
    postalCodeRegex: '^[Kk][Yy]\\d[-\\s]{0,1}\\d{4}$'
  },
  {
    Country: 'Central African Republic',
    ISO: 'CF',
    countryCode: 'CF',
    postalCodeRegex: ''
  },
  {
    Country: 'Chad',
    ISO: 'TD',
    countryCode: 'TD',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Chile',
    ISO: 'CL',
    countryCode: 'CL',
    postalCodeRegex: '^\\d{7}\\s\\(\\d{3}-\\d{4}\\)$'
  },
  {
    Country: 'China',
    ISO: 'CN',
    countryCode: 'CN',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Christmas Island',
    ISO: 'CX',
    countryCode: 'CX',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Cocos (Keeling) Island',
    ISO: 'CC',
    countryCode: 'CC',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Colombia',
    ISO: 'CO',
    countryCode: 'CO',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Comoros',
    ISO: 'KM',
    countryCode: 'KM',
    postalCodeRegex: ''
  },
  {
    Country: 'Congo (Brazzaville)',
    ISO: 'CG',
    countryCode: 'CG',
    postalCodeRegex: ''
  },
  {
    Country: 'Congo, Democratic Republic',
    ISO: 'CD',
    countryCode: 'CD',
    postalCodeRegex: '^[Cc][Dd]$'
  },
  {
    Country: 'Cook Islands',
    ISO: 'CK',
    countryCode: 'CK',
    postalCodeRegex: ''
  },
  {
    Country: 'Costa Rica',
    ISO: 'CR',
    countryCode: 'CR',
    postalCodeRegex: '^\\d{4,5}$'
  },
  {
    Country: 'Côte d"Ivoire (Ivory Coast)',
    ISO: 'CI',
    countryCode: 'CI',
    postalCodeRegex: ''
  },
  {
    Country: 'Croatia',
    ISO: 'HR',
    countryCode: 'HR',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Cuba',
    ISO: 'CU',
    countryCode: 'CU',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Curaçao',
    ISO: 'CW',
    countryCode: 'CW',
    postalCodeRegex: ''
  },
  {
    Country: 'Cyprus',
    ISO: 'CY',
    countryCode: 'CY',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Czech Republic',
    ISO: 'CZ',
    countryCode: 'CZ',
    postalCodeRegex: '^\\d{5}\\s\\(\\d{3}\\s\\d{2}\\)$'
  },
  {
    Country: 'Denmark',
    ISO: 'DK',
    countryCode: 'DK',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Djibouti',
    ISO: 'DJ',
    countryCode: 'DJ',
    postalCodeRegex: ''
  },
  {
    Country: 'Dominica',
    ISO: 'DM',
    countryCode: 'DM',
    postalCodeRegex: ''
  },
  {
    Country: 'Dominican Republic',
    ISO: 'DO',
    countryCode: 'DO',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'East Timor',
    ISO: 'TL',
    countryCode: 'TL',
    postalCodeRegex: ''
  },
  {
    Country: 'Ecuador',
    ISO: 'EC',
    countryCode: 'EC',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'El Salvador',
    ISO: 'SV',
    countryCode: 'SV',
    postalCodeRegex: '^1101$'
  },
  {
    Country: 'Egypt',
    ISO: 'EG',
    countryCode: 'EG',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Equatorial Guinea',
    ISO: 'GQ',
    countryCode: 'GQ',
    postalCodeRegex: ''
  },
  {
    Country: 'Eritrea',
    ISO: 'ER',
    countryCode: 'ER',
    postalCodeRegex: ''
  },
  {
    Country: 'Estonia',
    ISO: 'EE',
    countryCode: 'EE',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Ethiopia',
    ISO: 'ET',
    countryCode: 'ET',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Falkland Islands',
    ISO: 'FK',
    countryCode: 'FK',
    postalCodeRegex: '^[Ff][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Faroe Islands',
    ISO: 'FO',
    countryCode: 'FO',
    postalCodeRegex: '^\\d{3}$'
  },
  {
    Country: 'Fiji',
    ISO: 'FJ',
    countryCode: 'FJ',
    postalCodeRegex: ''
  },
  {
    Country: 'Finland',
    ISO: 'FI',
    countryCode: 'FI',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'France',
    ISO: 'FR',
    countryCode: 'FR',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'French Guiana',
    ISO: 'GF',
    countryCode: 'GF',
    postalCodeRegex: '^973\\d{2}$'
  },
  {
    Country: 'French Polynesia',
    ISO: 'PF',
    countryCode: 'PF',
    postalCodeRegex: '^987\\d{2}$'
  },
  {
    Country: 'French Southern and Antarctic Territories',
    ISO: 'TF',
    countryCode: 'TF',
    postalCodeRegex: ''
  },
  {
    Country: 'Gabon',
    ISO: 'GA',
    countryCode: 'GA',
    postalCodeRegex: '^\\d{2}\\s[a-zA-Z-_ ]\\s\\d{2}$'
  },
  {
    Country: 'Gambia',
    ISO: 'GM',
    countryCode: 'GM',
    postalCodeRegex: ''
  },
  {
    Country: 'Georgia',
    ISO: 'GE',
    countryCode: 'GE',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Germany',
    ISO: 'DE',
    countryCode: 'DE',
    postalCodeRegex: '^\\d{2}$'
  },
  {
    Country: 'Germany',
    ISO: 'DE',
    countryCode: 'DE',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Germany',
    ISO: 'DE',
    countryCode: 'DE',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Ghana',
    ISO: 'GH',
    countryCode: 'GH',
    postalCodeRegex: ''
  },
  {
    Country: 'Gibraltar',
    ISO: 'GI',
    countryCode: 'GI',
    postalCodeRegex: '^[Gg][Xx][1]{2}\\s{0,1}[1][Aa]{2}$'
  },
  {
    Country: 'Greece',
    ISO: 'GR',
    countryCode: 'GR',
    postalCodeRegex: '^\\d{3}\\s{0,1}\\d{2}$'
  },
  {
    Country: 'Greenland',
    ISO: 'GL',
    countryCode: 'GL',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Grenada',
    ISO: 'GD',
    countryCode: 'GD',
    postalCodeRegex: ''
  },
  {
    Country: 'Guadeloupe',
    ISO: 'GP',
    countryCode: 'GP',
    postalCodeRegex: '^971\\d{2}$'
  },
  {
    Country: 'Guam',
    ISO: 'GU',
    countryCode: 'GU',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Guatemala',
    ISO: 'GT',
    countryCode: 'GT',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Guernsey',
    ISO: 'GG',
    countryCode: 'GG',
    postalCodeRegex: '^[A-Za-z]{2}\\d\\s{0,1}\\d[A-Za-z]{2}$'
  },
  {
    Country: 'Guinea',
    ISO: 'GN',
    countryCode: 'GN',
    postalCodeRegex: ''
  },
  {
    Country: 'Guinea Bissau',
    ISO: 'GW',
    countryCode: 'GW',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Guyana',
    ISO: 'GY',
    countryCode: 'GY',
    postalCodeRegex: ''
  },
  {
    Country: 'Haiti',
    ISO: 'HT',
    countryCode: 'HT',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Heard and McDonald Islands',
    ISO: 'HM',
    countryCode: 'HM',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Honduras',
    ISO: 'HN',
    countryCode: 'HN',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Hong Kong',
    ISO: 'HK',
    countryCode: 'HK',
    postalCodeRegex: ''
  },
  {
    Country: 'Hungary',
    ISO: 'HU',
    countryCode: 'HU',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Iceland',
    ISO: 'IS',
    countryCode: 'IS',
    postalCodeRegex: '^\\d{3}$'
  },
  {
    Country: 'India',
    ISO: 'IN',
    countryCode: 'IN',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Indonesia',
    ISO: 'ID',
    countryCode: 'ID',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Iran',
    ISO: 'IR',
    countryCode: 'IR',
    postalCodeRegex: '^\\d{5}-\\d{5}$'
  },
  {
    Country: 'Iraq',
    ISO: 'IQ',
    countryCode: 'IQ',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Ireland',
    ISO: 'IE',
    countryCode: 'IE',
    postalCodeRegex: ''
  },
  {
    Country: 'Isle of Man',
    ISO: 'IM',
    countryCode: 'IM',
    postalCodeRegex: '^[Ii[Mm]\\d{1,2}\\s\\d\\[A-Z]{2}$'
  },
  {
    Country: 'Israel',
    ISO: 'IL',
    countryCode: 'IL',
    postalCodeRegex: '^\\b\\d{5}(\\d{2})?$'
  },
  {
    Country: 'Italy',
    ISO: 'IT',
    countryCode: 'IT',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Jamaica',
    ISO: 'JM',
    countryCode: 'JM',
    postalCodeRegex: '^\\d{2}$'
  },
  {
    Country: 'Japan',
    ISO: 'JP',
    countryCode: 'JP',
    postalCodeRegex: '^\\d{7}\\s\\(\\d{3}-\\d{4}\\)$'
  },
  {
    Country: 'Jersey',
    ISO: 'JE',
    countryCode: 'JE',
    postalCodeRegex: '^[Jj][Ee]\\d\\s{0,1}\\d[A-Za-z]{2}$'
  },
  {
    Country: 'Jordan',
    ISO: 'JO',
    countryCode: 'JO',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Kazakhstan',
    ISO: 'KZ',
    countryCode: 'KZ',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Kenya',
    ISO: 'KE',
    countryCode: 'KE',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Kiribati',
    ISO: 'KI',
    countryCode: 'KI',
    postalCodeRegex: ''
  },
  {
    Country: 'Korea, North',
    ISO: 'KP',
    countryCode: 'KP',
    postalCodeRegex: ''
  },
  {
    Country: 'Korea, South',
    ISO: 'KR',
    countryCode: 'KR',
    postalCodeRegex: '^\\d{6}\\s\\(\\d{3}-\\d{3}\\)$'
  },
  {
    Country: 'Kosovo',
    ISO: 'XK',
    countryCode: 'XK',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Kuwait',
    ISO: 'KW',
    countryCode: 'KW',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Kyrgyzstan',
    ISO: 'KG',
    countryCode: 'KG',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Latvia',
    ISO: 'LV',
    countryCode: 'LV',
    postalCodeRegex: '^[Ll][Vv][- ]{0,1}\\d{4}$'
  },
  {
    Country: 'Laos',
    ISO: 'LA',
    countryCode: 'LA',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Lebanon',
    ISO: 'LB',
    countryCode: 'LB',
    postalCodeRegex: '^\\d{4}\\s{0,1}\\d{4}$'
  },
  {
    Country: 'Lesotho',
    ISO: 'LS',
    countryCode: 'LS',
    postalCodeRegex: '^\\d{3}$'
  },
  {
    Country: 'Liberia',
    ISO: 'LR',
    countryCode: 'LR',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Libya',
    ISO: 'LY',
    countryCode: 'LY',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Liechtenstein',
    ISO: 'LI',
    countryCode: 'LI',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Lithuania',
    ISO: 'LT',
    countryCode: 'LT',
    postalCodeRegex: '^[Ll][Tt][- ]{0,1}\\d{5}$'
  },
  {
    Country: 'Luxembourg',
    ISO: 'LU',
    countryCode: 'LU',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Macau',
    ISO: 'MO',
    countryCode: 'MO',
    postalCodeRegex: ''
  },
  {
    Country: 'Macedonia',
    ISO: 'MK',
    countryCode: 'MK',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Madagascar',
    ISO: 'MG',
    countryCode: 'MG',
    postalCodeRegex: '^\\d{3}$'
  },
  {
    Country: 'Malawi',
    ISO: 'MW',
    countryCode: 'MW',
    postalCodeRegex: ''
  },
  {
    Country: 'Maldives',
    ISO: 'MV',
    countryCode: 'MV',
    postalCodeRegex: '^\\d{4,5}$'
  },
  {
    Country: 'Malaysia',
    ISO: 'MY',
    countryCode: 'MY',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Mali',
    ISO: 'ML',
    countryCode: 'ML',
    postalCodeRegex: ''
  },
  {
    Country: 'Malta',
    ISO: 'MT',
    countryCode: 'MT',
    postalCodeRegex: '^[A-Za-z]{3}\\s{0,1}\\d{4}$'
  },
  {
    Country: 'Marshall Islands',
    ISO: 'MH',
    countryCode: 'MH',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Mauritania',
    ISO: 'MR',
    countryCode: 'MR',
    postalCodeRegex: ''
  },
  {
    Country: 'Mauritius',
    ISO: 'MU',
    countryCode: 'MU',
    postalCodeRegex: ''
  },
  {
    Country: 'Martinique',
    ISO: 'MQ',
    countryCode: 'MQ',
    postalCodeRegex: '^972\\d{2}$'
  },
  {
    Country: 'Mayotte',
    ISO: 'YT',
    countryCode: 'YT',
    postalCodeRegex: '^976\\d{2}$'
  },
  {
    Country: 'Micronesia',
    ISO: 'FM',
    countryCode: 'FM',
    postalCodeRegex: '^\\d{5}(-{1}\\d{4})$'
  },
  {
    Country: 'Mexico',
    ISO: 'MX',
    countryCode: 'MX',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Micronesia',
    ISO: 'FM',
    countryCode: 'FM',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Moldova',
    ISO: 'MD',
    countryCode: 'MD',
    postalCodeRegex: '^[Mm][Dd][- ]{0,1}\\d{4}$'
  },
  {
    Country: 'Monaco',
    ISO: 'MC',
    countryCode: 'MC',
    postalCodeRegex: '^980\\d{2}$'
  },
  {
    Country: 'Mongolia',
    ISO: 'MN',
    countryCode: 'MN',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Montenegro',
    ISO: 'ME',
    countryCode: 'ME',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Montserrat',
    ISO: 'MS',
    countryCode: 'MS',
    postalCodeRegex: '^[Mm][Ss][Rr]\\s{0,1}\\d{4}$'
  },
  {
    Country: 'Morocco',
    ISO: 'MA',
    countryCode: 'MA',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Mozambique',
    ISO: 'MZ',
    countryCode: 'MZ',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Myanmar',
    ISO: 'MM',
    countryCode: 'MM',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Namibia',
    ISO: 'NA',
    countryCode: 'NA',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Nauru',
    ISO: 'NR',
    countryCode: 'NR',
    postalCodeRegex: ''
  },
  {
    Country: 'Nepal',
    ISO: 'NP',
    countryCode: 'NP',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Netherlands',
    ISO: 'NL',
    countryCode: 'NL',
    postalCodeRegex: '^\\d{4}\\s{0,1}[A-Za-z]{2}$'
  },
  {
    Country: 'New Caledonia',
    ISO: 'NC',
    countryCode: 'NC',
    postalCodeRegex: '^988\\d{2}$'
  },
  {
    Country: 'New Zealand',
    ISO: 'NZ',
    countryCode: 'NZ',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Nicaragua',
    ISO: 'NI',
    countryCode: 'NI',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Niger',
    ISO: 'NE',
    countryCode: 'NE',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Nigeria',
    ISO: 'NG',
    countryCode: 'NG',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Niue',
    ISO: 'NU',
    countryCode: 'NU',
    postalCodeRegex: ''
  },
  {
    Country: 'Norfolk Island',
    ISO: 'NF',
    countryCode: 'NF',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Northern Mariana Islands',
    ISO: 'MP',
    countryCode: 'MP',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Norway',
    ISO: 'NO',
    countryCode: 'NO',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Oman',
    ISO: 'OM',
    countryCode: 'OM',
    postalCodeRegex: '^\\d{3}$'
  },
  {
    Country: 'Pakistan',
    ISO: 'PK',
    countryCode: 'PK',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Palau',
    ISO: 'PW',
    countryCode: 'PW',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Panama',
    ISO: 'PA',
    countryCode: 'PA',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Papua New Guinea',
    ISO: 'PG',
    countryCode: 'PG',
    postalCodeRegex: '^\\d{3}$'
  },
  {
    Country: 'Paraguay',
    ISO: 'PY',
    countryCode: 'PY',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Peru',
    ISO: 'PE',
    countryCode: 'PE',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Philippines',
    ISO: 'PH',
    countryCode: 'PH',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Pitcairn Islands',
    ISO: 'PN',
    countryCode: 'PN',
    postalCodeRegex: '^[Pp][Cc][Rr][Nn]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Poland',
    ISO: 'PL',
    countryCode: 'PL',
    postalCodeRegex: '^\\d{2}[- ]{0,1}\\d{3}$'
  },
  {
    Country: 'Portugal',
    ISO: 'PT',
    countryCode: 'PT',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Portugal',
    ISO: 'PT',
    countryCode: 'PT',
    postalCodeRegex: '^\\d{4}[- ]{0,1}\\d{3}$'
  },
  {
    Country: 'Puerto Rico',
    ISO: 'PR',
    countryCode: 'PR',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Qatar',
    ISO: 'QA',
    countryCode: 'QA',
    postalCodeRegex: ''
  },
  {
    Country: 'Réunion',
    ISO: 'RE',
    countryCode: 'RE',
    postalCodeRegex: '^974\\d{2}$'
  },
  {
    Country: 'Romania',
    ISO: 'RO',
    countryCode: 'RO',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Russia',
    ISO: 'RU',
    countryCode: 'RU',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Saint Barthélemy',
    ISO: 'BL',
    countryCode: 'BL',
    postalCodeRegex: '^97133$'
  },
  {
    Country: 'Saint Helena',
    ISO: 'SH',
    countryCode: 'SH',
    postalCodeRegex: '^[Ss][Tt][Hh][Ll]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Saint Kitts and Nevis',
    ISO: 'KN',
    countryCode: 'KN',
    postalCodeRegex: ''
  },
  {
    Country: 'Saint Lucia',
    ISO: 'LC',
    countryCode: 'LC',
    postalCodeRegex: ''
  },
  {
    Country: 'Saint Martin',
    ISO: 'MF',
    countryCode: 'MF',
    postalCodeRegex: '^97150$'
  },
  {
    Country: 'Saint Pierre and Miquelon',
    ISO: 'PM',
    countryCode: 'PM',
    postalCodeRegex: '^97500$'
  },
  {
    Country: 'Saint Vincent and the Grenadines',
    ISO: 'VC',
    countryCode: 'VC',
    postalCodeRegex: '^[Vv][Cc]\\d{4}$'
  },
  {
    Country: 'San Marino',
    ISO: 'SM',
    countryCode: 'SM',
    postalCodeRegex: '^4789\\d$'
  },
  {
    Country: 'Sao Tome and Principe',
    ISO: 'ST',
    countryCode: 'ST',
    postalCodeRegex: ''
  },
  {
    Country: 'Saudi Arabia',
    ISO: 'SA',
    countryCode: 'SA',
    postalCodeRegex: '^\\d{5}(-{1}\\d{4})?$'
  },
  {
    Country: 'Senegal',
    ISO: 'SN',
    countryCode: 'SN',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Serbia',
    ISO: 'RS',
    countryCode: 'RS',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Serbia',
    ISO: 'RS',
    countryCode: 'RS',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Seychelles',
    ISO: 'SC',
    countryCode: 'SC',
    postalCodeRegex: ''
  },
  {
    Country: 'Sint Maarten',
    ISO: 'SX',
    countryCode: 'SX',
    postalCodeRegex: ''
  },
  {
    Country: 'Sierra Leone',
    ISO: 'SL',
    countryCode: 'SL',
    postalCodeRegex: ''
  },
  {
    Country: 'Singapore',
    ISO: 'SG',
    countryCode: 'SG',
    postalCodeRegex: '^\\d{2}$'
  },
  {
    Country: 'Singapore',
    ISO: 'SG',
    countryCode: 'SG',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Singapore',
    ISO: 'SG',
    countryCode: 'SG',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Slovakia',
    ISO: 'SK',
    countryCode: 'SK',
    postalCodeRegex: '^\\d{5}\\s\\(\\d{3}\\s\\d{2}\\)$'
  },
  {
    Country: 'Slovenia',
    ISO: 'SI',
    countryCode: 'SI',
    postalCodeRegex: '^([Ss][Ii][- ]{0,1}){0,1}\\d{4}$'
  },
  {
    Country: 'Solomon Islands',
    ISO: 'SB',
    countryCode: 'SB',
    postalCodeRegex: ''
  },
  {
    Country: 'Somalia',
    ISO: 'SO',
    countryCode: 'SO',
    postalCodeRegex: ''
  },
  {
    Country: 'South Africa',
    ISO: 'ZA',
    countryCode: 'ZA',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'South Georgia and the South Sandwich Islands',
    ISO: 'GS',
    countryCode: 'GS',
    postalCodeRegex: '^[Ss][Ii][Qq]{2}\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'South Korea',
    ISO: 'KR',
    countryCode: 'KR',
    postalCodeRegex: '^\\d{6}\\s\\(\\d{3}-\\d{3}\\)$'
  },
  {
    Country: 'Spain',
    ISO: 'ES',
    countryCode: 'ES',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Sri Lanka',
    ISO: 'LK',
    countryCode: 'LK',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Sudan',
    ISO: 'SD',
    countryCode: 'SD',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Suriname',
    ISO: 'SR',
    countryCode: 'SR',
    postalCodeRegex: ''
  },
  {
    Country: 'Swaziland',
    ISO: 'SZ',
    countryCode: 'SZ',
    postalCodeRegex: '^[A-Za-z]\\d{3}$'
  },
  {
    Country: 'Sweden',
    ISO: 'SE',
    countryCode: 'SE',
    postalCodeRegex: '^\\d{3}\\s*\\d{2}$'
  },
  {
    Country: 'Switzerland',
    ISO: 'CH',
    countryCode: 'CH',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Svalbard and Jan Mayen',
    ISO: 'SJ',
    countryCode: 'SJ',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Syria',
    ISO: 'SY',
    countryCode: 'SY',
    postalCodeRegex: ''
  },
  {
    Country: 'Taiwan',
    ISO: 'TW',
    countryCode: 'TW',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Tajikistan',
    ISO: 'TJ',
    countryCode: 'TJ',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Tanzania',
    ISO: 'TZ',
    countryCode: 'TZ',
    postalCodeRegex: ''
  },
  {
    Country: 'Thailand',
    ISO: 'TH',
    countryCode: 'TH',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Togo',
    ISO: 'TG',
    countryCode: 'TG',
    postalCodeRegex: ''
  },
  {
    Country: 'Tokelau',
    ISO: 'TK',
    countryCode: 'TK',
    postalCodeRegex: ''
  },
  {
    Country: 'Tonga',
    ISO: 'TO',
    countryCode: 'TO',
    postalCodeRegex: ''
  },
  {
    Country: 'Trinidad and Tobago',
    ISO: 'TT',
    countryCode: 'TT',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Tristan da Cunha',
    ISO: 'SH',
    countryCode: 'SH',
    postalCodeRegex: '^[Tt][Dd][Cc][Uu]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Tunisia',
    ISO: 'TN',
    countryCode: 'TN',
    postalCodeRegex: '^\\d{4}$'
  },
  {
    Country: 'Turkey',
    ISO: 'TR',
    countryCode: 'TR',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Turkmenistan',
    ISO: 'TM',
    countryCode: 'TM',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Turks and Caicos Islands',
    ISO: 'TC',
    countryCode: 'TC',
    postalCodeRegex: '^[Tt][Kk][Cc][Aa]\\s{0,1}[1][Zz]{2}$'
  },
  {
    Country: 'Tuvalu',
    ISO: 'TV',
    countryCode: 'TV',
    postalCodeRegex: ''
  },
  {
    Country: 'Uganda',
    ISO: 'UG',
    countryCode: 'UG',
    postalCodeRegex: ''
  },
  {
    Country: 'Ukraine',
    ISO: 'UA',
    countryCode: 'UA',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'United Arab Emirates',
    ISO: 'AE',
    countryCode: 'AE',
    postalCodeRegex: ''
  },
  {
    Country: 'United Kingdom',
    ISO: 'GB',
    countryCode: 'GB',
    postalCodeRegex: '^[A-Z]{1,2}[0-9R][0-9A-Z]?\\s*[0-9][A-Z-[CIKMOV]]{2}'
  },
  {
    Country: 'United States',
    ISO: 'US',
    countryCode: 'US',
    postalCodeRegex: '^\\b\\d{5}\\b(?:[- ]{1}\\d{4})?$'
  },
  {
    Country: 'Uruguay',
    ISO: 'UY',
    countryCode: 'UY',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'U.S. Virgin Islands',
    ISO: 'VI',
    countryCode: 'VI',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Uzbekistan',
    ISO: 'UZ',
    countryCode: 'UZ',
    postalCodeRegex: '^\\d{3} \\d{3}$'
  },
  {
    Country: 'Vanuatu',
    ISO: 'VU',
    countryCode: 'VU',
    postalCodeRegex: ''
  },
  {
    Country: 'Vatican',
    ISO: 'VA',
    countryCode: 'VA',
    postalCodeRegex: '^120$'
  },
  {
    Country: 'Venezuela',
    ISO: 'VE',
    countryCode: 'VE',
    postalCodeRegex: '^\\d{4}(\\s[a-zA-Z]{1})?$'
  },
  {
    Country: 'Vietnam',
    ISO: 'VN',
    countryCode: 'VN',
    postalCodeRegex: '^\\d{6}$'
  },
  {
    Country: 'Wallis and Futuna',
    ISO: 'WF',
    countryCode: 'WF',
    postalCodeRegex: '^986\\d{2}$'
  },
  {
    Country: 'Yemen',
    ISO: 'YE',
    countryCode: 'YE',
    postalCodeRegex: ''
  },
  {
    Country: 'Zambia',
    ISO: 'ZM',
    countryCode: 'ZM',
    postalCodeRegex: '^\\d{5}$'
  },
  {
    Country: 'Zimbabwe',
    ISO: 'ZW',
    countryCode: 'ZW',
    postalCodeRegex: ''
  }
]
