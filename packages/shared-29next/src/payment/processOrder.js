export function processOrder(elm, paymentInfo, placeOrderCB) {
  if (!window.ctrwowUtils.isLiveMode() || !paymentInfo) return

  // Tracking
  const trackingValue = elm.getAttribute('tracking-value')
  if (trackingValue) {
    console.log('paypay - trackingValue' + trackingValue)
    window.ctrwowUtils.tracking.pushGtmTrackingEvent(trackingValue)
  }

  // ! Show loading
  elm.querySelector('.paymentProccessing').style.display = 'block'

  const isNoExtraPopup = !elm.hasAttribute('use-diggy-popup') || !document.querySelector('.extra-popup')
  if (isNoExtraPopup) {
    if (typeof placeOrderCB === 'function') {
      placeOrderCB()
    }
  } else {
    // ? Emit Event for Diggy Popup
    window.ctrwowUtils.events.emit('triggerPaypalOrder')
  }
}
