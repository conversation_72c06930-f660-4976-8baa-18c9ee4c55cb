export const PAYMENT_METHOD = {
  PAYPAL: 'paypal',
  CREDIT_CARD: 'creditcard',
  IDEAL: 'ideal',
  SOFORT: 'sofort',
  STRIPE: 'stripe'
}

export const getUserPaymentType = () => window.localStorage.getItem('userPaymentType')
export const setUserPaymentType = (paymenttype) => window.localStorage.setItem('userPaymentType', paymenttype)

export const isPaidByStripe = () => getUserPaymentType() === PAYMENT_METHOD.STRIPE
export const isPaidBySofort = () => getUserPaymentType() === PAYMENT_METHOD.SOFORT
export const isPaidByIdeal = () => getUserPaymentType() === PAYMENT_METHOD.IDEAL
export const isPaidByPaypal = () => getUserPaymentType() === PAYMENT_METHOD.PAYPAL
export const isPaidByCreditCard = () => getUserPaymentType() === PAYMENT_METHOD.CREDIT_CARD
