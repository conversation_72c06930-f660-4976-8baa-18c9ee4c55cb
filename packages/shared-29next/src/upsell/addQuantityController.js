import * as orderInfomation from 'shared-checkout-flow/src/orderInfo/orderInfo'

export function addQuantityController(selector, quantityControlType, productListLength, onChange) {
  if (quantityControlType === 'incrementer') {
    addQuantityControllerToIncrementerControl(selector, productListLength, onChange)
  }
  if (quantityControlType === 'dropdown') {
    addQuantityControllerToDropdownControl(selector, onChange)
  }
  if (quantityControlType === 'none') {
    addQuantityControllerAutoControl(productListLength, onChange)
  }
}

function addQuantityControllerToIncrementerControl(selector, productListLength, onChange) {
  const { _qAll, _q } = window
  const selectorPrefix = '.productQuantityControlWrapper .quantityControl.incrementer'

  const maxQuantityElm = selector.querySelector(`${selectorPrefix} [max-value]`)
  let maxQuantity = maxQuantityElm ? parseInt(maxQuantityElm.getAttribute('max-value')) : null
  maxQuantity = maxQuantity && productListLength > maxQuantity ? maxQuantity : productListLength

  if (window._q('.js-upsell-packages .active')) {
    const qty = window._q('.js-upsell-packages .active').getAttribute('upsell-package').split(',').length
    maxQuantity = qty
  }

  const isValidQuantity = (number) => number > 0 && number <= maxQuantity
  const updateQuantityToElm = (value) => {
    Array.prototype.slice.call(_qAll(`${selectorPrefix} [name='numberInput']`)).forEach((input) => (input.value = value))
    Array.prototype.slice.call(_qAll('.upsell-qty')).forEach((upsellQtyElm) => (upsellQtyElm.textContent = value))
  }

  Array.prototype.slice.call(selector.querySelectorAll(`${selectorPrefix} .minus`)).forEach((minus) => {
    minus.addEventListener(
      'click',
      () => {
        const numberVal = (Number(_q(`${selectorPrefix} [name='numberInput']`).value) || 1) - 1

        if (isValidQuantity(numberVal)) {
          updateQuantityToElm(numberVal)
          onChange(numberVal)
        }
      },
      false
    )
  })

  Array.prototype.slice.call(selector.querySelectorAll(`${selectorPrefix} .plus`)).forEach((plus) => {
    plus.addEventListener(
      'click',
      () => {
        const numberVal = (Number(_q(`${selectorPrefix} [name='numberInput']`).value) || 1) + 1

        if (isValidQuantity(numberVal)) {
          updateQuantityToElm(numberVal)
          onChange(numberVal)
        }
      },
      false
    )
  })
}

function addQuantityControllerToDropdownControl(selector, onChange) {
  const selectorPrefix = '.productQuantityControlWrapper .quantityControl.dropdown'
  const selectors = Array.prototype.slice.call(_qAll(`${selectorPrefix} select`))
  const widgetSelector = Array.prototype.slice.call(selector.querySelectorAll(`${selectorPrefix} select`))
  widgetSelector.forEach((selectorElm) => {
    selectorElm.addEventListener(
      'change',
      () => {
        const numberVal = parseInt(selectorElm.value)

        selectors.forEach((item) => {
          // eslint-disable-next-line eqeqeq
          if (item != selectorElm) {
            item.value = numberVal
          }
        })

        onChange(numberVal)
      },
      false
    )
  })
}

function addQuantityControllerAutoControl(productListLength, onChange) {
  /**
   * type none and 1 product A => buy product A
   * type none and 2,3,4,.. product => detect product has quantity = orderInfo.quantity and buy it
   */
  const orderInfo = orderInfomation.getOrderInfo()
  /**
   * productListLength < orderInfo.quantity => return 1 <=> window.upsell_productIndex = 0
   */
  orderInfo ? (productListLength < orderInfo.quantity ? onChange(1) : onChange(orderInfo.quantity)) : onChange(1)
}

export function getDefaultQuantity(quantityControlType) {
  const input = document.querySelector(`.productQuantityControlWrapper .quantityControl.${quantityControlType} [name='numberInput']`)

  if (input) {
    return Number(input.value)
  }
}
