import { populateDataToNodeContent } from 'shared-checkout-flow/src/canReplaceContentWithPlaceholder'

export function replaceBracketsStrings() {
  const imgLoading = `<span class="js-img-loading">
                <img src="//d16hdrba6dusey.cloudfront.net/sitecommon/images/loading-price-v1.gif" width="20" height="10" class="no-lazy"  style="width: 20px; display: inline;">
            </span>`
  populateDataToNodeContent((textContent) =>
    textContent
      .replace(/{fullprice}/g, `<span class="spanFullPrice">${imgLoading}</span>`)
      .replace(/{price}/g, `<span class="spanUpsellPrice">${imgLoading}</span>`)
      .replace(/{unitprice}/g, `<span class="spanUnitPrice">${imgLoading}</span>`)
      .replace(/{saveprice}/g, `<span class="spanSavePrice">${imgLoading}</span>`)
      .replace(/{upsellQty}/g, `<span class="spanUpsellQty">${imgLoading}</span>`)
      .replace(/{shippingPrice}/g, `<span class="spanShippingPrice">${imgLoading}</span>`)
      .replace(/{taxamount}/g, `<span class="spanTaxAmount">${imgLoading}</span>`)
      .replace(/{unitfullprice}/g, `<span class="spanUnitFullPrice">${imgLoading}</span>`)
      .replace(/{nameProduct}/g, `<span class="spanProductName">${imgLoading}</span>`)
      .replace(/{unitpriceWithQty}/g, `<span class="spanUnitPriceWithQty">...</span>`)
      .replace(/{unit}/g, `<span class="unit">${imgLoading}</span>`)
      .replace(/{unitx\d+}/g, (unitx) => `<span class="${unitx}">${imgLoading}</span>`)
  )
}
