import { generateCurrencyNumber } from './generateCurrencyNumber'

export function generatePriceObj(product, fe_quantity) {
  // const unitDiscountedPrice = Number(product.price)
  // const unitFullPrice = Number(product.price_retail)
  const discountedPrice = Number(product.price_total)
  const fullPrice = Number(product.price_retail_total)
  const savePrice = Number((fullPrice - discountedPrice).toFixed(2))
  const unitPrice = Number((discountedPrice / product.qty).toFixed(2))
  // const unitPrice = Number((discountedPrice / fe_quantity).toFixed(2))

  return {
    productPrices: {
      DiscountedPrice: {
        FormattedValue: generateCurrencyNumber(discountedPrice),
        Value: discountedPrice,
        GlobalCurrencyCode: window.currencyCode
      },
      SavePrice: {
        FormattedValue: generateCurrencyNumber(savePrice),
        Value: savePrice,
        GlobalCurrencyCode: window.currencyCode
      },
      FullRetailPrice: {
        FormattedValue: generateCurrencyNumber(fullPrice),
        Value: fullPrice,
        GlobalCurrencyCode: window.currencyCode
      },
      UnitDiscountRate: {
        FormattedValue: generateCurrencyNumber(unitPrice),
        Value: unitPrice,
        GlobalCurrencyCode: window.currencyCode
      }
    }
  }
}
