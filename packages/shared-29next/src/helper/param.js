export function removeParameter(params) {
  let url = document.location.href
  for (const param of params) {
    const urlparts = url.split('?')

    if (urlparts.length >= 2) {
      const urlBase = urlparts.shift()
      const queryString = urlparts.join('?')

      const prefix = encodeURIComponent(param)
      const pars = queryString.split(/[&;]/g)
      for (let i = pars.length - 1; i >= 0; i--) {
        if (pars[i].lastIndexOf(prefix, 0) !== -1) {
          pars.splice(i, 1)
        }
      }
      url = urlBase
      if (pars.length > 0) {
        url += '?' + pars.join('&')
      }
    }
  }
  return url
}
