import currencySign from './currencySign.json'

function formatNumber(number) {
  const decimals = 2
  const decpoint = ',' // Or Number(0.1).toLocaleString().substring(1, 2)
  const thousand = '.' // Or Number(10000).toLocaleString().substring(2, 3)

  const n = Math.abs(number).toFixed(decimals).split('.')
  n[0] = n[0]
    .split('')
    .reverse()
    .map((c, i, a) => (i > 0 && i < a.length && i % 3 === 0 ? c + thousand : c))
    .reverse()
    .join('')

  const final = (Math.sign(number) < 0 ? '-' : '') + n.join(decpoint)

  return final
}

export function generateCurrencyNumber(number) {
  let num = Number(number).toFixed(2)
  if (window.currencyCode === 'EUR') {
    num = formatNumber(number)
  }
  return currencySign[window.currencyCode || 'USD'].replace('xxxxx', num)
}
