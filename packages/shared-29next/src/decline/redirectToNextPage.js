function redirect(href) {
  window.location.href = href
}
export function redirectToNextPage(confirmUrl) {
  const utils = window.ctrwowUtils
  utils.handleParam.clearParameter('paymentId', 'token', 'PayerID')
  // const orderInfo = window.localStorage.getItem('orderInfo') ? JSON.parse(window.localStorage.getItem('orderInfo')) : ''

  const nextPage = confirmUrl
  const currentParams = utils.link.getCustomPathName()
  redirect(nextPage + currentParams)
  // try {
  //   if (orderInfo && utils.localStorage().get('paypal_isMainOrder') === 'upsell') {
  //     const upsellIndex = orderInfo.upsellIndex
  //     const upsellUrls = orderInfo.upsells.length

  //     if (upsellIndex < upsellUrls) {
  //       nextPage = orderInfo.upsells[upsellIndex].upsellUrl.split('/').slice(-1)[0]
  //     }

  //     let redirectUrl = nextPage + currentParams
  //     const upParams = utils.localStorage().get('fireUpsellForGTMPurchase')
  //     utils.localStorage().remove('fireUpsellForGTMPurchase')

  //     if (upParams && utils.link.getQueryParameter(upParams)) {
  //       redirectUrl = utils.link.updateURLParameter(redirectUrl, upParams, '0')
  //     }
  //     redirect(redirectUrl)
  //   } else {
  //     redirect(nextPage + currentParams)
  //   }
  //   redirect(nextPage + currentParams)
  // } catch (e) {
  //   redirect(nextPage + currentParams)
  // }
}
