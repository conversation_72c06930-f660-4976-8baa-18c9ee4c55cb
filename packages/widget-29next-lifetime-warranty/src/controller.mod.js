/**
 * <AUTHOR> <<EMAIL>>
 * widget-29next-lifetime-warranty - Widget controller
 * @param element<DomElement>
 * @param props<Object>
 */

export default function initComponent(element, props) {
  let pidObj = element.getAttribute('lwProductIDs')
  if (!pidObj) return
  pidObj = JSON.parse(pidObj)
  console.log(pidObj)
  let lw_product

  const checkbox = element.querySelector('input[type=checkbox]')
  if (!window.ctrwowUtils.isBuilderMode()) {
    element.querySelector('.warranty_price').classList.add('loading')
  }

  function renderLWPrice() {
    const productList = window.ctrwowCheckout.productListData.getProductList().prices
    const currentProduct = window.ctrwowCheckout.checkoutData.getProduct()
    const currentProductID = currentProduct.productId
    const lwProductID = pidObj[currentProductID]
    lw_product = productList.find((item) => item.productId === lwProductID)
    const lwFormatedPrice = lw_product.productPrices.DiscountedPrice.FormattedValue
    element.querySelector('.warranty_price').classList.remove('loading')
    element.querySelector('.warranty_price').textContent = lwFormatedPrice

    window.ctrwowCheckout.checkoutData.setLifetimeWarrantyConfig('type', checkbox.checked ? lw_product : null)
  }

  function listener() {
    checkbox.addEventListener('change', () => {
      if (checkbox.checked) {
        checkbox.classList.add('checked')
      } else {
        checkbox.classList.remove('checked')
      }
      window.ctrwowCheckout.checkoutData.setLifetimeWarrantyConfig('type', checkbox.checked ? lw_product : null)
    })
  }

  window.ctrwowUtils.getDependencies([window.ctrwowUtils.getCtrLibLink('ctrwowCheckout')], { delayUntilInteract: false }).then(() =>
    window.ctrwowCheckout.ready().then(() => {
      window.ctrwowCheckout.productListData.onProductListChange(() => {
        listener()
      })
      window.ctrwowCheckout.checkoutData.onProductChange(() => {
        renderLWPrice()
      })
    })
  )
}
