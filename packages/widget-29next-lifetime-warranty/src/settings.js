/**
 * <AUTHOR> <<EMAIL>>
 * widget-29next-lifetime-warranty - settings
 */

export const WIDGET_SETTINGS = {
  attributes: {
    lwProductIDs: ''
  },
  traits: [
    {
      label: 'Default Checked',
      name: 'defaultChecked',
      type: 'checkbox',
      changeProp: 1
    } //,
    // {
    //   text: 'Get Data For Lifetime Warranty',
    //   full: true,
    //   name: 'getProductListLW',
    //   type: 'button',
    //   command: 'getProductListLW'
    // }
  ],
  'script-props': ['defaultChecked']
}
