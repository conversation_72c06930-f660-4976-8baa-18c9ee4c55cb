/**
 * @widgetName widget-29next-lifetime-warranty
 * @version 1.0.0
 * @see WIDGET_STORY
 * <AUTHOR> <<EMAIL>>
 */
export default (editor) => {
  const traitManager = editor.TraitManager
  traitManager.addType('lwBox', {
    createInput({ trait, elInput, component }) {
      const lwBox = component.get('lwBox') || []
      if (!lwBox.length) return

      const mainProductArr = lwBox.filter((item) => item.isChecked)
      const lwProductArr = lwBox.filter((item) => !item.isChecked)
      const el = document.createElement('div')
      el.innerHTML = `
        <div class="lw-items">
          ${mainProductArr
            .map(
              (opt) =>
                `<div class="lw-item">
                  <div class="main-product">${opt.productName}</div>
                  <div class="lw-product">
                    ↳ <select main-productid="${opt.productId}">
                      ${lwProductArr
                        .map(
                          (product) =>
                            `<option ${product.mainProductId === opt.productId ? 'selected' : ''} value="${product.productId}">
                              ${product.productName}
                            </option>`
                        )
                        .join('')}
                    </select>
                    <i class="fa fa-caret-down"></i>
                  </div>
                </div>`
            )
            .join('')}
        </div>
        <button class="applyLWBtn">Apply</button>
      `

      const btnSubmit = el.querySelector('.applyLWBtn')
      btnSubmit &&
        btnSubmit.addEventListener('click', (e) => {
          this.applyLwFnc(el, component)
          btnSubmit.classList.add('hidden')
        })

      const lwDdls = el.querySelectorAll('select')
      Array.prototype.slice.call(lwDdls).forEach((elm) => {
        elm.addEventListener('change', () => {
          this.onChangeLw(el)
        })
      })

      return el
    },
    onChangeLw(el) {
      const btnSubmit = el.querySelector('.applyLWBtn')
      btnSubmit.classList.remove('hidden')
    },
    applyLwFnc(elInput, component) {
      const lwBox = component.get('lwBox') || []
      if (!lwBox.length) return
      lwBox.map((item) => delete item.mainProductId)

      const lwProductDdls = elInput.querySelectorAll('.lw-item .lw-product select')
      Array.prototype.slice.call(lwProductDdls).forEach((lwProductDdl) => {
        const lwOption = lwProductDdl.options[lwProductDdl.selectedIndex]
        const lwProductId = Number(lwOption.getAttribute('value'))
        const lwObj = lwBox.find((obj) => obj.productId === lwProductId)
        lwObj.mainProductId = Number(lwProductDdl.getAttribute('main-productid'))
      })
      component.set('lwBox', [])
      component.set('lwBox', lwBox)
    }
  })
}
