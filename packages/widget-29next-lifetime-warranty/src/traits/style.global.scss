.gjs-trt-trait__wrp-getProductListLW {
  padding-bottom: 10px;
  .gjs-trt-trait--button {
    margin-bottom: 0;
  }
  button {
    background-color: black;
    border: 1px solid #898989;
    border-radius: 0;
    font-weight: bold;
    &:hover {
      background-color: #2c2c2c;
    }
  }
}
.gjs-trt-trait__wrp-lwBox {
  .gjs-field-lwBox {
    border: none;
  }
  .gjs-label {
    font-weight: bold;
  }
  .lw-items {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid white;
    margin-top: 5px;
  }
  .lw-item + .lw-item{
    padding-top: 10px;
    margin-top: 10px;
  }
  .main-product {
    font-weight: bold;
    margin-bottom: 3px;
  }
  .lw-product {
    position: relative;
    display: flex;
    align-items: center;
    font-weight: bold;
    select {
      border: 1px solid white;
      padding: 2px 15px 2px 5px;
      margin-left: 5px;
    }
    .fa-caret-down {
      position: absolute;
      pointer-events: none;
      top: 7px;
      right: 5px;
    }
  }
  .applyLWBtn {
    display: block;
    background: black;
    border: 1px solid #989898;
    width: 100%;
    padding: 5px;
    font-weight: bold;
    cursor: pointer;
    &.hidden {
      display: none !important;
    }
    &:hover {
      background-color: #2c2c2c;
    }
  }
}
