#id {
  padding: 15px;
  background-color: #fffbcf;
  border: 2px solid #fff591;
  border-radius: 3px;
  .title {
    overflow: hidden;
    padding: 10px;
    border-radius: 3px;
    background-color: #ffc438;
    .label {
      position: relative;
      display: inline-block;
      padding: 1px 0 0 25px;
      font-size: 14px;
      line-height: 1.2em;
      font-family: sans-serif;
      font-weight: bold;
      min-height: auto;
      cursor: pointer;
    }
    input {
      position: absolute;
      pointer-events: none;
      opacity: 0;
      &.checked ~ .checkmark,
      &:checked ~ .checkmark {
        .icon {
          opacity: 1;
        }
      }
    }
    .checkmark {
      position: absolute;
      top: 0;
      left: 0;
      width: 18px;
      height: 18px;
      background-color: white;
      border: 2px solid #e3a920;
      .icon {
        position: absolute;
        top: 2px;
        right: 2px;
        bottom: 2px;
        left: 2px;
        background-color: black;
        opacity: 0;
      }
    }
  }
  .desc {
    margin-top: 15px;
  }
}
.loading {
  font-size: 0;
}
.loading::before {
  content: "";
  background-image: url(https://d16hdrba6dusey.cloudfront.net/sitecommon/images/loading-price-v1.gif);
  display: inline-block;
  width: 20px;
  height: 10px;
  background-size: contain;
  background-repeat: no-repeat;
}
