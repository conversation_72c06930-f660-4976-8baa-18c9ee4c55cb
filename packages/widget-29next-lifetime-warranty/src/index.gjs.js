/**
 * @widgetName widget-29next-lifetime-warranty
 * @version 1.0.0
 * @see WIDGET_STORY
 * <AUTHOR> <<EMAIL>>
 */

import { widgetControllerWrapper } from 'shared-gjs-utils/src/widgetControllerWrapper'
import componentController from './controller.mod'
import widgetStyle from './style.scss'
import { WIDGET_SETTINGS } from './settings'
import getViewDefinition from './viewDefinition'
import loadTraits from './traits'
import './traits/style.global.scss'

export default (editor, config) => {
  loadTraits(editor, config)

  const { widgetName, widgetTypeId, packageName } = config
  const domComponents = editor.DomComponents

  domComponents.addType(widgetTypeId, {
    model: {
      defaults: {
        classes: ['js-lw-29next'],
        name: widgetName,
        css: widgetStyle.toString(),
        ...WIDGET_SETTINGS,
        'script-props': ['defaultChecked'],
        script: widgetControllerWrapper(componentController, { packageName })
      },
      init() {
        const proBoxData = this.get('lwBox')
        if (proBoxData && proBoxData.length) {
          this.addTrait(
            {
              label: 'Connect Lifetime Warranty With Main Item',
              name: 'lwBox',
              type: 'lwBox'
            },
            { at: 1 }
          )
        }
      }
    },
    view: getViewDefinition(editor, config)
  })
}
