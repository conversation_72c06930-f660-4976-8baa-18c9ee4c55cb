/**
 * <AUTHOR> <<EMAIL>>
 * widget-29next-lifetime-warranty - view definition
 */

import _get from 'lodash/fp/get'
import widgetTemplate from './template.html'
const viewDefinition = (editor, { Builder, appActions: { setNotify } }) => ({
  init({ model }) {
    editor.Commands.add('getProductListLW', () => {
      this.getProductListLW()
    })
    this.listenTo(model, 'change:defaultChecked', this.onChangeDefaultChecked)
    this.listenTo(model, 'change:lwBox', this.onApplyLWItems)
  },
  getProductListLW() {
    try {
      const _29nextProductListComponent = editor.DomComponents.getWrapper().find('.js-29next-product-list')[0]
      if (!_29nextProductListComponent) return
      const products = _29nextProductListComponent.get('proBox')
      if (!products.length) return
      this.model.set('lwBox', [])
      this.model.set('lwBox', products)
      this.model.removeTrait('lwBox')
      this.model.addTrait(
        {
          label: 'Connect Lifetime Warranty With Main Item',
          name: 'lwBox',
          type: 'lwBox'
        },
        { at: 1 }
      )
    } catch (e) {
      console.log(e)
    }
  },
  onChangeDefaultChecked(_, checked) {
    const checkbox = _.findType('checkbox')[0]
    if (checked) {
      checkbox.setAttributes({ checked: true })
      checkbox.addClass('checked')
    } else {
      const attr = checkbox.getAttributes()
      delete attr.checked
      checkbox.setAttributes(attr)
      checkbox.removeClass('checked')
    }
  },
  onApplyLWItems() {
    const lwBox = this.model.get('lwBox')
    if (lwBox && lwBox.length) {
      const lwItems = lwBox.filter((item) => Object.prototype.hasOwnProperty.call(item, 'mainProductId'))
      let lwProductIDs = '{'
      for (let i = 0, n = lwItems.length; i < n; i++) {
        const mainProductId = lwItems[i].mainProductId
        const lwProductId = lwItems[i].productId
        lwProductIDs += `"${mainProductId}": ${lwProductId}`
        if (i < n - 1) {
          lwProductIDs += ', '
        }
      }
      lwProductIDs += '}'
      this.model.setAttributes({ lwProductIDs: lwProductIDs })
    }
  },
  onRender({ model }) {
    if (_get('isPublishing', Builder.getConfig())) {
      return
    }
    if (model.findType('lifetimeWarranty').length === 0) {
      model.components(widgetTemplate)
      this.getProductListLW()
    }
  }
})

export default viewDefinition
