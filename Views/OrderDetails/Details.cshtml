@model HSU_NguyenHoangThanhSang_22207613.Models.OrderDetail

@{
    ViewData["Title"] = "Chi tiết đơn hàng #" + Model.Id.ToString("D6");
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-alt me-2"></i>Chi tiết đơn hàng #@Model.Id.ToString("D6")
                </h5>
                <div>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Thông tin đơn hàng -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Thông tin đơn hàng</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Mã đơn hàng:</strong></td>
                                        <td>
                                            <a asp-controller="Orders" asp-action="Details" asp-route-id="@Model.Order.Id" class="text-decoration-none">
                                                <span class="badge bg-primary">#@Model.Order.Id.ToString("D6")</span>
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Khách hàng:</strong></td>
                                        <td>@Model.Order.Account.FirstName @Model.Order.Account.LastName</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>@Model.Order.Account.Email</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ngày đặt:</strong></td>
                                        <td>@Model.Order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Trạng thái:</strong></td>
                                        <td>
                                            @if (Model.Order.OrderStatus != null)
                                            {
                                                <span class="badge bg-info">@Model.Order.OrderStatus.Name</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Chưa xác định</span>
                                            }
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Thông tin sản phẩm -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-box me-2"></i>Thông tin sản phẩm</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    @if (!string.IsNullOrEmpty(Model.Product?.Thumbnail))
                                    {
                                        <img src="@Model.Product.Thumbnail" alt="@Model.Product.Name" class="img-thumbnail me-3" style="width: 80px; height: 80px; object-fit: cover;" />
                                    }
                                    <div>
                                        <h6 class="mb-1">@(Model.Product?.Name ?? "Sản phẩm không xác định")</h6>
                                        <small class="text-muted">@(Model.Product?.Category?.Name ?? "Chưa phân loại")</small>
                                    </div>
                                </div>
                                
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Đơn giá:</strong></td>
                                        <td>@(Model.Product?.Price.ToString("N0") ?? "0") ₫</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Số lượng:</strong></td>
                                        <td><span class="badge bg-info">@Model.Quantity</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Thành tiền:</strong></td>
                                        <td>
                                            @{
                                                var total = (Model.Product?.Price ?? 0) * Model.Quantity;
                                            }
                                            <h5 class="text-primary mb-0">@total.ToString("N0") ₫</h5>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thông tin chi tiết -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Thông tin chi tiết</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>ID chi tiết:</strong></td>
                                                <td><span class="badge bg-primary">#@Model.Id.ToString("D6")</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Ngày tạo:</strong></td>
                                                <td>@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>Trạng thái:</strong></td>
                                                <td>
                                                    @if (Model.IsActive)
                                                    {
                                                        <span class="badge bg-success">Hoạt động</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">Đã xóa</span>
                                                    }
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
