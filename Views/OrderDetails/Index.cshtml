@model IEnumerable<HSU_NguyenHoangThanhSang_22207613.Models.OrderDetail>

@{
    ViewData["Title"] = "Quản lý Chi tiết đơn hàng";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-alt me-2"></i>Danh sách Chi tiết đơn hàng
                </h5>
            </div>
            <div class="card-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }
                
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Đơn hàng</th>
                                    <th>Sản phẩm</th>
                                    <th>Đơn giá</th>
                                    <th>Số lượng</th>
                                    <th>Thành tiền</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">#@item.Id.ToString("D6")</span>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>Đơn hàng #@(item.Order?.Id.ToString("D6") ?? "N/A")</strong>
                                                <br><small class="text-muted">@(item.Order?.Account?.FirstName ?? "") @(item.Order?.Account?.LastName ?? "")</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(item.Product?.Thumbnail))
                                                {
                                                    <img src="@item.Product.Thumbnail" alt="@item.Product.Name" class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;" />
                                                }
                                                <div>
                                                    <strong>@(item.Product?.Name ?? "Sản phẩm không xác định")</strong>
                                                    <br><small class="text-muted">@(item.Product?.Category?.Name ?? "Chưa phân loại")</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>@(item.Product?.Price.ToString("N0") ?? "0") ₫</td>
                                        <td><span class="badge bg-info">@item.Quantity</span></td>
                                        <td>
                                            @{
                                                var total = (item.Product?.Price ?? 0) * item.Quantity;
                                            }
                                            <strong>@total.ToString("N0") ₫</strong>
                                        </td>
                                        <td>
                                            <small>@item.CreatedDate.ToString("dd/MM/yyyy HH:mm")</small>
                                        </td>
                                        <td>
                                            <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Chưa có chi tiết đơn hàng nào</h5>
                        <p class="text-muted">Chi tiết đơn hàng được tạo tự động khi tạo đơn hàng.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
