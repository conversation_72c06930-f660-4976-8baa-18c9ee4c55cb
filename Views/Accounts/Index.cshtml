@model IEnumerable<HSU_NguyenHoangThanhSang_22207613.Models.Account>

@{
    ViewData["Title"] = "Quản lý Tà<PERSON> khoản";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-friends me-2"></i><PERSON>h sách Tà<PERSON>hoản
                </h5>
            </div>
            <div class="card-body">
                @{
                    ViewBag.SearchPlaceholder = "Tìm kiếm tài khoản theo tên đăng nhập, họ tên, email...";
                    ViewBag.SearchId = "accountSearch";
                    ViewBag.TableId = "accountsTable";
                }

                @section ActionButtons {
                    <a asp-action="CreateOrEdit" class="btn btn-create">
                        <i class="fas fa-plus me-2"></i>Thêm tài khoản mới
                    </a>
                }

                @await Html.PartialAsync("_SearchComponent")
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }
                
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                <div class="table-responsive">
                    <table id="accountsTable" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Tên đăng nhập</th>
                                <th>Họ tên</th>
                                <th>Email</th>
                                <th>Nhóm quyền</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.Any())
                            {
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <span class="badge bg-info">@item.UserName</span>
                                        </td>
                                        <td>@item.FirstName @item.LastName</td>
                                        <td>
                                            <a href="mailto:@item.Email" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i>@item.Email
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">@item.Group.Name</span>
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">Hoạt động</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">Không hoạt động</span>
                                            }
                                        </td>
                                        <td>@item.CreatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="CreateOrEdit" asp-route-id="@item.Id" class="btn btn-sm btn-warning" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" title="Xóa"
                                                        onclick="showDeleteConfirm('@Url.Action("Delete", new { id = item.Id })', 'Bạn có chắc chắn muốn xóa tài khoản &quot;@item.UserName&quot;?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                        Chưa có dữ liệu tài khoản nào.
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")

@section Scripts {
    <script>
        // Auto hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
