@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Đăng nhập - HSU Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .login-header {
            background-color: #343a40;
            color: #ffffff;
            padding: 2rem;
            text-align: center;
            position: relative;
        }
        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .login-header > * {
            position: relative;
            z-index: 1;
        }
        .login-body {
            padding: 2rem;
            background: #ffffff;
        }
        .form-control {
            border-radius: 0.25rem;
            border: 1px solid #ced4da;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            background: #ffffff;
        }
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            background: white;
            transform: none;
        }
        .form-label {
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
        }
        .btn-login {
            background-color: #007bff;
            border: 1px solid #007bff;
            border-radius: 0.25rem;
            padding: 0.375rem 0.75rem;
            font-weight: 400;
            text-transform: none;
            letter-spacing: normal;
            font-size: 1rem;
            color: #ffffff;
            transition: all 0.15s ease-in-out;
            position: relative;
            overflow: hidden;
        }
        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-login:hover {
            transform: none;
            box-shadow: none;
            background-color: #0056b3;
            border-color: #004085;
            color: #ffffff;
        }
        .btn-login:hover::before {
            left: 100%;
        }
        .alert-danger {
            background: linear-gradient(135deg, #ffebee 0%, #fff2f0 100%);
            border: 1px solid #ff7875;
            color: #cf1322;
            border-radius: 10px;
        }
        .text-muted {
            color: #636e72 !important;
        }
        .login-icon {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #1a202c;
        }
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .login-card {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="login-header">
                        <div class="login-icon">
                            <i class="fas fa-water fa-2x"></i>
                        </div>
                        <h4 class="mb-2">HSU Management</h4>
                        <p class="mb-0 opacity-75">Hệ thống quản lý thương mại điện tử</p>
                    </div>
                    <div class="login-body">
                        @if (!string.IsNullOrEmpty(ViewBag.Error))
                        {
                            <div class="alert alert-danger border-0" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @ViewBag.Error
                            </div>
                        }
                        
                        <form method="post" asp-action="Login">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2" style="color: #2b6cb0;"></i>Tên đăng nhập
                                </label>
                                <input type="text" class="form-control" id="username" placeholder="Nhập tên đăng nhập của bạn" name="username" required>
                            </div>
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2" style="color: #2b6cb0;"></i>Mật khẩu
                                </label>
                                <input type="password" class="form-control" id="password" placeholder="Nhập mật khẩu của bạn" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary btn-login w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập hệ thống
                            </button>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <div class="p-3 rounded-3" style="background: rgba(168, 218, 220, 0.1); border: 1px solid rgba(168, 218, 220, 0.2);">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-2" style="color: #2b6cb0;"></i>
                                    <strong>Tài khoản demo:</strong><br>
                                    <span class="fw-bold" style="color: #2b6cb0;">sangnht</span> | <span class="fw-bold" style="color: #2b6cb0;">Admin@1234</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
