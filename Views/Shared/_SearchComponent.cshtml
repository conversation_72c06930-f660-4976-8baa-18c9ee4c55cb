@{
    var searchPlaceholder = ViewBag.SearchPlaceholder ?? "T<PERSON><PERSON> kiếm...";
    var searchId = ViewBag.SearchId ?? "searchInput";
    var tableId = ViewBag.TableId ?? "dataTable";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="search-container">
        <input type="text" 
               id="@searchId" 
               class="search-input" 
               placeholder="@searchPlaceholder"
               autocomplete="off">
        <i class="fas fa-search search-icon"></i>
        <button type="button" class="search-clear" id="@(searchId)Clear">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="d-flex gap-2">
        @if (ViewBag.ActionButtons != null)
        {
            @Html.Raw(ViewBag.ActionButtons)
        }
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('@searchId');
    const clearButton = document.getElementById('@(searchId)Clear');
    const table = document.getElementById('@tableId');
    
    if (!searchInput || !table) return;
    
    // Show/hide clear button
    searchInput.addEventListener('input', function() {
        if (this.value.length > 0) {
            clearButton.classList.add('show');
        } else {
            clearButton.classList.remove('show');
        }
        performSearch();
    });
    
    // Clear search
    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        clearButton.classList.remove('show');
        performSearch();
        searchInput.focus();
    });
    
    // Search functionality
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = searchTerm === '' || text.includes(searchTerm);
            
            if (isVisible) {
                row.style.display = '';
                visibleCount++;
                // Highlight search term
                highlightSearchTerm(row, searchTerm);
            } else {
                row.style.display = 'none';
            }
        });
        
        // Show/hide no results message
        showNoResults(tbody, visibleCount === 0 && searchTerm !== '');
    }
    
    function highlightSearchTerm(row, searchTerm) {
        if (searchTerm === '') {
            // Remove existing highlights
            row.querySelectorAll('.search-highlight').forEach(el => {
                el.outerHTML = el.textContent;
            });
            return;
        }
        
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
            if (cell.querySelector('a, button')) return; // Skip cells with links/buttons
            
            const text = cell.textContent;
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
            
            if (highlightedText !== text) {
                cell.innerHTML = highlightedText;
            }
        });
    }
    
    function showNoResults(tbody, show) {
        let noResultsRow = tbody.querySelector('.no-results-row');
        
        if (show) {
            if (!noResultsRow) {
                const colCount = tbody.closest('table').querySelectorAll('thead th').length;
                noResultsRow = document.createElement('tr');
                noResultsRow.className = 'no-results-row';
                noResultsRow.innerHTML = `
                    <td colspan="${colCount}" class="no-results">
                        <i class="fas fa-search"></i><br>
                        Không tìm thấy kết quả nào phù hợp với từ khóa tìm kiếm.
                    </td>
                `;
                tbody.appendChild(noResultsRow);
            }
        } else {
            if (noResultsRow) {
                noResultsRow.remove();
            }
        }
    }
    
    // Enter key to focus search
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
        }
    });
});
</script>
