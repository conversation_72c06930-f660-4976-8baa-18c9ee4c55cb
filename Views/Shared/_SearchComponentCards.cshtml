@{
    var searchPlaceholder = ViewBag.SearchPlaceholder ?? "T<PERSON><PERSON> kiếm...";
    var searchId = ViewBag.SearchId ?? "searchInput";
    var containerId = ViewBag.ContainerId ?? "cardsContainer";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="search-container">
        <input type="text" 
               id="@searchId" 
               class="search-input" 
               placeholder="@searchPlaceholder"
               autocomplete="off">
        <i class="fas fa-search search-icon"></i>
        <button type="button" class="search-clear" id="@(searchId)Clear">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="d-flex gap-2">
        @RenderSection("ActionButtons", required: false)
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('@searchId');
    const clearButton = document.getElementById('@(searchId)Clear');
    const container = document.getElementById('@containerId');
    
    if (!searchInput || !container) return;
    
    // Show/hide clear button
    searchInput.addEventListener('input', function() {
        if (this.value.length > 0) {
            clearButton.classList.add('show');
        } else {
            clearButton.classList.remove('show');
        }
        performSearch();
    });
    
    // Clear search
    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        clearButton.classList.remove('show');
        performSearch();
        searchInput.focus();
    });
    
    // Search functionality for cards
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const cards = container.querySelectorAll('.card');
        let visibleCount = 0;
        
        cards.forEach(card => {
            const text = card.textContent.toLowerCase();
            const isVisible = searchTerm === '' || text.includes(searchTerm);
            
            if (isVisible) {
                card.style.display = '';
                visibleCount++;
                // Highlight search term
                highlightSearchTerm(card, searchTerm);
            } else {
                card.style.display = 'none';
            }
        });
        
        // Show/hide no results message
        showNoResults(container, visibleCount === 0 && searchTerm !== '');
    }
    
    function highlightSearchTerm(card, searchTerm) {
        if (searchTerm === '') {
            // Remove existing highlights
            card.querySelectorAll('.search-highlight').forEach(el => {
                el.outerHTML = el.textContent;
            });
            return;
        }
        
        // Only highlight text content, avoid buttons and links
        const textElements = card.querySelectorAll('h6, small, .text-muted, td');
        textElements.forEach(element => {
            if (element.querySelector('a, button, .badge')) return; // Skip elements with interactive content
            
            const text = element.textContent;
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
            
            if (highlightedText !== text) {
                element.innerHTML = highlightedText;
            }
        });
    }
    
    function showNoResults(container, show) {
        let noResultsDiv = container.querySelector('.no-results-card');
        
        if (show) {
            if (!noResultsDiv) {
                noResultsDiv = document.createElement('div');
                noResultsDiv.className = 'no-results-card card text-center py-5';
                noResultsDiv.innerHTML = `
                    <div class="card-body">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Không tìm thấy kết quả</h5>
                        <p class="text-muted">Không có dữ liệu nào phù hợp với từ khóa tìm kiếm của bạn.</p>
                    </div>
                `;
                container.appendChild(noResultsDiv);
            }
        } else {
            if (noResultsDiv) {
                noResultsDiv.remove();
            }
        }
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
        }
        
        if (e.key === 'Escape' && document.activeElement === searchInput) {
            searchInput.blur();
        }
    });
});
</script>
