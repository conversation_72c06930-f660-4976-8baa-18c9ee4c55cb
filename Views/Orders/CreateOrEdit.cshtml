@model HSU_NguyenHoangThanhSang_22207613.Models.Order

@{
    ViewData["Title"] = Model.Id == 0 ? "Thêm mới Đơn hàng" : "Chỉnh sửa Đơn hàng";
    var isEdit = Model.Id != 0;
}

<div class="checkout-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card-modern checkout-card fade-in-up">
                    <div class="checkout-header text-center bg-primary">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-@(isEdit ? "edit" : "plus") me-2"></i>@ViewData["Title"]
                        </h4>
                        <p class="mb-0 mt-2 text-white opacity-75">@(isEdit ? "Cập nhật thông tin đơn hàng" : "Tạo đơn hàng mới trong hệ thống")</p>
                    </div>
                    <div class="card-body p-4">
                        <form asp-action="CreateOrEdit" method="post">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger rounded-3" role="alert"></div>

                            @if (isEdit)
                            {
                                <input type="hidden" asp-for="Id" />
                                <input type="hidden" asp-for="CreatedDate" />
                            }

                            <div class="checkout-step bg-light border-start border-primary border-4">
                                <h6 class="mb-3 text-primary">
                                    <i class="fas fa-info-circle me-2 text-primary"></i>Thông tin Đơn hàng
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label asp-for="AccountId" class="form-label fw-medium">
                                            <i class="fas fa-user me-2 text-primary"></i>Khách hàng <span class="text-danger">*</span>
                                        </label>
                                        <select asp-for="AccountId" class="form-select form-control-modern" asp-items="ViewBag.AccountId">
                                            <option value="">-- Chọn khách hàng --</option>
                                        </select>
                                        <span asp-validation-for="AccountId" class="text-danger small"></span>
                                    </div>

                                    <div class="col-md-6">
                                        <label asp-for="OrderDate" class="form-label fw-medium">
                                            <i class="fas fa-calendar-alt me-2 text-primary"></i>Ngày đặt hàng <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="OrderDate" class="form-control form-control-modern" type="datetime-local" />
                                        <span asp-validation-for="OrderDate" class="text-danger small"></span>
                                    </div>

                                    <div class="col-md-6">
                                        <label asp-for="OrderStatusId" class="form-label fw-medium">
                                            <i class="fas fa-flag me-2 text-primary"></i>Trạng thái đơn hàng
                                        </label>
                                        <select asp-for="OrderStatusId" class="form-select form-control-modern" asp-items="ViewBag.OrderStatusId">
                                            <option value="">-- Chọn trạng thái --</option>
                                        </select>
                                        <span asp-validation-for="OrderStatusId" class="text-danger small"></span>
                                        <small class="form-text text-muted">Có thể để trống và cập nhật sau</small>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mt-4">
                                            <div class="form-check form-switch">
                                                <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                                                <label asp-for="IsActive" class="form-check-label fw-medium">
                                                    <i class="fas fa-toggle-on me-2 text-success"></i>Trạng thái hoạt động
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Bật để kích hoạt đơn hàng này</small>
                                        </div>
                                    </div>

                                    @if (isEdit)
                                    {
                                        <div class="col-12">
                                            <label class="form-label fw-medium">
                                                <i class="fas fa-clock me-2 text-primary"></i>Ngày tạo
                                            </label>
                                            <input type="text" class="form-control form-control-modern" value="@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")" readonly />
                                        </div>
                                    }
                                </div>
                            </div>

                            <div class="alert alert-info border-0 rounded-3 bg-light">
                                <i class="fas fa-lightbulb me-2 text-warning"></i>
                                <strong>Mẹo:</strong> Sau khi tạo đơn hàng, bạn có thể thêm sản phẩm vào đơn hàng trong trang chi tiết đơn hàng.
                            </div>

                            <div class="d-flex justify-content-between gap-3 mt-4">
                                <a asp-action="Index" class="btn btn-outline-secondary rounded-3 px-4">
                                    <i class="fas fa-arrow-left me-2"></i>Quay lại
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg px-5 pulse-on-hover">
                                    <i class="fas fa-@(isEdit ? "save" : "plus") me-2"></i>@(isEdit ? "Cập nhật" : "Tạo mới")
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var orderDateInput = document.getElementById('OrderDate');
            if (orderDateInput && !orderDateInput.value) {
                var now = new Date();
                now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                orderDateInput.value = now.toISOString().slice(0, 16);
            }
        });
    </script>
}
