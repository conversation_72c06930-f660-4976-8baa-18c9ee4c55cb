@model HSU_NguyenHoangThanhSang_22207613.Models.ViewModels.OrderCreateViewModel

@{
    ViewData["Title"] = "Tạo mới Đơn hàng với Chi tiết";
}

<div class="checkout-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="card-modern checkout-card fade-in-up">
                    <div class="checkout-header text-center bg-primary">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-shopping-cart me-2"></i>Tạo Đơn Hàng Mới
                        </h4>
                        <p class="mb-0 mt-2 text-white opacity-75">Điền thông tin để tạo đơn hàng với chi tiết sản phẩm</p>
                    </div>
                    <div class="card-body p-4">
                        <form asp-action="CreateWithDetails" method="post" id="orderForm">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger rounded-3" role="alert"></div>

                            <div class="checkout-step bg-light border-start border-primary border-4">
                                <h6 class="mb-3 text-primary">
                                    <i class="fas fa-user-circle me-2 text-primary"></i>Bước 1: Thông tin Đơn hàng
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label asp-for="AccountId" class="form-label fw-medium">
                                            <i class="fas fa-user me-2 text-primary"></i>Khách hàng <span class="text-danger">*</span>
                                        </label>
                                        <select asp-for="AccountId" class="form-select form-control-modern" asp-items="ViewBag.AccountId">
                                            <option value="">-- Chọn khách hàng --</option>
                                        </select>
                                        <span asp-validation-for="AccountId" class="text-danger small"></span>
                                    </div>

                                    <div class="col-md-6">
                                        <label asp-for="OrderDate" class="form-label fw-medium">
                                            <i class="fas fa-calendar-alt me-2 text-primary"></i>Ngày đặt hàng <span class="text-danger">*</span>
                                        </label>
                                        <input asp-for="OrderDate" class="form-control form-control-modern" type="datetime-local" />
                                        <span asp-validation-for="OrderDate" class="text-danger small"></span>
                                    </div>

                                    <div class="col-md-6">
                                        <label asp-for="OrderStatusId" class="form-label fw-medium">
                                            <i class="fas fa-flag me-2 text-primary"></i>Trạng thái đơn hàng
                                        </label>
                                        <select asp-for="OrderStatusId" class="form-select form-control-modern" asp-items="ViewBag.OrderStatusId">
                                            <option value="">-- Chọn trạng thái --</option>
                                        </select>
                                        <span asp-validation-for="OrderStatusId" class="text-danger small"></span>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mt-4">
                                            <div class="form-check form-switch">
                                                <input asp-for="IsActive" class="form-check-input" type="checkbox" role="switch" />
                                                <label asp-for="IsActive" class="form-check-label fw-medium">
                                                    <i class="fas fa-toggle-on me-2 text-success"></i>Trạng thái hoạt động
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="checkout-step bg-light border-start border-primary border-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0 text-primary">
                                        <i class="fas fa-shopping-bag me-2 text-primary"></i>Bước 2: Chi tiết Sản phẩm
                                    </h6>
                                    <button type="button" class="btn btn-primary btn-sm pulse-on-hover" onclick="addOrderDetail()">
                                        <i class="fas fa-plus me-1"></i>Thêm sản phẩm
                                    </button>
                                </div>

                                <div id="orderDetailsContainer" class="mb-4">
                                </div>

                                <div class="alert alert-info border-0 rounded-3 bg-light">
                                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                                    <strong>Mẹo:</strong> Nhấn "Thêm sản phẩm" để thêm sản phẩm vào đơn hàng. Bạn có thể thêm nhiều sản phẩm khác nhau.
                                </div>

                                <div class="card-modern bg-gradient" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0 text-dark">
                                                <i class="fas fa-calculator me-2"></i>Tổng cộng:
                                            </h5>
                                            <h3 class="mb-0 fw-bold" style="color: var(--primary-color);" id="grandTotal">0 ₫</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between gap-3 mt-4">
                                <a asp-action="Index" class="btn btn-outline-secondary rounded-3 px-4">
                                    <i class="fas fa-arrow-left me-2"></i>Quay lại
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg px-5 pulse-on-hover">
                                    <i class="fas fa-check me-2"></i>Tạo đơn hàng
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <script>
        let detailIndex = 0;

        const originalProducts = @Html.Raw(Json.Serialize(ViewBag.Products ?? new { }));
        const products = Object.entries(originalProducts).reduce((product, [key, value]) => {
            product[key] = {
                Name: value.name,
                Price: value.price
            };
            return product;
        }, {});

        document.addEventListener('DOMContentLoaded', function () {
            var orderDateInput = document.getElementById('OrderDate');
            if (orderDateInput && !orderDateInput.value) {
                var now = new Date();
                now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                orderDateInput.value = now.toISOString().slice(0, 16);
            }

            addOrderDetail();
        });

        function addOrderDetail() {
            const container = document.getElementById('orderDetailsContainer');
            if (!container) return;

            let productOptions = '<option value="">-- Chọn sản phẩm --</option>';

            if (products && typeof products === 'object' && Object.keys(products).length > 0) {
                Object.keys(products).forEach(productId => {
                    const product = products[productId];
                    if (product && product.Name && product.Price !== undefined) {
                        productOptions += `<option value="${productId}">${product.Name} - ${product.Price.toLocaleString('vi-VN')} ₫</option>`;
                    }
                });
            } else {
                productOptions += '<option value="">Không có sản phẩm nào</option>';
            }

            const detailHtml = `
                <div class="card-modern mb-3 order-detail-row fade-in-up" data-index="${detailIndex}">
                    <div class="card-body p-3">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-box me-2 text-primary"></i>Sản phẩm
                                </label>
                                <select name="OrderDetails[${detailIndex}].ProductId" class="form-select form-control-modern product-select" onchange="updatePrice(${detailIndex})">
                                    ${productOptions}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-medium">Đơn giá</label>
                                <input type="text" class="form-control form-control-modern price-display" readonly placeholder="0 ₫">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-medium">Số lượng</label>
                                <input name="OrderDetails[${detailIndex}].Quantity" type="number" class="form-control form-control-modern quantity-input" min="1" value="1" onchange="updateTotal()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-medium">Thành tiền</label>
                                <input type="text" class="form-control form-control-modern total-display" readonly placeholder="0 ₫">
                            </div>
                            <div class="col-md-1">
                                <button type="button" class="btn btn-outline-danger btn-sm rounded-3" onclick="removeOrderDetail(${detailIndex})" title="Xóa sản phẩm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', detailHtml);
            detailIndex++;
        }

        function removeOrderDetail(index) {
            const row = document.querySelector(`[data-index="${index}"]`);
            if (row) {
                row.remove();
                updateTotal();
            }
        }

        function updatePrice(index) {
            const row = document.querySelector(`[data-index="${index}"]`);
            const productSelect = row.querySelector('.product-select');
            const priceDisplay = row.querySelector('.price-display');
            const quantityInput = row.querySelector('.quantity-input');

            if (productSelect.value && products[productSelect.value]) {
                const price = products[productSelect.value].Price;
                priceDisplay.value = price.toLocaleString('vi-VN') + ' ₫';
                updateTotal();
            } else {
                priceDisplay.value = '0 ₫';
                updateTotal();
            }
        }

        function updateTotal() {
            let grandTotal = 0;

            document.querySelectorAll('.order-detail-row').forEach(row => {
                const productSelect = row.querySelector('.product-select');
                const quantityInput = row.querySelector('.quantity-input');
                const totalDisplay = row.querySelector('.total-display');

                if (productSelect.value && products[productSelect.value] && quantityInput.value) {
                    const price = products[productSelect.value].Price;
                    const quantity = parseInt(quantityInput.value) || 0;
                    const total = price * quantity;

                    totalDisplay.value = total.toLocaleString('vi-VN') + ' ₫';
                    grandTotal += total;
                } else {
                    totalDisplay.value = '0 ₫';
                }
            });

            document.getElementById('grandTotal').textContent = grandTotal.toLocaleString('vi-VN') + ' ₫';
        }
    </script>
}
