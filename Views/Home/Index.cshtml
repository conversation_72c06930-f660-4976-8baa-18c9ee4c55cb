@model HSU_NguyenHoangThanhSang_22207613.Models.ViewModels.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard - Tổng quan hệ thống
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Khách hàng</h6>
                                        <h3 class="mb-0">@Model.TotalCustomers</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="@Url.Action("Index", "Accounts")" class="text-white text-decoration-none">
                                    <small>Xem chi tiết <i class="fas fa-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Sản phẩm</h6>
                                        <h3 class="mb-0">@Model.TotalProducts</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-box fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="@Url.Action("Index", "Products")" class="text-white text-decoration-none">
                                    <small>Xem chi tiết <i class="fas fa-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Đơn hàng</h6>
                                        <h3 class="mb-0">@Model.TotalOrders</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-shopping-cart fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="@Url.Action("Index", "Orders")" class="text-white text-decoration-none">
                                    <small>Xem chi tiết <i class="fas fa-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Danh mục</h6>
                                        <h3 class="mb-0">@Model.TotalCategories</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-newspaper fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="@Url.Action("Index", "Articles")" class="text-white text-decoration-none">
                                    <small>Xem chi tiết <i class="fas fa-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thống kê bổ sung -->
                <div class="row mt-4">
                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Tổng doanh thu</h6>
                                        <h3 class="mb-0">@Model.TotalRevenue.ToString("N0") ₫</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <small>Từ tất cả đơn hàng</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="card bg-dark text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Đơn hàng hôm nay</h6>
                                        <h3 class="mb-0">@Model.TodayOrders</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-day fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <small>@DateTime.Today.ToString("dd/MM/yyyy")</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sản phẩm bán chạy và đơn hàng gần đây -->
                <div class="row mt-4">
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-fire me-2"></i>Sản phẩm bán chạy
                                </h6>
                            </div>
                            <div class="card-body">
                                @if (Model.TopSellingProducts.Any())
                                {
                                    <div class="list-group list-group-flush">
                                        @foreach (var product in Model.TopSellingProducts)
                                        {
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <span>@product.ProductName</span>
                                                <span class="badge bg-primary rounded-pill">@product.TotalSold</span>
                                            </div>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <p class="text-muted mb-0">Chưa có dữ liệu bán hàng</p>
                                }
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-clock me-2"></i>Đơn hàng gần đây
                                </h6>
                            </div>
                            <div class="card-body">
                                @if (Model.RecentOrders.Any())
                                {
                                    <div class="list-group list-group-flush">
                                        @foreach (var order in Model.RecentOrders)
                                        {
                                            <div class="list-group-item">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <strong>#@order.Id.ToString("D6")</strong>
                                                        <br>
                                                        <small class="text-muted">@order.Account.FirstName @order.Account.LastName</small>
                                                    </div>
                                                    <div class="text-end">
                                                        @if (order.OrderStatus != null)
                                                        {
                                                            var badgeClass = order.OrderStatus.Code switch
                                                            {
                                                                "PENDING" => "bg-warning",
                                                                "PROCESSING" => "bg-info",
                                                                "SHIPPED" => "bg-primary",
                                                                "DELIVERED" => "bg-success",
                                                                "CANCELLED" => "bg-danger",
                                                                _ => "bg-secondary"
                                                            };
                                                            <span class="badge @badgeClass">@order.OrderStatus.Name</span>
                                                        }
                                                        <br>
                                                        <small class="text-muted">@order.OrderDate.ToString("dd/MM/yyyy")</small>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <p class="text-muted mb-0">Chưa có đơn hàng nào</p>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
