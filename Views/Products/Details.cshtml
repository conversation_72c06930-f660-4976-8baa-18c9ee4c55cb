@model HSU_NguyenHoangThanhSang_22207613.Models.Product

@{
    ViewData["Title"] = "Chi tiết Sản phẩm";
}

<div class="row">
    <div class="col-lg-10 col-md-12 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Chi tiết Sản phẩm
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        @if (!string.IsNullOrEmpty(Model.Thumbnail))
                        {
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-image me-2"></i>Hình ảnh
                                </label>
                                <div class="text-center">
                                    <img src="@Model.Thumbnail" alt="@Model.Name" class="img-fluid rounded shadow" style="max-height: 300px;" />
                                </div>
                            </div>
                        }
                    </div>
                    
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-barcode me-2"></i>SKU
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-secondary fs-6">@Model.SKU</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-money-bill me-2"></i>Giá bán (VND)
                                    </label>
                                    <div class="form-control-plaintext">
                                        <h4 class="text-success">@Model.Price.ToString("N0") ₫</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-tag me-2"></i>Tên sản phẩm
                                    </label>
                                    <div class="form-control-plaintext">
                                        <h4>@Model.Name</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Description))
                        {
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-align-left me-2"></i>Mô tả sản phẩm
                                        </label>
                                        <div class="form-control-plaintext">
                                            <p class="text-muted">@Model.Description</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-tags me-2"></i>Danh mục
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-info fs-6">@Model.Category.Name</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-layer-group me-2"></i>Loại sản phẩm
                                    </label>
                                    <div class="form-control-plaintext">
                                        @if (Model.ProductType != null)
                                        {
                                            <span class="badge bg-secondary fs-6">@Model.ProductType.Name</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa phân loại</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-truck me-2"></i>Nhà cung cấp
                                    </label>
                                    <div class="form-control-plaintext">
                                        @if (Model.Supplier != null)
                                        {
                                            <span class="badge bg-warning fs-6">@Model.Supplier.Name</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có nhà cung cấp</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-toggle-on me-2"></i>Trạng thái
                                    </label>
                                    <div class="form-control-plaintext">
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success">Hoạt động</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Không hoạt động</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Ngày tạo
                            </label>
                            <div class="form-control-plaintext">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    </div>
                </div>

                @if (Model.OrderDetails != null && Model.OrderDetails.Any())
                {
                    <hr>
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-shopping-cart me-2"></i>Lịch sử đặt hàng (@Model.OrderDetails.Count())
                        </label>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Đơn hàng</th>
                                        <th>Khách hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Số lượng</th>
                                        <th>Thành tiền</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var orderDetail in Model.OrderDetails.Where(od => od.IsActive).Take(10))
                                    {
                                        <tr>
                                            <td>
                                                <a asp-controller="Orders" asp-action="Details" asp-route-id="@orderDetail.Order.Id" class="text-decoration-none">
                                                    #@orderDetail.Order.Id.ToString("D6")
                                                </a>
                                            </td>
                                            <td>@orderDetail.Order.Account.FirstName @orderDetail.Order.Account.LastName</td>
                                            <td>@orderDetail.Order.OrderDate.ToString("dd/MM/yyyy")</td>
                                            <td><span class="badge bg-info">@orderDetail.Quantity</span></td>
                                            <td><strong>@((orderDetail.Quantity * Model.Price).ToString("N0")) ₫</strong></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                    <div>
                        <a asp-action="CreateOrEdit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa
                        </a>
                        <button type="button" class="btn btn-danger" 
                                onclick="showDeleteConfirm('@Url.Action("Delete", new { id = Model.Id })', 'Bạn có chắc chắn muốn xóa sản phẩm &quot;@Model.Name&quot;?')">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_DeleteConfirmModal")
